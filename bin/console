#!/usr/bin/env /Applications/MAMP/bin/php/php8.2.4/bin/php
<?php

if (php_sapi_name() != 'cli' and php_sapi_name() != 'cgi-fcgi') {
    exit();
}

//DOCUMENT_ROOT
define('FULL_DOCUMENT_ROOT', str_replace('/bin', '', __DIR__));
if (!isset($_SERVER['HTTP_HOST']) and php_sapi_name() == 'cli') {
    $_SERVER['HTTP_HOST'] = '';
}

require_once 'bootstrap.php';

// Get dispatch data
$dispatcher = $container->get(MatGyver\Services\DispatcherService::class);
$data = $dispatcher->dispatch();

// Call controller's dispatch
$controller = $container->get($data['controller']);
$controller->dispatch($data['action'], $data['param'], $data['query']);
