sources:
  /opt/supervisor/: https://s3-eu-west-1.amazonaws.com/elasticbeanstalk-eu-west-1-258931655285/packages/supervisor-3.3.4-standalone.tar.gz

files:
  /etc/php.d/99-matgyver.ini:
    content: |
      upload_max_filesize = 1000M
      post_max_size = 1000M
      max_input_vars = 2000
      # redis
      session.save_handler = rediscluster
      session.save_path = "seed[]=v1-preprod-session.xxxx.clustercfg.euw1.cache.amazonaws.com:6379"

  /etc/init.d/supervisord:
    mode: "120755"
    content: /opt/supervisor/etc_init.d_supervisord

  /etc/supervisord.conf:
    content: |
      [inet_http_server]
      port=:9001

      [supervisord]
      logfile=/var/log/supervisor/supervisord.log
      logfile_maxbytes=50MB
      logfile_backups=10
      loglevel=info
      pidfile=/var/run/supervisord.pid
      nodaemon=false
      minfds=1024
      minprocs=200

      [rpcinterface:supervisor]
      supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

      [supervisorctl]
      serverurl=http://127.0.0.1:9001 ; use an http:// url to specify an inet socket

      [program:default]
      command=/var/www/html/bin/console -q default
      numprocs=2
      process_name=%(program_name)s-%(process_num)s
      directory=/var/www/html/
      autorestart=true
      user=webapp

      [program:mail_queue]
      command=/var/www/html/bin/console -q mail_queue
      numprocs=2
      process_name=%(program_name)s-%(process_num)s
      directory=/var/www/html/
      autorestart=true
      user=webapp

      [program:cron]
      command=/var/www/html/bin/console -q cron
      numprocs=1
      process_name=%(program_name)s-%(process_num)s
      directory=/var/www/html/
      autorestart=true
      user=webapp

      [program:bounces]
      command=/var/www/html/bin/console -q bounces
      numprocs=1
      process_name=%(program_name)s-%(process_num)s
      directory=/var/www/html/
      autorestart=true
      user=webapp

      [program:abonnements]
      command=/var/www/html/bin/console -q abonnements
      numprocs=1
      process_name=%(program_name)s-%(process_num)s
      directory=/var/www/html/
      autorestart=true
      user=webapp

commands:
  01_install_wkhtmltox:
    command: yum --assumeyes install https://s3-eu-west-1.amazonaws.com/elasticbeanstalk-eu-west-1-258931655285/packages/wkhtmltox-0.12.5-1.centos6.x86_64.rpm
    test: test ! -f /usr/local/bin/wkhtmltopdf

  02_install_supervisor:
    command: |
      dnf --assumeyes install pip
      /usr/bin/yes | pip install supervisor
      mkdir /var/log/supervisor/
    test: test ! -f /usr/local/bin/supervisorctl

  03_chmod_supervisor:
    command: chmod +x /opt/supervisor/etc_init.d_supervisord

  04_install_redis:
    command: /usr/bin/yes 'no' | /usr/bin/pecl install redis
    test: '! /usr/bin/pecl info redis'
  05_remove_extension_redis:
    command: /bin/sed -i -e '/extension="redis.so"/d' /etc/php.ini
  06_create_conf_redis:
    command: /bin/echo 'extension="redis.so"' > /etc/php.d/41-redis.ini

container_commands:
  01_db_migrate:
    command: vendor/bin/phinx migrate
    leader_only: true
  02_generate_proxies:
    command: vendor/bin/doctrine orm:generate-proxies var/cache/doctrine/proxies/
  03_start_supervisor:
    command: /usr/local/bin/supervisord

option_settings:
  aws:elasticbeanstalk:container:php:phpini:
    document_root: /web
  aws:elasticbeanstalk:environment:proxy:
    ProxyServer: apache
