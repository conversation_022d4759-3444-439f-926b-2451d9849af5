--- vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Setup.php      2020-08-04 17:25:02.899742294 +0000
+++ vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Setup.php      2020-08-04 17:25:23.099598006 +0000
@@ -180,16 +180,6 @@
             return $cache;
         }

-        if (extension_loaded('redis')) {
-            $redis = new \Redis();
-            $redis->connect('127.0.0.1');
-
-            $cache = new \Doctrine\Common\Cache\RedisCache();
-            $cache->setRedis($redis);
-
-            return $cache;
-        }
-
         return new ArrayCache();
     }
 }