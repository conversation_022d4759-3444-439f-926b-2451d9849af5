# Installation

# Pré-requis
* modules Apache :
  * headers
  * rewrite

* extensions PHP :
  * pdo
  * pdo_mysql
  * gettext
  * mbstring
  * gd
  * imap
  
* WkHtmlToPdf + WkHtmlToImage

_Using WkHtmlToPdf Without X Server :_  
https://github.com/JazzCore/python-pdfkit/wiki/Using-wkhtmltopdf-without-X-server

## Upstream
  - git remote add upstream https://gitlab.com/matgyver1/base-saas.git

## Fichiers
* Créer le dossier "var/sessions" accessible en écriture
* Lancer `bin/generate-env.sh dev`
* Editer `config/config.php` et définir :
  * SECRET_KEY avec appel à Tools::generateToken()
  * les identifiants de connexion à la BDD
  * les différentes clés, vars, paths, etc ...
* Editer `config/config.php.preprod` et `config/config.php.prod` et vérifier les variables, notamment : 
  * SESSIONS_PATH
  * APP_DOMAIN
  * APP_URL
  * APP_EMAIL
  * MEDIAS_URL
  * CDN_URL
  * MASTER_API_KEY
* Editer `s3-sync.sh` et définir le domaine de prod et preprod
* Editer les fichiers dans `.ebextensions` et notamment :
  * session.save_path
* Editer les fichiers dans `.elasticbeanstalk` et notamment :
  * default_ec2_keyname
* Editer `.gitlab-ci.yml` et notamment : 
  * AWS_S3_NAME
  * environment.url
  
## Dossiers (accessibles en écriture)
* var/cache/
* var/cache/*
* var/logs/
* var/sessions/
* web/lib/upload/

## htaccess
* Modifier SetEnvIf Origin "http(s)?://(www\.)?(domain.local:8890)$"
* Définir le domaine CloudFront : xxxx.cloudfront.net

## BDD
* Créer la table migration : 
```
CREATE TABLE `migrations` (
 `version` bigint(20) NOT NULL,
 `migration_name` varchar(100) DEFAULT NULL,
 `start_time` timestamp NULL DEFAULT NULL,
 `end_time` timestamp NULL DEFAULT NULL,
 `breakpoint` tinyint(1) NOT NULL DEFAULT '0',
 PRIMARY KEY (`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8
```

* Exécuter les migrations pour créer la structure de la base de données :
```
$ vendor/bin/phinx migrate
```

## Composer

Installer les dépendances du projet :
```
$ composer install
```

Lorsque vous ajoutez une nouvelle classe, ou alors que vous modifiez le path d'une existante, il faut penser à regénérer le fichier d'autoload via :

```
$ composer dump-autoload`

```
(fait automatiquement à la fin d'un install ou update)

## Accès à l'administration
* Accéder à https://matgyver.local:8890/admin/index/ et se connecter avec :
```
<EMAIL>
fdV5HbnJ6@DCf
```

## Console

Vous pouvez utiliser toutes les commandes console via :

```
$ bin/console -a action_name [-f filename] 
```

## Migrations

Le projet utilise la lib [Phinx](http://docs.phinx.org/en/latest/) pour la gestion des migrations.

Créer une nouvelle migration : 

```
$ vendor/bin/phinx create MyNewMigrationName
```

Le fichier de migration sera créé dans le dossier `var/migrations`

Appliquer les migrations non appliquées sur sa base :
```
$ vendor/bin/phinx migrate
```

Effectuer un rollback de la précédente migration :
```
$ vendor/bin/phinx rollback
```

## GitLab
* Accéder à Settings > CI/CD et définir les variables suivantes : 
   - AWS_ACCESS_KEY_ID
   - AWS_DEFAULT_REGION
   - AWS_SECRET_ACCESS_KEY

* Pour la preprod : 
   - PREPROD_DB_HOST
   - PREPROD_DB_LOGIN
   - PREPROD_DB_PASS
   - PREPROD_S3_KEY
   - PREPROD_S3_SECRET
   - PREPROD_SQS_KEY
   - PREPROD_SQS_SECRET
   - PREPROD_MASTER_API_KEY

* Pour la prod : 
   - PROD_DB_HOST
   - PROD_DB_LOGIN
   - PROD_DB_PASS
   - PROD_S3_KEY
   - PROD_S3_SECRET
   - PROD_SQS_KEY
   - PROD_SQS_SECRET
   - PROD_MASTER_API_KEY
