stages:
  - build
  - deploy

variables:
  GIT_SUBMODULE_STRATEGY: recursive

before_script:
  - '[ "$CI_COMMIT_REF_NAME" == "master" ] && ENV="prod" || ENV="preprod"'
  - '[ "$ENV" == "prod" ] && AWS_WWW_NAME="v1-prod-matgyver-alb" || AWS_WWW_NAME="v1-preprod-matgyver-alb"'
  - '[ "$ENV" == "prod" ] && AWS_S3_NAME="matgyver-v1-eu-west-1-prod-not-versioned" || AWS_S3_NAME="matgyver-v1-eu-west-1-preprod-not-versioned"'

# Install all composer dependencies
build:composer:
  image: composer
  stage: build
  before_script:
    - eval $(ssh-agent -s)
    - ssh-add <(echo "$SSH_PRIVATE_KEY")
    - mkdir -p ~/.ssh
    - bash -c '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
    - apk update
    - apk add libgcrypt
    - apk add zlib-dev
    - apk add libpng-dev
    - apk add libxml2-dev
    - apk add --no-cache linux-headers
    - docker-php-ext-install pdo_mysql
    - docker-php-ext-install gd
    - docker-php-ext-install soap
    - docker-php-ext-install exif
  script:
    - composer clearcache
    - composer install --no-progress --no-dev
    - patch -p 0 -i doctrine_orm_fix.patch
  artifacts:
    paths:
      - vendor/
    expire_in: 12 hours
  only:
    - /^preprod\/.*/
    - master
  tags:
    - aws

# Generate config files
build:config:
  image: php:8.2
  stage: build
  script:
    - chmod +x ./bin/generate-env.sh
    - ./bin/generate-env.sh $ENV
    - cat ./web/.htaccess >> ./web/.htaccess.$ENV && mv ./web/.htaccess.$ENV ./web/.htaccess
    - mv .elasticbeanstalk/config.${ENV}.yml .elasticbeanstalk/config.yml
    - mv .ebextensions/ebextensions.config.${ENV} .ebextensions/ebextensions.config
  artifacts:
    paths:
      - config/config.php
      - web/.htaccess
      - .ebextensions/ebextensions.config
      - .elasticbeanstalk/config.yml
    expire_in: 12 hours
  only:
    - /^preprod\/.*/
    - master
  tags:
    - aws

# Deploy to AWS
deploy_preprod:www:
  image: coxauto/aws-ebcli
  stage: deploy
  script:
    - eb deploy $AWS_WWW_NAME --timeout=60
    - chmod +x ./bin/s3-sync.sh
    - ./bin/s3-sync.sh $ENV $AWS_S3_NAME
  environment:
    name: preprod
    url: https://matgyver-preprod.com
  only:
    - /^preprod\/.*/
  dependencies:
    - build:composer
    - build:config
  tags:
    - aws
    - preprod

deploy_prod:www:
  image: coxauto/aws-ebcli
  stage: deploy
  script:
    - eb deploy $AWS_WWW_NAME --timeout=120
    - chmod +x ./bin/s3-sync.sh
    - ./bin/s3-sync.sh $ENV $AWS_S3_NAME
  environment:
    name: prod
    url: https://matgyver.com
  only:
    - master
  dependencies:
    - build:composer
    - build:config
  tags:
    - aws
    - prod
  when: manual
  allow_failure: false
