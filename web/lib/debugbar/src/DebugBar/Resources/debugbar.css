/* Hide debugbar when printing a page */
@media print {
  div.phpdebugbar {
    display: none;
  }
}

div.phpdebugbar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  border-top: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Helvetica, Arial, sans-serif;
  background: #fff;
  z-index: 10000;
  font-size: 14px;
  color: #000;
  text-align: left;
  line-height: 1;
  letter-spacing: normal;
  direction: ltr;
}

div.phpdebugbar a,
div.phpdebugbar-openhandler {
  cursor: pointer;
}

div.phpdebugbar-drag-capture {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10001;
  background: none;
  display: none;
  cursor: ns-resize;
}

div.phpdebugbar-closed {
    width: auto;
}

div.phpdebugbar * {
  margin: 0;
  padding: 0;
  border: 0;
  font-weight: normal;
  text-decoration: none;
  clear: initial;
  width: auto;
  -moz-box-sizing: content-box;
       box-sizing: content-box;
}

div.phpdebugbar ol, div.phpdebugbar ul {
  list-style: none;
}

div.phpdebugbar ul li, div.phpdebugbar ol li, div.phpdebugbar dl li {
  line-height: normal;
}

div.phpdebugbar table, .phpdebugbar-openhandler table {
  border-collapse: collapse;
  border-spacing: 0;
  color: inherit;
}

div.phpdebugbar input[type='text'], div.phpdebugbar input[type='password'] {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Helvetica, Arial, sans-serif;
  background: #fff;
  font-size: 14px;
  color: #000;
  border: 0;
  padding: 0;
  margin: 0;
}

div.phpdebugbar code, div.phpdebugbar pre, div.phpdebugbar samp {
  background: none;
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
  font-size: 1em;
  border: 0;
  padding: 0;
  margin: 0;
}

div.phpdebugbar code, div.phpdebugbar pre {
  color: #000;
}

div.phpdebugbar pre.sf-dump {
  color: #a0a000;
  outline: 0;
}

a.phpdebugbar-restore-btn {
  float: left;
  padding: 5px 8px;
  font-size: 14px;
  color: #555;
  text-decoration: none;
  border-right: 1px solid #ddd;
}

div.phpdebugbar-resize-handle {
  display: none;
  height: 4px;
  margin-top: -4px;
  width: 100%;
  background: none;
  border-bottom: 1px solid #ccc;
  cursor: ns-resize;
}

div.phpdebugbar-closed, div.phpdebugbar-minimized{
  border-top: 1px solid #ccc;
}
/* -------------------------------------- */

a.phpdebugbar-restore-btn {
    background: #efefef url(data:image/svg+xml,%3Csvg%20viewBox%3D%220%200%2020%2020%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Ccircle%20fill%3D%22%23000%22%20cx%3D%2210%22%20cy%3D%2210%22%20r%3D%229%22%2F%3E%3Cpath%20d%3D%22M6.039%208.342c.463%200%20.772.084.927.251.154.168.191.455.11.862-.084.424-.247.727-.487.908-.241.182-.608.272-1.1.272h-.743l.456-2.293h.837zm-2.975%204.615h1.22l.29-1.457H5.62c.461%200%20.84-.047%201.139-.142.298-.095.569-.254.812-.477.205-.184.37-.387.497-.608.127-.222.217-.466.27-.734.13-.65.032-1.155-.292-1.518-.324-.362-.84-.543-1.545-.543H4.153l-1.089%205.479zM9.235%206.02h1.21l-.289%201.458h1.079c.679%200%201.147.115%201.405.347.258.231.335.607.232%201.125l-.507%202.55h-1.23l.481-2.424c.055-.276.035-.464-.06-.565-.095-.1-.298-.15-.608-.15H9.98L9.356%2011.5h-1.21l1.089-5.48M15.566%208.342c.464%200%20.773.084.928.251.154.168.19.455.11.862-.084.424-.247.727-.488.908-.24.182-.607.272-1.1.272h-.742l.456-2.293h.836zm-2.974%204.615h1.22l.29-1.457h1.046c.461%200%20.84-.047%201.139-.142.298-.095.569-.254.812-.477.205-.184.37-.387.497-.608.127-.222.217-.466.27-.734.129-.65.032-1.155-.292-1.518-.324-.362-.84-.543-1.545-.543H13.68l-1.089%205.479z%22%20fill%3D%22%23FFF%22%2F%3E%3C%2Fsvg%3E)  no-repeat center / 20px 20px;
}
div.phpdebugbar-header {
  min-height: 26px;
  line-height: 16px;
}
div.phpdebugbar-header:before, div.phpdebugbar-header:after {
  display: table;
  line-height: 0;
  content: "";
}
div.phpdebugbar-header:after {
  clear: both;
}
div.phpdebugbar-header-left {
  float: left;
}
div.phpdebugbar-header-right {
  float: right;
}
div.phpdebugbar-header > div > * {
  padding: 5px;
  font-size: 14px;
  height: 16px;
  color: #555;
  text-decoration: none;
}
div.phpdebugbar-header-left > *,
div.phpdebugbar-header-right > * {
  line-height: 0;
  display: flex;
  align-items: center;
}
div.phpdebugbar-header-left > * {
  float: left;
}
div.phpdebugbar-header-right > * {
  float: right;
}
div.phpdebugbar-header-right > select {
  padding: 0;
  line-height: 1em;
}

/* -------------------------------------- */

span.phpdebugbar-indicator,
a.phpdebugbar-indicator
{
  border-right: 1px solid #ddd;
}

a.phpdebugbar-tab.phpdebugbar-active {
  background: #ccc;
  color: #444;
  background-image: linear-gradient(bottom, rgb(173,173,173) 41%, rgb(209,209,209) 71%);
  background-image: -o-linear-gradient(bottom, rgb(173,173,173) 41%, rgb(209,209,209) 71%);
  background-image: -moz-linear-gradient(bottom, rgb(173,173,173) 41%, rgb(209,209,209) 71%);
  background-image: -webkit-linear-gradient(bottom, rgb(173,173,173) 41%, rgb(209,209,209) 71%);
  background-image: -ms-linear-gradient(bottom, rgb(173,173,173) 41%, rgb(209,209,209) 71%);
  background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0.41, rgb(173,173,173)), color-stop(0.71, rgb(209,209,209)));
}
  a.phpdebugbar-tab span.phpdebugbar-badge {
    display: none;
    margin-left: 5px;
    font-size: 11px;
    line-height: 14px;
    padding: 0 6px;
    background: #ccc;
    border-radius: 4px;
    color: #555;
    font-weight: normal;
    text-shadow: none;
  }
  a.phpdebugbar-tab.phpdebugbar-active  span.phpdebugbar-badge {
    background: #555;
    color: #fff;
  }
  a.phpdebugbar-tab i {
    display: none;
    vertical-align: middle;
  }
  a.phpdebugbar-tab span.phpdebugbar-badge.phpdebugbar-visible {
    display: inline;
  }
  a.phpdebugbar-tab span.phpdebugbar-badge.phpdebugbar-important {
    background: #ed6868;
    color: white;
  }

a.phpdebugbar-close-btn, a.phpdebugbar-open-btn, a.phpdebugbar-restore-btn, a.phpdebugbar-minimize-btn , a.phpdebugbar-maximize-btn {
  width: 16px;
  height: 16px;
}

a.phpdebugbar-maximize-btn { display: none}

a.phpdebugbar-minimize-btn { display: block}

div.phpdebugbar-minimized a.phpdebugbar-maximize-btn { display: block}

div.phpdebugbar-minimized a.phpdebugbar-minimize-btn { display: none}

a.phpdebugbar-minimize-btn {
  background:url(data:image/svg+xml,%3Csvg%20viewBox=%220%200%201792%201792%22%20fill=%22none%22%20xmlns=%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d=%22m1683%20653.5-742%20741c-12.667%2012.67-27.667%2019-45%2019s-32.333-6.33-45-19l-742-741c-12.667-12.667-19-27.833-19-45.5s6.333-32.833%2019-45.5l166-165c12.667-12.667%2027.667-19%2045-19s32.333%206.333%2045%2019l531%20531%20531-531c12.67-12.667%2027.67-19%2045-19s32.33%206.333%2045%2019l166%20165c12.67%2012.667%2019%2027.833%2019%2045.5s-6.33%2032.833-19%2045.5Z%22%20fill=%22%23555%22%2F%3E%3C%2Fsvg%3E) no-repeat center / 14px 14px;
}

a.phpdebugbar-maximize-btn {
  background: url(data:image/svg+xml,%3Csvg%20viewBox=%220%200%201792%201792%22%20fill=%22none%22%20xmlns=%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d=%22m1683%201229.5-166%20165c-12.67%2012.67-27.67%2019-45%2019s-32.33-6.33-45-19l-531-531-531%20531c-12.667%2012.67-27.667%2019-45%2019s-32.333-6.33-45-19l-166-165c-12.667-12.67-19-27.83-19-45.5s6.333-32.83%2019-45.5l742-741c12.667-12.667%2027.667-19%2045-19s32.333%206.333%2045%2019l742%20741c12.67%2012.67%2019%2027.83%2019%2045.5s-6.33%2032.83-19%2045.5Z%22%20fill=%22%23555%22%2F%3E%3C%2Fsvg%3E) no-repeat center / 14px 14px;
}

a.phpdebugbar-close-btn {
  background: url(data:image/svg+xml,%3Csvg%20viewBox=%220%200%201792%201792%22%20fill=%22none%22%20xmlns=%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d=%22M1490%201258c0%2026.67-9.33%2049.33-28%2068l-136%20136c-18.67%2018.67-41.33%2028-68%2028s-49.33-9.33-68-28l-294-294-294%20294c-18.667%2018.67-41.333%2028-68%2028s-49.333-9.33-68-28l-136-136c-18.667-18.67-28-41.33-28-68s9.333-49.33%2028-68l294-294-294-294c-18.667-18.667-28-41.333-28-68s9.333-49.333%2028-68l136-136c18.667-18.667%2041.333-28%2068-28s49.333%209.333%2068%2028l294%20294%20294-294c18.67-18.667%2041.33-28%2068-28s49.33%209.333%2068%2028l136%20136c18.67%2018.667%2028%2041.333%2028%2068s-9.33%2049.333-28%2068l-294%20294%20294%20294c18.67%2018.67%2028%2041.33%2028%2068Z%22%20fill=%22%23555%22%2F%3E%3C%2Fsvg%3E) no-repeat center / 14px 14px;
}

a.phpdebugbar-open-btn {
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 1792 1792'%3e%3cpath fill='%23555' d='M1646 991.796c0 16.494-8.25 34.064-24.75 52.684l-268.22 316.13c-22.89 27.14-54.95 50.16-96.2 69.05S1177.4 1458 1142.27 1458H273.728c-18.095 0-34.194-3.46-48.297-10.38-14.104-6.92-21.155-18.36-21.155-34.32 0-16.5 8.249-34.06 24.747-52.69l268.228-316.13c22.884-27.14 54.949-50.156 96.194-69.049 41.246-18.893 79.431-28.34 114.556-28.34h868.549c18.09 0 34.19 3.459 48.3 10.378 14.1 6.918 21.15 18.361 21.15 34.327Zm-273.82-274.615v127.728H708.001c-50.027 0-102.448 12.64-157.264 37.919-54.817 25.28-98.457 57.078-130.921 95.397L150.79 1294.35l-3.992 4.79c0-2.13-.133-5.46-.399-9.98-.266-4.52-.399-7.85-.399-9.98V512.817c0-48.962 17.563-91.005 52.688-126.13 35.125-35.126 77.168-52.688 126.131-52.688h255.455c48.962 0 91.005 17.562 126.13 52.688 35.126 35.125 52.688 77.168 52.688 126.13v25.546h434.278c48.96 0 91 17.563 126.13 52.688 35.12 35.125 52.68 77.168 52.68 126.13Z'/%3e%3c/svg%3e") no-repeat center / 14px 14px;
}

.phpdebugbar-indicator {
  position: relative;
  cursor: pointer;
}
  .phpdebugbar-indicator span.phpdebugbar-text {
    margin-left: 5px;
  }
  .phpdebugbar-indicator span.phpdebugbar-tooltip {
    display: none;
    position: absolute;
    top: -30px;
    background: #efefef70;
    border: 1px solid #ccc;
    color: #555;
    font-size: 11px;
    padding: 2px 6px;
    z-index: 1000;
    text-align: center;
    white-space: nowrap;
    right: 0;
    line-height: 1.5;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
  }
  .phpdebugbar-indicator:hover span.phpdebugbar-tooltip:not(.phpdebugbar-disabled) {
    display: block;
  }

select.phpdebugbar-datasets-switcher {
  float: right;
  display: none;
  margin: 2px 0 0 7px;
  max-width: 200px;
  max-height: 23px;
  padding: 0;
}

/* -------------------------------------- */

div.phpdebugbar-body {
  border-top: 1px solid #ccc;
  display: none;
  position: relative;
  height: 300px;
}

/* -------------------------------------- */

div.phpdebugbar-panel {
  display: none;
  height: 100%;
  overflow: auto;
  width: 100%;
}
div.phpdebugbar-panel.phpdebugbar-active {
  display: block;
}

/* -------------------------------------- */

div.phpdebugbar-mini-design a.phpdebugbar-tab {
  position: relative;
  border-right: 1px solid #ddd;
}
  div.phpdebugbar-mini-design a.phpdebugbar-tab span.phpdebugbar-text {
    display: none;
  }
  div.phpdebugbar-mini-design a.phpdebugbar-tab:hover span.phpdebugbar-text {
    display: block;
    position: absolute;
    top: -30px;
    background: #efefef70;
    opacity: 1;
    border: 1px solid #ccc;
    color: #555;
    font-size: 11px;
    padding: 2px 6px;
    z-index: 1000;
    text-align: center;
    right: 0;
    line-height: 1.5;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
  }
  div.phpdebugbar-mini-design a.phpdebugbar-tab i {
    display:inline-block;
  }

/* -------------------------------------- */

  a.phpdebugbar-tab.phpdebugbar-tab-history {
    min-width: 16px;
    height: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  a.phpdebugbar-tab.phpdebugbar-tab-history .phpdebugbar-text {
      display: none;
      white-space: nowrap;
  }
  a.phpdebugbar-tab.phpdebugbar-tab-history i {
    display:inline-block;
  }
  .phpdebugbar-widgets-dataset-history table {
      width: 100%;
      table-layout: fixed;
  }
    .phpdebugbar-widgets-dataset-history table th {
        font-weight: bold;
    }
    .phpdebugbar-widgets-dataset-history table td, .phpdebugbar-widgets-dataset-history table th {
        padding: 6px 3px;
        border-bottom: 1px solid #ddd;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .phpdebugbar-widgets-dataset-history table td a{
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .phpdebugbar-widgets-dataset-history table tr.phpdebugbar-widgets-active {
        background: #ccc;
        color: #444;
    }
    .phpdebugbar-widgets-dataset-history span.phpdebugbar-badge {
        margin: 0 5px 0 2px;
        font-size: 11px;
        line-height: 14px;
        padding: 0 6px;
        background: #ccc;
        border-radius: 4px;
        color: #555;
        font-weight: normal;
        text-shadow: none;
        vertical-align: middle;
    }
    .phpdebugbar-widgets-dataset-history .phpdebugbar-widgets-dataset-actions {
        text-align: center;
        padding: 7px 0;
        position: sticky;
        top: 0;
        background: #fff;
    }
    .phpdebugbar-widgets-dataset-history .phpdebugbar-widgets-dataset-actions a {
        margin: 0 10px;
    }
    .phpdebugbar-widgets-dataset-history .phpdebugbar-widgets-dataset-actions input {
        appearance: checkbox !important;
        margin: 5px;
    }
