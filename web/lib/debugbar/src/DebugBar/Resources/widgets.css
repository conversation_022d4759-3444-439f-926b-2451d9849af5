pre.phpdebugbar-widgets-code-block {
    white-space: pre;
    word-wrap: normal;
    overflow: hidden;
}
  pre.phpdebugbar-widgets-code-block code {
    display: block;
    overflow-x: auto;
    overflow-y: hidden;
  }
  pre.phpdebugbar-widgets-code-block code.phpdebugbar-widgets-numbered-code {
    padding: 5px;
  }
  pre.phpdebugbar-widgets-code-block ul li.phpdebugbar-widgets-highlighted-line {
    font-weight: bolder;
    text-decoration: underline;
  }
  pre.phpdebugbar-widgets-code-block ul li.phpdebugbar-widgets-highlighted-line span {
    position: absolute;
    background: #800000;
    min-width: calc(100% - 85px);
    margin-left: 10px;
    opacity: 0.15;
  }
  pre.phpdebugbar-widgets-code-block ul {
    position: static;
    float: left;
    padding: 5px;
    background: #cacaca;
    border-right: 1px solid #aaa;
    text-align: right;
  }

  .phpdebugbar-widgets-kvlist span.phpdebugbar-widgets-filename,
  li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-filename {
    display: block;
    font-style: italic;
    float: right;
    margin-left: 8px;
    color: #888;
  }

  a.phpdebugbar-widgets-editor-link:hover {
    color: #aaaaaa;
  }

  a.phpdebugbar-widgets-editor-link:before {
    font-family: PhpDebugbarFontAwesome;
    margin-right: 4px;
    font-size: 12px;
    font-style: normal;
  }

  a.phpdebugbar-widgets-editor-link:before {
    content: "\f08e";
    margin-left: 4px;
  }

/* -------------------------------------- */

ul.phpdebugbar-widgets-list {
  margin: 0;
  padding: 0;
  list-style: none;
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
}
  ul.phpdebugbar-widgets-list li.phpdebugbar-widgets-list-item {
    padding: 3px;
    border-bottom: 1px solid #eee;
    position: relative;
    overflow: hidden;
  }
  ul.phpdebugbar-widgets-list li.phpdebugbar-widgets-list-item:hover {
    background: #fafafa;
  }

/* -------------------------------------- */

div.phpdebugbar-widgets-messages {
  position: relative;
  height: 100%;
}
  div.phpdebugbar-widgets-messages ul.phpdebugbar-widgets-list {
    padding-bottom: 45px;
  }
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-value:before {
    font-family: PhpDebugbarFontAwesome;
    margin-right: 8px;
    font-size: 11px;
  }
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-value.phpdebugbar-widgets-alert:before {
    content: "\f0f3";
    color: #cbcf38;
  }
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-value.phpdebugbar-widgets-debug:before {
    content: "\f188";
    color: #78d79a;
  }
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-value.phpdebugbar-widgets-warning:before,
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-value.phpdebugbar-widgets-emergency:before,
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-value.phpdebugbar-widgets-notice:before {
    content: "\f071";
    color: #ecb03d;
  }
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-value.phpdebugbar-widgets-error,
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-value.phpdebugbar-widgets-critical:before {
    color: red;
  }
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-value.phpdebugbar-widgets-error:before,
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-value.phpdebugbar-widgets-critical:before {
    content: "\f057";
  }
  dl.phpdebugbar-widgets-kvlist dd.phpdebugbar-widgets-value pre.sf-dump,
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item pre.sf-dump {
    display: inline-block !important;
  }
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-collector,
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-label {
    float: right;
    font-size: 12px;
    padding: 2px 4px;
    color: #888;
    margin: 0 2px;
    text-decoration: none;
    text-shadow: none;
    background: none;
    font-weight: normal;
  }
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-collector {
    color: #555;
    font-style: italic;
  }
  div.phpdebugbar-widgets-messages div.phpdebugbar-widgets-toolbar {
    position: fixed;
    bottom: 0;
    width: 100%;
    background: #fff;
  }
  div.phpdebugbar-widgets-messages li .phpdebugbar-widgets-label-called-from {
    float: right;
    color: #666;
    padding-left: 5px;
    border-bottom: 1px dotted #666;
  }
  div.phpdebugbar-widgets-messages li .phpdebugbar-widgets-label-called-from:before {
    content: "\f08d";
    font-family: PhpDebugbarFontAwesome;
    margin-right: 4px;
    font-size: 12px;
  }
    div.phpdebugbar-widgets-messages div.phpdebugbar-widgets-toolbar input {
      border: 0;
      margin: 0;
      margin-left: 7px;
      width: 50%;
      box-shadow: none;
    }
    div.phpdebugbar-widgets-messages div.phpdebugbar-widgets-toolbar input:focus {
      outline: none;
    }
      div.phpdebugbar-widgets-messages div.phpdebugbar-widgets-toolbar a.phpdebugbar-widgets-filter {
        float: right;
        font-size: 12px;
        padding: 2px 4px;
        background: #7cacd5;
        margin: 0 2px;
        border-radius: 4px;
        color: #fff;
        text-decoration: none;
      }
      div.phpdebugbar-widgets-messages div.phpdebugbar-widgets-toolbar a.phpdebugbar-widgets-filter.phpdebugbar-widgets-excluded {
        background: #eee;
        color: #888;
      }

/* -------------------------------------- */

dl.phpdebugbar-widgets-kvlist {
  margin: 0;
}
  dl.phpdebugbar-widgets-kvlist dt {
    float: left;
    width: 150px;
    padding: 5px;
    border-top: 1px solid #eee;
    font-weight: bold;
    clear: both;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  dl.phpdebugbar-widgets-kvlist dd {
    margin-left: 160px;
    padding: 5px;
    border-top: 1px solid #eee;
    cursor: pointer;
    min-height: 17px;
  }

/* -------------------------------------- */

dl.phpdebugbar-widgets-varlist,
dl.phpdebugbar-widgets-htmlvarlist {
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
}
  dl.phpdebugbar-widgets-htmlvarlist dd {
    cursor: initial;
  }

/* -------------------------------------- */

ul.phpdebugbar-widgets-timeline {
  margin: 0;
  padding: 0;
  list-style: none;
}
  ul.phpdebugbar-widgets-timeline .phpdebugbar-widgets-measure {
    height: 20px;
    position: relative;
    border-bottom: 1px solid #eee;
    display: block;
  }
  ul.phpdebugbar-widgets-timeline li:hover {
    background: #fafafa;
  }
    ul.phpdebugbar-widgets-timeline li span.phpdebugbar-widgets-label,
    ul.phpdebugbar-widgets-timeline li span.phpdebugbar-widgets-collector {
      position: absolute;
      font-size: 12px;
      font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
      color: #555;
      top: 4px;
      left: 5px;
      background: none;
      text-shadow: none;
      font-weight: normal;
      white-space: pre;
    }
    ul.phpdebugbar-widgets-timeline li span.phpdebugbar-widgets-collector {
      left: initial;
      right: 5px;
    }
    ul.phpdebugbar-widgets-timeline li span.phpdebugbar-widgets-value {
      display: block;
      position: absolute;
      height: 10px;
      background: #3db9ec;
      top: 5px;
      border-radius: 2px;
      min-width: 1px;
    }
    ul.phpdebugbar-widgets-timeline table.phpdebugbar-widgets-params {
      display: none;
      width: 70%;
      margin: 10px;
      border: 1px solid #ddd;
      font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
      border-collapse: collapse;
    }
      ul.phpdebugbar-widgets-timeline table.phpdebugbar-widgets-params td {
        border: 1px solid #ddd;
        padding: 0 5px;
      }
      ul.phpdebugbar-widgets-timeline table.phpdebugbar-widgets-params .phpdebugbar-widgets-name {
        width: 20%;
        font-weight: bold;
      }

/* -------------------------------------- */

div.phpdebugbar-widgets-exceptions li.phpdebugbar-widgets-list-item {
  cursor: pointer;
}
  div.phpdebugbar-widgets-exceptions li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-message {
    display: block;
    color: red;
  }

  div.phpdebugbar-widgets-exceptions li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-type {
    display: block;
    position: absolute;
    right: 4px;
    top: 4px;
    font-weight: bold;
  }

  div.phpdebugbar-widgets-exceptions li.phpdebugbar-widgets-list-item pre.phpdebugbar-widgets-file {
    display: none;
    margin: 10px;
    padding: 5px;
    border: 1px solid #ddd;
    font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
  }

  div.phpdebugbar-widgets-exceptions li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-filename {
    float: none;
  }

ul.phpdebugbar-widgets-timeline table.phpdebugbar-widgets-params {
    display: table;
    border: 0;
    width: 99%;
}
