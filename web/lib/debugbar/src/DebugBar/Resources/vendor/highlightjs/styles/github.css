/*

github.com style (c) <PERSON><PERSON> <<EMAIL>>

*/
div.phpdebugbar pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em;
}
div.phpdebugbar code.hljs {
  padding: 3px 5px;
}
div.phpdebugbar .hljs {
  background: #f3f3f3;
  color: #444;
}
div.phpdebugbar .hljs-comment {
  color: #697070;
}
div.phpdebugbar .hljs-punctuation,
div.phpdebugbar .hljs-tag {
  color: #444a;
}
div.phpdebugbar .hljs-tag .hljs-attr,
div.phpdebugbar .hljs-tag .hljs-name {
  color: #444;
}
div.phpdebugbar .hljs-attribute,
div.phpdebugbar .hljs-doctag,
div.phpdebugbar .hljs-keyword,
div.phpdebugbar .hljs-meta .hljs-keyword,
div.php<PERSON>bu<PERSON> .hljs-name,
div.phpdebugbar .hljs-selector-tag {
  font-weight: 700;
}
div.phpdebugbar .hljs-deletion,
div.phpdebugbar .hljs-number,
div.phpdebugbar .hljs-quote,
div.phpdebugbar .hljs-selector-class,
div.phpdebugbar .hljs-selector-id,
div.phpdebugbar .hljs-string,
div.phpdebugbar .hljs-template-tag,
div.phpdebugbar .hljs-type {
  color: #800;
}
div.phpdebugbar .hljs-section,
div.phpdebugbar .hljs-title {
  color: #800;
  font-weight: 700;
}
div.phpdebugbar .hljs-link,
div.phpdebugbar .hljs-operator,
div.phpdebugbar .hljs-regexp,
div.phpdebugbar .hljs-selector-attr,
div.phpdebugbar .hljs-selector-pseudo,
div.phpdebugbar .hljs-symbol,
div.phpdebugbar .hljs-template-variable,
div.phpdebugbar .hljs-variable {
  color: #ab5656;
}
div.phpdebugbar .hljs-literal {
  color: #695;
}
div.phpdebugbar .hljs-addition,
div.phpdebugbar .hljs-built_in,
div.phpdebugbar .hljs-bullet,
div.phpdebugbar .hljs-code {
  color: #397300;
}
div.phpdebugbar .hljs-meta {
  color: #1f7199;
}
div.phpdebugbar .hljs-meta .hljs-string {
  color: #38a;
}
div.phpdebugbar .hljs-emphasis {
  font-style: italic;
}
div.phpdebugbar .hljs-strong {
  font-weight: 700;
}
