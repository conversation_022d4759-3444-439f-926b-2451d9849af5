const editors = {};
let somethingChanged = false;

window.onbeforeunload = function() {
	if (somethingChanged) {
		return false;
	}
	return null;
}

$(document).ready(function () {
	$('form').on('submit', function() {
		window.onbeforeunload = null;
		if ($(this).find('button[name="submit2"]').length > 0) {
			$(this).find('button[type="submit"],input[type="submit"]').addClass('spinner spinner-left disabled');
		} else {
			$(this).find('button[type="submit"]').addClass('spinner spinner-left').prop('disabled', true);
		}
	});

	if (!$('#kt_wizard').length) {
		$('input[type="text"], select').bind('change', function() {
			let id = $(this).attr('id');
			let filter = $(this).closest('.filters');
			if (id != 'kt_quick_search_input' && !filter.length) {
				somethingChanged = true;
				$('input[type="text"]').unbind('change');
			}
		});
	}
	$('.btn-cancel').on('click', function() {
		somethingChanged = false;
	});

	if ($('.tinymce-small').length > 0) {
		$('.tinymce-small').each(function() {
			let tinyMceId = $(this).attr('id');
			editors[tinyMceId] = tinymce.init({
				selector: '#' + tinyMceId,
				menubar: false,
				statusbar: false,
				height: 250,
				min_height: 250,
				resize: true,
				autoresize_bottom_margin: 0,
				contextmenu: false,
				paste_as_text: true,
				toolbar_sticky: true,
				browser_spellcheck: true,
				relative_urls: false,
				remove_script_host: false,
				language: 'fr_FR',
				toolbar: ['bold italic underline | forecolor | link | alignleft aligncenter alignright alignjustify | bullist numlist | outdent indent | table'],
				plugins: 'lists link autoresize paste table',
				content_style: "@import url('https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700');",
			});
		});
	}
	if ($('.tinymce').length > 0) {
		$('.tinymce').each(function() {
			let tinyMceId = $(this).attr('id');
			editors[tinyMceId] = tinymce.init({
				selector: '#' + tinyMceId,
				menubar: false,
				statusbar: false,
				height: 450,
				min_height: 250,
				resize: true,
				autoresize_bottom_margin: 0,
				contextmenu: false,
				paste_as_text: true,
				toolbar_sticky: true,
				browser_spellcheck: true,
				relative_urls: false,
				remove_script_host: false,
				language: 'fr_FR',
				toolbar: ['bold italic underline | forecolor | link | alignleft aligncenter alignright alignjustify | styleselect fontsizeselect | bullist numlist | outdent indent | table | blockquote'],
				plugins : 'advlist autolink link lists autoresize paste table',
				content_style: "@import url('https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700');",
			});
		});
	}

	/* ---------- ckeditor ---------- */
	/*if ($('.ckeditorsmall').length > 0) {
		$('.ckeditorsmall').each(function() {
			var ckeditorId = $(this).attr('id');
			ClassicEditor
				.create( document.querySelector('#' + ckeditorId), {
					toolbar: ['bold', 'italic', 'underline', '|', 'fontColor', '|', 'link', 'bulletedList'],
					mediaEmbed: {
						previewsInData: true
					},
					typing: {
						transformations: {
							remove: ['mathematical'],
						}
					},
					htmlSupport: {
						allow: [
							// Enables <div> elements.
							{
								name: 'div',
								classes: true,
							},
						]
					}
				})
				.then( newEditor => {
					newEditor.editing.view.document.on( 'clipboardInput', ( evt, data ) => {
						const dataTransfer = data.dataTransfer;
						let content = dataTransfer.getData('text/html');
						if (content) {
							content = content.replace(/<img[^>]*>/g, '');
							const viewFragment = newEditor.data.processor.toView(content);
							const modelFragment = newEditor.data.toModel(viewFragment);
							newEditor.model.insertContent(modelFragment);
							evt.stop();
						}
					} );
					editors[ckeditorId] = newEditor;
				} )
				.catch( error => {
					console.error( error );
				} );
		});
	}
	if ($(".ckeditor").length > 0) {
		$('.ckeditor').each(function() {
			var ckeditorId = $(this).attr('id');
			ClassicEditor
				.create( document.querySelector('#' + ckeditorId), {
					toolbar: ['heading', 'fontColor', 'fontSize', '|', 'alignment', '|', 'bold', 'italic', 'underline', 'strikethrough', 'link', 'bulletedList', 'numberedList', '|', 'indent', 'outdent', 'blockQuote', '|', 'mediaEmbed', 'ckfinder'],
					alignment: {
						options: ['left', 'center', 'right', 'justify']
					},
					ckfinder: {
						options: {
							resourceType: 'Medias'
						}
					},
					mediaEmbed: {
						previewsInData: true
					}
				})
				.then( newEditor => {
					editors[ckeditorId] = newEditor;
				} )
				.catch( error => {
					console.error( error );
				} );
		});
	}*/

	initDataTables();

	$('.date-time-picker').datetimepicker({
		format: 'YYYY-MM-DD HH:mm:ss',
		locale: (appLanguage == 'fr_FR' ? 'fr' : 'en')
	});
	$('.date-picker').datetimepicker({
		format: 'YYYY-MM-DD',
		locale: (appLanguage == 'fr_FR' ? 'fr' : 'en')
	});

	$('.date-mask-picker').each(function() {
		let options = {
			format: 'DD/MM/YY',
			locale: (appLanguage == 'fr_FR' ? 'fr' : 'en'),
			autoclose: true,
			useCurrent: false,
		};
		if ($(this).data('min-date')) {
			options.minDate = $(this).data('min-date');
		}
		if ($(this).data('max-date')) {
			options.maxDate = $(this).data('max-date');
		}
		$(this).datetimepicker(options);
	});
	$('.date-month-mask-picker').datetimepicker({
		format: 'MM/YY',
		locale: (appLanguage == 'fr_FR' ? 'fr' : 'en'),
		autoclose: true,
		useCurrent: false
	});

	$(".date-mask").each(function() {
		$(this).inputmask({
			"alias" : "datetime",
			"inputFormat": "dd/mm/yy",
			//autoUnmask: true
		});
	});
	$(".date-month-mask").each(function() {
		$(this).inputmask({
			"alias" : "datetime",
			"inputFormat": "mm/yy",
			//autoUnmask: true
		});
	});
	$(".time-mask").each(function() {
		$(this).inputmask({
			"alias" : "datetime",
			"inputFormat": "HH:MM",
			//autoUnmask: true,
			"positionCaretOnClick": "none",
			//"caret": "",
		});
	});

	if ($('.date-picker-linked').length > 0) {
		var datePicker1 = $($('.date-picker-linked .date-picker')[0]).attr('id');
		var datePicker2 = $($('.date-picker-linked .date-picker')[1]).attr('id');
		$('#' + datePicker1).on('change.datetimepicker', function (e) {
			$('#' + datePicker2).datetimepicker('minDate', e.date);
		});
		$('#' + datePicker2).on('change.datetimepicker', function (e) {
			$('#' + datePicker1).datetimepicker('maxDate', e.date);
		});

		if ($('#' + datePicker1 + ' input').val()) {
			var value = $('#' + datePicker1 + ' input').val();
			$('#' + datePicker2).datetimepicker('minDate', moment().format(value));
		}
		if ($('#' + datePicker2 + ' input').val()) {
			var value = $('#' + datePicker2 + ' input').val();
			$('#' + datePicker1).datetimepicker('maxDate', moment().format(value));
		}
	}
	if ($('.date-time-range-picker').length) {
		let options = {
			buttonClasses: ' btn',
			applyClass: 'btn-primary',
			cancelClass: 'btn-secondary',
			timePicker: true,
			timePickerIncrement: 5,
			timePicker24Hour: true,
			locale: {
				format: 'DD/MM/YYYY HH:mm',
				applyLabel: __('Appliquer'),
				cancelLabel: __('Annuler'),
				monthNames: [
					"Janvier",
					"Février",
					"Mars",
					"Avril",
					"Mai",
					"Juin",
					"Juillet",
					"Août",
					"Septembre",
					"Octobre",
					"Novembre",
					"Décembre"
				],
			}
		};
		if ($('.date-time-range-picker .input').val()) {
			let value = $('.date-time-range-picker .input').val();
			value = value.split(' - ');
			options.startDate = value[0];
			options.endDate = value[1];
		}
		$('.date-time-range-picker').daterangepicker(options, function(start, end, label) {
			$('.date-time-range-picker .form-control').val(start.format('DD/MM/YYYY HH:mm') + ' - ' + end.format('DD/MM/YYYY HH:mm'));
		});
	}

	$('[data-rel="tooltip"]').tooltip()

	$('[data-rel="select2"],[rel="select2"]').each(function() {
		let options = {
			language: (appLanguage == 'fr_FR' ? "fr" : "en"),
		};
		if ($(this).data('keep-on-select')) {
			options.closeOnSelect = false;
		}
		if ($(this).data('placeholder')) {
			options.placeholder = $(this).data('placeholder');
			options.allowClear = true;
		}
		$(this).select2(options);
	});

	function select2ChosenImg(img) {
		if (typeof img.element != 'undefined') {
			return $('<span><img src="'+ img.element.dataset.imgSrc + '"> '+ img.text +'</span>');
		}
	}

	$('[data-rel="select2-img"],[rel="select2-img"]').select2({
		templateSelection: select2ChosenImg,
		templateResult: select2ChosenImg,
		allowHtml: true
	}).on('select2:open', function (e) {
		$('.select2-search input').attr('placeholder', __('Rechercher'))
	});

	if ($('.image-input').length > 0) {
		$('.image-input').each(function() {
			var id = $(this).attr('id');
			new KTImageInput(id);
		});
	}

	if ($('.btn-create-modal-content').length > 0) {
		$('.btn-create-modal-content').on('click', function() {
			let image = $(this).data('image');
			let content = $(this).data('content');
			var customClass = 'swal2-lg';
			if ($(this).data('size')) {
				customClass = $(this).data('size');
			}
			Swal.fire({
				title: '',
				text: content,
				imageUrl: image,
				imageWidth: 600,
				animation: false,
				customClass: customClass
			});
		});
	}

	if ($('.form-panel-container').length > 0) {
		$('.form-panel-container').each(function() {
			let id = $(this).attr('id');
			let element = KTUtil.getById(id);
			new KTOffcanvas(element, {
				overlay: true,
				baseClass: 'offcanvas',
				placement: 'right',
				closeBy: id + '_close',
				toggleBy: id + '_toggle'
			});
		});
	}

	$('.nouislider').each(function() {
		let id = $(this).attr('id');

		let slider = document.getElementById(id);
		let value = $(this).data('value');
		noUiSlider.create(slider, {
			start: [ value ],
			step: 1,
			range: {
    			'min': [ 0 ],
    			'max': [ 12 ]
			},
			format: wNumb({
				decimals: 0
			}),
			pips: {
				mode: 'values',
				values: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
				density: 10
			}
		});

		let sliderInput = document.getElementById(id + '_input');
		slider.noUiSlider.on('update', function(values, handle) {
			sliderInput.value = values[handle];

			if (values[handle] == 0) {
				$('.' + id + '_unreadable').removeClass('d-none');
			} else {
				$('.' + id + '_unreadable').addClass('d-none');
			}
		});

		slider.noUiSlider.on('change', function (values, handle) {
			if (values[handle] < 0) {
				slider.noUiSlider.set(0);
			} else if (values[handle] == 0) {
				$('.' + id + '_unreadable').removeClass('d-none');
			} else if (values[handle] > 11) {
				//slider.noUiSlider.set(11);
			} else {
				$('.' + id + '_unreadable').addClass('d-none');
			}

			somethingChanged = true;
		});
	});

	$('.autosize').each(function() {
		let id = $(this).attr('id');
		let textarea = $('#' + id);
		autosize(textarea);
	});

	if ($('#kt_aside_menu').length > 0) {
		let _asideEl = KTUtil.getById('kt_aside_menu');
		let _asideOffcanvas = new KTOffcanvas(_asideEl, {
			overlay: true,
			baseClass: 'offcanvas-mobile',
			//closeBy: 'kt_todo_aside_close',
			toggleBy: 'kt_aside_menu_toggle'
		});

		//find last active link
		//let lastActiveLink = $('#kt_aside_menu .navi-link.active').last();
		//let position = lastActiveLink.offset().top - $('#kt_aside_menu').offset().top - 106;
		//$('#kt_aside_menu').scrollTop(position);
	}

	$('select').each(function() {
		if ($(this).data('target')) {
			let target = $(this).data('target');
			$(this).on('change', function() {
				$('#' + target).addClass('d-none');
				if ($(this).val() == '') {
					$('#' + target).removeClass('d-none');
				}
			});

			//first load
			//$(this).trigger('change');
			$('#' + target).addClass('d-none');
			if ($(this).val() == '') {
				$('#' + target).removeClass('d-none');
			}
		}
	});

	if ($('.show-more').length) {
		$('.show-more').each(function() {
			let maxLines = $(this).data('nb-lines');

			let divHeight = $(this).outerHeight();
			let fontSize = $(this).css('font-size');
			let lineHeight = Math.floor(parseInt(fontSize.replace('px','')) * 1.5);
			let lines = Math.floor(divHeight / lineHeight);

			if (lines > maxLines) {
				$(this).addClass('stretched');
				$(this).append('<div class="show-more-button"><a href="#">→ ' + __('Voir plus') + '</a></div>');
			}
		});

		$('.show-more-button a').on('click', function(e) {
			e.preventDefault();
			$(this).closest('.show-more').removeClass('stretched');
			$(this).closest('.show-more-button').remove();
		});
	}

	$('input[data-focus]').each(function() {
		$(this).trigger('focus');
	});

	$('#kt_quick_search_dropdown_menu').on('shown.bs.dropdown', function() {
		$('#kt_quick_search_input').focus();
	});

	$('.dropdown-menu.dropdown-always-opened a').click(function(e) {
		e.stopPropagation();
	});
});

function initDataTables() {
	let datatableLanguage = {
		searchPlaceholder: "Search",
		url: cdnDir + "assets/js/plugins/datatables/jquery.dataTables.english.json",
	}
	if (appLanguage == 'fr_FR') {
		datatableLanguage = {
			url: cdnDir + "assets/js/plugins/datatables/jquery.dataTables.french.json",
			searchPlaceholder: "Recherche par mots clés",
			select: {
				rows: {
					0: "",
					_: "%d éléments sélectionnés",
					1: "1 élément sélectionné"
				}
			}
		};
	}

	$('.datatable').each(function() {
		let sortColumn = 0;
		let sortOrder = 'desc';
		if ($(this).data('sort-column')) {
			sortColumn = $(this).data('sort-column');
		}
		if ($(this).data('sort-order')) {
			sortOrder = $(this).data('sort-order');
		}
		let options = {
			responsive: true,
			stateSave: true,
			dom: `<'row'<'col-sm-12'f>><'row'<'col-sm-12'tr>>
			<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7 dataTables_pager'lp>>`,
			aaSorting: [[ sortColumn, sortOrder ]],
			columnDefs: [
				{ orderable: false, responsivePriority: 1, targets: -1 }
			],
			language: datatableLanguage
		};
		if ($(this).data('sort-column') || $(this).data('sort-order')) {
			options.stateSaveParams = function (settings, data) {
				delete data.order;
			};
		}
		if ($(this).hasClass('sortable-table')) {
			options.ordering = false;
			options.bPaginate = false;
		}
		$(this).DataTable(options);
	});
}

function CreateAlertSuccess(content, title) {
	if (!title) {
		title = __('Félicitations !');
	}
	Swal.fire(title, content, "success");
}

function CreateAlertError(content, title) {
	if (!title) {
		title = __('Erreur');
	}
	Swal.fire(title, content, "error");
}

function CreateConfirmationAlert(content, redirect, button, title) {
	if (!button) {
		button = __('Valider');
	}
	if (!title) {
		title = __('Confirmation');
	}
	Swal.fire({
		title: title,
		text: content,
		icon: "success",
		showCancelButton: true,
		confirmButtonText: button,
		cancelButtonText: __('Annuler'),
		reverseButtons: true,
		confirmButtonColor: '#1BC5BD',
		iconColor: '#1BC5BD',
	}).then(function(result) {
		if (result.value) {
			window.location.href = redirect;
		}
	});
}

function CreateConfirmationDeleteAlert(objectName, idObject, content, button, title, redirect) {
	if (!button) {
		button = __('Supprimer');
	}
	if (!title) {
		title = __('Confirmation');
	}
	Swal.fire({
		title: title,
		text: content,
		icon: "warning",
		showCancelButton: true,
		confirmButtonText: button,
		cancelButtonText: __('Annuler'),
		reverseButtons: true,
		confirmButtonColor: '#F64E60',
		iconColor: '#F64E60',
	}).then(function(result) {
		if (result.value) {
			if (redirect) {
				window.location.href = redirect;
			} else {
				$('#suppr_' + objectName + ' #' + objectName).val(idObject);
				$('#suppr_' + objectName).submit();
			}
		}
	});
}

function ShowToast(type, message) {
	toastr.options = {
		"closeButton": true,
		"debug": false,
		"newestOnTop": false,
		"progressBar": true,
		"positionClass": "toast-top-right",
		"preventDuplicates": false,
		"onclick": null,
		"showDuration": "300",
		"hideDuration": "1000",
		"timeOut": (type == 'error' ? "10000" : "5000"),
		"extendedTimeOut": "1000",
		"showEasing": "swing",
		"hideEasing": "linear",
		"showMethod": "fadeIn",
		"hideMethod": "fadeOut"
	};

	if (type == 'error') {
		toastr.error(message);
	} else if (type == 'success') {
		toastr.success(message);
	} else if (type == 'notice') {
		toastr.info(message);
	}
}

function uniqid(container) {
	var nom = $(this).val();

	var accent = [
		/[\300-\306]/g, /[\340-\346]/g, // A, a
		/[\310-\313]/g, /[\350-\353]/g, // E, e
		/[\314-\317]/g, /[\354-\357]/g, // I, i
		/[\322-\330]/g, /[\362-\370]/g, // O, o
		/[\331-\334]/g, /[\371-\374]/g, // U, u
		/[\321]/g, /[\361]/g, // N, n
		/[\307]/g, /[\347]/g, // C, c
	];
	var noaccent = ['A', 'a', 'E', 'e', 'I', 'i', 'O', 'o', 'U', 'u', 'N', 'n', 'C', 'c'];

	for (var i = 0; i < accent.length; i++) {
		nom = nom.replace(accent[i], noaccent[i]);
	}

	uniqid = nom.replace(/\s/g, '-').replace(/[^a-zA-Z0-9\-]/g, '').replace(RegExp('-{2,}', 'g'), '-');

	var target = container.currentTarget.id + '_uniqid';
	if ($(container.currentTarget).data('target')) {
		target = $(container.currentTarget).data('target');
	}
	$('#' + target).val(uniqid.toLowerCase());
}

function standalone_uniqid(container) {
	var nom = $(this).val();
	var uniqid = '';

	var accent = [
		/[\300-\306]/g, /[\340-\346]/g, // A, a
		/[\310-\313]/g, /[\350-\353]/g, // E, e
		/[\314-\317]/g, /[\354-\357]/g, // I, i
		/[\322-\330]/g, /[\362-\370]/g, // O, o
		/[\331-\334]/g, /[\371-\374]/g, // U, u
		/[\321]/g, /[\361]/g, // N, n
		/[\307]/g, /[\347]/g, // C, c
	];
	var noaccent = ['A', 'a', 'E', 'e', 'I', 'i', 'O', 'o', 'U', 'u', 'N', 'n', 'C', 'c'];

	for (var i = 0; i < accent.length; i++) {
		nom = nom.replace(accent[i], noaccent[i]);
	}

	uniqid = nom.replace(/\s/g, '-').replace(/[^a-zA-Z0-9\-]/g, '').replace(RegExp('-{2,}', 'g'), '-');

	$('#' + container.currentTarget.id).val(uniqid.toLowerCase());
}

function selectFileWithCKFinder(elementId) {
	CKFinder.popup({
		chooseFiles: true,
		width: 800,
		height: 600,
		skin: 'neko',
		plugins: [
			baseDir + '/lib/ckfinder360/plugins/CustomDialog/CustomDialog.js'
		],
		onInit: function (finder) {
			finder.on('files:choose', function (evt) {
				var file = evt.data.files.first();
				var fileUrl = file.getUrl();
				$('#' + elementId).val(fileUrl);
				$('#' + elementId).trigger('input');
				if ($('#actual-' + elementId).length) {
					$('#actual-' + elementId).val(fileUrl);
				}
				if ($('#' + elementId).data('target')) {
					$('#' + $('#' + elementId).data('target') + ' img').attr('src', fileUrl);
					$('#' + $('#' + elementId).data('target')).removeClass('d-none');
				}
				if ($('#actual-' + elementId).length && $('#actual-' + elementId).data('target')) {
					$('#' + $('#actual-' + elementId).data('target') + ' img').attr('src', fileUrl);
				}
			});

			finder.on('file:choose:resizedImage', function (evt) {
				var output = document.getElementById(elementId);
				output.value = evt.data.resizedUrl;
			});
		}
	});
}

function displayVideo(videoUrl) {
	let content = '<div class="embed-responsive embed-responsive-16by9">';
	content += '<iframe class="embed-responsive-item rounded border" src="' + videoUrl + '" width="854" height="480" frameborder="0" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe>';
	content += '</div>';
	$('#ModalVideo .modal-body').html(content);
	$('#ModalVideo').on('hidden.bs.modal', function() {
		$('#ModalVideo .modal-body').html('');
	});
	$('#ModalVideo').modal('show');
}

function ToggleFilters() {
	if ($('.filters').hasClass('d-none')) {
		$('.filters').removeClass('d-none');
		$('#btnFilters').removeClass('btn-light-primary').addClass('btn-primary');
	} else {
		$('.filters').addClass('d-none');
		$('#btnFilters').removeClass('btn-primary').addClass('btn-light-primary');
	}
}

function insertText(id, text) {
	if ($('#' + id).hasClass('ckeditor') || $('#' + id).hasClass('ckeditorsmall')) {
		if (typeof editors[id] !== 'undefined') {
			let editor = editors[id];
			/*let value = editor.getData();
			value += '<p>' + text + '</p>';
			editor.setData(value);*/

			editor.model.change(writer => {
				writer.insertText(text, editor.model.document.selection.getFirstPosition());
			});
		}
	} else if ($('#' + id).hasClass('tinymce') || $('#' + id).hasClass('tinymce-small')) {
		if (typeof editors[id] !== 'undefined') {
			let editor = tinyMCE.get(id);
			let cursorPosition = editor.selection.getBookmark(2).start;
			let sum = cursorPosition.reduce(function(previousValue, currentValue) {
				return currentValue + previousValue;
			});
			if (!sum) {
				editor.setContent(editor.getContent() + '<p>' + text + '</p>');
			} else {
				editor.execCommand('mceInsertContent', false, '<p>' + text + '</p>');
			}
		}
	} else {
		let content = $('#' + id).val();
		$('#' + id).val((content ? content + "\n" : '') + text.replace('<br>', "\n"));

		if ($('#' + id).hasClass('autosize')) {
			let textarea = $('#' + id);
			autosize.update(textarea);
		}
	}
}

function createModalImg(imgUrl, title, downloadUrl) {
	$('#modalPreview').remove();
	if (!downloadUrl) {
		downloadUrl = imgUrl;
	}
	if (imgUrl.indexOf('?') !== -1) {
		downloadUrl = downloadUrl + '&download';
	} else {
		downloadUrl = downloadUrl + '?download';
	}

	var modal = '<div class="modal fade" id="modalPreview" tabindex="-1" role="dialog" aria-labelledby="exampleModalSizeSm" aria-hidden="true">';
	modal += '<div class="modal-dialog modal-dialog-centered modal-xl" role="document">';
	modal += '<div class="modal-content">\n' +
		'            <div class="modal-header">\n' +
		'                <h5 class="modal-title" id="exampleModalLabel">' + title + '</h5>\n' +
		'                <button type="button" class="close" data-dismiss="modal" aria-label="Close">\n' +
		'                    <i aria-hidden="true" class="ki ki-close"></i>\n' +
		'                </button>\n' +
		'            </div>\n' +
		'            <div class="modal-body text-center">\n' +
		'                <img class="img-fluid" src=\'' + imgUrl + '\'>\n' +
		'            </div>\n' +
		'            <div class="modal-footer d-flex flex-row justify-content-between">\n' +
		'                <a class="btn btn-primary font-weight-bold mr-4" href="' + downloadUrl + '" target="_blank">' + __('Télécharger') + '</a>\n' +
		'                <button type="button" class="btn btn-light-primary font-weight-bold" data-dismiss="modal">' + __('Fermer') + '</button>\n' +
		'            </div>\n' +
		'        </div>';

	$('body').append(modal);
	$('#modalPreview').modal('show');
}

function createModalDocument(url, title) {
	$('#modalPreview').remove();
	let downloadUrl = url;
	if (url.indexOf('?') !== -1) {
		downloadUrl = url + '&download';
	} else {
		downloadUrl = url + '?download';
	}

	let content = '<iframe class="border-0 w-100 h-1000px" src=\'' + url + '\'></iframe>';

	//get extension
	let extension = url.split('.').pop();
	let isImage = ['jpg', 'jpeg', 'png'].includes(extension);
	if (isImage) {
		content = '<img class="img-fluid" src=\'' + url + '\'>';
	}

	var modal = '<div class="modal fade" id="modalPreview" tabindex="-1" role="dialog" aria-labelledby="exampleModalSizeSm" aria-hidden="true">';
	modal += '<div class="modal-dialog modal-dialog-centered modal-xl" role="document">';
	modal += '<div class="modal-content">\n' +
		'            <div class="modal-header">\n' +
		'                <h5 class="modal-title" id="exampleModalLabel">' + title + '</h5>\n' +
		'                <button type="button" class="close" data-dismiss="modal" aria-label="Close">\n' +
		'                    <i aria-hidden="true" class="ki ki-close"></i>\n' +
		'                </button>\n' +
		'            </div>\n' +
		'            <div class="modal-body text-center">\n' +
		'                ' + content + '\n' +
		'            </div>\n' +
		'            <div class="modal-footer d-flex flex-row justify-content-between">\n' +
		'                <a class="btn btn-primary font-weight-bold mr-4" href="' + downloadUrl + '" target="_blank">' + __('Télécharger') + '</a>\n' +
		'                <button type="button" class="btn btn-light-primary font-weight-bold" data-dismiss="modal">' + __('Fermer') + '</button>\n' +
		'            </div>\n' +
		'        </div>';

	$('body').append(modal);
	$('#modalPreview').modal('show');
}

function toggleLeftMenu() {
	if ($('#kt_aside_menu').hasClass('d-lg-none')) {
		$('#kt_aside_menu').removeClass('d-lg-none');
		$('#kt_aside_content').addClass('ml-lg-8');
	} else {
		$('#kt_aside_menu').addClass('d-lg-none');
		$('#kt_aside_content').removeClass('ml-lg-8');
	}
}

function scrollToElement(id, offset) {
	let position = $(id).offset().top;
	if (offset) {
		position = position + offset;
	}
	$('html, body').animate({
		scrollTop: position
	}, 1000);
}

function printDocument(url) {
	var iframe = this._printIframe;
	if (!this._printIframe) {
		iframe = this._printIframe = document.createElement('iframe');
		document.body.appendChild(iframe);

		iframe.style.display = 'none';
		iframe.onload = function() {
			setTimeout(function() {
				iframe.focus();
				iframe.contentWindow.print();
			}, 1);
		};
	}

	iframe.src = url;
}

function CreateMailBoxAlert(nbOpenedMails) {
	Swal.fire({
		title: __('Messagerie'),
		text: n__('Vous avez 1 message ouvert', 'Vous avez %d messages ouverts', nbOpenedMails, nbOpenedMails),
		icon: "info",
		showCancelButton: true,
		confirmButtonText: n__('Voir le message', 'Voir les messages', nbOpenedMails),
		cancelButtonText: __('Fermer'),
		reverseButtons: true,
		iconColor: '#3fc3ee',
	}).then(function(result) {
		if (result.value) {
			window.location.href = baseDir + '/app/mailbox/';
		}
	});
}
