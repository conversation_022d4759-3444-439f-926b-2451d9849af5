let sortableEnabled = false;

$(document).ready(function () {
    if ($('#notes .note').length) {
        sortNotes();
    }
});

function ShowNoteForm()
{
    HideEditNoteForm();

    $('.tab-notes-form').show();

    $('.tab-notes-form .label').off('click');
    $('.tab-notes-form .label').on('click', function () {
        $('.tab-notes-form .label').removeClass('active');
        $(this).addClass('active');
        $('.tab-notes-form #label').val($(this).data('label'));

        $('.tab-notes-form .tab-home-left-icon').removeClass (function (index, className) {
            return (className.match(/(^|\s)note-\S+/g) || []).join(' ');
        });
        $('.tab-notes-form .tab-home-left-icon').addClass('note-' + $(this).data('label'));
    });

    let textarea = $('.tab-notes-form #note_content');
    if (!textarea.hasClass('autosize')) {
        autosize(textarea);
        textarea.addClass('autosize');
    } else {
        autosize.update(textarea);
    }

    $('.tab-notes-form #note_content').startBootstrapSuggest();
    $('.tab-notes-form #note_content').focus();
}

function HideNoteForm()
{
    $('.tab-notes-form').hide();
}

$.fn.startBootstrapSuggest = function () {
    if (typeof users != 'undefined') {
        $(this).suggest('@', {
            data: users,
            map: function (user) {
                return {
                    value: user.username,
                    text: '<img src="'+user.avatar+'"><strong>'+user.username+'</strong> <small>'+user.fullname+'</small>'
                }
            }
        });
    }
}

function AddNote()
{
    $('#create_note #btn-validate').attr('disabled', true);
    KTApp.block('.tab-notes-form', {});

    let dossier_id = $('#create_note #dossier_id').val();
    let content = $('#create_note #note_content').val();
    let label = $('#create_note #label').val();

    $.post(
        baseDir + '/ajax/dossier/note/',
        {dossier_id: dossier_id, content: content, label:label, async: false, CSRFGuard_token: CSRFGuard_token},
        function (data) {
            $('#create_note #btn-validate').attr('disabled', false);
            KTApp.unblock('.tab-notes-form');
            if (data.status) {
                if ($('#dossierNotes #noNoteInfo').length) {
                    $('#dossierNotes #noNoteInfo').remove();
                }
                $('#notes .timeline-items').prepend(data.note);
                $('#create_note')[0].reset();
                $('#create_note .label').removeClass('active');
                $('.tab-notes-form').hide();
                setTimeout(function() {
                    //HideNoteForm();
                }, 2000);
                ShowToast('success', data.message);

                let nbNotes = parseInt($('.navi-link.active .navi-label span').text());
                nbNotes++;
                $('.navi-link.active .navi-label span').html(nbNotes);
            } else {
                ShowToast('error', data.message);
            }
        },
        'json'
    );
}

function HideEditNoteForm()
{
    enableSortNotes();
    if ($('.edit-note').length > 0) {
        var id_note = $('.edit-note').data('note');
        $('.edit-note').remove();
        $('#note' + id_note + ' .timeline-content').show();
        $('#note' + id_note + ' .timeline-media i').removeClass('fa fa-pen').addClass('flaticon-notepad');
    }
}

function EditNote(id_note)
{
    HideNoteForm();
    HideEditNoteForm();

    $.post(
        baseDir + '/ajax/dossier/note/get/',
        {id_note: id_note, async: false, CSRFGuard_token: CSRFGuard_token},
        function (data) {
            if (data.status) {
                disableSortNotes();

                $('#note' + id_note + ' .timeline-media i').removeClass('flaticon-notepad').addClass('fa fa-pen');
                $('#note' + id_note + ' .timeline-content').hide();
                $('#note' + id_note + ' .timeline-content').after(data.form);
                $('#update_note .label').on('click', function () {
                    $('#update_note .label').removeClass('active');
                    $(this).addClass('active');
                    $('#update_note #label').val($(this).data('label'));

                    $('#note' + id_note).removeClass (function (index, className) {
                        return (className.match(/(^|\s)note-\S+/g) || []).join(' ');
                    });
                    $('#note' + id_note).addClass('note-' + $(this).data('label'));
                });
                $('#update_note #note_content').startBootstrapSuggest();
                autosize($('#update_note #note_content'));
            } else {
                ShowToast('error', data.message);
            }
        },
        'json'
    );
}

function UpdateNote(id_note)
{
    $('#update_note #btn-validate').attr('disabled', true);
    KTApp.block('.tab-notes-form', {});

    let content = $('#update_note #note_content').val();
    let label = $('#update_note #label').val();
    $.post(
        baseDir + '/ajax/dossier/note/update/',
        {id_note: id_note, content: content, label:label, async: false, CSRFGuard_token: CSRFGuard_token},
        function (data) {
            $('#update_note #btn-validate').attr('disabled', false);
            KTApp.unblock('.tab-notes-form');
            if (data.status) {
                $('#note' + id_note).replaceWith(data.note);
                enableSortNotes();
                ShowToast('success', data.message);
            } else {
                ShowToast('error', data.message);
            }
        },
        'json'
    );
}

function DeleteNote(id_note)
{
    $.post(
        baseDir + '/ajax/dossier/note/delete/',
        {id_note: id_note, async: false, CSRFGuard_token: CSRFGuard_token},
        function (data) {
            if (data.status) {
                $('#note' + id_note).remove();

                let nbNotes = parseInt($('.navi-link.active .navi-label span').text());
                nbNotes--;
                $('.navi-link.active .navi-label span').html(nbNotes);
            } else {
                ShowToast('error', data.message);
            }
        },
        'json'
    );
}

function sortNotes()
{
    if ($('#notes .note').length) {
        $('#notes').sortable({
            revert: true,
            items: '.note',
            axis: 'y',
            handle: '.timeline-media',
            update: function (event, ui) {
                $.post(
                    baseDir + '/ajax/dossier/notes/positions/',
                    {notes: $(this).sortable('toArray'), CSRFGuard_token: CSRFGuard_token},
                    function (data) {
                        if (!data.status) {
                            ShowToast('error', data.message);
                        }
                    },
                    'json'
                );
            }
        });
        sortableEnabled = true;
    }
}

function disableSortNotes()
{
    if ($('#notes .note').length && sortableEnabled) {
        $('#notes').sortable('disable');
    }
}
function enableSortNotes()
{
    if ($('#notes .note').length) {
        if (!sortableEnabled) {
            sortNotes();
        } else {
            $('#notes').sortable('enable');
        }
    }
}

function displayDossierNotes(dossierId) {
    $('#modalNotes').remove();
    var modal = '<div class="modal fade" id="modalNotes" tabindex="-1" role="dialog" aria-labelledby="exampleModalSizeSm" aria-hidden="true">';
    modal += '<div class="modal-dialog modal-dialog-centered modal-xl" role="document">';
    modal += '<div class="modal-content">\n' +
        '            <div class="modal-header">\n' +
        '                <h5 class="modal-title" id="exampleModalLabel">' + __('Notes') + '</h5>\n' +
        '                <button type="button" class="close" data-dismiss="modal" aria-label="Close">\n' +
        '                    <i aria-hidden="true" class="ki ki-close"></i>\n' +
        '                </button>\n' +
        '            </div>\n' +
        '            <div class="modal-body"><div class="p-20 w-100 d-flex justify-content-center"><div class="spinner spinner-primary"></div></div></div>\n' +
        '            <div class="modal-footer d-flex flex-row justify-content-between">\n' +
        '                <a class="btn btn-light-primary font-weight-bold" href="' + baseDir + '/app/dossier/notes/' + dossierId + '/">' + __('Ajouter une note') + '</a>\n' +
        '                <button type="button" class="btn btn-light-primary font-weight-bold" data-dismiss="modal">' + __('Fermer') + '</button>\n' +
        '            </div>\n' +
        '        </div>';

    $('body').append(modal);
    $('#modalNotes').modal('show');

    $.post(
        baseDir + '/ajax/dossier/notes/' + dossierId + '/',
        { CSRFGuard_token: CSRFGuard_token},
        function (data) {
            if (data.status) {
                $('#modalNotes .modal-body').html(data.content);
            } else {
                ShowToast('error', data.message);
            }
        },
        'json'
    );
}

function PrintNote(noteId)
{
    let myWindow = window.open('', 'PRINT', 'height=600,width=800');

    myWindow.document.write('<html><head><title>' + document.title  + '</title>');
    myWindow.document.write('</head><body >');
    myWindow.document.write('<h1>Note</h1>');
    myWindow.document.write('<h3>' + $('#note' + noteId + ' .note-user').html() + '</h3>');
    myWindow.document.write('<p>' + $('#note' + noteId + ' .note-date').html() + '</p>');
    myWindow.document.write($('#note' + noteId + ' .note-content').html());
    myWindow.document.write('</body></html>');

    myWindow.document.close(); // necessary for IE >= 10
    myWindow.focus(); // necessary for IE >= 10*/

    myWindow.print();


    return true;
}
