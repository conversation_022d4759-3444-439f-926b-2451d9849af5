$(document).ready(function() {
    $(document).on('click', '.img-rotate-left', function() {
        $(this).imgFileRotate('left');
    });
    $(document).on('click', '.img-rotate-right', function() {
        $(this).imgFileRotate('right');
    });
});

$.fn.imgFileRotate = function(direction) {
    let container = $(this).closest('.img-container');
    let id = container.attr('id');
    let fileName = container.find('.overlay-rotate').data('filename');

    KTApp.block('#' + id, {});
    $.post(
        baseDir + '/ajax/image/rotate/' + direction + '/',
        {fileName: fileName, imageId: id, CSRFGuard_token: CSRFGuard_token},
        function (data) {
            KTApp.unblock('#' + id);
            if (data.status) {
                let imgLink = container.find('img').attr('src');
                container.find('img').attr('src', imgLink + '?' + new Date().getTime());
            } else {
                ShowToast('error', data.message);
            }
        },
        'json'
    );
}
