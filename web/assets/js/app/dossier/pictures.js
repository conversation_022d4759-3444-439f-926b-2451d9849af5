function openModalPictures(dropzoneId) {
    if ($('#ModalPictures').length) {
        $('#ModalPictures').remove();
    }

    let dossierId = $('#dossier_id').val();
    $.post(
        baseDir + '/ajax/dossier/pictures/modal/',
        { dropzoneId: dropzoneId, dossierId: dossierId, CSRFGuard_token: CSRFGuard_token },
        function (data) {
            if (data.status) {
                createModalPictures(data.content);
            } else {
                ShowToast('error', data.message);
            }
        },
        'json'
    );
}

function createModalPictures(content) {
    var modal = '<div class="modal fade" id="ModalPictures" tabindex="-1" role="dialog" aria-labelledby="ModalPictures" aria-hidden="true">';
    modal += '<div class="modal-dialog modal-dialog-centered modal-xl" role="document">';
    modal += '<div class="modal-content">\n' +
        '            <div class="modal-header">\n' +
        '                <h5 class="modal-title" id="exampleModalLabel">' + __('Photographies') + '</h5>\n' +
        '                <button type="button" class="close" data-dismiss="modal" aria-label="Close">\n' +
        '                    <i aria-hidden="true" class="ki ki-close"></i>\n' +
        '                </button>\n' +
        '            </div>\n' +
        '            <div class="modal-body">' + content + '</div>\n' +
        '            <div class="modal-footer">\n' +
        '                <button type="button" class="btn btn-light-primary font-weight-bold" data-dismiss="modal">' + __('Fermer') + '</button>\n' +
        '            </div>\n' +
        '        </div>';
    $('body').append(modal);
    $('#ModalPictures').modal('show');
}

function selectPicture(dropzoneId, pictureId) {
    let thumbnail = $('#ModalPictures #picture_' + pictureId).data('thumbnail');
    if ($('#actual-' + dropzoneId + '-0').length) {
        $('#actual-' + dropzoneId + '-0').remove();
    }

    let content = '<div class="image-input image-input-outline mb-7 mr-7" id="preview-' + dropzoneId + '">';
    content += '<div class="image-input-wrapper w-auto min-w-120px">';
    content += '<img class="img-fluid max-h-100" src="' + thumbnail + '">';
    content += '</div>';
    content += '<span class="btn btn-xs btn-icon btn-circle btn-white btn-hover-text-primary btn-shadow" data-action="remove" onclick="unselectPicture(\'' + dropzoneId + '\');">';
    content += '<i class="ki ki-bold-close icon-xs text-muted"></i>';
    content += '</span>';
    content += '</div>';
    content += '<input type="hidden" name="' + dropzoneId + '" value="' + pictureId + '">';
    $('#' + dropzoneId + '_files').html(content);

    $('#dropzone_' + dropzoneId + ' .controls').addClass('d-none');
    $('#ModalPictures').modal('hide');
}

function unselectPicture(dropzoneId) {
    $('#' + dropzoneId + '_files').html('');
    $('#dropzone_' + dropzoneId + ' .controls').removeClass('d-none');
}
