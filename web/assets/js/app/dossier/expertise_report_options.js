$(document).ready(function() {
    $('input[name="type"]').on('change', function() {
        let selectedType = $('input[name="type"]:checked').val();
        $('#observations_pre_report_container').addClass('d-none');
        $('#observations_final_report_container').addClass('d-none');
        $('#recipients_container').addClass('d-none');
        if (selectedType == 'preliminary') {
            $('#observations_pre_report_container').removeClass('d-none');
            $('#recipients_container').removeClass('d-none');
        } else if (selectedType == 'final') {
            $('#observations_final_report_container').removeClass('d-none');
        }
    });
    $('input[name="type"]:checked').trigger('change');

    $('input[name="display_mandate_priority"]').on('change', function() {
        let isChecked = $(this).is(':checked');
        if (isChecked) {
            $('.mandate-priority-container').removeClass('d-none');
        } else {
            $('.mandate-priority-container').addClass('d-none');
        }
    });
    $('input[name="display_mandate_priority"]:checked').trigger('change');
});
