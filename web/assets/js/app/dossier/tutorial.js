let _validations = [];

$(document).ready(function() {
    $('#registration').on('change', function (e) {
        searchRegistration();
    });

    $('#company_id').on('change', function (e) {
        searchCompany();
    });

    $(document).on('shown.bs.tab', 'a[data-toggle="tab"]', function (e) {
        let contactType = e.target.dataset.contactType;
        let value = e.target.dataset.value;
        $('#' + contactType + '_create').val(value);
        $('#' + contactType + '_create').trigger('change');
    })

    $('#link_address_expertise_immobilization_0').on('change', function() {
        if ($(this).is(':checked')) {
            $('#immobilization_place_content').addClass('d-none');
        } else {
            $('#immobilization_place_content').removeClass('d-none');
        }
    });

    $('#contact_create, #mandate_create, #contact_address_id, #contact_first_name, #contact_last_name, #contact_email, #mandate_address_id, #mandate_first_name, #mandate_last_name, #mandate_email').on('change', function() {
        displayContact();
    });
    displayContact();

    var _wizardEl = KTUtil.getById('kt_wizard');

    let missionStepId = 4;
    let vehicleStepId = 5;
    if ($('.expertise-judiciaire').length) {
        missionStepId = 5;
        vehicleStepId = 7;
    }

    var fieldsStep4 = {
        intervention_subject_id: {
            validators: {
                notEmpty: {
                    message: __('Merci de sélectionner un objet d\'intervention')
                },
            }
        },
        department_id: {
            validators: {
                notEmpty: {
                    message: __('Merci de sélectionner un département')
                },
            }
        },
    };
    _validations[missionStepId] = FormValidation.formValidation(
        _wizardEl,
        {
            fields: fieldsStep4,
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap: new FormValidation.plugins.Bootstrap({
                    eleValidClass: '',
                })
            }
        }
    );

    var fieldsStep5 = {
        brand: {
            validators: {
                notEmpty: {
                    message: __('Merci d\'indiquer la marque du véhicule')
                }
            }
        },
        model: {
            validators: {
                notEmpty: {
                    message: __('Merci d\'indiquer le modèle du véhicule')
                }
            }
        },
        /*registration: {
            validators: {
                notEmpty: {
                    message: __('Merci d\'indiquer l\'immatriculation du véhicule')
                }
            }
        }*/
    };
    _validations[vehicleStepId] = FormValidation.formValidation(
        _wizardEl,
        {
            fields: fieldsStep5,
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap: new FormValidation.plugins.Bootstrap({
                    eleValidClass: '',
                })
            }
        }
    );

    if ($('.expertise-judiciaire').length) {
        $('#kt_repeater_1').repeater({
            initEmpty: false,
            show: function () {
                $(this).slideDown();
            },
            hide: function (deleteElement) {
                $(this).slideUp(deleteElement);
            }
        });

        var containers = document.querySelectorAll('.draggable-zone');
        if (containers.length) {
            var swappable = new Sortable.default(containers, {
                draggable: '.draggable',
                handle: '.draggable .draggable-handle',
                mirror: {
                    appendTo: 'body',
                    constrainDimensions: true
                }
            });
        }
    }

    $('#btnAddCourtQuestion').on('click', function() {
        setTimeout(function() {
            let lastCard = $('#kt_repeater_1 .card').last();
            let index = lastCard.find('textarea').attr('name');
            index = index.replace('court_questions[', '');
            index = index.replace('][court_questions]', '');
            lastCard.find('textarea').attr('id', 'court_questions_' + index);
            lastCard.find('.recorder').data('target', 'court_questions_' + index);
            lastCard.find('.recorder').attr('id', 'audio_recorder' + index);

            let textarea = lastCard.find('textarea');
            autosize(textarea);
        }, 300);
    });

    $('.select-address').on('change', function() {
        let addressId = parseInt($(this).val());
        let id = $(this).attr('id').replace('_address_id', '');
        if (!addressId || Number.isNaN(addressId)) {
            $('#tab' + id + '-content-address-infos').html('');
        } else {
            $.post(
                baseDir + '/ajax/dossier/address/getResume/',
                { addressId: addressId, CSRFGuard_token: CSRFGuard_token },
                function (data) {
                    if (data.status) {
                        $('#tab' + id + '-content-address-infos').html(data.content);
                    } else {
                        ShowToast('error', data.message);
                    }
                },
                'json'
            );
        }
    });

    if ($('input[name="court_president_type"]').length) {
        $('input[name="court_president_type"]').on('change', function() {
            let value = $('input[name="court_president_type"]:checked').val();
            if (value == 'court_president') {
                $('#court_president_address_id').closest('.form-group').find('label').html(__('Président du Tribunal'));
            } else if (value == 'vice_president') {
                $('#court_president_address_id').closest('.form-group').find('label').html(__('Vice-Président du Tribunal'));
            }
        });
    }
});

function displayContact()
{
    let domNode = $('#label_send_to_contact').parent();
    $('#send_to_contact_content').remove();

    let content = '';
    let email = '';
    let name = '';
    let contactCreate = $('#contact_create').val();
    if (contactCreate == 'select') {
        let addressId = $('#contact_address_id').val();
        if (addressId) {
            if (addressId == 'mandate') {
                let mandateCreate = $('#mandate_create').val();
                if (mandateCreate == 'select') {
                    let mandateAddressId = $('#mandate_address_id').val();
                    if (mandateAddressId) {
                        email = $('#mandate_address_id option:selected').data('email');
                        name = $('#mandate_address_id option:selected').text().trim();
                    }
                } else {
                    email = $('#mandate_email').val();
                    name = $('#mandate_first_name').val() + ' ' + $('#mandate_last_name').val();
                }
            } else {
                email = $('#contact_address_id option:selected').data('email');
                name = $('#contact_address_id option:selected').text().trim();
            }
        }
    } else {
        email = $('#contact_email').val();
        name = $('#contact_first_name').val() + ' ' + $('#contact_last_name').val();
    }

    content = 'Nom du lésé : ' + name + '<br>';
    if (email) {
        content += 'Email du lésé : ' + email;
    } else {
        content += '<span class="text-danger">Aucune adresse email définie, le dossier ne sera pas envoyé.</span>';
    }

    domNode.append('<div id="send_to_contact_content" class="form-text">' + content + '</div>');
}

function AddSurcharge(surchargeId, name, price, comment) {
    var last_id = $('.table-surcharges tbody tr:last').data('id');
    if (isNaN(last_id)) {
        last_id = -1;
    }
    last_id++;

    let priceTaxIncl = parseFloat(price).toFixed(2);

    let title = name;
    if (comment) {
        title += "\n" + comment.replaceAll('<br/>', "\n");
    }

    let row = '<tr data-id="'+last_id+'">'+
        '<td><input type="hidden" name="surcharges['+last_id+'][id]" value="' + surchargeId + '"><textarea name="surcharges['+last_id+'][name]" id="surcharge_name_' + last_id + '" class="form-control autosize" rows="2">' + title + '</textarea></td>'+
        '<td class="w-100px"><input name="surcharges['+last_id+'][amount]" type="number" min="0" step="any" class="form-control input-small w-100px" value="' + priceTaxIncl + '"></td>'+
        '<td class="w-60px"><a onclick="RemoveSurcharge('+last_id+');" class="btn btn-clean btn-icon btn-light btn-hover-danger cursor-pointer mt-2"><i class="fas fa-minus-circle"></i></a></td>'+
        '</tr>';

    $('.table-surcharges tbody').append(row);
    let textarea = $('#surcharge_name_' + last_id);
    autosize(textarea);
}

function RemoveSurcharge(id) {
    $('.table-surcharges tbody tr[data-id="' + id + '"]').remove();
}

function searchRegistration() {
    let registration = $('#registration').val();
    if (registration) {
        $.post(
            baseDir + '/ajax/dossier/vehicle/registration/',
            { registration: registration, CSRFGuard_token: CSRFGuard_token},
            function (data) {
                if (data.status) {
                    if (data.alert) {
                        Swal.fire({
                            title: "Immatriculation existante",
                            html: "<p>Nous avons trouvé un autre dossier avec la même immatriculation, voulez-vous continuer ?</p><p>" + data.infos + "</p>",
                            icon: "warning",
                            showCancelButton: true,
                            confirmButtonText: "Voir le dossier existant",
                            cancelButtonText: "Continuer à créer le dossier"
                        }).then(function(result) {
                            if (result.value) {
                                window.location.href = data.redirect;
                            }
                        });
                    }
                } else {
                    ShowToast('error', data.message);
                }
            },
            'json'
        );
    }
}

function searchCompany() {
    let companyId = $('#company_id').val();
    if (companyId) {
        $.post(
            baseDir + '/ajax/company/get/',
            { id: companyId, CSRFGuard_token: CSRFGuard_token},
            function (data) {
                if (data.status) {
                    let isJudiciaire = $('.expertise-judiciaire');
                    if (isJudiciaire && data.judiciaireAmount) {
                        $('#price').val(data.judiciaireAmount);
                    } else if (data.amount) {
                        $('#price').val(data.amount);
                    }
                } else {
                    ShowToast('error', data.message);
                }
            },
            'json'
        );
    }
}

function AddDefendeur(defendeur) {
    let lastDefendeurId = $('.defendeur').last().data('defendeur-id');
    let defendeurId = lastDefendeurId + 1;

    KTApp.block('.defendeur', {});
    $.post(
        baseDir + '/ajax/dossier/expertise/judiciaire/defendeur/',
        {defendeurId: defendeurId, CSRFGuard_token: CSRFGuard_token},
        function (data) {
            KTApp.unblock('.defendeur');
            if (data.status) {
                $('#defendeurs').append(data.content);
                $('#accordionDefendeur' + defendeurId + ' [data-rel="select2"]').each(function() {
                    let options = {
                        language: (appLanguage == 'fr_FR' ? "fr" : "en"),
                    };
                    if ($(this).data('keep-on-select')) {
                        options.closeOnSelect = false;
                    }
                    if ($(this).data('placeholder')) {
                        options.placeholder = $(this).data('placeholder');
                    }
                    $(this).select2(options);
                });

                if (defendeur) {
                    setTimeout(function() {
                        fillDefendeur(defendeurId, defendeur);
                    }, 500);
                }

                HandleAddressesInputs();
            } else {
                ShowToast('error', data.message);
            }
        },
        'json'
    );
}

function AddContact(contact) {
    let lastContactId = $('.contact').last().data('contact-id');
    let contactId = lastContactId + 1;

    KTApp.block('.contact', {});
    $.post(
        baseDir + '/ajax/dossier/expertise/judiciaire/contact/',
        {contactId: contactId, CSRFGuard_token: CSRFGuard_token},
        function (data) {
            KTApp.unblock('.contact');
            if (data.status) {
                $('#contacts').append(data.content);
                $('#accordionContact' + contactId + ' [data-rel="select2"]').each(function() {
                    let options = {
                        language: (appLanguage == 'fr_FR' ? "fr" : "en"),
                    };
                    if ($(this).data('keep-on-select')) {
                        options.closeOnSelect = false;
                    }
                    if ($(this).data('placeholder')) {
                        options.placeholder = $(this).data('placeholder');
                    }
                    $(this).select2(options);
                });

                if (contact) {
                    setTimeout(function() {
                        fillContact(contactId, contact);
                    }, 500);
                }

                HandleAddressesInputs();
            } else {
                ShowToast('error', data.message);
            }
        },
        'json'
    );
}

function displayModalMissionDocument() {
    let myDropzone = Dropzone.forElement("#ModalMissionDocument #mission_document");
    myDropzone.removeAllFiles(true);

    $('#ModalMissionDocument').modal({
        backdrop: 'static',
        keyboard: false
    });
    displayModalMissionDocumentContent('modal-document-form');
    $('#ModalMissionDocument #submit').unbind('click');
    $('#ModalMissionDocument #submit').bind('click', function() {
        let missionDocument = $('#ModalMissionDocument input[name="mission_document[]"]').val();
        if (missionDocument) {
            $('#court_order_document').val(missionDocument);
            displayModalMissionDocumentContent('modal-processing-document');
            KTApp.block('#ModalMissionDocument .modal-content', {});
            $.post(
                baseDir + '/ajax/dossier/expertise/judiciaire/document/content/',
                {document: missionDocument, CSRFGuard_token: CSRFGuard_token},
                function (data) {
                    if (data.status) {
                        displayModalMissionDocumentContent('modal-extracting-document');
                        let file = data.file;
                        $.post(
                            baseDir + '/ajax/dossier/expertise/judiciaire/document/extract/',
                            {file: file, CSRFGuard_token: CSRFGuard_token},
                            function (data) {
                                KTApp.unblock('#ModalMissionDocument .modal-content');
                                if (data.status) {
                                    displayModalMissionDocumentContent('modal-document-processed');
                                    fillDossierForm(data.result);
                                } else {
                                    displayModalMissionDocumentContent('modal-document-form');
                                    ShowToast('error', data.message);
                                }
                            },
                            'json'
                        );
                    } else {
                        displayModalMissionDocumentContent('modal-document-form');
                        KTApp.unblock('#ModalMissionDocument .modal-content');
                        ShowToast('error', data.message);
                    }
                },
                'json'
            );
        }
    });
}

function displayModalMissionDocumentContent(container) {
    $('#ModalMissionDocument .modal-content-container').addClass('d-none');
    $('#ModalMissionDocument #' + container).removeClass('d-none');
    if (container == 'modal-document-processed') {
        $('#ModalMissionDocument .modal-header button').remove();
    }
}

function saveMissionDocumentConsent() {
    let consent = $('#court_order_transcript_accept_checkbox').is(':checked');
    if (consent) {
        $('#court_order_transcript_accept').val(1);
        $('#court_order_transcript_accept_date').val(Date.now());
        $('#ModalMissionDocument').modal('hide');
    } else {
        $('#modal-document-processed-error').removeClass('d-none');
        $('#modal-document-processed-error .alert').html('Merci de confirmer la vérification des informations préremplies.');
    }
}

function fillDossierForm(result) {
    if (result.rg_number) {
        $('#rg_number').val(result.rg_number);
    }
    if (result.portalis_number) {
        $('#portalis_number').val(result.portalis_number);
    }
    if (result.minute_number) {
        $('#minute_number').val(result.minute_number);
    }
    if (result.order_date) {
        $('#order_date').val(result.order_date);
    }

    if (result.vehicle) {
        $('#brand').val(result.vehicle.brand);
        $('#model').val(result.vehicle.model);
        $('#registration').val(result.vehicle.registration);
    }

    if (result.court.name) {
        $('#tabmandate-new').click();
        $('#mandate_company').val(result.court.name);
        if (result.court.address) {
            $('#mandate_address').val(result.court.address.line1);
            $('#mandate_zip').val(result.court.address.zip);
            $('#mandate_city').val(result.court.address.city);
        }
    }

    if (result.court_president.first_name) {
        $('#tabcourt_president-new').click();
        $('#court_president_first_name').val(result.court_president.first_name);
        $('#court_president_last_name').val(result.court_president.last_name);
    }

    if (result.court_clerk.first_name) {
        $('#tabcourt_clerk-new').click();
        $('#court_clerk_first_name').val(result.court_clerk.first_name);
        $('#court_clerk_last_name').val(result.court_clerk.last_name);
    }

    if (result.demandeurs) {
        result.demandeurs.forEach(function(demandeur, index) {
            setTimeout(function() {
                if (index == 0) {
                    fillContact('', demandeur);
                } else {
                    var demandeurId = index + 1;
                    if (!$('#accordionContact' + demandeurId).length) {
                        AddContact(demandeur);
                    } else {
                        fillContact(demandeurId, demandeur);
                    }
                }
            }, (index * 1000));
        });
    }

    if (result.defendeurs) {
        result.defendeurs.forEach(function(defendeur, index) {
            var defendeurId = index + 1;
            setTimeout(function() {
                if (!$('#accordionDefendeur' + defendeurId).length) {
                    AddDefendeur(defendeur);
                } else {
                    fillDefendeur(defendeurId, defendeur);
                }
            }, (index * 1000));
        });
    }

    let questions = result.questions;
    for (let i = 0; i < questions.length; i++) {
        if (!$('textarea[name="court_questions[' + i + '][court_questions]"]').length) {
            $('#btnAddCourtQuestion').click();
        }
        $('textarea[name="court_questions[' + i + '][court_questions]"]').val(questions[i]);

        let textarea = $('textarea[name="court_questions[' + i + '][court_questions]"]');
        autosize.update(textarea);
    }
}

function fillDefendeur(defendeurId, defendeur) {
    $('#tabdefendeur' + defendeurId + '-new').click();
    $('#defendeur' + defendeurId + '_first_name').val(defendeur.first_name);
    $('#defendeur' + defendeurId + '_last_name').val(defendeur.last_name);
    $('#defendeur' + defendeurId + '_address').val(defendeur.address.line1);
    $('#defendeur' + defendeurId + '_zip').val(defendeur.address.zip);
    $('#defendeur' + defendeurId + '_city').val(defendeur.address.city);
    $('#defendeur' + defendeurId + '_company').val(defendeur.address.company);
    if (typeof defendeur.company !== 'undefined' && defendeur.company) {
        $('#defendeur' + defendeurId + '_company').val(defendeur.company);
    }

    if (defendeur.lawyer.company || defendeur.lawyer.first_name || defendeur.lawyer.last_name) {
        $('#tabdefendeur' + defendeurId + '_lawyer-new').click();
        $('#defendeur' + defendeurId + '_lawyer_company').val(defendeur.lawyer.company);
        $('#defendeur' + defendeurId + '_lawyer_first_name').val(defendeur.lawyer.first_name);
        $('#defendeur' + defendeurId + '_lawyer_last_name').val(defendeur.lawyer.last_name);
        $('#defendeur' + defendeurId + '_lawyer_address').val(defendeur.lawyer.address.line1);
        $('#defendeur' + defendeurId + '_lawyer_zip').val(defendeur.lawyer.address.zip);
        $('#defendeur' + defendeurId + '_lawyer_city').val(defendeur.lawyer.address.city);
    }
}

function fillContact(contactId, contact) {
    let expanded = $('#accordionContact' + contactId + ' .card-title').attr('aria-expanded');
    if (!expanded) {
        $('#accordionContact' + contactId + ' .card-title').click();
    }

    $('#tabcontact' + contactId + '-new').click();
    $('#contact' + contactId + '_first_name').val(contact.first_name);
    $('#contact' + contactId + '_last_name').val(contact.last_name);
    $('#contact' + contactId + '_address').val(contact.address.line1);
    $('#contact' + contactId + '_zip').val(contact.address.zip);
    $('#contact' + contactId + '_city').val(contact.address.city);
    $('#contact' + contactId + '_company').val(contact.address.company);
    if (typeof contact.company !== 'undefined' && contact.company) {
        $('#contact' + contactId + '_company').val(contact.company);
    }

    if (contact.lawyer.company || contact.lawyer.first_name || contact.lawyer.last_name) {
        $('#tabcontact' + contactId + '_lawyer-new').click();
        $('#contact' + contactId + '_lawyer_company').val(contact.lawyer.company);
        $('#contact' + contactId + '_lawyer_first_name').val(contact.lawyer.first_name);
        $('#contact' + contactId + '_lawyer_last_name').val(contact.lawyer.last_name);
        $('#contact' + contactId + '_lawyer_address').val(contact.lawyer.address.line1);
        $('#contact' + contactId + '_lawyer_zip').val(contact.lawyer.address.zip);
        $('#contact' + contactId + '_lawyer_city').val(contact.lawyer.address.city);
    }
}
