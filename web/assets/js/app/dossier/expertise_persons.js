function setPresence(presence, expertiseId, personId) {
    KTApp.block('#person_presence_' + personId, {});
    $.post(
        baseDir + '/ajax/dossier/expertise/person/presence/' + expertiseId + '/' + personId + '/',
        { presence:presence, CSRFGuard_token:CSRFGuard_token, async: false },
        function (response) {
            KTApp.unblock('#person_presence_' + personId);
            if (response.status) {
                $('#person_presence_' + personId).replaceWith(response.content);
                $('#person_presence_' + personId + ' [data-toggle="tooltip"]').tooltip();
            } else {
                ShowToast('error', response.message);
            }
        },
        'json'
    );
}