$(document).ready(function(){
	var table_id = '#table_invoices';
	var ajax_file = 'invoices/app/datatable/';

    var table = $(table_id).DataTable({
	    stateSave: true,
        dom: `<'row'<'col-sm-12'f>><'row'<'col-sm-12'tr>>
			<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7 dataTables_pager'lp>>`,
        language: {
            url: cdnDir+"assets/js/plugins/datatables/jquery.dataTables.french.json",
            searchPlaceholder: __("Rechercher"),
            sLengthMenu: "_MENU_ "+__("Éléments par page")
        },
        bDestroy: true,
        bAutoWidth: false,
        buttons: [ ],
        aaSorting: [[ 0, "desc" ]],
        aoColumns: [
          {"bSortable": true},
          {"bSortable": true},
          {"bSortable": false},
          {"bSortable": true},
          {"bSortable": true},
          {"bSortable": false},
        ],
        iDisplayLength: 10,
        processing: true,
        serverSide: true,
        ajax: {
            url: baseDir + '/ajax/' + ajax_file,
            type: "post",
            data: {CSRFGuard_token: CSRFGuard_token},
            complete: function (data) {
                if (data.responseJSON.resetPage) {
                    table.page('first').draw();
                }
                $(table_id + ' [data-toggle="tooltip"]').tooltip()
            },
        }
    });
});
