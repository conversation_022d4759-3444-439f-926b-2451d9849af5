$.fn.dataTable.Debounce = function ( table, options ) {
    var tableId = table.settings()[0].sTableId;
    $('.dataTables_filter input[aria-controls="' + tableId + '"]') // select the correct input field
        .unbind() // Unbind previous default bindings
        .bind('input', (delay(function (e) { // Bind our desired behavior
            table.search($(this).val()).draw();
            return;
        }, 750))); // Set delay in milliseconds
}

function delay(callback, ms) {
    var timer = 0;
    return function () {
        var context = this, args = arguments;
        clearTimeout(timer);
        timer = setTimeout(function () {
            callback.apply(context, args);
        }, ms || 0);
    };
}

$(document).ready(function(){
	var table_id = '#table_corporations';
	var timer;

    var table = $(table_id).DataTable({
	    stateSave: true,
        responsive: true,
        dom: `<'row'<'col-sm-12'f>><'row'<'col-sm-12'tr>>
			<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7 dataTables_pager'lp>>`,
        "language": {
            "url": cdnDir+"assets/js/plugins/datatables/jquery.dataTables.french.json",
            "searchPlaceholder": __("Rechercher"),
            "select": {
                "rows": {
                    0: "",
                    _: "%d "+__("entreprises sélectionnées"),
                    1: __("1 entreprise sélectionnée")
                }
            },
            "sLengthMenu": "_MENU_ "+__("Éléments par page"),
        },
        "bDestroy": true,
        "bAutoWidth": false,
        "aaSorting": [[ 0, "desc" ]],
        "buttons": [ ],
        "columnDefs": [
            { "orderable": false, "targets": -1 }
        ],
        "columns": [
            { "data": 0 },
            { "data": 1 },
            { "data": 2 },
            { "data": 3 },
            { "data": 4 },
            { "data": 5 },
        ],
        "iDisplayLength": 10,
        processing: true,
        serverSide: true,
        "ajax":{
            url: baseDir+"/ajax/corporations/datatable/",
            type: "post",
            data: { CSRFGuard_token:CSRFGuard_token },
            complete: function (data) {
                if (data.responseJSON.resetPage) {
                    table.page('first').draw();
                }
                $(table_id + ' [data-rel="tooltip"]').tooltip()
            },
        }
    });

    setTimeout(function() {
        var debounce = new $.fn.dataTable.Debounce(table);
    }, 300);
});
