$(document).ready(function(){
	let table_id = '#table_commissions';
	let ajax_file = 'affiliation/commissions/datatable/';
	let timer;

	let data = { CSRFGuard_token:CSRFGuard_token };

	if ($(table_id).data('idpartner')) {
        data.id_partner = $(table_id).data('idpartner');
    }

    let table = $(table_id).DataTable({
        responsive: true,
	    stateSave: true,
        dom: `<'row'<'col-sm-12'tr>>
			<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7 dataTables_pager'lp>>`,
        language: {
            "url": cdnDir+"assets/js/plugins/datatables/jquery.dataTables.french.json",
            "searchPlaceholder": __("Rechercher"),
            "select": {
                "rows": {
                    0: "",
                    _: "%d "+__("commissions sélectionnées"),
                    1: __("1 commission sélectionnée")
                }
            },
            sLengthMenu: "_MENU_ "+__("Éléments par page")
        },
        "bDestroy": true,
        "bAutoWidth": false,
        aaSorting: [[ 0, "desc" ]],
        "buttons": [ ],
        "iDisplayLength": 10,
        processing: true,
        serverSide: true,
        "ajax":{
            url: baseDir + '/ajax/' + ajax_file,
            type: "post",
            data: data,
            complete: function (data) {
                if (data.responseJSON.resetPage) {
                    table.page('first').draw();
                }
            },
        }
    });
});
