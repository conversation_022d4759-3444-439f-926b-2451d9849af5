function ExportQuotes() {
    var nb_secondes = $('#loader').data('time');
    var date_start = $('#date_start').val();
    var date_end = $('#date_end').val();

    $('#loader').html('<div class="progress" style="width:400px; margin: 0 auto"><div class="progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width:0px"></div></div>');

    $('.progress-bar').animate({ width: '400px' }, nb_secondes*1000, 'linear');

    $.post(
        baseDir + '/ajax/quotes/export/csv/',
        { date_start:date_start, date_end:date_end, CSRFGuard_token:CSRFGuard_token, async: false },
        function(data) {
            $('#loader').html('');
            if (data.status) {
                ShowToast('success', data.message);
                $('#loader').html('<a class="btn btn-primary btn-lg font-weight-bolder" href="'+data.file+'" target="_blank">'+__('Télécharger le fichier')+'</a>');
            } else {
                ShowToast('error', data.message);
            }
        },
    'json');
}