$(document).ready(function () {
    Pace.on('done', function() {
        $('.page-loading').fadeOut('slow');
    });
});

function displayHistory(idHistory)
{
    let link = $('#histories').data('link');
    $('#iframe-history').prop('src', link + '?history=' + idHistory);
    $('#histories a.nav-link').removeClass('active');
    $('#histories #history' + idHistory).addClass('active');
}

function restoreHistory()
{
    $('#formEditorSubmit').attr('disabled', true).addClass('spinner spinner-left spinner-white');

    let CSRFGuard_token = $('#CSRFGuard_token').val();
    let idHistory = $('#histories a.nav-link.active').data('history');

    $.post(
        baseDir + '/ajax/page_editor/restore/',
        { idHistory: idHistory, CSRFGuard_token:CSRFGuard_token, async: false },
        function (data) {
            if (data.status) {
                let redirection = $('#btnIconLeaveEditor').prop('href');
                window.location.href = redirection;
            } else {
                $('#formEditorSubmit').attr('disabled', false).removeClass('spinner spinner-left spinner-white');
                ShowToast('error', data.message);
            }
        },
        'json'
    );
}