function reviewClientError(idClientError) {
    $("#btn-review-" + idClientError).attr('disabled', true);
    $.post(
        baseDir + '/ajax/client/error/review/',
        {
            idClientError: idClientError,
            CSRFGuard_token: CSRFGuard_token
        },
        function (data) {
            $("#btn-review-" + idClientError).attr('disabled', false);
            if (data.status) {
                if ($('#box-clients-errors').length > 0) {
                    $("#client-error-" + idClientError).remove();
                    let tbodyContent = $.trim($('#table_clients_errors tbody').html());
                    if (!tbodyContent) {
                        $('#box-clients-errors').remove();
                    }
                } else {
                    $("#btn-review-" + idClientError).remove();
                }
            } else {
                alert(data.message);
            }
        },
        'json'
    );
}
