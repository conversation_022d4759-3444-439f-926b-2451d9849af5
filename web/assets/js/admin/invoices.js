$('#logo').on('change, input', function() {
    if ($(this).val() == '') {
        $('#invoicePreview td.logo').html('');
    } else {
        $('#invoicePreview td.logo').html('<img src="' + $(this).val() + '">');
    }
});

$('input[data-target]').on('input', function() {
    var input = $(this).val();
    if (input == '') {
        $('#invoicePreview ' + $(this).data('target')).hide();
    } else {
        if ($(this).data('prefix')) {
            var input = $(this).data('prefix') + $(this).val();
        }
        $('#invoicePreview ' + $(this).data('target')).each(function(index, element) {
            if ($(this).data('nl')) {
                $(this).show().html(input + '<br>');
            } else {
                $(this).show().html(input);
            }
        });
    }
});

$('select[data-target]').on('change', function() {
    var selectedOption = $(this).children("option:selected").text();
    if (selectedOption == '' || selectedOption == '-') {
        $('#invoicePreview ' + $(this).data('target')).css('display', 'none');
    } else {
        $('#invoicePreview ' + $(this).data('target')).css('display', 'block').html(selectedOption);
    }
});

$('textarea[data-target]').on('input', function() {
    if ($(this).val() == '') {
        $('#invoicePreview ' + $(this).data('target')).css('display', 'none');
    } else {
        $('#invoicePreview ' + $(this).data('target')).css('display', 'block').html(nl2br($(this).val()));
    }
});

function nl2br(str) {
    return (str + '').replace(/([^>\r\n]?)(\r\n|\n\r|\r|\n)/g, '$1' + '<br>' + '$2');
}

var editor1 = CKEDITOR.replace('infos1', {
    height: '200',
    toolbar: 'Basic',
    on: {
        change: function (event) {
            var content = editor1.getData();
            $('#invoicePreview .infos1').html(content);
        }
    }
});

var editor2 = CKEDITOR.replace('infos2', {
    height: '200',
    toolbar: 'Basic',
    on: {
        change: function (event) {
            var content = editor2.getData();
            $('#invoicePreview .infos2').html(content);
        }
    }
});

CKEDITOR.replace('infos_new_page', {
    height: '200',
    toolbar: 'Basic'
});