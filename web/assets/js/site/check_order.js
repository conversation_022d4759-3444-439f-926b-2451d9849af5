function CheckOrder(id_order) {
    $.post(
        baseDir + '/ajax/check_order/',
        { id_order:id_order, CSRFGuard_token:CSRFGuard_token },
        function(msg) {
            if(msg.status) {
                if (msg.redirection) {
                    window.location.href = msg.redirection;
                }
                if (msg.message == 'waiting') {
                    setTimeout(function() { CheckOrder(id_order); }, 5000);
                } else {
                    $('#result').replaceWith(msg.message);
                }
            } else {
                $('#result').html(msg.message);
            }
        },
        'json'
    );
}