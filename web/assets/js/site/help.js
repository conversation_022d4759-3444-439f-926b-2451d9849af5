$(document).ready(function() {
    var timer_help;
    $('#input_help').on('keyup', function(e) {
        var search_string = $(this).val();
        if (search_string !== '' && search_string.length >= 3) {
            clearTimeout(timer_help);
            timer_help = setTimeout(function() { searchInHelp(); }, 500);
        }
        if (search_string === '') {
            clearTimeout(timer_help);
            timer_help = setTimeout(function() { showHome(); }, 1000);
        }
    });

});

function searchInHelp() {
    var search_string = $('#input_help').val();

    if (search_string !== '' && search_string.length >= 3) {
        $('#help_title').html(__('Recherche'));
        $('.quick-search-close').show();
        $('#pagination').html('');

        KTApp.block('#table-articles', {});
        $.post(
            baseDir+"/ajax/site/help/search/",
            { search: search_string, CSRFGuard_token:CSRFGuard_token },
            function(data) {
                KTApp.unblock('#table-articles', {});
                if (data.status) {
                    $('#articles').html(data.html);

                    var search_array = search_string.split(" ");
                    $.each(search_array, function(index, value) {
                        $('#articles').highlight(value);
                    });
                } else {
                    $('#articles').html('<div class="alert alert-custom alert-light-danger">'+__('Une erreur est survenue')+'</div>');
                }
            },
            'json'
        );
    }

    return false;
}

function setArticleNote(id_article, type) {
    $('.card-note').addClass('inactive');
    $('.card-note.card-note-' + type).removeClass('inactive');
    $.post(
        baseDir+"/ajax/help/article/note/",
        { id_article: id_article, type: type, CSRFGuard_token:CSRFGuard_token },
        function(data) {

        },
        'json'
    );
}

/*
highlight v4
<http://johannburkard.de/blog/programming/javascript/highlight-javascript-text-higlighting-jquery-plugin.html>
*/
jQuery.fn.highlight=function(t){function e(t,i){var n=0;if(3==t.nodeType){var a=t.data.toUpperCase().indexOf(i);if(a>=0){var s=document.createElement("mark");s.className="highlight";var r=t.splitText(a);r.splitText(i.length);var o=r.cloneNode(!0);s.appendChild(o),r.parentNode.replaceChild(s,r),n=1}}else if(1==t.nodeType&&t.childNodes&&!/(script|style)/i.test(t.tagName))for(var h=0;h<t.childNodes.length;++h)h+=e(t.childNodes[h],i);return n}return this.length&&t&&t.length?this.each(function(){e(this,t.toUpperCase())}):this},jQuery.fn.removeHighlight=function(){return this.find("mark.highlight").each(function(){with(this.parentNode.firstChild.nodeName,this.parentNode)replaceChild(this.firstChild,this),normalize()}).end()};

// Ignore accents
String.prototype.removeAccents=function(e){return e?this.replace(/[áàãâä]/gi,"a").replace(/[éè¨ê]/gi,"e").replace(/[íìïî]/gi,"i").replace(/[óòöôõ]/gi,"o").replace(/[úùüû]/gi,"u").replace(/[ç]/gi,"c").replace(/[ñ]/gi,"n"):this};
