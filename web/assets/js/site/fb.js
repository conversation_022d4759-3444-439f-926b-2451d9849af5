function fbApi(accountId, event, eventId, params) {
    var xhr = new XMLHttpRequest();
    xhr.open("POST", baseDir+"/ajax/fb/api/", true);
    xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    let data = {
        CSRFGuard_token:CSRFGuard_token,
        accountId: accountId,
        event: event,
        eventId: eventId,
        params: params
    };
    xhr.send(serialize(data));
}

serialize = function(obj, prefix) {
    var str = [],
        p;
    for (p in obj) {
        if (obj.hasOwnProperty(p)) {
            var k = prefix ? prefix + "[" + p + "]" : p,
                v = obj[p];
            str.push((v !== null && typeof v === "object") ?
                serialize(v, k) :
                encodeURIComponent(k) + "=" + encodeURIComponent(v));
        }
    }
    return str.join("&");
}