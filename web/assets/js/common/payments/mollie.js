var profileId = $('#mollie_profile_id').val();
var mollie = Mollie(profileId, { locale: appLanguage, testmode: false });
var cardButton = document.getElementById('btnValidate');

var elementStyles = {
    styles: {
        base: {
            color: '#3F4254',
            fontWeight: 'normal',
            fontSize: '14px',
            '::placeholder': {
                color: '#B5B5C3',
            },
        },
        invalid: {
            color: '#E25950',
            '::placeholder': {
                color: '#FFCCA5',
            },
        },
    }
};

var cardNumber = mollie.createComponent('cardNumber', elementStyles);
cardNumber.mount('#mollie-card-number');

var cardHolder = mollie.createComponent('cardHolder', elementStyles);
cardHolder.mount('#mollie-card-holder');

var expiryDate = mollie.createComponent('expiryDate', elementStyles);
expiryDate.mount('#mollie-expiry-date');

var verificationCode = mollie.createComponent('verificationCode', elementStyles);
verificationCode.mount('#mollie-verification-code');

var mollieInputElements = [cardNumber, cardHolder, expiryDate, verificationCode];
var mollieElements = ['cardNumber', 'cardHolder', 'expiryDate', 'verificationCode'];

mollieInputElements.forEach(function(element, idx) {
    element.addEventListener('change', function (event) {
        if (event.touched) {
            $('#card-errors').html('');
            $('.form-group').removeClass('has-warning');
            if (!event.valid) {
                $('.form-group.' + mollieElements[idx]).addClass('has-warning');
                $('#card-errors').html('<i class="fa fa-warning"></i> ' + event.error);
            }
        }
    });
});

$(document).ready(function() {
    setPayPalPayment();
});

function setPayPalPayment()
{
    $('input[name="id_payment"]').on('change', function() {
        if ($(this).data('type') == 'mollie-paypal') {
            $('.payment.mollie-paypal #mollie_paypal_token_nonce').val('a');
        } else {
            $('.payment.mollie-paypal #mollie_paypal_token_nonce').val('');
        }
    });
}

function processMolliePayment(custom)
{
    mollie.createToken().then(function (result) {
        if (result.error) {
            displayError(result.error.message, 'payment');
            setPaymentInProcess(false);
        } else {
            let formData = getFormInputs();
            formData['card_token'] = result.token;
            formData['CSRFGuard_token'] = CSRFGuard_token;
            formData['custom'] = custom;
            $.post(
                baseDir + mollieAjaxRoute,
                formData,
                function (result) {
                    handleMollieServerResponse(result);
                },
                'json'
            );
        }
    });
}

function handleMollieServerResponse(response) {
    if (response.error) {
        displayError(response.error, 'payment');
        setPaymentInProcess(false);
        if (response.error.match(/nom/g) || response.error.match(/email/g)) {
            goToStep(1);
        }
    } else if (typeof response.status !== 'undefined' && !response.status) {
        displayError(response.message, 'payment');
        setPaymentInProcess(false);
    } else if (response.requires_action) {
        window.location.href = response.href;
    } else {
        if (typeof redirection !== 'undefined') {
            window.location.href = redirection;
        } else if (response.redirection) {
            window.location.href = response.redirection;
        } else if (response.reference) {
            window.location.href = baseDir + '/checkout/?reference=' + response.reference;
        } else if (response.message) {
            $('#use_payment_method').html('');
            $('#card-form').html('<div class="alert alert-success">' + response.message + '</div>');
        } else {
            window.location.href = baseDir + '/checkout/';
        }
    }
}

function processMolliePaymentOneClick(custom, token) {
    setPaymentInProcess(true);

    let formData = getFormInputs();
    formData['CSRFGuard_token'] = CSRFGuard_token;
    formData['custom'] = custom;
    formData['token'] = token;
    formData['mollie_use_token'] = true;

    $.post(
        baseDir + '/ajax/mollie/charge/',
        formData,
        function (result) {
            handleMollieServerResponse(result);
        },
        'json'
    );
}

function processMolliePaypal(custom) {
    setPaymentInProcess(true);

    let formData = getFormInputs();
    formData['CSRFGuard_token'] = CSRFGuard_token;
    formData['custom'] = custom;
    formData['card_token'] = '';
    formData['paypal'] = true;

    $.post(
        baseDir + '/ajax/mollie/charge/',
        formData,
        function (result) {
            handleMollieServerResponse(result);
        },
        'json'
    );
}

