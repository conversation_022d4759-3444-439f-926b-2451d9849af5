'use strict';

var fileId = '',
  wrapper = {},
  buttonNewPhoto = {},
  buttonClose = {},
  video = {},
  canvas = {},
  selectCamera = {};

function initCameraContainer(id) {
  fileId = id;

  wrapper = document.getElementById('cameraContainer' + id);
  buttonNewPhoto = document.getElementById('btnNewPhoto' + id);
  video = document.querySelector('#cameraContainer' + id + ' video');
  canvas = document.querySelector('#cameraContainer' + id + ' canvas');

  buttonClose = document.getElementById('btnClose' + id);
  buttonClose.addEventListener('click', hideCameraContainer);

  selectCamera = document.getElementById('selectCamera' + id);
  selectCamera.addEventListener('change', changeVideoSource);

  initEvent({ video: true });

  if (navigator.mediaDevices === undefined) {
    navigator.mediaDevices = {};
  }

  if (navigator.mediaDevices.getUserMedia === undefined) {
    navigator.mediaDevices.getUserMedia = function (constraints) {
      var getUserMedia = navigator.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia || navigator.msGetUserMedia;
      if (!getUserMedia) {
        return Promise.reject(new Error('getUserMedia is not implemented in this browser'));
      }

      return new Promise(function (resolve, reject) {
        getUserMedia.call(navigator, constraints, resolve, reject);
      });
    }
  }
}

function onTakeAPhoto() {
  canvas.getContext('2d').drawImage(video, 0, 0, video.width, video.height);

  canvas.toBlob(function (blob) {
    const formData = new FormData();
    formData.append('img', blob, 'img');
    formData.append('CSRFGuard_token', CSRFGuard_token);
    $.ajax({
      url: baseDir + '/ajax/webcam/picture/save/',
      type: 'POST',
      data: formData,
      processData: false,
      contentType: false,
      success(data) {
        if (data.status) {
          closeMediaStream();

          $('#cameraContainer' + fileId).addClass('d-none');
          $('#cameraContainer' + fileId).closest('.form-group').find('.dropzone').removeClass('d-none');

          let file = {
            name: data.file,
            type: 'image/jpeg',
            size: data.fileSize,
            status: 'success'
          };
          let myDropzone = Dropzone.forElement('#' + fileId);
          myDropzone.emit("addedfile", file);
          myDropzone.emit("complete", file);
        } else {
          ShowToast('error', data.message);
        }
      },
      error() {
        ShowToast('error', 'Upload error');
      },
    });
  }, 'image/jpeg', 1);
}

function onLoadVideo() {
  video.setAttribute('width', this.videoWidth);
  video.setAttribute('height', this.videoHeight);
  canvas.setAttribute('width', this.videoWidth);
  canvas.setAttribute('height', this.videoHeight);
  video.play();
}

function onMediaStream(stream) {
  window.localStream = stream;

  let options = selectCamera.options;
  if (!options.length) {
    navigator.mediaDevices.enumerateDevices().then(function (devices) {
      for (var i = 0; i < devices.length; i++) {
        var device = devices[i];
        if (device.kind === 'videoinput') {
          var option = document.createElement('option');
          option.value = device.deviceId;
          option.text = device.label || 'camera ' + (i + 1);
          selectCamera.appendChild(option);
        }
      }
    });
  }

  if ('srcObject' in video) {
    video.srcObject = stream;
  } else {
    video.src = window.URL.createObjectURL(stream);
  }

  buttonNewPhoto.addEventListener('click', onTakeAPhoto);
  video.addEventListener('loadedmetadata', onLoadVideo);
}

function onMediaError(err) {
  ShowToast('error', err.name + ': ' + err.message);
}

function initEvent(constraints) {
  navigator.mediaDevices
    .getUserMedia(constraints)
    .then(onMediaStream)
    .catch(onMediaError);
}

function closeMediaStream() {
  localStream.getTracks().forEach( (track) => {
    track.stop();
  });
}

function hideCameraContainer() {
  closeMediaStream();
  $('#cameraContainer' + fileId).addClass('d-none');
  $('#cameraContainer' + fileId).closest('.form-group').find('.dropzone').removeClass('d-none');
}

function changeVideoSource() {
  let option = selectCamera.value;

  if (localStream) {
    closeMediaStream();
  }

  let constraints = {
    video: {
      width: {
        min: 640,
        ideal: 640,
        max: 640,
      },
      height: {
        min: 480,
        ideal: 480,
        max: 480
      },
      deviceId: {
        exact: option
      }
    }
  }
  initEvent(constraints);
}