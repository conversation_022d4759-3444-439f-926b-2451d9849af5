/*global tarteaucitron */
tarteaucitron.lang = {
    "middleBarHead": "☝ 🍪",
    "adblock": "Witaj! Ta witryna oferuje daje mozliwość wyboru aktywacji usług zewnętrznych.",
    "adblock_call": "Prosze wylaczyc adblocker aby rozpoczac dostosowanie do potrzeb uzytkownika.",
    "reload": "Odswież stronę",
    
    "alertBigScroll": "Poprzez kontynuowanie przewijania,",
    "alertBigClick": "Pozostając na tej stronie",
    "alertBig": "zgadzasz się na korzystanie ze wszystkich zewnetrzynych usług",
    
    "alertBigPrivacy": "Ta witryna używa plików cookie i pozwala wybrać na które chcesz zezwolić",
    "alertSmall": "Zarządzanie usługami",
    "personalize": "Personalizacja",
    "acceptAll": "OK, akceptuję wszystko",
    "close": "zamknij",

    "privacyUrl": "Polityka prywatności",
    
    "all": "Preferencja dla wszystkich usług",

    "info": "Ochrona prywatności",
    "disclaimer": "Zgadzając się na korzystanie z usług zewnętrznych, akceptujesz ich pliki cookies oraz wykorzystanie technologii śledzących, niezbędnych do ich funkcjonowania.",
    "allow": "Zezwalaj",
    "deny": "Odmów",
    "noCookie": "Ta usługa nie korzysta z plików cookie.",
    "useCookie": "Ta usługa może zainstalować pliki cookie",
    "useCookieCurrent": "Ta usługa zainstalowała pliki cookie",
    "useNoCookie": "Ta usługa nie zainstalowała żadnego pliku cookie.",
    "more": "Więcej informacji",
    "source": "Zobacz oficjalną stronę internetową",
    "credit": "Cookies menadżer od tarteaucitron.js",

    "toggleInfoBox": "Pokaż/ukryj informacje o zapisie plików cookie",
    "title": "Panel zarządzania plikami cookies",
    "cookieDetail": "Szczegóły plików cookie dla",
    "ourSite": "na naszej stronie",
    "newWindow": "(nowe okno)",
    "allowAll": "Zezwól na wszystkie pliki cookies",
    "denyAll": "Zablokuj wszystkie pliki cookies",
    
    "fallback": "jest nieaktywna.",

    "ads": {
        "title": "Sieć reklamowa",
        "details": "Sieci reklamowe mogą generować przychody ze sprzedaży powierzchni reklamowej na stronie."
    },
    "analytic": {
        "title": "Pomiar oglądalności",
        "details": "Usługi pomiaru oglądalności wykorzystywane są do generowania przydatnych statystyk potrzebnych w doskonaleniu strony."
    },
    "social": {
        "title": "Portale społecznościowe",
        "details": "Sieci społecznościowe mogą poprawić użyteczność serwisu i pomóc w promocji za pośrednictwem udostępniania strony."
    },
    "video": {
        "title": "Filmy",
        "details": "Usługa udostępniania wideo pomoże dodać multimedia do strony i zwiększyć jej ogladalność."
    },
    "comment": {
        "title": "Komentarze",
        "details": "Zarządzanie komentarzami ułatwia komentowanie i zwalcza spam."
    },
    "support": {
        "title": "Pomoc",
        "details": "Usługa pomocy technicznej pozwala skontaktować się z administratorem witryny i pomaga ją udoskonalić."
    },
    "api": {
        "title": "APIs",
        "details": "APIs służą do ładowania skryptów: geolokalizacji, wyszukiwarek, tłumaczenia, ..."
    },
    "other": {
        "title": "Inne",
        "details": "Usługi do wyświetlania treści internetowych."
    },
    
    "mandatoryTitle": "Mandatory cookies",
    "mandatoryText": "This site uses cookies necessary for its proper functioning which cannot be deactivated."
};
