var options = {
    "privacyUrl": baseDir + "/privacy/",
    "hashtag": "#tarteaucitron",
    "cookieName": "tarteaucitron",
    "orientation": "bottom",
    "showAlertSmall": true,
    "cookieslist": false,
    "showIcon": false,
    "iconPosition": "BottomRight",
    "adblocker": false,
    "DenyAllCta" : false,
    "AcceptAllCta" : true,
    "highPrivacy": true,
    "handleBrowserDNTRequest": false,
    "removeCredit": true,
    "moreInfoLink": true,
    "useExternalCss": false,
    "readmoreLink": "",
    "mandatory": true,
};
tarteaucitron.init(options);

/* facebookpixel events */
document.addEventListener('facebookpixel_allowed', function (e) {
    saveConsent('facebookpixel', 'allowed');
}, false);
document.addEventListener('facebookpixel_denied', function (e) {
    saveConsent('facebookpixel', 'denied');
}, false);

/* google events */
document.addEventListener('gtag_allowed', function (e) {
    saveConsent('gtag', 'allowed');
}, false);
document.addEventListener('gtag_denied', function (e) {
    saveConsent('gtag', 'denied');
}, false);

/* gtm events */
document.addEventListener('googletagmanager_allowed', function (e) {
    saveConsent('googletagmanager', 'allowed');
}, false);
document.addEventListener('googletagmanager_denied', function (e) {
    saveConsent('googletagmanager', 'denied');
}, false);

function saveConsent(service, response) {
    var data = {
        url: window.location.href,
        service: service,
        response: response,
        CSRFGuard_token: CSRFGuard_token,
    };
    $.ajax({
        type: 'POST',
        url: '/ajax/cookie/consent/',
        data: data,
        success: function(res) {
        }
    });
}
