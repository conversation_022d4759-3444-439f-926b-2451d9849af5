<?xml version="1.0" encoding="utf-8"?>
<svg width="436px" height="423" viewBox="0 0 436 423" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g filter="url(#filter0_d_2926_24380)"/>
  <g filter="url(#filter1_d_2926_24380)"/>
  <g filter="url(#filter2_d_2926_24380)"/>
  <g filter="url(#filter3_d_2926_24380)">
    <rect x="12.115" y="9.81006" width="409.751" height="397.192" rx="25" fill="white"/>
    <rect x="12.615" y="10.3101" width="408.751" height="396.192" rx="24.5" stroke="#EFF0F7"/>
  </g>
  <rect x="165.317" y="340.176" width="103.347" height="34.9926" rx="17.4963" fill="#563AFF"/>
  <path d="M191.552 358.14H193.424V359.5C192.956 359.838 192.336 360.034 191.77 360.034C190.388 360.034 189.213 358.837 189.213 357.4C189.213 355.975 190.388 354.778 191.77 354.778C192.499 354.778 193.315 355.104 193.783 355.648L194.752 354.745C194.023 353.995 192.869 353.538 191.77 353.538C189.637 353.538 187.831 355.3 187.831 357.4C187.831 359.5 189.637 361.274 191.77 361.274C192.869 361.274 194.023 360.806 194.752 360.055V357.03H191.552V358.14ZM198.799 360.208C197.928 360.208 197.308 359.598 197.167 358.717H201.53C201.53 356.671 200.507 355.387 198.766 355.387C197.156 355.387 195.883 356.617 195.883 358.314C195.883 360.045 197.221 361.274 198.842 361.274C199.659 361.274 200.638 360.926 201.128 360.404L200.366 359.62C200.007 359.968 199.365 360.208 198.799 360.208ZM198.777 356.454C199.626 356.454 200.126 357.041 200.246 357.836H197.178C197.352 356.998 197.972 356.454 198.777 356.454ZM205.348 359.936C205.228 360.055 205.021 360.142 204.793 360.142C204.477 360.142 204.227 359.87 204.227 359.49V356.617H205.5V355.518H204.227V353.973H202.943V355.518H202.127V356.617H202.943V359.653C202.943 360.599 203.596 361.274 204.521 361.274C204.956 361.274 205.413 361.1 205.729 360.828L205.348 359.936ZM211.739 361.274C213.045 361.274 213.948 360.578 213.948 359.49C213.948 358.325 212.925 358.01 211.968 357.749C211.119 357.487 210.771 357.39 210.771 357.02C210.771 356.628 211.163 356.399 211.652 356.399C212.175 356.399 212.762 356.617 213.284 356.954L213.807 356.062C213.187 355.659 212.425 355.387 211.652 355.387C210.401 355.387 209.53 356.106 209.53 357.15C209.53 358.173 210.303 358.51 211.533 358.837C212.196 359.011 212.719 359.163 212.719 359.609C212.719 360.001 212.36 360.251 211.761 360.251C211.152 360.251 210.444 360.001 209.835 359.566L209.269 360.436C209.955 360.969 210.88 361.274 211.739 361.274ZM217.802 359.936C217.682 360.055 217.475 360.142 217.247 360.142C216.931 360.142 216.681 359.87 216.681 359.49V356.617H217.954V355.518H216.681V353.973H215.397V355.518H214.581V356.617H215.397V359.653C215.397 360.599 216.05 361.274 216.975 361.274C217.41 361.274 217.867 361.1 218.183 360.828L217.802 359.936ZM221.523 355.387C220.815 355.387 220.075 355.583 219.281 355.975L219.738 356.9C220.217 356.66 220.728 356.475 221.261 356.475C222.284 356.475 222.633 357.107 222.633 357.759V357.901C222.121 357.716 221.577 357.607 221.066 357.607C219.793 357.607 218.781 358.369 218.781 359.479C218.781 360.567 219.673 361.274 220.87 361.274C221.523 361.274 222.219 360.991 222.633 360.48V361.144H223.927V357.759C223.927 356.334 223.013 355.387 221.523 355.387ZM221.164 360.306C220.532 360.306 220.054 359.968 220.054 359.435C220.054 358.902 220.598 358.532 221.261 358.532C221.751 358.532 222.219 358.608 222.633 358.739V359.381C222.491 359.979 221.806 360.306 221.164 360.306ZM226.793 356.584V355.518H225.465V361.144H226.793V358.304C226.793 357.237 227.598 356.519 228.806 356.519V355.387C227.892 355.387 227.152 355.855 226.793 356.584ZM232.647 359.936C232.527 360.055 232.32 360.142 232.092 360.142C231.776 360.142 231.526 359.87 231.526 359.49V356.617H232.799V355.518H231.526V353.973H230.242V355.518H229.426V356.617H230.242V359.653C230.242 360.599 230.895 361.274 231.82 361.274C232.255 361.274 232.712 361.1 233.028 360.828L232.647 359.936ZM236.618 360.208C235.747 360.208 235.127 359.598 234.986 358.717H239.349C239.349 356.671 238.326 355.387 236.585 355.387C234.975 355.387 233.702 356.617 233.702 358.314C233.702 360.045 235.04 361.274 236.661 361.274C237.477 361.274 238.457 360.926 238.946 360.404L238.185 359.62C237.826 359.968 237.184 360.208 236.618 360.208ZM236.596 356.454C237.445 356.454 237.945 357.041 238.065 357.836H234.997C235.171 356.998 235.791 356.454 236.596 356.454ZM244.69 353.538V356.399C244.255 355.768 243.569 355.387 242.742 355.387C241.241 355.387 240.153 356.606 240.153 358.325C240.153 360.055 241.241 361.274 242.742 361.274C243.569 361.274 244.255 360.893 244.69 360.251V361.144H246.018V353.538H244.69ZM243.09 360.142C242.144 360.142 241.469 359.381 241.469 358.325C241.469 357.27 242.144 356.519 243.09 356.519C244.026 356.519 244.679 357.27 244.679 358.325C244.679 359.381 244.026 360.142 243.09 360.142Z" fill="white"/>
  <rect x="12.115" y="51.104" width="409.038" height="175.479" fill="#F7F7FC"/>
  <g filter="url(#filter4_d_2926_24380)">
    <rect x="38.3464" y="163.992" width="171.423" height="157.324" rx="21" fill="white"/>
  </g>
  <g filter="url(#filter5_d_2926_24380)">
    <rect x="224.212" y="163.992" width="171.423" height="157.324" rx="21" fill="white"/>
  </g>
  <line opacity="0.2" x1="12.115" y1="50.604" x2="421.866" y2="50.604" stroke="#A0A3BD"/>
  <circle cx="39.5139" cy="29.8101" r="5.48145" fill="#FF4267"/>
  <circle cx="72.2336" cy="29.8101" r="5.48145" fill="#2FF2B8"/>
  <circle cx="55.8738" cy="29.8101" r="5.48145" fill="#FFCD42"/>
  <defs>
    <filter id="filter0_d_2926_24380" x="434.866" y="0.810059" width="253.249" height="140.79" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="3"/>
      <feGaussianBlur stdDeviation="6"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.127552 0 0 0 0 0.123438 0 0 0 0 0.329167 0 0 0 0.04 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2926_24380"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2926_24380" result="shape"/>
    </filter>
    <filter id="filter1_d_2926_24380" x="434.866" y="141.011" width="253.249" height="140.79" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="3"/>
      <feGaussianBlur stdDeviation="6"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.127552 0 0 0 0 0.123438 0 0 0 0 0.329167 0 0 0 0.04 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2926_24380"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2926_24380" result="shape"/>
    </filter>
    <filter id="filter2_d_2926_24380" x="434.866" y="281.213" width="253.249" height="140.79" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="3"/>
      <feGaussianBlur stdDeviation="6"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.127552 0 0 0 0 0.123438 0 0 0 0 0.329167 0 0 0 0.04 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2926_24380"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2926_24380" result="shape"/>
    </filter>
    <filter id="filter3_d_2926_24380" x="0.11499" y="0.810059" width="433.751" height="421.192" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="3"/>
      <feGaussianBlur stdDeviation="6"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.127552 0 0 0 0 0.123438 0 0 0 0 0.329167 0 0 0 0.04 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2926_24380"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2926_24380" result="shape"/>
    </filter>
    <filter id="filter4_d_2926_24380" x="26.3464" y="154.992" width="195.423" height="181.325" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="3"/>
      <feGaussianBlur stdDeviation="6"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.127552 0 0 0 0 0.123438 0 0 0 0 0.329167 0 0 0 0.05 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2926_24380"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2926_24380" result="shape"/>
    </filter>
    <filter id="filter5_d_2926_24380" x="212.212" y="154.992" width="195.423" height="181.325" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="3"/>
      <feGaussianBlur stdDeviation="6"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.127552 0 0 0 0 0.123438 0 0 0 0 0.329167 0 0 0 0.05 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2926_24380"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2926_24380" result="shape"/>
    </filter>
  </defs>
  <text style="fill: rgb(33, 31, 84); font-family: 'Clarity City'; font-size: 20px; font-weight: 500; white-space: pre;" x="42" y="137.204">Browse questions by category</text>
  <text style="fill: rgb(33, 31, 84); font-family: 'Clarity City'; font-size: 23px; font-weight: 700; white-space: pre;" transform="matrix(1, 0, 0, 1, -117.847, 31.484341)"><tspan x="159.847" y="78.241">Help Center</tspan><tspan x="159.847" dy="1em">​</tspan></text>
  <g transform="matrix(1, 0, 0, 1, -24, -11)">
    <rect width="72" height="72" rx="18" fill="#7D42FB" x="111.571" y="204.201"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M 156.279 240.233 L 159.931 242.039 C 161.747 242.936 163.045 244.628 163.444 246.614 L 164.077 249.781 C 164.55 252.146 162.74 254.352 160.33 254.352 L 134.806 254.352 C 132.395 254.352 130.588 252.146 131.06 249.781 L 131.692 246.614 C 132.09 244.628 133.388 242.936 135.205 242.039 L 138.857 240.233 L 138.857 228.726 C 138.857 225.941 139.963 223.271 141.934 221.3 L 144.619 218.615 C 146.248 216.986 148.89 216.986 150.519 218.615 L 153.202 221.3 C 155.173 223.269 156.279 225.941 156.279 228.726 L 156.279 240.233 Z M 151.926 230.396 C 151.926 232.802 149.976 234.752 147.571 234.752 C 145.165 234.752 143.215 232.802 143.215 230.396 C 143.215 227.991 145.165 226.041 147.571 226.041 C 149.976 226.041 151.926 227.991 151.926 230.396 Z" fill="white"/>
    <path opacity="0.35" d="M 147.571 234.752 C 149.976 234.752 151.926 232.802 151.926 230.396 C 151.926 227.991 149.976 226.041 147.571 226.041 C 145.165 226.041 143.215 227.991 143.215 230.396 C 143.215 232.802 145.165 234.752 147.571 234.752 Z" fill="white"/>
    <path opacity="0.35" d="M 141.036 254.352 C 141.036 256.493 142.612 259.156 144.217 261.314 C 145.896 263.575 149.241 263.575 150.923 261.314 C 152.526 259.156 154.102 256.493 154.102 254.352 L 141.036 254.352 Z" fill="white"/>
  </g>
  <g transform="matrix(1, 0, 0, 1, -49.707123, 5.46123)">
    <rect width="72" height="72" rx="18" fill="#FF813A" x="323.813" y="187.138"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M 376.771 211.908 C 377.764 210.914 379.49 211.373 379.818 212.74 C 380.295 214.73 380.354 216.882 379.869 219.111 C 378.666 224.655 374.128 229.071 368.543 230.062 C 366.245 230.47 364.03 230.306 361.992 229.723 L 355.517 236.174 C 354.695 239.813 351.458 242.534 347.573 242.534 C 343.067 242.534 339.415 238.881 339.415 234.375 C 339.415 230.48 342.15 227.235 345.798 226.423 L 352.237 220.009 C 351.64 217.957 351.472 215.724 351.884 213.403 C 352.876 207.818 357.291 203.28 362.833 202.079 C 365.064 201.594 367.218 201.653 369.208 202.13 C 370.577 202.456 371.032 204.186 370.038 205.179 L 364.658 210.56 C 362.898 212.32 362.559 215.189 364.136 217.117 C 365.949 219.334 369.225 219.454 371.197 217.482 L 376.771 211.908 Z M 350.631 234.375 C 350.631 236.065 349.261 237.435 347.571 237.435 C 345.882 237.435 344.512 236.065 344.512 234.375 C 344.512 232.686 345.882 231.316 347.571 231.316 C 349.261 231.316 350.631 232.686 350.631 234.375 Z" fill="white"/>
    <path opacity="0.35" d="M 347.571 237.435 C 349.261 237.435 350.631 236.065 350.631 234.375 C 350.631 232.686 349.261 231.316 347.571 231.316 C 345.882 231.316 344.512 232.686 344.512 234.375 C 344.512 236.065 345.882 237.435 347.571 237.435 Z" fill="white"/>
  </g>
  <text style="fill: rgb(51, 51, 51); font-family: 'Clarity City'; font-size: 14px; font-weight: 500; white-space: pre;" x="71.696" y="291.718">Getting started</text>
  <text style="fill: rgb(51, 51, 51); font-family: 'Clarity City'; font-size: 14px; font-weight: 500; white-space: pre;" x="258.696" y="291.718" transform="matrix(1, 0, 0, 1, -3, 0)">Troubleshooting<tspan x="258.6960144042969" dy="1em">​</tspan></text>
</svg>