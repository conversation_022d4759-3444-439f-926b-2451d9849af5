.section1 {
    padding: 50px 0;
    background: white;
    border-top-left-radius: 0.42rem;
    border-top-right-radius: 0.42rem;
}
.section-offers {
    padding: 40px 20px;
    background: #eeebff;
}
.section3 {
    padding: 50px 0;
    background: white;
    border-top-left-radius: 0.42rem;
    border-top-right-radius: 0.42rem;
}

.pricing-table-top {
    min-height: 150px;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    padding: 32px 24px;
    position: -webkit-sticky;
    position: sticky;
    top: 0;
}
.pricing-content-wrapper, .pricing-table-top {
    border-bottom: 1px solid #dcddeb;
    display: flex;
}
.empty-state, .pricing-table-top {
    background-color: white;
    text-align: center;
}


.pricing-content-wrapper {
    height: 78px;
    min-height: 78px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-top: 16px;
    padding-bottom: 16px;
}
.pricing-content-wrapper.left {
    align-items: flex-start;
    padding-left: 24px;
}

.text-200.medium, .text-300.medium, .text-400.medium {
    font-weight: 500;
}
.text-200 {
    font-size: 18px;
    line-height: 1.111em;
    color: #6e7191;
}
.offices-tab-link.last, .pricing-content-wrapper.border-bottom-none, .pricing-content-wrapper.empty.border-bottom-none, .pricing-content-wrapper.left.border-bottom-none {
    border-bottom-style: none;
}

.pricing-column.featured, .pricing-table-top.featured {
    background-color: #eeebff;
}
