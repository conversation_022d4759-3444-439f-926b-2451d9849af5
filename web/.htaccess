# ##############################################################################
# # SECURITY                                                                   #
# ##############################################################################

# Prevent from listing directory files
Options -Indexes

# Cross-domain AJAX requests limited only to domain
<IfModule mod_headers.c>
   SetEnvIf Origin "http(s)?://(www\.)?(DOMAIN_NAME|xxxx.cloudfront.net)$" AccessControlAllowOrigin=$0
   Header add Access-Control-Allow-Origin %{AccessControlAllowOrigin}e env=AccessControlAllowOrigin
   Header set X-Content-Type-Options nosniff
   Header set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
</IfModule>

# Protect hidden files from being viewed
<Files .*>
    Order Deny,Allow
    Deny From All
    Satisfy All
</Files>

# Block access to backup and source files.
<FilesMatch "(^#.*#|\.(bak|config|dist|fla|inc|ini|log|psd|sh|sql|sw[op])|~)$">
    Order allow,deny
    Deny from all
    Satisfy All
</FilesMatch>

<FilesMatch "\.(ttf|otf|eot|svg|woff)$">
    FileETag None
</FilesMatch>

ErrorDocument 403 /404
ErrorDocument 404 /404


# ##############################################################################
# # URL REWRITES                                                               #
# ##############################################################################

<IfModule mod_rewrite.c>
    Options +FollowSymlinks

    RewriteEngine On
    RewriteBase /

    RewriteCond %{REQUEST_URI} ^.+$
    RewriteCond %{REQUEST_FILENAME} \.(gif|jpe?g|png|js|css|swf|ico|txt|pdf|xml)$ [OR]
    RewriteCond %{REQUEST_FILENAME} -f [OR]
    RewriteCond %{REQUEST_FILENAME} -d [OR]
    RewriteCond %{REQUEST_FILENAME} -l
    RewriteRule ^ - [L]

    #Final Declarations
	RewriteRule . index.php [L]

</IfModule>



# ##############################################################################
# # MIME TYPES AND ENCODING                                                    #
# ##############################################################################

<IfModule mod_mime.c>
    AddType audio/mp4                                   m4a f4a f4b
    AddType audio/ogg                                   oga ogg

    AddType application/javascript                      js
    AddType application/json                            json webmanifest

    AddType video/mp4                                   mp4 m4v f4v f4p
    AddType video/ogg                                   ogv
    AddType video/webm                                  webm
    AddType video/x-flv                                 flv

    AddType application/font-woff                       woff
    AddType application/vnd.ms-fontobject               eot

    AddType application/x-font-ttf                      ttc ttf
    AddType font/opentype                               otf

    AddType     image/svg+xml                           svg svgz
    AddEncoding gzip                                    svgz

    AddType application/x-httpd-php                     php

    AddType application/octet-stream                    safariextz
    AddType application/x-chrome-extension              crx
    AddType application/x-opera-extension               oex
    AddType application/x-shockwave-flash               swf
    AddType application/x-web-app-manifest+json         webapp
    AddType application/x-xpinstall                     xpi
    AddType application/xml                             atom rdf rss xml
    AddType image/webp                                  webp
    AddType image/x-icon                                ico
    AddType text/cache-manifest                         appcache manifest
    AddType text/vtt                                    vtt
    AddType text/x-component                            htc
    AddType text/x-vcard                                vcf
</IfModule>


# Use UTF-8 encoding for anything served as `text/html` or `text/plain`.
AddDefaultCharset utf-8

# Force UTF-8 for certain file formats.
<IfModule mod_mime.c>
    AddCharset utf-8 .atom .css .js .json .rss .vtt .webapp .xml
</IfModule>



# ##############################################################################
# # WEB PERFORMANCE                                                            #
# ##############################################################################

<IfModule mod_deflate.c>

    # Force compression for mangled headers.
    # http://developer.yahoo.com/blogs/ydn/posts/2010/12/pushing-beyond-gzipping
    <IfModule mod_setenvif.c>
        <IfModule mod_headers.c>
            SetEnvIfNoCase ^(Accept-EncodXng|X-cept-Encoding|X{15}|~{15}|-{15})$ ^((gzip|deflate)\s*,?\s*)+|[X~-]{4,13}$ HAVE_Accept-Encoding
            RequestHeader append Accept-Encoding "gzip,deflate" env=HAVE_Accept-Encoding
        </IfModule>
    </IfModule>

    # Compress all output labeled with one of the following MIME-types
    <IfModule mod_filter.c>
        AddOutputFilterByType DEFLATE application/atom+xml \
                                      application/javascript \
                                      application/json \
                                      application/rss+xml \
                                      application/vnd.ms-fontobject \
                                      application/x-font-ttf \
                                      application/x-web-app-manifest+json \
                                      application/xhtml+xml \
                                      application/xml \
                                      font/opentype \
                                      image/svg+xml \
                                      image/x-icon \
                                      text/css \
                                      text/html \
                                      text/plain \
                                      text/x-component \
                                      text/xml
    </IfModule>

</IfModule>

# ETag removal
# Since we are sending far-future expires headers (see below), ETags can be removed

<IfModule mod_headers.c>
    Header unset ETag
</IfModule>

FileETag None


# Expires headers (for better cache control)
<IfModule mod_expires.c>

    ExpiresActive On
    ExpiresDefault                                      "access plus 1 month"

    ExpiresByType text/css                              "access plus 1 year"

    ExpiresByType application/json                      "access plus 0 seconds"
    ExpiresByType application/xml                       "access plus 0 seconds"
    ExpiresByType text/xml                              "access plus 0 seconds"

    ExpiresByType image/x-icon                          "access plus 1 week"

    ExpiresByType text/x-component                      "access plus 1 month"

    ExpiresByType text/html                             "access plus 0 seconds"

    ExpiresByType application/javascript                "access plus 1 year"

    ExpiresByType application/x-web-app-manifest+json   "access plus 0 seconds"
    ExpiresByType text/cache-manifest                   "access plus 0 seconds"

    ExpiresByType audio/ogg                             "access plus 1 month"
    ExpiresByType image/gif                             "access plus 1 month"
    ExpiresByType image/jpeg                            "access plus 1 month"
    ExpiresByType image/png                             "access plus 1 month"
    ExpiresByType video/mp4                             "access plus 1 month"
    ExpiresByType video/ogg                             "access plus 1 month"
    ExpiresByType video/webm                            "access plus 1 month"

    ExpiresByType application/atom+xml                  "access plus 1 hour"
    ExpiresByType application/rss+xml                   "access plus 1 hour"

    ExpiresByType application/font-woff                 "access plus 1 month"
    ExpiresByType application/vnd.ms-fontobject         "access plus 1 month"
    ExpiresByType application/x-font-ttf                "access plus 1 month"
    ExpiresByType font/opentype                         "access plus 1 month"
    ExpiresByType image/svg+xml                         "access plus 1 month"

</IfModule>


# To force sending Content-Length
# This prevents IE to show Nan for mp3 length
<FilesMatch "\.mp3$">
  SetEnv no-gzip 1
</FilesMatch>


#Bloquer l'indexation des fichiers Word et PDF
<IfModule mod_headers.c>
  <Files ~ "\.(doc|docx|pdf)$">
    Header Set X-Robots-Tag "noindex, nofollow"
  </Files>
</IfModule>
