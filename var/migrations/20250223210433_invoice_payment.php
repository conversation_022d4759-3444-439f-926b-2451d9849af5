<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class InvoicePayment extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $table = $this->table('shop_invoices_payments');
        $table->addColumn('client_id', 'integer')
            ->addColumn('invoice_id', 'integer')
            ->addColumn('amount', 'float')
            ->addColumn('payment_mode', 'string', ['limit' => 20])
            ->addColumn('date', 'datetime')
            ->addForeignKey('client_id', DB_PREFIX . 'clients', 'id', ['delete' => 'CASCADE', 'update' => 'NO_ACTION'])
            ->addForeignKey('invoice_id', 'shop_invoices', 'id', ['delete' => 'CASCADE', 'update' => 'NO_ACTION'])
            ->create();
    }
}
