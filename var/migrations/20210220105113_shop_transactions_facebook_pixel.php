<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class ShopTransactionsFacebookPixel extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->table('shop_transactions')
            ->addColumn('facebook_pixel_id','integer', ['after' => 'date_valid', 'null' => true])
            ->addForeignKey('facebook_pixel_id', DB_PREFIX . 'integrations_accounts', 'id', ['delete' => 'SET NULL', 'update' => 'CASCADE'])
            ->update();
    }
}
