<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class DossierVehicle extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $this->execute('CREATE TABLE px_dossiers_vehicles (id INT AUTO_INCREMENT NOT NULL, client_id INT NOT NULL, dossier_id INT NOT NULL, registration VARCHAR(50) NOT NULL, serial_number VARCHAR(50) NOT NULL, serial_number_valid TINYINT(1) NOT NULL, formule_number VARCHAR(255) NOT NULL, insurance_card VARCHAR(255) NOT NULL, brand VARCHAR(50) NOT NULL, model VARCHAR(50) NOT NULL, engine VARCHAR(50) NOT NULL, dismantled VARCHAR(10) NOT NULL, reassembled VARCHAR(10) DEFAULT NULL, draining VARCHAR(10) DEFAULT NULL, car_storage VARCHAR(10) DEFAULT NULL, car_storage_fees DOUBLE PRECISION NOT NULL, buy_date DATE DEFAULT NULL, buy_price DOUBLE PRECISION NOT NULL, buy_payment VARCHAR(50) NOT NULL, buy_mileage INT NOT NULL, registration_date DATE DEFAULT NULL, color VARCHAR(20) NOT NULL, immobilization_place_id INT DEFAULT NULL, immobilization_place_name VARCHAR(255) NOT NULL, immobilization_address VARCHAR(255) NOT NULL, immobilization_zip VARCHAR(50) NOT NULL, immobilization_city VARCHAR(255) NOT NULL, observations LONGTEXT NOT NULL, date_first_circulation date DEFAULT NULL, INDEX IDX_B0E06E1219EB6921 (client_id), UNIQUE INDEX UNIQ_B0E06E12611C0C56 (dossier_id), INDEX IDX_B0E06E1219EB6920 (immobilization_place_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->execute('ALTER TABLE px_dossiers_vehicles ADD CONSTRAINT FK_B0E06E1219EB6921 FOREIGN KEY (client_id) REFERENCES mg_clients (id) ON DELETE CASCADE');
        $this->execute('ALTER TABLE px_dossiers_vehicles ADD CONSTRAINT FK_B0E06E12611C0C56 FOREIGN KEY (dossier_id) REFERENCES px_dossiers (id) ON DELETE CASCADE');
        $this->execute('ALTER TABLE px_dossiers_vehicles ADD CONSTRAINT FK_B0E06E1219EB6920 FOREIGN KEY (immobilization_place_id) REFERENCES px_address (id) ON DELETE SET NULL');
    }

    public function down(): void
    {
        $this->execute('DROP TABLE px_dossiers_vehicles');
    }
}
