<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class DossierInstitution extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $this->execute('CREATE TABLE px_dossiers_institutions (id INT AUTO_INCREMENT NOT NULL, client_id INT NOT NULL, dossier_id INT NOT NULL, type VARCHAR(50) NOT NULL, name VARCHAR(255) NOT NULL, address VARCHAR(255) NOT NULL, zip VARCHAR(50) NOT NULL, city VARCHAR(255) NOT NULL, email VARCHAR(255) NOT NULL, telephone VARCHAR(50) NOT NULL, reference VARCHAR(255) NOT NULL, date DATETIME NOT NULL, INDEX IDX_90BE8E6319EB6921 (client_id), INDEX IDX_90BE8E63611C0C56 (dossier_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->execute('ALTER TABLE px_dossiers_institutions ADD CONSTRAINT FK_90BE8E6319EB6921 FOREIGN KEY (client_id) REFERENCES mg_clients (id) ON DELETE CASCADE');
        $this->execute('ALTER TABLE px_dossiers_institutions ADD CONSTRAINT FK_90BE8E63611C0C56 FOREIGN KEY (dossier_id) REFERENCES px_dossiers (id) ON DELETE CASCADE');

    }

    public function down(): void
    {
        $this->execute('DROP TABLE px_dossiers_institutions');

    }
}