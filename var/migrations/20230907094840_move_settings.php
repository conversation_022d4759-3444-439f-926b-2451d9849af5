<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class MoveSettings extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $table = $this->table('px_vehicle_types');
        $table->dropForeignKey('client_id')
            ->removeColumn('client_id')
            ->update();

        $table = $this->table('px_interventions_subjects');
        $table->dropForeignKey('client_id')
            ->removeColumn('client_id')
            ->update();

        $table = $this->table('px_mission_types');
        $table->dropForeignKey('client_id')
            ->removeColumn('client_id')
            ->update();

        $table = $this->table('px_expertises');
        $table->dropForeignKey('client_id')
            ->removeColumn('client_id')
            ->update();
    }

    public function down(): void
    {
        $table = $this->table('px_vehicle_types');
        $table->addColumn('client_id', 'integer', ['after' => 'id', 'default' => 2])
            ->addForeignKey('client_id', DB_PREFIX . 'clients', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->update();

        $table = $this->table('px_interventions_subjects');
        $table->addColumn('client_id', 'integer', ['after' => 'id', 'default' => 2])
            ->addForeignKey('client_id', DB_PREFIX . 'clients', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->update();

        $table = $this->table('px_mission_types');
        $table->addColumn('client_id', 'integer', ['after' => 'id', 'default' => 2])
            ->addForeignKey('client_id', DB_PREFIX . 'clients', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->update();

        $table = $this->table('px_expertises');
        $table->addColumn('client_id', 'integer', ['after' => 'id', 'default' => 2])
            ->addForeignKey('client_id', DB_PREFIX . 'clients', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->update();
    }
}
