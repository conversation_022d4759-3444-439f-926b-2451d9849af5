<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class AddNotificationLogin extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $rows = [];
        $stmt = $this->query("SELECT * FROM mg_users");
        $users = $stmt->fetchAll();
        foreach ($users as $user) {
            $rows[] = [
                'client_id' => $user['client_id'],
                'user_id' => $user['id'],
                'type' => 90,
                'date' => date('Y-m-d H:i:s'),
            ];
        }
        $this->table('mg_users_emails_notifications')->insert($rows)->save();
    }
}
