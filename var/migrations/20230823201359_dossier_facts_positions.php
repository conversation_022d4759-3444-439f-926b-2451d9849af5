<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class DossierFactsPositions extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $table = $this->table('px_dossiers_facts');
        $table->addColumn('position', 'integer', ['after' => 'claims'])
            ->update();

        $stmt = $this->query("SELECT * FROM px_dossiers");
        $dossiers = $stmt->fetchAll();
        foreach ($dossiers as $dossier) {
            $stmt = $this->query("SELECT * FROM px_dossiers_facts WHERE dossier_id = " . $dossier['id'] . " ORDER BY date ASC, id ASC;");
            $facts = $stmt->fetchAll();
            $this->sortFacts($facts);
            if ($facts) {
                foreach ($facts as $id => $fact) {
                    $this->execute("UPDATE px_dossiers_facts SET position = " . $id . " WHERE id = " . $fact['id'] . ";");
                }
            }
        }
    }

    public function down(): void
    {
        $table = $this->table('px_dossiers_facts');
        $table->removeColumn('position')
            ->update();
    }

    public function sortFacts(array &$facts)
    {
        usort($facts, function (array $fact1, array $fact2) {
            if ($fact1['date'] and $fact2['date']) {
                $time1 = strtotime($fact1['date']);
                $time2 = strtotime($fact2['date']);
                return $time1 - $time2;
            }
            if ($fact1['mileage'] and $fact2['mileage']) {
                return $fact1['mileage'] - $fact2['mileage'];
            }
            if ($fact1['date']) {
                return -1;
            }
            return 1;
        });
    }
}
