<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class DossierMainCompany extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $table = $this->table('px_dossiers');
        $table->addColumn('company_id', 'integer', ['null' => true, 'after' => 'client_id'])
            ->addForeignKey('company_id', DB_PREFIX . 'companies', 'id', ['delete' => 'SET NULL', 'update' => 'CASCADE'])
            ->update();

        $stmt = $this->query("SELECT * FROM " . DB_PREFIX . "companies WHERE client_id = 2");
        $company = $stmt->fetch();
        if ($company) {
            $this->execute("UPDATE px_dossiers SET company_id = " . $company['id'] . " WHERE client_id = 2");
        }

        $table = $this->table('px_dossiers_institutions');
        $table->addColumn('locked', 'boolean', ['after' => 'reference'])
            ->update();
        $table->dropForeignKey('user_id')
            ->update();
        $table->removeColumn('user_id')
            ->update();

        $this->execute("UPDATE px_dossiers_institutions SET type = 'contact', locked = true WHERE type = 'client';");
        $this->execute("UPDATE px_dossiers_institutions SET type = 'expertise_place', locked = true WHERE type = 'expert';");
        $this->execute("UPDATE px_dossiers_institutions SET type = 'expert', locked = true WHERE type = 'user';");
    }

    public function down(): void
    {
        $this->execute("UPDATE px_dossiers_institutions SET type = 'client' WHERE type = 'contact';");
        $this->execute("UPDATE px_dossiers_institutions SET type = 'expert' WHERE type = 'expertise_place';");
        $this->execute("UPDATE px_dossiers_institutions SET type = 'user' WHERE type = 'expert';");

        $table = $this->table('px_dossiers');
        $table->dropForeignKey('company_id')
            ->update();
        $table->removeColumn('company_id')
            ->update();

        $table = $this->table('px_dossiers_institutions');
        $table->removeColumn('locked')
            ->update();
        $table->addColumn('user_id', 'integer', ['null' => true, 'after' => 'type'])
            ->addForeignKey('user_id', DB_PREFIX . 'users', 'id', ['delete' => 'SET NULL', 'update' => 'CASCADE'])
            ->update();
    }
}
