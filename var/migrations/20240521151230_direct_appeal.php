<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class DirectAppeal extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $table = $this->table('px_dossiers_direct_appeals', ['signed' => true]);
        $table->addColumn('client_id', 'integer')
            ->addColumn('dossier_id', 'integer')
            ->addColumn('expertise_id', 'integer')
            ->addColumn('title', 'string')
            ->addColumn('damage_events', 'string')
            ->addColumn('technically_repairable', 'boolean')
            ->addColumn('ve_procedure', 'boolean')
            ->addColumn('costing_rates', 'text')
            ->addColumn('observations', 'text')
            ->addColumn('document_id', 'integer', ['null' => true])
            ->addColumn('file', 'text')
            ->addColumn('date', 'datetime')
            ->addForeignKey('client_id', DB_PREFIX . 'clients', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKey('dossier_id', 'px_dossiers', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKey('expertise_id', 'px_dossiers_expertises', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKey('document_id', 'px_dossiers_documents', 'id', ['delete' => 'SET NULL', 'update' => 'CASCADE'])
            ->create();
    }
}
