<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class GameQuestClient extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $this->execute('CREATE TABLE game_quests_clients (id INT AUTO_INCREMENT NOT NULL, client_id INT NOT NULL, quest_id INT NOT NULL, step_id INT NOT NULL, status VARCHAR(20) NOT NULL, rewards_claimed TINYINT(1) NOT NULL, date DATETIME NOT NULL, date_claimed DATETIME DEFAULT NULL, INDEX IDX_2BD55D5719EB6921 (client_id), INDEX IDX_2BD55D57209E9EF4 (quest_id), INDEX IDX_2BD55D5773B21E9C (step_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->execute('ALTER TABLE game_quests_clients ADD CONSTRAINT FK_2BD55D5719EB6921 FOREIGN KEY (client_id) REFERENCES mg_clients (id) ON DELETE CASCADE ON UPDATE CASCADE');
        $this->execute('ALTER TABLE game_quests_clients ADD CONSTRAINT FK_2BD55D57209E9EF4 FOREIGN KEY (quest_id) REFERENCES game_quests (id) ON DELETE CASCADE ON UPDATE CASCADE');
        $this->execute('ALTER TABLE game_quests_clients ADD CONSTRAINT FK_2BD55D5773B21E9C FOREIGN KEY (step_id) REFERENCES game_quests_steps (id) ON DELETE CASCADE ON UPDATE CASCADE');
    }

    public function down(): void
    {
        $this->execute('DROP TABLE game_quests_clients');
    }
}