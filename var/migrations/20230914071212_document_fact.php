<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class DocumentFact extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $table = $this->table('px_dossiers_facts');
        $table->addColumn('document_id', 'integer', ['null' => true, 'after' => 'expertise_id'])
            ->addForeignKey('document_id', 'px_dossiers_documents', 'id', ['delete' => 'SET NULL', 'update' => 'CASCADE'])
            ->update();

        $table = $this->table('px_dossiers_documents');
        $table->addColumn('mileage', 'integer', ['after' => 'first_part'])
            ->addColumn('mileage_type', 'string', ['after' => 'mileage', 'limit' => 10])
            ->update();
    }
}
