<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class AddConsignment extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $stmt = $this->query("SELECT * FROM px_dossiers WHERE judiciaire = 1");
        $dossiers = $stmt->fetchAll();
        $rows = [];
        foreach ($dossiers as $dossier) {
            $price = $dossier['price'];
            if (!$price) {
                continue;
            }

            $rows[] = [
                'client_id' => $dossier['client_id'],
                'dossier_id' => $dossier['id'],
                'amount' => $price,
                'date' => $dossier['date'],
            ];
        }
        $this->table('px_dossiers_consignments')->insert($rows)->save();

        $stmt = $this->query("SELECT * FROM px_dossiers_documents WHERE type = 'consignment' OR type = 'consignment_initial' OR type = 'consignment_additional';");
        $documents = $stmt->fetchAll();
        $rows = [];
        foreach ($documents as $document) {
            $rows[] = [
                'client_id' => $document['client_id'],
                'dossier_id' => $document['dossier_id'],
                'document_id' => $document['id'],
                'amount' => 0,
                'date' => $document['date'],
            ];
        }
        $this->table('px_dossiers_consignments')->insert($rows)->save();

        $stmt = $this->query("SELECT * FROM px_dossiers_expertises_judiciaires WHERE consignment_date IS NOT NULL;");
        $expertisesJudiciaires = $stmt->fetchAll();
        $rows = [];
        foreach ($expertisesJudiciaires as $expertiseJudiciaire) {
            $stmt = $this->query("SELECT * FROM px_dossiers_expertises WHERE id = " . $expertiseJudiciaire['expertise_id']);
            $expertise = $stmt->fetch();

            $rows[] = [
                'client_id' => $expertiseJudiciaire['client_id'],
                'dossier_id' => $expertise['dossier_id'],
                'status' => $expertiseJudiciaire['consignment'],
                'amount' => 0,
                'date' => $expertiseJudiciaire['consignment_date'],
            ];
        }
        $this->table('px_dossiers_consignments')->insert($rows)->save();

        $table = $this->table('px_dossiers_expertises_judiciaires');
        $table->removeColumn('consignment')
            ->removeColumn('consignment_date')
            ->update();
    }

    public function down(): void
    {
        $table = $this->table('px_dossiers_expertises_judiciaires');
        $table->addColumn('consignment', 'string', ['limit' => 50, 'after' => 'portalis_number'])
            ->addColumn('consignment_date', 'date', ['null' => true, 'after' => 'consignment'])
            ->update();
    }
}
