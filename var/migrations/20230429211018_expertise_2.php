<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class Expertise2 extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $table = $this->table('px_dossiers_expertises');
        $table->addColumn('type', 'string', ['limit' => 50, 'after' => 'expert_id'])
            ->addColumn('mission_type', 'string', ['limit' => 50, 'after' => 'type'])
            ->addColumn('mission_date', 'date', ['null' => true, 'after' => 'mission_type'])
            ->addColumn('incident_number', 'string', ['limit' => 50, 'after' => 'sms'])
            ->addColumn('incident_date', 'date', ['null' => true, 'after' => 'incident_number'])
            ->addColumn('expert_reference', 'string', ['after' => 'incident_date'])
            ->addColumn('case_name', 'string', ['after' => 'expert_reference'])
            ->addColumn('evaluation', 'text', ['after' => 'case_name'])
            ->addColumn('notes', 'text', ['after' => 'evaluation'])
            ->addColumn('followup', 'text', ['after' => 'notes'])
            ->addColumn('positions', 'text', ['after' => 'followup'])
            ->addColumn('status', 'string', ['limit' => 20, 'after' => 'positions'])
            ->update();
    }
}
