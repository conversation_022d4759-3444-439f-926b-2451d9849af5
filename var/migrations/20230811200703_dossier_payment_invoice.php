<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class DossierPaymentInvoice extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->table('px_dossiers_payments')
            ->addColumn('invoice_id', 'integer', ['after' => 'payment_link_id', 'null' => true])
            ->addForeignKey('invoice_id', 'shop_invoices', 'id', ['delete' => 'SET NULL', 'update' => 'CASCADE'])
            ->update();
    }
}
