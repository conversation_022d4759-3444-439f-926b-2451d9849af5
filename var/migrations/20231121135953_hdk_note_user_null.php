<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class HdkNoteUserNull extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $table = $this->table('hdk_notes');
        $table->changeColumn('user_id', 'integer', ['null' => true])
            ->addForeignKey('user_id', DB_PREFIX . 'users', 'id', ['delete' => 'SET NULL', 'update' => 'CASCADE'])
            ->update();
    }

    public function down(): void
    {
        $table = $this->table('hdk_notes');
        $table->dropForeignKey('user_id')
            ->update();
        $table->changeColumn('user_id', 'integer', ['null' => false])
            ->update();
    }
}
