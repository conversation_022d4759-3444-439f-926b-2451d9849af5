<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class DossierConvocationsAlertMailTemplate extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->execute(file_get_contents(__DIR__ . '/sql/mail_template_dossier_convocations_alert.sql'));
    }
}
