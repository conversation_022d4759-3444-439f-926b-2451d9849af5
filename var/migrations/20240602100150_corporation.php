<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class Corporation extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $this->execute('CREATE TABLE mg_corporations (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, address VARCHAR(255) NOT NULL, address2 VARCHAR(255) NOT NULL, zip VARCHAR(50) NOT NULL, city VARCHAR(255) NOT NULL, phone VARCHAR(50) NOT NULL, website VARCHAR(255) NOT NULL, date DATETIME NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');

    }

    public function down(): void
    {
        $this->execute('DROP TABLE mg_corporations');

    }
}
