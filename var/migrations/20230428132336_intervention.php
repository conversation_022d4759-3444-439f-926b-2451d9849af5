<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class Intervention extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $this->execute('CREATE TABLE px_interventions (id INT AUTO_INCREMENT NOT NULL, client_id INT NOT NULL, name VARCHAR(255) NOT NULL, INDEX IDX_DE387AF119EB6921 (client_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->execute('ALTER TABLE px_interventions ADD CONSTRAINT FK_DE387AF119EB6921 FOREIGN KEY (client_id) REFERENCES mg_clients (id) ON DELETE CASCADE');
    }

    public function down(): void
    {
        $this->execute('DROP TABLE px_interventions');
    }
}