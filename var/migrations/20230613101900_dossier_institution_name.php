<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class DossierInstitutionName extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $table = $this->table('px_dossiers_institutions');
        $table->addColumn('user_id', 'integer', ['null' => true, 'after' => 'type'])
            ->addColumn('place_id', 'integer', ['null' => true, 'after' => 'user_id'])
            ->addColumn('first_name', 'string', ['after' => 'name'])
            ->addColumn('last_name', 'string', ['after' => 'first_name'])
            ->addColumn('address2', 'string', ['after' => 'address'])
            ->addColumn('company', 'string', ['after' => 'telephone'])
            ->addForeignKey('user_id', DB_PREFIX . 'users', 'id', ['delete' => 'SET NULL', 'update' => 'CASCADE'])
            ->addForeignKey('place_id', 'px_address', 'id', ['delete' => 'SET NULL', 'update' => 'CASCADE'])
            ->update();

        $table->removeColumn('name')
            ->update();
    }

    public function down(): void
    {
        $table = $this->table('px_dossiers_institutions');
        $table->addColumn('name', 'string', ['after' => 'type'])
            ->update();
        $table->dropForeignKey('place_id')
            ->dropForeignKey('address_id')
            ->update();

        $table->removeColumn('place_id')
            ->removeColumn('address_id')
            ->removeColumn('first_name')
            ->removeColumn('last_name')
            ->removeColumn('address2')
            ->removeColumn('company')
            ->update();
    }
}
