<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class DossierFactSample extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $table = $this->table('px_dossiers_facts');
        $table->addColumn('sample_id', 'integer', ['after' => 'document_id', 'null' => true])
            ->addForeignKey('sample_id', 'px_dossiers_expertises_samples', 'id', ['delete' => 'SET NULL', 'update' => 'CASCADE'])
            ->update();
    }
}
