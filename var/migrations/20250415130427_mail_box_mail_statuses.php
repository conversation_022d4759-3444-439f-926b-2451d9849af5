<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class MailBoxMailStatuses extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $stmt = $this->query("SELECT * FROM mailbox_mails WHERE status='open'");
        $rows = $stmt->fetchAll();
        foreach ($rows as $row) {
            $lastReply = $this->fetchRow("SELECT * FROM mailbox_replies WHERE mail_id=" . $row['id'] . " ORDER BY id DESC LIMIT 1");
            if ($lastReply && $lastReply['user_id'] == $row['sender_id']) {
                $this->execute("UPDATE mailbox_mails SET status='waiting_answer' WHERE id=" . $row['id']);
            } else {
                $this->execute("UPDATE mailbox_mails SET status='waiting_to_be_processed' WHERE id=" . $row['id']);
            }
        }
    }
}
