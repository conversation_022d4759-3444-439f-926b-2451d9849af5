<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class DossierAddExpert extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $stmt = $this->query("SELECT * FROM mg_clients");
        $clients = $stmt->fetchAll();
        foreach ($clients as $client) {
            $stmt = $this->query("SELECT * FROM mg_users WHERE client_id = " . $client['id'] . ";");
            $user = $stmt->fetch();
            if ($user) {
                $this->execute("UPDATE px_dossiers SET user_id = " . $user['id'] . " WHERE client_id = " . $client['id'] . ";");
            }
        }
    }

    public function down(): void {}
}
