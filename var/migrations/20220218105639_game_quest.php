<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class GameQuest extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $this->execute('CREATE TABLE game_quests (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, image VARCHAR(255) NOT NULL, description LONGTEXT NOT NULL, end_content LONGTEXT NOT NULL, identifier VARCHAR(50) NOT NULL, start_route VARCHAR(255) NOT NULL, conditions LONGTEXT NOT NULL, rewards LONGTEXT NOT NULL, nb_points int(11) NOT NULL, active tinyint(4) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
    }

    public function down(): void
    {
        $this->execute('DROP TABLE game_quests');
    }
}