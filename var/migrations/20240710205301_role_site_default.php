<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class RoleSiteDefault extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $stmt = $this->query('SELECT * FROM `mg_users_roles` WHERE `client_id` = 1 AND `role_id` = 1');
        $userRole = $stmt->fetch();
        if ($userRole) {
            $stmt = $this->query('SELECT * FROM `mg_users_roles_universes` WHERE user_role_id = ' . $userRole['user_role_id'] . ' AND universe_id = 11;');
            $userRoleUniverse = $stmt->fetch();
            if (!$userRoleUniverse) {
                $row = [
                    'user_role_id' => $userRole['user_role_id'],
                    'universe_id' => 11,
                    'attributes' => '',
                    'objects' => '',
                ];
                $this->table('mg_users_roles_universes')->insert($row)->save();
            }
        }
    }
}
