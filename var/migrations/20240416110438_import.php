<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class Import extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $table = $this->table(DB_PREFIX . 'imports', ['signed' => true]);
        $table->addColumn('client_id', 'integer')
            ->addColumn('type', 'string', ['limit' => 50])
            ->addColumn('file', 'string')
            ->addColumn('status', 'string', ['limit' => 50])
            ->addColumn('date', 'datetime')
            ->addColumn('date_processed', 'datetime', ['null' => true])
            ->addForeignKey('client_id', DB_PREFIX . 'clients', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->create();

        $table = $this->table(DB_PREFIX . 'imports_data');
        $table->addColumn('client_id', 'integer')
            ->addColumn('import_id', 'integer')
            ->addColumn('data', 'text')
            ->addColumn('status', 'string', ['limit' => 50])
            ->addColumn('result', 'text')
            ->addColumn('date', 'datetime')
            ->addColumn('date_processed', 'datetime', ['null' => true])
            ->addForeignKey('client_id', DB_PREFIX . 'clients', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKey('import_id', DB_PREFIX . 'imports', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->create();
    }
}
