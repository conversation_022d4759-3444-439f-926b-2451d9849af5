<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class ExpertiseMissionType extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $table = $this->table('px_dossiers_expertises');
        $table->addColumn('mission_type_id', 'integer', ['after' => 'mission_type', 'null' => true])
            ->addForeignKey('mission_type_id', 'px_mission_types', 'id', ['delete' => 'SET NULL', 'update' => 'CASCADE'])
            ->update();

        $table->removeColumn('mission_type')
            ->update();
    }

    public function down(): void
    {
        $table = $this->table('px_dossiers_expertises');

        $table->addColumn('mission_type', 'integer', ['after' => 'mission_type_id', 'null' => true])
            ->update();

        $table->dropForeignKey('mission_type_id')
            ->update();
        $table->removeColumn('mission_type_id')
            ->update();
    }
}
