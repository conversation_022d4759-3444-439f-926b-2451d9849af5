<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class DossierClient extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $table = $this->table('px_dossiers');
        $table->dropForeignKey('company_id')
            ->removeColumn('company_id')
            ->update();

        $table = $this->table('px_dossiers_expertises_clients');
        $table->rename('px_dossiers_clients')
            ->update();

        $table = $this->table('px_dossiers_clients');
        $table->dropForeignKey('expertise_id')
            ->update();
        $table->removeColumn('expertise_id')
            ->update();

        $table->addColumn('dossier_id', 'integer', ['null' => true, 'after' => 'client_id'])
            ->addColumn('place_id', 'integer', ['null' => true, 'after' => 'dossier_id'])
            ->renameColumn('name', 'company')
            ->addColumn('first_name', 'string', ['limit' => 255, 'after' => 'company'])
            ->addColumn('last_name', 'string', ['limit' => 255, 'after' => 'first_name'])
            ->removeColumn('manager')
            ->addForeignKey('dossier_id', 'px_dossiers', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKey('place_id', 'px_address', 'id', ['delete' => 'SET NULL', 'update' => 'CASCADE'])
            ->update();
    }

    public function down(): void
    {
        $table = $this->table('px_dossiers');
        $table->addColumn('company_id', 'integer', ['null' => true, 'after' => 'department_id'])
            ->addForeignKey('company_id', 'px_address', 'id', ['delete' => 'SET NULL', 'update' => 'CASCADE'])
            ->update();

        $table = $this->table('px_dossiers_clients');
        $table->rename('px_dossiers_expertises_clients')
            ->update();

        $table = $this->table('px_dossiers_expertises_clients');
        $table->dropForeignKey('dossier_id')
            ->dropForeignKey('place_id')
            ->update();
        $table->removeColumn('dossier_id')
            ->removeColumn('place_id')
            ->removeColumn('first_name')
            ->removeColumn('last_name')
            ->update();

        $table->addColumn('expertise_id', 'integer', ['null' => true, 'after' => 'client_id'])
            ->renameColumn('company', 'name')
            ->addColumn('manager', 'string')
            ->addForeignKey('expertise_id', 'px_dossiers_expertises', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->update();
    }
}
