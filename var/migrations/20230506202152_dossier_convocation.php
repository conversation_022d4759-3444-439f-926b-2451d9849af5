<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class DossierConvocation extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $this->execute('CREATE TABLE px_dossiers_convocations (id INT AUTO_INCREMENT NOT NULL, client_id INT NOT NULL, dossier_id INT NOT NULL, person_id INT NOT NULL, content TEXT NOT NULL, document_id INT DEFAULT NULL, date DATETIME NOT NULL, INDEX IDX_BBF0705619EB6921 (client_id), INDEX IDX_BBF07056611C0C56 (dossier_id), INDEX IDX_BBF07056611C0C57 (person_id), INDEX IDX_BBF07056611C0C58 (document_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->execute('ALTER TABLE px_dossiers_convocations ADD CONSTRAINT FK_BBF0705619EB6921 FOREIGN KEY (client_id) REFERENCES mg_clients (id) ON DELETE CASCADE');
        $this->execute('ALTER TABLE px_dossiers_convocations ADD CONSTRAINT FK_BBF07056611C0C56 FOREIGN KEY (dossier_id) REFERENCES px_dossiers (id) ON DELETE CASCADE');
        $this->execute('ALTER TABLE px_dossiers_convocations ADD CONSTRAINT FK_BBF07056611C0C57 FOREIGN KEY (person_id) REFERENCES px_dossiers_expertises_persons (id) ON DELETE CASCADE');
        $this->execute('ALTER TABLE px_dossiers_convocations ADD CONSTRAINT FK_BBF07056611C0C58 FOREIGN KEY (document_id) REFERENCES px_dossiers_documents (id) ON DELETE SET NULL');
    }

    public function down(): void
    {
        $this->execute('ALTER TABLE px_dossiers_convocations DROP FOREIGN KEY FK_BBF07056611C0C56');
        $this->execute('ALTER TABLE px_dossiers_convocations DROP FOREIGN KEY FK_BBF07056611C0C57');
        $this->execute('ALTER TABLE px_dossiers_convocations DROP FOREIGN KEY FK_BBF07056611C0C58');
        $this->execute('DROP TABLE px_dossiers_convocations');
    }
}
