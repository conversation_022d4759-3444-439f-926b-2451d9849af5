<?php
/**
* Environment : dev, test, or prod
*/
define("ENV", 'preprod');
define("MASTER_API_KEY", "PREPROD_MASTER_API_KEY");
define("SECRET_KEY", "PREPROD_SECRET_KEY");
define('CLIENT_MASTER', 1);
define("FREE_CLIENTS", [CLIENT_MASTER]);

/*
 *  MAIN DATABASE
 */
define("DB_NAME", "matgyver");
define("DB_HOST", "PREPROD_DB_HOST");
define("DB_LOGIN", "PREPROD_DB_LOGIN");
define("DB_PASS", "PREPROD_DB_PASS");
define("DB_PORT", 3306);
define("DB_PREFIX", "mg_");

/**
*   PATHS
*/
define("SESSIONS_PATH", 'seed[]=v1-preprod-session.xxxxxx.clustercfg.euw1.cache.amazonaws.com:6379');
define("WKHTMLTOIMAGE_PATH", '/usr/local/bin/wkhtmltoimage');
define("WKHTMLTOPDF_PATH", '/usr/local/bin/wkhtmltopdf');

/*
 *  DOMAINE
 */
define('APP_NAME', 'MatGyver');
define('APP_DOMAIN', 'matgyver-preprod.com');
define('APP_URL', 'https://matgyver-preprod.com');
define('APP_EMAIL', '<EMAIL>');

/**
 * ASSETS / CDN
 */
define('MEDIAS_URL', 'https://xxxxxx.cloudfront.net/versioned/');
define('CDN_URL', "https://xxxxxx.cloudfront.net/");

/**
 * APPS
 */
define('AWEBER_KEY', 'xxxxx');
define('AWEBER_SECRET', 'xxxxx');

define('CKFINDER_LICENSE_NAME', '');
define('CKFINDER_LICENSE_KEY', '');

define('GOOGLE_RECAPTCHA_SECRET_KEY', 'PREPROD_GOOGLE_RECAPTCHA_SECRET_KEY');
define('GOOGLE_RECAPTCHA_PUBLIC_KEY', 'PREPROD_GOOGLE_RECAPTCHA_PUBLIC_KEY');

/**
 * Sessions
 */
define('SESSION_TIMEOUT', 10800);

/**
 * Logs
 */
define('LOGGER_NAME', 'matgyver');
define('TRANSACTION_LOGGER_NAME', 'v1-preprod-logs');
define('SQS_LOGGER_NAME', 'matgyver-sqs');
define('ENABLE_LOGS_MYSQL', true);
define('ENABLE_LOGS_MAILS', false);

define('ENABLE_LOGS_AWSKINESIS', true);
define('AWS_KINESIS_REGION', 'eu-west-1');
define('AWS_KINESIS_VERSION', 'latest');

define('ENABLE_LOGS_SLACK', false);
define('SLACK_TOKEN', 'PREPROD_SLACK_TOKEN');
define('SLACK_CHANNEL', 'PREPROD_SLACK_CHANNEL');

define('ENABLE_LOGS_FILES', false);

/**
 * S3
 */
define('S3_BUCKET', 'matgyver-v1-eu-west-1-preprod-versioned');
define('S3_REGION', 'eu-west-1');
define('S3_ENDPOINT', 'https://s3-' . S3_REGION . '.amazonaws.com/' . S3_BUCKET);
define('S3_ASSETS_BUCKET', 'matgyver-v1-eu-west-1-preprod-not-versioned');
define('S3_ASSETS_REGION', 'eu-west-1');
define('S3_ASSETS_ENDPOINT', 'https://s3-' . S3_ASSETS_REGION . '.amazonaws.com/' . S3_ASSETS_BUCKET);

define('S3_KEY', 'PREPROD_S3_KEY');
define('S3_SECRET', 'PREPROD_S3_SECRET');

/**
 * SQS
 */
define('QUEUE_HANDLER', QUEUE_HANDLERS_DATABASE);
define('SQS_REGION', 'eu-west-1');
define('SQS_VERSION', 'latest');
define('SQS_KEY', 'PREPROD_SQS_KEY');
define('SQS_SECRET', 'PREPROD_SQS_SECRET');

define('SQS_QUEUES', [
    'bounces' => 'https://sqs.eu-west-1.amazonaws.com/12345/v1-prod-bounces_and_complaints_from_ses',
    'cron' => 'https://sqs.eu-west-1.amazonaws.com/12345/cron',
    'mail_queue' => 'https://sqs.eu-west-1.amazonaws.com/12345/mail_queue',
    'default' => 'https://sqs.eu-west-1.amazonaws.com/12345/default',
    'abonnements' => 'https://sqs.eu-west-1.amazonaws.com/12345/abonnements',
]);

/**
 * Timezone
 */
define("SERVER_TIMEZONE", "Europe/Paris");

/**
 * Components
 */
define('GAME_ACTIVATED', false);
define('ONBOARDING_ACTIVATED', false);
define('CHAT_ACTIVATED', false);
define('AFFILIATION_ACTIVATED', false);
define('FILE_SYSTEM', FILE_SYSTEM_FILES);
define('EVENTS_ACTIVATED', true);
define('BUG_TRACKER_ENABLED', true);
define('BUG_TRACKER_CLIENTS', []);
