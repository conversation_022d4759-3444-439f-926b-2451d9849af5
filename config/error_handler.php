<?php

use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\Logger\SqsLoggerService;
use MatGyver\Services\Logger\TransactionLoggerService;
use Symfony\Component\ErrorHandler\Debug;

// Enable debug view
if (ENV !== ENV_PROD) {
    Debug::enable();
}

// Init log (code log + errors + exceptions)
LoggerService::init(LOGGER_NAME);

// Init transactions log
TransactionLoggerService::init(TRANSACTION_LOGGER_NAME);

// Init SQS log
SqsLoggerService::init(SQS_LOGGER_NAME);
