<?php
/**
* Environment : dev, test, or prod
*/
define("ENV", 'dev');
define("MASTER_API_KEY", "TzPSEfXn5HgNyADjH6WXIiw7XVHqlGZj");
define("SECRET_KEY", "604eb2ff14f5f1ad73a21b7e7b827e62a37211e8366545c6156c5404717f3832");
define('CLIENT_MASTER', 1);
define("FREE_CLIENTS", [CLIENT_MASTER]);

/*
 *  MAIN DATABASE
 */
define("DB_NAME", "matgyver");
define("DB_HOST", "localhost");
define("DB_LOGIN", "root");
define("DB_PASS", "******");
define("DB_PORT", 3306);
define("DB_PREFIX", "mg_");

/**
 *   PATHS
 */
define("SESSIONS_PATH", FULL_DOCUMENT_ROOT . "/var/sessions");
define("WKHTMLTOIMAGE_PATH", '/usr/local/bin/wkhtmltoimage');
define("WKHTMLTOPDF_PATH", '/usr/local/bin/wkhtmltopdf');

/**
 *  DOMAIN
 */
define('APP_NAME', 'MatGyver');
define('APP_DOMAIN', 'matgyver.dev:8890');
define('APP_URL', 'https://matgyver.dev:8890');
define('APP_EMAIL', '<EMAIL>');

/**
 * ASSETS / CDN
 */
define('CDN_URL', 'https://matgyver.dev:8890/');
define('MEDIAS_URL', 'https://s3-eu-west-1.amazonaws.com/matgyver-dev/');

/**
 * APPS
 */
define('AWEBER_KEY', 'xxxxx');
define('AWEBER_SECRET', 'xxxxx');

define('AWS_KEY', 'xxxx');
define('AWS_SECRET', 'xxxxx');

define('CKFINDER_LICENSE_NAME', '');
define('CKFINDER_LICENSE_KEY', '');

define('GOOGLE_RECAPTCHA_SECRET_KEY', '**********');
define('GOOGLE_RECAPTCHA_PUBLIC_KEY', '**********');

/**
 * Sessions
 */
define('SESSION_TIMEOUT', 10800);

/**
 * Logs
 */
define('LOGGER_NAME', 'matgyver');
define('TRANSACTION_LOGGER_NAME', 'matgyver-transaction');
define('SQS_LOGGER_NAME', 'matgyver-sqs');
define('ENABLE_LOGS_MYSQL', true);
define('ENABLE_LOGS_MAILS', false);

define('ENABLE_LOGS_AWSKINESIS', false);
define('AWS_KINESIS_REGION', 'eu-west-1');
define('AWS_KINESIS_VERSION', 'latest');

define('ENABLE_LOGS_SLACK', false);
define('SLACK_TOKEN', 'xxxxxx');
define('SLACK_CHANNEL', '#logs');

define('ENABLE_LOGS_FILES', false);

/**
 * S3
 */
define('S3_BUCKET', 'matgyver');
define('S3_REGION', 'eu-west-1');
define('S3_ENDPOINT', 'https://s3-' . S3_REGION . '.amazonaws.com/' . S3_BUCKET);
define('S3_ASSETS_BUCKET', 'matgyver-assets');
define('S3_ASSETS_REGION', 'eu-west-1');
define('S3_ASSETS_ENDPOINT', 'https://s3-' . S3_ASSETS_REGION . '.amazonaws.com/' . S3_ASSETS_BUCKET);

define('S3_KEY', 'xxxxx');
define('S3_SECRET', 'xxxxxx');

/**
 * SQS
 */
define('QUEUE_HANDLER', QUEUE_HANDLERS_DATABASE);
define('SQS_REGION', 'eu-west-1');
define('SQS_VERSION', 'latest');
define('SQS_KEY', 'xxxxx');
define('SQS_SECRET', 'xxxxxx');

define('SQS_QUEUES', [
    'bounces' => 'https://sqs.eu-west-1.amazonaws.com/12345/cron',
    'cron' => 'https://sqs.eu-west-1.amazonaws.com/12345/cron',
    'mail_queue' => 'https://sqs.eu-west-1.amazonaws.com/12345/mail_queue',
    'default' => 'https://sqs.eu-west-1.amazonaws.com/12345/default',
    'abonnements' => 'https://sqs.eu-west-1.amazonaws.com/12345/abonnements',
]);

/**
 * Timezone
 */
define("SERVER_TIMEZONE", "Europe/Paris");

/**
 * Components
 */
define('GAME_ACTIVATED', false);
define('ONBOARDING_ACTIVATED', false);
define('CHAT_ACTIVATED', false);
define('AFFILIATION_ACTIVATED', false);
define('FILE_SYSTEM', FILE_SYSTEM_FILES);
define('EVENTS_ACTIVATED', true);
define('BUG_TRACKER_ENABLED', true);
define('BUG_TRACKER_CLIENTS', []);
