<?php

/**
 * Paths
 */
define('CONFIG_PATH', FULL_DOCUMENT_ROOT . '/config');
define('SOURCES_PATH', FULL_DOCUMENT_ROOT . '/src');

define('VENDOR_PATH', FULL_DOCUMENT_ROOT . '/vendor');

define('CONTROLLERS_PATH', SOURCES_PATH . '/Controllers');
define('HELPERS_PATH', SOURCES_PATH . '/Helpers');
define('I18N_PATH', SOURCES_PATH . '/I18n');
define('VIEWS_PATH', SOURCES_PATH . '/Views');

define('FORMS_PATH', VIEWS_PATH . '/forms');
define('LAYOUTS_PATH', VIEWS_PATH . '/layouts');

define('VAR_PATH', FULL_DOCUMENT_ROOT . '/var');
define('LOGS_PATH', VAR_PATH . '/logs');
define('CACHE_PATH', VAR_PATH . '/cache');
define('ROUTING_CACHE_PATH', CACHE_PATH . '/routing');

define('WEB_PATH', FULL_DOCUMENT_ROOT . '/web');
define('WEB_LIB_PATH', WEB_PATH . '/lib');
define('ASSETS_PATH', WEB_PATH . '/assets');
define('IMAGES_PATH', ASSETS_PATH . '/images');
define('UPLOAD_PATH', WEB_LIB_PATH . '/upload');
