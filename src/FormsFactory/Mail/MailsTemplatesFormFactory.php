<?php

namespace Mat<PERSON>yver\FormsFactory\Mail;

use MatGyver\Entity\Mail\Template\MailTemplate;
use Mat<PERSON><PERSON>ver\Enums\FieldsEnum;
use MatGyver\Forms\Mail\MailTemplateForm;
use MatG<PERSON>ver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\Helpers\Tools;
use Symfony\Component\Validator\Constraints\NotBlank;
use function __;

class MailsTemplatesFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(MailTemplateForm::class);

        $field = new BuilderFormField('subject');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Sujet'));
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez entrer le sujet de l\'email')]));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('description');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Description'));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('body');
        $field->setType(FieldsEnum::TYPE_CKEDITOR);
        $field->setLabel(__('Description'));
        $this->builderFields->addField($field);

        $this->setTitle(__('Modification d\'un email'));
        $this->setCancelUrl(Tools::makeLink('admin', 'mails_templates'));
    }

    /**
     * @param MailTemplate|null $mailTemplate
     */
    public function preRenderFields(?MailTemplate $mailTemplate)
    {
        if (!$mailTemplate) {
            throw new \Exception(__('Impossible de créer un email'));
        }
    }
}
