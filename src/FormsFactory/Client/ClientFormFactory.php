<?php

namespace Mat<PERSON><PERSON>ver\FormsFactory\Client;

use Mat<PERSON><PERSON><PERSON>\Entity\Client\Client;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Forms\Clients\ClientForm;
use MatGyver\FormsFactory\AbstractFormFactory;
use Mat<PERSON><PERSON>ver\FormsFactory\Builder\BuilderFormContentFunction;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormGroup;
use MatGyver\FormsFactory\Builder\BuilderFormSelect;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Clients\ClientsService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Limit\LimitClientsService;
use MatGyver\Services\Limit\LimitService;
use Symfony\Component\Validator\Constraints\Date;
use Symfony\Component\Validator\Constraints\NotBlank;

class ClientFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(ClientForm::class);

        $this->addName();
        $this->addSubscription();

        $this->setTitle(__('Création d\'un client'));
        $this->setCancelUrl(Tools::makeLink('admin', 'clients'));
    }

    /**
     * @param Client|null $client
     */
    public function preRenderFields(?Client $client)
    {
        if ($client) {
            $this->setTitle(__('Modification d\'un client'));

            $select = $this->builderFields->getField('subscription')->getSelect();
            if ($select instanceof BuilderFormSelect) {
                $select->setParam2($client->getId());
            }

            $this->addLimits();

            $group = $this->builderFields->getField('group_limits');
            if ($group instanceof BuilderFormGroup) {
                $limitsContent = $group->getField('limits')->getContentFunction();
                if ($limitsContent instanceof BuilderFormContentFunction) {
                    $limitsContent->setClass(LimitClientsService::class);
                    $limitsContent->setFunction('renderClientLimits');
                    $limitsContent->setParam($client);
                }
            }
        } else {
            $this->addUserInfos();
            $this->addLimits();
            $this->builderFields->updateField('date_end_subscription', 'value', date('Y-m-d', strtotime('+1 month')));
        }

        if (SUBDOMAIN_ENABLED) {
            Assets::addInlineJs('
            <script type="text/javascript">
                $(document).ready(function() {
                   $(\'#name\').keyup(uniqid).change(uniqid);
                   $(\'#uniqid\').change(standalone_uniqid);
                });
            </script>');
        }
    }

    private function addName()
    {
        $field = new BuilderFormField('name');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Nom du client'));
        $field->addValidation(new NotBlank(['message' => __('Veuillez entrer le nom du client')]));
        $field->setRequired(true);
        if (SUBDOMAIN_ENABLED) {
            $field->setData([
                'target' => 'uniqid',
            ]);
        }
        $this->builderFields->addField($field);

        if (SUBDOMAIN_ENABLED) {
            $field = new BuilderFormField('uniqid');
            $field->setType(FieldsEnum::TYPE_INPUT_GROUP);
            $field->setLabel(__('Lien'));
            $field->setData([
                'input_type' => 'text',
                'pre-addon' => 'https://',
                'addon' => '.' . APP_DOMAIN
            ]);
            $field->addValidation(new NotBlank(['message' => __('Veuillez entrer le lien du client')]));
            $field->setRequired(true);
            $this->builderFields->addField($field);
        }
    }

    private function addSubscription()
    {
        $field = new BuilderFormField('subscription');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Abonnement'));
        $select = new BuilderFormSelect();
        $select->setClass(ClientsService::class);
        $select->setFunction('generateSelectSubscription');
        $field->setSelect($select);
        $field->addValidation(new NotBlank(['message' => __('Veuillez sélectionner un abonnement')]));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('date_end_subscription');
        $field->setType(FieldsEnum::TYPE_DATE);
        $field->setLabel(__('Date de fin d\'abonnement'));
        $field->addValidation(new Date(['message' => __('Veuillez indiquer une date de fin d\'abonnement')]));
        $this->builderFields->addField($field);
    }

    private function addUserInfos()
    {
        $group = new BuilderFormGroup('group_user');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Création du compte administrateur'));
        $group->setOpened(true);

        $fields = $this->getUserInfosFields();
        foreach ($fields as $field) {
            $group->addField($field);
        }

        $this->builderFields->addField($group);
    }

    private function addLimits()
    {
        $container = ContainerBuilderService::getInstance();
        $limits = $container->get(LimitService::class)->getRepository()->findAll();
        if (!$limits) {
            return;
        }

        $group = new BuilderFormGroup('group_limits');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Limites'));
        $group->setOpened(true);

        $field = new BuilderFormField('limits');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setLabel('');
        $content = new BuilderFormContentFunction();
        $content->setClass(LimitService::class);
        $content->setFunction('renderLimits');
        $content->setParam(null);
        $field->setContentFunction($content);
        $group->addField($field);

        $this->builderFields->addField($group);
    }
}
