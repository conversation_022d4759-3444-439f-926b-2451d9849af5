<?php

namespace Mat<PERSON>yver\FormsFactory\Support;

use MatGyver\Entity\Support\Department\SupportDepartment;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Forms\Support\SupportDepartmentForm;
use MatG<PERSON>ver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormSelect;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Support\SupportServiceService;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Positive;

class SupportDepartmentFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(SupportDepartmentForm::class);

        $field = new BuilderFormField('name');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Nom'));
        $field->addValidation(new NotBlank(['message' => __('Veuillez entrer le nom du département')]));
        $field->setRequired(true);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('id_service');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Service'));
        $select = new BuilderFormSelect();
        $select->setClass(SupportServiceService::class);
        $select->setFunction('generateServiceSelect');
        $field->setSelect($select);
        $field->addValidation(new Positive(['message' => __('Veuillez sélectionner un service')]));
        $field->setRequired(true);
        $this->builderFields->addField($field);

        $this->setTitle(__('Création d\'un département'));
        $this->setCancelUrl(Tools::makeLink('admin', 'support', 'departments'));
    }

    /**
     * @param SupportDepartment|null $department
     */
    public function preRenderFields(?SupportDepartment $department)
    {
        if ($department) {
            $this->setTitle(__('Modification d\'un département'));

            $select = $this->builderFields->getField('id_service')->getSelect();
            if ($select instanceof BuilderFormSelect) {
                $select->setParam($department->getService()->getId());
            }
        }
    }
}
