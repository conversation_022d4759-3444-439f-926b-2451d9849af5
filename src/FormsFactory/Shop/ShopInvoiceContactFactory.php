<?php

namespace MatGyver\FormsFactory\Shop;

use MatGyver\Enums\ConfigEnum;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Forms\Shop\ShopInvoicesContactForm;
use MatG<PERSON>ver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormGroup;
use MatGyver\Helpers\Tools;
use MatGyver\Menus\InvoicesSettingsMenu;
use MatGyver\Services\ConfigService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\TwigService;

class ShopInvoiceContactFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(ShopInvoicesContactForm::class);

        $this->addInfos();
        $this->addAdditionalInfos();
        $this->addPreview();

        $this->setTitle(__('Coordonnées'));
        $this->setCancelUrl(Tools::makeLink('admin', 'shop', 'invoices'));
    }

    public function preRenderFields()
    {
        $container = ContainerBuilderService::getInstance();

        $navigationMenu = $container->get(InvoicesSettingsMenu::class)->createMenu();
        $this->setNavigationMenu($navigationMenu);

        $settings = $container->get(ConfigService::class)->findByName(ConfigEnum::INVOICE, CLIENT_MASTER);
        if ($settings) {
            $invoiceSettings = json_decode($settings->getValue(), true);
            $config = ['logo', 'contact_email', 'code_siret', 'tva_intra', 'code_naf'];
            foreach ($config as $configName) {
                if (isset($invoiceSettings[$configName]) and $invoiceSettings[$configName]) {
                    $this->builderFields->updateField($configName, 'value', $invoiceSettings[$configName]);
                }
            }

            $config = ['infos1', 'infos2', 'infos_sale_ue_intracom', 'infos_sale_outside_ue', 'infos_new_page'];
            $group = $this->builderFields->getField('additional_infos');
            foreach ($config as $configName) {
                if (isset($invoiceSettings[$configName]) and $invoiceSettings[$configName]) {
                    $group->updateField($configName, 'value', $invoiceSettings[$configName]);
                }
            }
        }
    }

    private function addInfos()
    {
        $field = new BuilderFormField('logo');
        $field->setType(FieldsEnum::TYPE_IMAGE);
        $field->setLabel(__('Logo'));
        $field->setData(['target' => 'preview-logo']);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('contact_email');
        $field->setType(FieldsEnum::TYPE_EMAIL);
        $field->setLabel(__('Adresse email de contact'));
        $field->setData(['target' => 'span.contact_email']);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('code_siret');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Code Siret'));
        $field->setData(['target' => 'span.siret']);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('tva_intra');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Numéro de TVA intracommunautaire'));
        $field->setData(['target' => 'span.tva_intra']);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('code_naf');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Code NAF / APE'));
        $field->setData(['target' => '.code_naf']);
        $this->builderFields->addField($field);
    }

    private function addAdditionalInfos()
    {
        $group = new BuilderFormGroup('additional_infos');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Informations complémentaires'));
        $group->setOpened(true);

        $field = new BuilderFormField('infos1');
        $field->setType(FieldsEnum::TYPE_TEXTAREA);
        $field->setLabel(__('Informations complémentaires'));
        $field->setHelp(__('Ces informations apparaîtront en dessous de la liste des produits.'));
        $group->addField($field);

        $field = new BuilderFormField('infos2');
        $field->setType(FieldsEnum::TYPE_TEXTAREA);
        $field->setLabel(__('Informations complémentaires'));
        $field->setHelp(__('Ces informations apparaîtront tout en bas de la facture, en dessous des informations de votre société.'));
        $group->addField($field);

        $field = new BuilderFormField('infos_sale_ue_intracom');
        $field->setType(FieldsEnum::TYPE_TEXTAREA);
        $field->setLabel(__('Informations à afficher dans le cas d\'un paiement dans l\'Union Européenne (avec numéro de TVA intracommunautaire)'));
        $field->setHelp(__('Si vous résidez en Union Européenne et si vous réalisez des ventes à des entreprises de l’Union Européenne et possédant un numéro de TVA intracommunautaire, ces ventes sont exonérées de TVA.') . '<br>' . __('Ces informations apparaîtront en dessous du total de la facture.'));
        $group->addField($field);

        $field = new BuilderFormField('infos_sale_outside_ue');
        $field->setType(FieldsEnum::TYPE_TEXTAREA);
        $field->setLabel(__('Informations à afficher dans le cas d\'un paiement hors de l\'Union Européenne'));
        $field->setHelp(__('Si vous résidez en Union Européenne, les ventes hors de l\'UE sont exonérées de TVA.') . '<br>' . __('Ces informations apparaîtront en dessous du total de la facture.'));
        $group->addField($field);

        $field = new BuilderFormField('infos_new_page');
        $field->setType(FieldsEnum::TYPE_CKEDITOR);
        $field->setLabel(__('Pages supplémentaires'));
        $field->setHelp(__('Vous pouvez ajouter ici des informations (conditions générales de vente, etc.) qui apparaîtront sur des pages supplémentaires.'));
        $group->addField($field);

        $this->builderFields->addField($group);
    }

    private function addPreview()
    {
        $container = ContainerBuilderService::getInstance();
        $settings = $container->get(ConfigService::class)->getConfig();
        $invoiceSettings = [];
        if (isset($settings[ConfigEnum::INVOICE])) {
            $invoiceSettings = json_decode($settings[ConfigEnum::INVOICE], true);
        }

        $logo = '';
        if (isset($invoiceSettings['logo'])) {
            $logo = $invoiceSettings['logo'];
        }
        if (!$logo and isset($settings['logo']) and $settings['logo']) {
            $logo = $settings['logo'];
        }

        $group = new BuilderFormGroup('preview');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Aperçu'));
        $group->setOpened(true);

        $field = new BuilderFormField('preview_content');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent(TwigService::getInstance()->set('settings', $settings)
            ->set('invoiceSettings', $invoiceSettings)
            ->set('logo', $logo)
            ->render('forms/admin/invoices/invoice_preview.php'));
        $group->addField($field);

        $this->builderFields->addField($group);
    }
}
