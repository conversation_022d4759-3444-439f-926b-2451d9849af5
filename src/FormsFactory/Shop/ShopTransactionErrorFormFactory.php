<?php

namespace Mat<PERSON>yver\FormsFactory\Shop;

use MatGyver\Entity\Shop\Transaction\Error\ShopTransactionError;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Forms\Shop\ShopTransactionErrorForm;
use Mat<PERSON>yver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormSelect;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\Transaction;
use MatGyver\Services\Shop\Transaction\Error\ShopTransactionErrorStatusService;
use MatGyver\Services\Users\UsersService;
use Symfony\Component\Validator\Constraints\NotBlank;

class ShopTransactionErrorFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(ShopTransactionErrorForm::class);

        $field = new BuilderFormField('id_status');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Statut de l\'impayé'));
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez sélectionner le statut de l\'impayé')]));
        $select = new BuilderFormSelect();
        $select->setClass(ShopTransactionErrorStatusService::class);
        $select->setFunction('generateSelectStatus');
        $field->setSelect($select);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('notify_user_id');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Administrateur à prévenir par email'));
        $select = new BuilderFormSelect();
        $select->setClass(UsersService::class);
        $select->setFunction('appGenerateSelectSuperUsers');
        $field->setSelect($select);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('comment');
        $field->setType(FieldsEnum::TYPE_TEXTAREA);
        $field->setLabel(__('Commentaire'));
        $field->setHelp(__('Visible uniquement par les administrateurs'));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('date');
        $field->setType(FieldsEnum::TYPE_DATETIME);
        $field->setLabel(__('Date'));
        $this->builderFields->addField($field);

        $this->setTitle(__('Statut d\'un impayé'));
        $this->setCancelUrl(Tools::makeLink('admin', 'shop', 'transactions/errors'));
    }

    /**
     * @param ShopTransactionError|null $transactionError
     * @throws \Exception
     */
    public function preRenderFields(?ShopTransactionError $transactionError)
    {
        if (!$transactionError) {
            throw new \Exception(__('Impossible de créer un abonnement'));
        }

        $this->setHeaderText($this->getTransactionErrorInfos($transactionError));
        $this->setCancelUrl(Tools::makeLink('admin', 'shop', 'transaction/error/' . $transactionError->getId()));
        $this->addHiddenFields('redirect', Tools::makeLink('admin', 'shop', 'transaction/error/' . $transactionError->getId()));

        $select = $this->builderFields->getField('id_status')->getSelect();
        if ($select instanceof BuilderFormSelect) {
            $select->setParam($transactionError->getTransactionErrorStatus()->getId());
        }
    }

    /**
     * @param ShopTransactionError $transactionError
     * @return string
     */
    private function getTransactionErrorInfos(ShopTransactionError $transactionError): string
    {
        return '
        <p>
            <strong>' . __('Numéro de Transaction') . ' : </strong>' . $transactionError->getTransaction()->getReference() . '<br>
            <strong>' . __('Date'). ' : </strong>' . dateTimeFr($transactionError->getDate()->format('Y-m-d H:i:s')) . '<br>
            <strong>' . __('Type'). ' : </strong>' . Transaction::displayTransactionPaymentMethod($transactionError->getType()) . '
            <strong>' . __('Etat') . ' : </strong>' . $transactionError->getTransactionErrorStatus()->getLibelle() . '
        </p>';
    }
}
