<?php

namespace Mat<PERSON>yver\FormsFactory\Shop;

use Mat<PERSON><PERSON>ver\Entity\Shop\Attribute\ShopAttribute;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Forms\Shop\ShopAttributeForm;
use MatGyver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormSelect;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Shop\ShopAttributesGroupsService;
use Symfony\Component\Validator\Constraints\NotBlank;

class ShopAttributeFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(ShopAttributeForm::class);

        $this->addName();
        $this->addAttributeGroup();
        $this->addColor();

        $this->setTitle(__('Création d\'un attribut'));
        $this->setCancelUrl(Tools::makeLink('admin', 'shop', 'attributes'));
    }

    /**
     * @param ShopAttribute|null $shopAttribute
     */
    public function preRenderFields(?ShopAttribute $shopAttribute)
    {
        if ($shopAttribute) {
            $this->setTitle(__('Modification d\'un attribut'));

            $select = $this->builderFields->getField('id_group')->getSelect();
            if ($select instanceof BuilderFormSelect) {
                $select->setParam($shopAttribute->getAttributeGroup()->getId());
            }
        }
    }

    private function addName()
    {
        $field = new BuilderFormField('name');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Nom de l\'attribut'));
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez entrer le nom de l\'attribut')]));
        $this->builderFields->addField($field);
    }

    private function addAttributeGroup()
    {
        $field = new BuilderFormField('id_group');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Groupe'));
        $select = new BuilderFormSelect();
        $select->setClass(ShopAttributesGroupsService::class);
        $select->setFunction('generateSelectGroup');
        $field->setSelect($select);
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez sélectionner un groupe')]));
        $this->builderFields->addField($field);
    }

    private function addColor()
    {
        $field = new BuilderFormField('color');
        $field->setType(FieldsEnum::TYPE_COLOR);
        $field->setLabel(__('Couleur'));
        $this->builderFields->addField($field);
    }
}
