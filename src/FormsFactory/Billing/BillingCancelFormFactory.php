<?php

namespace MatGyver\FormsFactory\Billing;

use Mat<PERSON><PERSON><PERSON>\Enums\FieldsEnum;
use MatGyver\Forms\Billing\BillingCancelForm;
use Mat<PERSON><PERSON>ver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormGroup;
use MatGyver\FormsFactory\Builder\BuilderFormSelect;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Clients\ClientsCancelService;
use MatGyver\Services\RightsService;
use Symfony\Component\Validator\Constraints\NotBlank;

class BillingCancelFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(BillingCancelForm::class);
        $this->setBtnValidateClass('btn-danger');
        $this->setBtnValidateText(__('Fermer mon compte'));
        $this->setBtnCancelText(__('Retour'));

        $field = new BuilderFormField('content_alert');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($this->getAlertContent());
        $this->builderFields->addField($field);

        $field = new BuilderFormField('subscription_cancel');
        $field->setType(FieldsEnum::TYPE_SWITCH);
        $field->setLabel(__('Fermeture de compte'));
        $field->setHelp(__('Oui, je confirme demander la fermeture de mon compte'));
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez confirmer la demande de fermeture de votre compte')]));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('password');
        $field->setType(FieldsEnum::TYPE_PASSWORD);
        $field->setLabel(__('Entrez ici le mot de passe de l\'adresse email') . ' ' . $_SESSION['email']);
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez indiquer votre mot de passe')]));
        $this->builderFields->addField($field);

        $field = new BuilderFormGroup('cancel_reason');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Raison'));
        $select = new BuilderFormSelect();
        $select->setClass(ClientsCancelService::class);
        $select->setFunction('generateSelectReasons');
        $field->setSelect($select);
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez sélectionner une raison')]));
        $this->builderFields->addField($field);

        $field = new BuilderFormGroup('reason');
        $field->setType(FieldsEnum::TYPE_TEXTAREA);
        $field->setLabel(__('Souhaitez-vous nous communiquer plus d\'informations ?'));
        $field->setPlaceholder(__('Indiquez ici la raison de votre demande de fermeture de compte'));
        $this->builderFields->addField($field);

        $this->setTitle(__('Fermeture de mon compte'));
        $this->setCancelUrl(Tools::makeLink('app', 'billing'));
    }

    public function preRenderFields()
    {
        if (!RightsService::isAdmin()) {
            throw new \Exception(__('Vous n\'avez pas l\'autorisation d\'accéder à cette page'));
        }
    }

    /**
     * @return string
     */
    private function getAlertContent(): string
    {
        return '
        <div class="alert alert-custom alert-light-danger fade show mb-5">
            <div class="alert-text">
                <p><strong>' . __('Attention : la fermeture de votre compte entraînera la suppression définitive de votre compte, toutes les données seront perdues.') . '</strong></p>
                <p class="mb-0">' . __('Si vous avez déjà réglé le mois en cours, votre compte restera actif jusqu\'à la date d\'échéance de votre abonnement, puis sera supprimé de manière automatique.') . '</p>
            </div>
        </div>';
    }
}
