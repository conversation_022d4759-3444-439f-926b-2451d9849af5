<?php
namespace MatGyver\FormsFactory\Dossier;

use MatGyver\Entity\Dossier\DossierConsignment;
use MatGyver\Entity\Dossier\DossierContact;
use MatGyver\Entity\Dossier\DossierInstitution;
use MatGyver\Entity\Dossier\DossierMandate;
use MatGyver\Entity\Dossier\Expertise\DossierExpertise;
use MatGyver\Enums\ConfigEnum;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Entity\Dossier\Dossier;
use MatGyver\Forms\Dossier\DossierForm;
use MatGyver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormSelect;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Currency;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Address\AddressService;
use MatGyver\Services\Breakdown\BreakdownService;
use MatGyver\Services\Company\CompanyService;
use MatGyver\Services\ConfigService;
use MatGyver\Services\CountriesService;
use MatGyver\Services\Department\DepartmentService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Dossier\DossierContactService;
use MatGyver\Services\Dossier\DossierMandateService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseJudiciaireService;
use MatGyver\Services\I18nService;
use MatGyver\Services\Intervention\InterventionSubjectService;
use MatGyver\Services\Mission\MissionTypeService;
use MatGyver\Services\RightsService;
use MatGyver\Services\Surcharge\SurchargeCommentService;
use MatGyver\Services\Surcharge\SurchargeService;
use MatGyver\Services\TwigService;
use MatGyver\Services\Users\UsersExpertsService;
use MatGyver\Services\Users\UsersManagersService;
use Symfony\Component\Validator\Constraints\NotBlank;

class DossierFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(DossierForm::class);

        $this->addMandate();
        $this->addContact();
        $this->addVehicleOwner();
        if (isset($_GET['judiciaire'])) {
            $this->addExpertiseJudiciaire(1);
            $this->addAddress('mandate', __('Tribunal'), 2);
            $this->builderFields->removeField('mandate_siren');
            $this->addAddress('court_president', __('Président du tribunal'), 2);
            $this->addAddress('court_clerk', __('Greffier du tribunal'), 2);
            $this->addAddress('court_assistant', __('Adjoint(e) administratif(ve)'), 2);
            $this->addAddress('contact', __('Demandeur'), 3);
            $this->addAddress('contact_lawyer', __('Avocat du demandeur'), 3);
            $this->addAddress('contact_expert', __('Expert du demandeur'), 3);
            $this->addOtherContacts(3);
            $this->addAddress('vehicle_owner', __('Propriétaire du véhicule'), 4);
            $this->addAddress('vehicle_owner2', __('Co-propriétaire du véhicule'), 4);
        } else {
            $this->addAddress('mandate', __('Mandant'), 1);
            $this->addAddress('contact', __('Lésé'), 2);
            $this->addAddress('vehicle_owner', __('Propriétaire du véhicule'), 3);
        }

        $this->addIntervention();
        if (isset($_GET['judiciaire'])) {
            $this->addCourtQuestions(6);
        }
        $this->addVehicle((isset($_GET['judiciaire']) ? 7 : 5));
        $this->addExpertise((isset($_GET['judiciaire']) ? 8 : 6));
        if (!isset($_GET['light'])) {
            $this->addImmobilization((isset($_GET['judiciaire']) ? 8 : 6));
            $this->addMisc((isset($_GET['judiciaire']) ? 9 : 7));
        }

        if (isset($_GET['judiciaire'])) {
            $this->addDefendeur(10);
        }

        $this->setTitle(__('Création d\'un dossier'));
        $this->setCancelUrl(Tools::makeLink('app', 'index'));
    }

    /**
     * @param Dossier|null $dossier
     */
    public function preRenderFields(?Dossier $dossier)
    {
        if ($dossier) {
            $this->setTitle(__('Modification d\'un dossier'));
        }
    }

    private function addMandate()
    {
        if (isset($_GET['judiciaire'])) {
            $field = new BuilderFormField('mandate_type');
            $field->setType(FieldsEnum::TYPE_HIDDEN);
            $field->setValue(DossierMandate::TYPE_COURT);
            $field->setStep(2);
            $this->builderFields->addField($field);

            $content = '
            <div class="form-group">
                <label class="control-label">' . __('Tribunal') . ' *</label>
            </div>';
            $field = new BuilderFormField('mandate_type_content');
            $field->setType(FieldsEnum::TYPE_CONTENT);
            $field->setContent($content);
            $field->setStep(2);
            $this->builderFields->addField($field);
        } else {
            $options = [];
            $types = DossierMandateService::getTypes();
            foreach ($types as $type => $name) {
                $options[] = ['value' => $type, 'label' => $name, 'label_class' => 'radio-bordered'];
            }
            $field = new BuilderFormField('mandate_type');
            $field->setType(FieldsEnum::TYPE_RADIO);
            $field->setLabel(__('Type de mandant'));
            $field->setRequired(true);
            $field->addValidation(new NotBlank(['message' => __('Veuillez sélectionner un type de mandant')]));
            $field->setValue(DossierMandate::TYPE_BUYER);
            $field->setOptions($options);
            $field->setStep(1);
            $field->setData(['class' => 'radio-inline']);
            $this->builderFields->addField($field);
        }
    }

    private function addContact()
    {
        $stepId = (isset($_GET['judiciaire']) ? 3 : 2);

        $options = [];
        $types = DossierContactService::getTypes();
        foreach ($types as $type => $name) {
            $options[] = ['value' => $type, 'label' => $name, 'label_class' => 'radio-bordered'];
        }
        $field = new BuilderFormField('contact_type');
        $field->setType(FieldsEnum::TYPE_RADIO);
        $field->setLabel((isset($_GET['judiciaire']) ? __('Type de demandeur') : __('Type de lésé')));
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez sélectionner un type de lésé')]));
        $field->setValue(DossierContact::TYPE_BUYER);
        $field->setOptions($options);
        $field->setStep($stepId);
        $field->setData(['class' => 'radio-inline']);
        $this->builderFields->addField($field);

        if (isset($_GET['light'])) {
            $field = new BuilderFormField('dossier_light');
            $field->setType(FieldsEnum::TYPE_CONTENT);
            $field->setContent('<input type="hidden" name="dossier_light" id="dossier_light" value="1">');
            $field->setStep($stepId);
            $this->builderFields->addField($field);
        }
    }

    private function addVehicleOwner()
    {
        $content = '
        <div class="form-group">
            <label class="control-label">' . __('Propriétaire du véhicule') . ' *</label>
        </div>';
        $field = new BuilderFormField('vehicle_owner_content');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($content);
        $field->setStep((isset($_GET['judiciaire']) ? 4 : 3));
        $this->builderFields->addField($field);
    }

    private function addAddress(string $contactType, string $title, int $stepId)
    {
        $selectText = __('Sélectionner un mandant');
        $newText = __('Créer un mandant');
        if (isset($_GET['judiciaire'])) {
            $selectText = __('Sélectionner un tribunal');
            $newText = __('Créer un tribunal');
        }
        switch ($contactType) {
            case 'court_president':
                $selectText = __('Sélectionner un président de tribunal');
                $newText = __('Créer un président de tribunal');
                break;
            case 'court_clerk':
                $selectText = __('Sélectionner un greffier');
                $newText = __('Créer un greffier');
                break;
            case 'court_assistant':
                $selectText = __('Sélectionner un adjoint administratif');
                $newText = __('Créer un adjoint administratif');
                break;
            case 'contact':
                $selectText = __('Sélectionner un lésé');
                $newText = __('Créer un lésé');
                if (isset($_GET['judiciaire'])) {
                    $selectText = __('Sélectionner un demandeur');
                    $newText = __('Créer un demandeur');
                }
                break;
            case 'contact_lawyer':
                $selectText = __('Sélectionner un avocat');
                $newText = __('Créer un avocat');
                break;
            case 'contact_expert':
                $selectText = __('Sélectionner un expert');
                $newText = __('Créer un expert');
                break;
            case 'vehicle_owner':
                $selectText = __('Sélectionner un propriétaire');
                $newText = __('Créer un propriétaire');
                break;
            case 'vehicle_owner2':
                $selectText = __('Sélectionner un co-propriétaire');
                $newText = __('Créer un co-propriétaire');
                break;
            case 'immobilization_place':
                $selectText = __('Sélectionner un lieu d\'immobilisation');
                $newText = __('Créer un lieu d\'immobilisation');
                break;
            case 'expertise_place':
                $selectText = __('Sélectionner un lieu d\'expertise');
                $newText = __('Créer un lieu d\'expertise');
                break;
        }

        $content = '
        <div class="card card-custom gutter-b" id="' . $contactType . '_card">
            <div class="card-header card-header-tabs-line">
                <div class="card-toolbar">
                    <input type="hidden" name="' . $contactType . '_create" id="' . $contactType . '_create" value="select">
                    <ul class="nav nav-tabs nav-bold nav-tabs-line" id="tab' . $contactType . '" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="tab' . $contactType . '-home" data-toggle="tab" data-contact-type="' . $contactType . '" data-value="select" href="#tab' . $contactType . '-content-select">
                                <span class="nav-text">' . $selectText . '</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="tab' . $contactType . '-new" data-toggle="tab" data-contact-type="' . $contactType . '" data-value="new" href="#tab' . $contactType . '-content-new">
                                <span class="nav-text">' . $newText . '</span>
                            </a>
                        </li>
                    </ul>
                    <div style="position: absolute; top: 20px; right: 30px;">
                        <a class="btn btn-light-primary btn-sm" onclick="modalCorporations(\'' . $contactType . '\');"><i class="fas fa-search"></i> ' . __('Rechercher') . '</a>
                    </div>
                </div>
            </div>
            <div class="card-body">';
        $field = new BuilderFormField($contactType . '_content1');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($content);
        $field->setStep($stepId);
        $this->builderFields->addField($field);


        $content = '
                <div class="tab-content" id="tab' . $contactType . 'Content">
                    <div class="tab-pane fade active show" id="tab' . $contactType . '-content-select" role="tabpanel">';
        $field = new BuilderFormField($contactType . '_content1_1');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($content);
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        if ($contactType == 'court_president') {
            $options = [
                ['value' => DossierInstitution::TYPE_COURT_PRESIDENT, 'label' => __('Président')],
                ['value' => DossierInstitution::TYPE_VICE_PRESIDENT, 'label' => __('Vice-Président')],
            ];
            $field = new BuilderFormField('court_president_type');
            $field->setType(FieldsEnum::TYPE_RADIO);
            $field->setLabel('');
            $field->setOptions($options);
            $field->setStep($stepId);
            $field->setValue(DossierInstitution::TYPE_COURT_PRESIDENT);
            $this->builderFields->addField($field);
        }

        $options = [];
        $createLabel = __('Création d\'un nouveau mandant');

        switch ($contactType) {
            case 'mandate':
                $options[] = ['value' => '', 'label' => __('Cliquez pour sélectionner un mandant')];
                if (isset($_GET['judiciaire'])) {
                    $options = [['value' => 'contact', 'label' => __('Cliquez pour sélectionner un tribunal')]];
                }
                break;
            case 'court_president':
                $options[] = ['value' => '', 'label' => __('Cliquez pour sélectionner un président')];
                break;
            case 'court_clerk':
                $options[] = ['value' => '', 'label' => __('Cliquez pour sélectionner un greffier')];
                break;
            case 'court_assistant':
                $options[] = ['value' => '', 'label' => __('Cliquez pour sélectionner un adjoint administratif')];
                break;
            case 'contact':
                //$options[] = ['value' => '', 'label' => __('Nouveau lésé')];
                if (isset($_GET['judiciaire'])) {
                    $options[] = ['value' => 'mandate', 'label' => __('Cliquez pour sélectionner un demandeur')];
                } else {
                    $options[] = ['value' => 'mandate', 'label' => __('Le lésé est le mandant')];
                }
                $createLabel = __('Création d\'un nouveau lésé');
                break;
            case 'contact_lawyer':
                $options[] = ['value' => '', 'label' => __('Cliquez pour sélectionner un avocat')];
                break;
            case 'contact_expert':
                $options[] = ['value' => '', 'label' => __('Cliquez pour sélectionner un expert')];
                break;
            case 'vehicle_owner':
                //$options[] = ['value' => '', 'label' => __('Nouveau propriétaire')];
                if (!isset($_GET['judiciaire'])) {
                    $options[] = ['value' => 'mandate', 'label' => __('Le propriétaire du véhicule est le mandant')];
                    $options[] = ['value' => 'contact', 'label' => __('Le propriétaire du véhicule est le lésé')];
                } else {
                    $options[] = ['value' => 'contact', 'label' => __('Le propriétaire du véhicule est le demandeur')];
                }
                $createLabel = __('Création d\'un nouveau propriétaire');
                break;
            case 'vehicle_owner2':
                if (!isset($_GET['judiciaire'])) {
                    $options[] = ['value' => 'mandate', 'label' => __('Le co-propriétaire du véhicule est le mandant')];
                    $options[] = ['value' => 'contact', 'label' => __('Le co-propriétaire du véhicule est le lésé')];
                } else {
                    $options[] = ['value' => 'contact', 'label' => __('Le co-propriétaire du véhicule est le demandeur')];
                }
                $createLabel = __('Création d\'un nouveau co-propriétaire');
                break;
            case 'expertise_place':
                $options[] = ['value' => '', 'label' => __('Lieu à déterminer')];
                $options[] = ['value' => DossierExpertise::PLACE_TYPE_HOME, 'label' => __('Au domicile du propriétaire du véhicule')];
                $options[] = ['value' => DossierExpertise::PLACE_TYPE_CONTACT, 'label' => __('À l\'adresse du lésé')];
                $options[] = ['value' => DossierExpertise::PLACE_TYPE_MANDATE, 'label' => __('À l\'adresse du mandant')];
                $createLabel = __('Création d\'un nouveau lieu d\'expertise');
                break;
            case 'immobilization_place':
                //$options[] = ['value' => '', 'label' => __('Nouveau lieu d\'immobilisation')];
                $options[] = ['value' => DossierExpertise::PLACE_TYPE_HOME, 'label' => __('Au domicile du propriétaire du véhicule')];
                $options[] = ['value' => DossierExpertise::PLACE_TYPE_CONTACT, 'label' => __('À l\'adresse du lésé')];
                $options[] = ['value' => DossierExpertise::PLACE_TYPE_MANDATE, 'label' => __('À l\'adresse du mandant')];
                $options[] = ['value' => DossierExpertise::PLACE_TYPE_ROLLING, 'label' => __('Véhicule roulant et non immobilisé')];
                $createLabel = __('Création d\'un nouveau lieu d\'immobilisation');
                break;
        }

        $container = ContainerBuilderService::getInstance();
        $addresses = $container->get(AddressService::class)->getRepository()->findBy([], ['company' => 'ASC']);
        foreach ($addresses as $address) {
            $options[] = [
                'value' => $address->getId(),
                'label' => $address->getName() . ' - ' . $address->getAddress() . ' ' . $address->getZip() . ' ' . $address->getCity(),
                'data' => [
                    'email' => $address->getEmail(),
                ]
            ];
        }

        $field = new BuilderFormField($contactType . '_address_id');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel($title);
        if ($contactType == 'contact' and !isset($_GET['judiciaire'])) {
            $field->setHelp(__('Le lésé est la personne ou société que nous représentons'));
        }
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $field->setSelect($select);
        $field->setStep($stepId);
        $field->setClass('select-address');
        //$field->setData(['target' => $contactType . '_create']);
        if ($contactType == 'contact' or $contactType == 'vehicle_owner') {
            $select->setValue('mandate');
        }
        $this->builderFields->addField($field);

        $content = '<div id="tab' . $contactType . '-content-address-infos"></div>';
        $field = new BuilderFormField($contactType . '_content_address_infos');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($content);
        $field->setStep($stepId);
        $this->builderFields->addField($field);



        $content = '
        </div>
        <div class="tab-pane fade" id="tab' . $contactType . '-content-new" role="tabpanel">';
        $field = new BuilderFormField($contactType . '_content2');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($content);
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $field = new BuilderFormField($contactType . '_first_name');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Prénom'));
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $field = new BuilderFormField($contactType . '_last_name');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Nom'));
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $field = new BuilderFormField($contactType . '_email');
        $field->setType(FieldsEnum::TYPE_EMAIL);
        $field->setLabel(__('Adresse email'));
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        if ($contactType != 'court_president' and $contactType != 'court_clerk' and $contactType != 'court_assistant') {
            $field = new BuilderFormField($contactType . '_company');
            $field->setType(FieldsEnum::TYPE_TEXT);
            $field->setLabel(__('Nom de la société'));
            $field->setStep($stepId);
            $this->builderFields->addField($field);

            $field = new BuilderFormField($contactType . '_telephone');
            $field->setType(FieldsEnum::TYPE_TEXT);
            $field->setLabel(__('Téléphone'));
            $field->setStep($stepId);
            $this->builderFields->addField($field);

            $addressFields = $this->getAddressFields();
            foreach ($addressFields as $addressField) {
                if ($addressField->getId() == 'state' or $addressField->getId() == 'country') {
                    continue;
                }
                if ($addressField->getId() == 'address') {
                    $addressField->setData([
                        'targets' => [
                            'zip' => $contactType . '_zip',
                            'city' => $contactType . '_city'
                        ],
                    ]);
                    $addressField->setClass('input-group-address');
                }
                $id = $addressField->getId();
                $id = $contactType . '_' . $id;
                $addressField->setId($id);
                $addressField->setName($id);
                $addressField->setStep($stepId);
                $this->builderFields->addField($addressField);
            }

            $field = new BuilderFormField($contactType . '_siren');
            $field->setType(FieldsEnum::TYPE_TEXT);
            $field->setLabel(__('SIREN'));
            $field->setStep($stepId);
            $this->builderFields->addField($field);
        }

        $field = new BuilderFormField($contactType . '_content3');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('</div></div></div></div>');
        $field->setStep($stepId);
        $this->builderFields->addField($field);
    }

    public function addExpertiseJudiciaire(int $stepId)
    {
        $intro = __('Nous vous donnons la possibilité de télécharger l\'ordonnance en cliquant sur le bouton ci-contre et nous pré-remplirons un certain nombre d\'informations que vous pourrez ensuite relire, corriger et compléter si nécessaire.');
        $button = __('Télécharger l\'ordonnance');
        $orderType = __('Type d\'ordonnance');
        $orderDate = __('Ordonnance du');
        $defaultCountry = $_SESSION['settings'][ConfigEnum::COUNTRY] ?? DEFAULT_COUNTRY;
        if ($defaultCountry === 'BE') {
            $intro = __('Nous vous donnons la possibilité de télécharger le jugement contradictoire en cliquant sur le bouton ci-contre et nous pré-remplirons un certain nombre d\'informations que vous pourrez ensuite relire, corriger et compléter si nécessaire.');
            $button = __('Télécharger le jugement contradictoire');
            $orderType = __('Type de jugement');
            $orderDate = __('Jugement du');
        }

        $content = '
        <h3 class="font-weight-bolder">' . __('Dossier d\'expertise judiciaire') . '</h3>
        <p>' . __('Suivez les différentes étapes ci-contre pour créer votre dossier d\'expertise judiciaire.') . '</p>
        <div class="d-flex flex-row justify-content-between w-100 mb-8">    
            <div class="w-100 mr-4">
                <p>' . $intro . '</p>
            </div>
            <div class="pt-2">
                <a class="btn btn-light-primary" onclick="displayModalMissionDocument();"><i class="fas fa-file-pdf"></i> ' . $button . '</a>
            </div>
        </div>
        <div class="card card-custom gutter-b expertise-judiciaire">
            <div class="card-header">
                <h3 class="card-title">' . $button . '</h3>
            </div>
            <div class="card-body">';
        $field = new BuilderFormField('expertise_judiciaire_content1');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($content);
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('order_date');
        $field->setType(FieldsEnum::TYPE_DATE_MASK);
        $field->setLabel($orderDate);
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $refusalConditions = DossierExpertiseJudiciaireService::getOrderTypes();
        $options = [];
        foreach ($refusalConditions as $type => $name) {
            $options[] = ['value' => $type, 'label' => $name];
        }
        $field = new BuilderFormField('order_type');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel($orderType);
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $field->setSelect($select);
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('rg_number');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(I18nService::getRGNumber());
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('expertise_number');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Numéro Expertise'));
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('minute_number');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(I18nService::getMinuteNumber());
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('portalis_number');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Numéro Portalis'));
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('court_chamber');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Chambre'));
        $field->setStep($stepId);
        $this->builderFields->addField($field);


        /*$field = new BuilderFormField('notes');
        $field->setType(FieldsEnum::TYPE_TEXTAREA);
        $field->setLabel(__('Notes'));
        $field->setStep($stepId);
        $this->builderFields->addField($field);*/


        $field = new BuilderFormField('last_report_date');
        $field->setType(FieldsEnum::TYPE_DATE_MASK);
        $field->setLabel(__('Date limite pour déposer le rapport d\'expertise'));
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('expert_reference');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Référence de l\'expert'));
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('case_name');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Nom de l\'affaire'));
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('court_content2');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('</div></div>');
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('court_order_document');
        $field->setType(FieldsEnum::TYPE_HIDDEN);
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('court_order_transcript_accept');
        $field->setType(FieldsEnum::TYPE_HIDDEN);
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('court_order_transcript_accept_date');
        $field->setType(FieldsEnum::TYPE_HIDDEN);
        $field->setStep($stepId);
        $this->builderFields->addField($field);
    }

    private function addIntervention()
    {
        $stepId = 4;
        $missionStepId = 4;
        if (isset($_GET['judiciaire'])) {
            $stepId = 5;
            $missionStepId = 6;
        }
        $container = ContainerBuilderService::getInstance();

        $field = new BuilderFormField('user_id');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Expert'));
        $select = new BuilderFormSelect();
        $select->setClass(UsersExpertsService::class);
        $select->setFunction('generateSelectExperts');
        if (RightsService::hasRole(ROLE_EDITOR) or RightsService::hasRole(ROLE_ADMIN)) {
            $select->setParam($_SESSION['user']['id']);
        }
        $field->setSelect($select);
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        if (!RightsService::hasRole(ROLE_SUPEREDITOR)) {
            $field = new BuilderFormField('manager_id');
            $field->setType(FieldsEnum::TYPE_SELECT);
            $field->setLabel(__('Gestionnaire'));
            $select = new BuilderFormSelect();
            $select->setClass(UsersManagersService::class);
            $select->setFunction('generateSelectManagers');
            $field->setSelect($select);
            $field->setStep($stepId);
            $this->builderFields->addField($field);
        }

        $companies = $container->get(CompanyService::class)->getRepository()->findBy([], ['name' => 'ASC']);
        $options = [];
        $selectedOption = null;
        foreach ($companies as $company) {
            $options[] = ['value' => $company->getId(), 'label' => $company->getName() . ' - ' . $company->getFirstName() . ' ' . $company->getLastName() . ' (' . $company->getAddress() . ' - ' . $company->getZip() . ' ' . $company->getCity() . ')'];
            if ($company->getEmail() == $_SESSION['user']['email']) {
                $selectedOption = $company->getId();
            }
            if (isset($_GET['judiciaire']) and $_SESSION['client']['id'] == 2 and $company->getCity() == 'Toulouse') {
                $selectedOption = $company->getId();
            }
        }

        $field = new BuilderFormField('company_id');
        $field->setType(FieldsEnum::TYPE_SELECT);
        if ($_SESSION['client']['id'] == CLIENT_BCA) {
            $field->setLabel(__('Centre de Gestion'));
            $field->setRequired(true);
            $field->addValidation(new NotBlank(['message' => __('Veuillez sélectionner le centre de gestion')]));
        } else {
            $field->setLabel(__('Siège social de l\'expert'));
            $field->setRequired(true);
            $field->addValidation(new NotBlank(['message' => __('Veuillez sélectionner le siège social de l\'expert')]));
        }
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $field->setSelect($select);
        $field->setStep($stepId);
        if ($selectedOption) {
            $select->setValue($selectedOption);
        }
        $this->builderFields->addField($field);

        $field = new BuilderFormField('date_mission');
        $field->setType(FieldsEnum::TYPE_DATE_MASK);
        $field->setLabel(__('Date de mission'));
        $field->setValue(date('d/m/Y'));
        $field->setStep($missionStepId);
        $this->builderFields->addField($field);

        $missionTypes = $container->get(MissionTypeService::class)->getRepository()->findBy([], ['name' => 'ASC']);
        $options = [];
        $selected = 0;
        foreach ($missionTypes as $missionType) {
            if (isset($_GET['judiciaire']) and $missionType->getName() == 'Expertise judiciaire') {
                $selected = $missionType->getId();
            } else {
                if ($missionType->getName() == 'Assistance Technique à titre privé') {
                    $selected = $missionType->getId();
                }
            }
            $options[] = ['value' => $missionType->getId(), 'label' => $missionType->getName()];
        }
        $field = new BuilderFormField('mission_type_id');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Type de mission'));
        $field->addValidation(new NotBlank(['message' => __('Veuillez sélectionner le type de mission')]));
        $field->setRequired(true);
        $field->setHelp(__('Le type de mission déterminera le type et le titre du rapport d\'expertise.'));
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $select->setValue($selected);
        $field->setSelect($select);
        $field->setStep($stepId);
        if (isset($_GET['judiciaire'])) {
            $field = new BuilderFormField('mission_type_id');
            $field->setType(FieldsEnum::TYPE_HIDDEN);
            $field->setValue($selected);
            $field->setStep($stepId);
        }
        $this->builderFields->addField($field);


        $interventionsSubjects = $container->get(InterventionSubjectService::class)->getRepository()->findBy([], ['name' => 'ASC']);
        $interventionSubjectExpertiseJudiciaire = null;
        if (isset($_GET['judiciaire'])) {
            foreach ($interventionsSubjects as $interventionSubject) {
                if ($interventionSubject->getName() == 'Expertise Judiciaire') {
                    $interventionSubjectExpertiseJudiciaire = $interventionSubject;
                    break;
                }
            }
        }
        if ($interventionSubjectExpertiseJudiciaire) {
            $field = new BuilderFormField('interventions_subjects_ids[]');
            $field->setType(FieldsEnum::TYPE_HIDDEN);
            $field->setValue($interventionSubjectExpertiseJudiciaire->getId());
            $field->setStep($stepId);
            $this->builderFields->addField($field);
        } else {
            $options = [];
            foreach ($interventionsSubjects as $interventionSubject) {
                $options[] = ['value' => $interventionSubject->getId(), 'label' => $interventionSubject->getName()];
            }
            $field = new BuilderFormField('interventions_subjects_ids[]');
            $field->setType(FieldsEnum::TYPE_SELECT);
            $field->setLabel(__('Objets de l\'intervention dans le cadre de la mission mandant - expert *'));
            if (!isset($_GET['judiciaire'])) {
                $field->setHelp(__('Cette information apparaîtra dans le récapitulatif mission envoyé au lésé. Choix multiples possibles.'));
            }
            $field->setPlaceholder(__('Cliquez pour sélectionner un objet d\'intervention'));
            $select = new BuilderFormSelect();
            $select->setOptions($options);
            $field->setSelect($select);
            $field->setStep($stepId);
            $field->setData(['multiple' => true]);
            $this->builderFields->addField($field);
        }

        if (!isset($_GET['judiciaire'])) {
            $field = new BuilderFormField('intervention_comment');
            $field->setType(FieldsEnum::TYPE_TEXTAREA);
            $field->setLabel(__('Déclaration de la demande client et commentaire de l\'expert'));
            $field->setHelp(__('Cette information apparaîtra dans le récapitulatif mission envoyé au lésé'));
            $field->setStep($missionStepId);
            $this->builderFields->addField($field);

            $field = new BuilderFormField('audio_recorder');
            $field->setType(FieldsEnum::TYPE_AUDIO_RECORDER);
            $field->setLabel(__('Enregistreur audio'));
            $field->setData(['target' => 'intervention_comment']);
            $field->setStep($missionStepId);
            $this->builderFields->addField($field);
        }

        $origins = $container->get(BreakdownService::class)->getRepository()->findBy([], ['name' => 'ASC']);
        $options = [];
        foreach ($origins as $origin) {
            $options[] = ['value' => $origin->getId(), 'label' => $origin->getName()];
        }

        if (!isset($_GET['judiciaire'])) {
            $field = new BuilderFormField('origin_ids[]');
            $field->setType(FieldsEnum::TYPE_SELECT);
            $field->setLabel(__('Objet de la panne ou du litige *'));
            $field->setHelp(__('Sert à définir le champ d\'intervention et à donner des précisions pour la convocation. Choix multiples possibles.'));
            $field->setPlaceholder(__('Cliquez pour sélectionner un objet de panne ou du litige'));
            $select = new BuilderFormSelect();
            $select->setOptions($options);
            $field->setSelect($select);
            $field->setData(['multiple' => true]);
            $field->setStep($stepId);
            $this->builderFields->addField($field);

            $field = new BuilderFormField('mandate_priority');
            $field->setType(FieldsEnum::TYPE_TEXTAREA);
            $field->setLabel(__('Souhait prioritaire du mandant'));
            $field->setPlaceholder(__('Faire réparer le véhicule, céder le véhicule, obtenir un remboursement, etc.'));
            $field->setStep($missionStepId);
            $this->builderFields->addField($field);

            $field = new BuilderFormField('audio_recorder2');
            $field->setType(FieldsEnum::TYPE_AUDIO_RECORDER);
            $field->setLabel(__('Enregistreur audio'));
            $field->setData(['target' => 'mandate_priority']);
            $field->setStep($missionStepId);
            $this->builderFields->addField($field);
        }

        $departments = $container->get(DepartmentService::class)->getRepository()->findBy([], ['number' => 'ASC']);
        $options = [];
        foreach ($departments as $department) {
            $options[] = ['value' => $department->getId(), 'label' => $department->getNumber(), 'label_class' => 'radio-bordered'];
        }
        if ($options) {
            $field = new BuilderFormField('department_id');
            $field->setLabel(__('Département d\'intervention'));
            if (count($departments) >= 10) {
                $field->setType(FieldsEnum::TYPE_SELECT);
                $select = new BuilderFormSelect();
                $select->setOptions($options);
                $field->setSelect($select);
            } else {
                $field->setType(FieldsEnum::TYPE_RADIO);
                $field->setOptions($options);
                $field->setData(['class' => 'radio-inline']);
            }
            $field->setStep($stepId);
            $this->builderFields->addField($field);
        }

        if (isset($_GET['judiciaire'])) {
            $field = new BuilderFormField('circumstances');
            $field->setType(FieldsEnum::TYPE_CKEDITOR_SMALL);
            $field->setLabel(__('Circonstances de l\'affaire'));
            $field->setStep($stepId);
            $this->builderFields->addField($field);

            $field = new BuilderFormField('audio_recorder');
            $field->setType(FieldsEnum::TYPE_AUDIO_RECORDER);
            $field->setLabel(__('Enregistreur audio'));
            $field->setData(['target' => 'circumstances']);
            $field->setStep($stepId);
            $this->builderFields->addField($field);
        }
    }

    private function addCourtQuestions(int $stepId)
    {
        $content = '
        <h3 class="font-weight-bolder">' . __('Questions du tribunal') . '</h3>
        <div id="kt_repeater_1">
            <div data-repeater-list="court_questions" class="draggable-zone">';

        for ($i = 0; $i <= 9; $i++) {
            $content .= '
                <div data-repeater-item class="card card-custom gutter-b draggable">
                    <div class="card-header">
                        <h3 class="card-title">' . __('Question du tribunal') . '</h3>
                        <div class="card-toolbar">
                            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary draggable-handle">
                                <i class="fas fa-arrows-alt text-dark-50"></i>
                            </a>
                            <a href="javascript:;" data-repeater-delete="" class="btn btn-sm btn-hover-light-danger btn-icon">
                                <i class="la la-remove"></i>
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="form-group w-100">
                            <textarea class="form-control autosize" name="court_questions" id="court_questions_' . $i . '" placeholder="' . __('Indiquez ici la question du tribunal') . '"></textarea>
                        </div>
                        <div class="recorder" id="audio_recorder' . $i . '" data-target="court_questions_' . $i . '">
                            <div class="recorder-container d-flex flex-column">
                                <div class="d-flex flex-row justify-content-between align-items-center">
                                    <div id="btns">
                                        <a class="btn btn-clean btn-icon recordButton" id="record"><i class="fas fa-circle text-danger"></i></a>
                                        <a class="btn btn-clean btn-icon stopButton d-none" id="stop"><i class="fas fa-stop text-warning"></i></a>
                                    </div>
                                    <div id="canvas-container">
                                        <canvas id="level" height="40" width="300"></canvas>
                                    </div>
                                    <div id="audio-source">
                                        <select id="audio-in-select" class="form-control"></select>
                                    </div>
                                </div>
                                <div id="result"></div>
                            </div>
                        </div>
                    </div>
                </div>';
        }
        $content .= '
            </div>
            <div data-repeater-create="" class="btn font-weight-bold btn-light-primary" id="btnAddCourtQuestion">
                <i class="la la-plus"></i> ' . __('Ajouter une question') . '
            </div>
        </div>';
        $field = new BuilderFormField('court_questions_content2');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($content);
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        DossierExpertiseJudiciaireService::addModalMissionDocument();
    }

    private function addVehicle(int $stepId)
    {
        $field = new BuilderFormField('brand');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Marque *'));
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('model');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Modèle *'));
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('registration_content1');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('<div class="d-flex flex-row justify-content-between w-100 registration-content"><div class="w-100 mr-4">');
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('registration');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Immatriculation'));
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('registration_content2');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('</div><div class="w-300px">');
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $options = [];
        $container = ContainerBuilderService::getInstance();
        $countries = $container->get(CountriesService::class)->getCountries();
        foreach ($countries as $country) {
            $options[] = [
                'value' => $country->getCode(),
                'label' => $country->getName(),
                'data' => [
                    'img-src' => Assets::getImageUrl('flags/countries/' . strtolower($country->getCode()) . '.png')
                ]
            ];
        }

        $field = new BuilderFormField('registration_country');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Pays'));
        $field->setData(['rel' => 'select2-img']);
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $select->setValue('FR');
        $field->setSelect($select);
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('registration_content3');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('</div></div><p class="text-muted mb-8"><small>' . __('3 façons de saisir votre immatriculation :') . '<br><img class="img-fluid" src="' . Assets::getImageUrl('help/registration.png') . '"></small></p>');
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('formule_number');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('N° de formule sur carte grise (si immatriculation depuis 2009)'));
        $field->setHelp('<a class="btn-link cursor-pointer" onclick="createModalImg(\'' . Assets::getImageUrl('help/formule_number.png') . '\', \'' . __('Comment trouver le numéro de formule ?') . '\');">' . __('Comment trouver le numéro de formule ?') . '</a>');
        $field->setStep($stepId);
        $field->setData(['autocomplete' => 'do-not-autofill', 'data-form-type' => 'other']);
        $this->builderFields->addField($field);
    }

    private function addExpertise(int $stepId)
    {
        $field = new BuilderFormField('date_expertise');
        $field->setType(FieldsEnum::TYPE_DATETIME_MASK);
        $field->setLabel(__('Date d\'expertise provisoire à confirmer'));
        if (isset($_GET['light'])) {
            $field->setLabel(__('Date et heure d\'expertise si convoqué'));
        }
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $this->addAddress('expertise_place', __('Lieu d\'expertise'), $stepId);

        $options = [
            [
                'value' => true,
                'label' => __('Cochez cette case si le lieu d\'immobilisation est le même que le lieu d\'expertise.'),
                'checked' => false,
                'class' => 'checkbox-outline checkbox-outline-2x checkbox-primary'
            ]
        ];
        $field = new BuilderFormField('link_address_expertise_immobilization');
        $field->setType(FieldsEnum::TYPE_CHECKBOX);
        $field->setStep($stepId);
        $field->setOptions($options);
        $this->builderFields->addField($field);
    }

    private function addImmobilization(int $stepId)
    {
        $field = new BuilderFormField('immobilization_place_content_content1');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('<div id="immobilization_place_content">');
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $this->addAddress('immobilization_place', __('Lieu d\'immobilisation'), $stepId);

        $field = new BuilderFormField('immobilization_place_content_content2');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('</div>');
        $field->setStep($stepId);
        $this->builderFields->addField($field);
    }

    private function addMisc(int $stepId)
    {
        $container = ContainerBuilderService::getInstance();
        $expertisePrice = $container->get(ConfigService::class)->findByName('expertise_price');
        $expertisePrice = ($expertisePrice ? $expertisePrice->getValue() : 0);

        $expertisePriceType = $container->get(ConfigService::class)->findByName('expertise_price_type');
        $expertisePriceType = ($expertisePriceType ? $expertisePriceType->getValue() : '');

        $field = new BuilderFormField('price_content1');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('<div class="d-flex flex-row">');
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('price');
        $field->setType(FieldsEnum::TYPE_INPUT_GROUP);
        $field->setLabel(__('Prix de la 1ère expertise'));
        if (isset($_GET['judiciaire'])) {
            $field->setLabel(__('Montant de la consignation'));
        }
        $field->setData([
            'input_type' => 'float',
            'addon' => Currency::displayCurrency(DEFAULT_CURRENCY),
            'size' => 'input-group-small'
        ]);
        $field->setStep($stepId);
        $field->setValue($expertisePrice);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('price_content2');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('<div class="ml-2">');
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $options = [
            ['value' => Dossier::PRICE_FIXED, 'label' => __('Prix fixe')],
            ['value' => Dossier::PRICE_PER_HOUR, 'label' => __('Prix par heure')],
        ];
        $field = new BuilderFormField('price_type');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel('&nbsp;');
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $select->setValue($expertisePriceType);
        $field->setSelect($select);
        $field->setData(['rel' => '']);
        $field->setClass('min-w-1px');
        $field->setStep($stepId);
        $field->setValue($expertisePriceType);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('price_content3');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('</div></div>');
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        if (isset($_GET['judiciaire'])) {
            $options = [
                ['value' => DossierConsignment::STATUS_WAITING, 'label' => __('Consignation en attente')],
                ['value' => DossierConsignment::STATUS_FILED, 'label' => __('Consignation déposée')],
            ];
            $field = new BuilderFormField('consignment');
            $field->setType(FieldsEnum::TYPE_RADIO);
            $field->setLabel(__('Consignation'));
            $field->setOptions($options);
            $field->setStep($stepId);
            $field->setValue(DossierConsignment::STATUS_WAITING);
            $this->builderFields->addField($field);
        }

        if (!isset($_GET['judiciaire'])) {
            $field = new BuilderFormField('display_price');
            $field->setType(FieldsEnum::TYPE_SWITCH);
            $field->setLabel(__('Afficher le prix de l\'expertise au lésé'));
            $field->setOptions([
                'on' => 'valid',
                'off' => 'invalid',
            ]);
            $field->setData([
                'label_active' => __('Afficher le prix de l\'expertise au lésé'),
                'label_desactive' => __('Ne pas afficher le prix de l\'expertise au lésé'),
            ]);
            $field->setValue('valid');
            if ($_SESSION['client']['id'] == CLIENT_BCA or $_SESSION['client']['id'] == CLIENT_ONEEXPERT or isset($_GET['light'])) {
                $field->setValue('invalid');
            }
            $field->setStep($stepId);
            $this->builderFields->addField($field);
        }

        $surcharges = $container->get(SurchargeService::class)->getRepository()->findBy([], ['name' => 'ASC']);
        if ($surcharges) {
            $content = '
            <div class="form-group">
                <label class="control-label ">' . __('Frais annexes') . '</label>
                <div class="controls">
                    <table class="table table-separate table-head-custom table-surcharges">
                        <tbody></tbody>
                    </table>
                    <div class="d-flex flex-row">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm font-weight-bold btn-light-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                ' . __('Ajouter un frais') . '
                            </button>
                            <div class="dropdown-menu">';
            foreach ($surcharges as $surcharge) {
                $content .= '
                <a class="dropdown-item cursor-pointer" onclick="AddSurcharge(' . $surcharge->getId() . ', \'' . addslashes($surcharge->getName()) . '\', \'' . number_format($surcharge->getAmount(), 2, '.', '') . '\', \'' . str_replace(array("\r\n", "\r", "\n"), array('<br/>', '<br/>', '<br/>'), $surcharge->getComment()) . '\'); return false;">
                    ' . $surcharge->getName() . ' (' . $surcharge->displayAmount() . ')
                </a>';
            }
            $content .= '
                            </div>
                        </div>
                    </div>
                </div>
            </div>';

            $field = new BuilderFormField('surcharges_content');
            $field->setType(FieldsEnum::TYPE_CONTENT);
            $field->setContent($content);
            $field->setStep($stepId);
            $this->builderFields->addField($field);
        }

        $field = new BuilderFormField('observations');
        $field->setType(FieldsEnum::TYPE_TEXTAREA);
        $field->setLabel(__('Observations concernant le tarif'));
        if (isset($_GET['judiciaire'])) {
            $field->setLabel(__('Observations internes concernant le tarif'));
        } else {
            $field->setHelp(__('Cette information sera visible par le lésé, et apparaîtra sur le récapitulatif de mission.'));
        }
        $field->setClass('autosize');
        $field->setStep($stepId);
        $surchargesComments = $container->get(SurchargeCommentService::class)->getRepository()->findBy([], ['comment' => 'ASC']);
        if ($surchargesComments) {
            $presets = [];
            foreach ($surchargesComments as $surchargesComment) {
                $presets[] = str_replace("\r\n", '<br>', $surchargesComment->getComment());
            }
            $field->setData(['presets' => $presets]);
        }
        $this->builderFields->addField($field);

        if (!isset($_GET['judiciaire'])) {
            $field = new BuilderFormField('send_to_contact');
            $field->setType(FieldsEnum::TYPE_SWITCH);
            $field->setLabel(__('Envoyer au contact'));
            $field->setOptions([
                'on' => 'valid',
                'off' => 'invalid',
            ]);
            $field->setData([
                'label_active' => __('Envoyer le dossier au lésé'),
                'label_desactive' => __('Ne pas envoyer le dossier au lésé pour l\'instant'),
            ]);
            $field->setValue('valid');
            $field->setHelp(__('Si vous cochez cette case, le dossier sera envoyé au lésé et il pourra compléter les informations manquantes.'));
            $field->setStep($stepId);
            $this->builderFields->addField($field);
        }
    }

    private function addDefendeur(int $stepId)
    {
        $content = '<div id="defendeurs">';
        $content .= TwigService::getInstance()->set('id', 1)
            ->render(FORMS_PATH . '/app/dossier/defendeur.php');
        $content .= '</div>';
        $field = new BuilderFormField('defendeur_content');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($content);
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $content = '<div class="mt-8"><a class="btn btn-light-primary" onclick="AddDefendeur();">' . __('Ajouter un défendeur') . '</a></div>';
        $field = new BuilderFormField('defendeur_content_btn');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($content);
        $field->setStep($stepId);
        $this->builderFields->addField($field);
    }

    private function addOtherContacts(int $stepId)
    {
        $content = '<div id="contacts">';
        $content .= TwigService::getInstance()->set('id', 2)
            ->render(FORMS_PATH . '/app/dossier/contact.php');
        $content .= TwigService::getInstance()->set('id', 3)
            ->render(FORMS_PATH . '/app/dossier/contact.php');
        $content .= '</div>';
        $field = new BuilderFormField('contacts_content');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($content);
        $field->setStep($stepId);
        $this->builderFields->addField($field);

        $content = '<div class="mt-8"><a class="btn btn-light-primary" onclick="AddContact();">' . __('Ajouter un demandeur') . '</a></div>';
        $field = new BuilderFormField('contacts_content_btn');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($content);
        $field->setStep($stepId);
        $this->builderFields->addField($field);
    }
}
