<?php
namespace <PERSON><PERSON>yver\FormsFactory\Dossier\Expertise;

use Mat<PERSON><PERSON>ver\Entity\Dossier\Expertise\DossierExpertise;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Entity\Dossier\Expertise\DossierExpertiseVehicle;
use MatGyver\Forms\Dossier\Expertise\DossierExpertiseVehicleExaminationForm;
use MatGyver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\Helpers\Tools;

class DossierExpertiseVehicleExaminationFormFactory extends AbstractFormFactory
{
    public function __construct(?DossierExpertise $expertise = null)
    {
        parent::__construct();
        $this->setFormService(DossierExpertiseVehicleExaminationForm::class);

        $this->addExamination();

        $this->setTitle(__('Conditions d\'examen'));
        $this->setBtnValidateText(__('Valider et passer à l\'étape suivante'));
        if ($expertise) {
            $this->addHiddenFields('expertise_id', $expertise->getId());
            $this->setPrevPageUrl(Tools::makeLink('app', 'dossier', 'expertise/vehicle/recommendations/' . $expertise->getId()));
        }
    }

    /**
     * @param DossierExpertiseVehicle|null $dossierExpertiseVehicle
     */
    public function preRenderFields(?DossierExpertiseVehicle $dossierExpertiseVehicle)
    {
        if ($dossierExpertiseVehicle) {
            $vehicle = $dossierExpertiseVehicle->getExpertise()->getDossier()->getVehicle();
            if ($vehicle) {
                $this->builderFields->getField('dismantled')->setValue($vehicle->getDismantled());
                $this->builderFields->getField('reassembled')->setValue($vehicle->getReassembled());
                $this->builderFields->getField('draining')->setValue($vehicle->getDraining());
                $this->builderFields->getField('draining_comment')->setValue($vehicle->getDrainingComment());
            }
        }
    }

    private function addExamination()
    {
        $options = [
            ['value' => 'yes', 'label' => __('Oui')],
            ['value' => 'no', 'label' => __('Non')],
            ['value' => 'unsure', 'label' => __('Sans réponse')],
        ];

        $field = new BuilderFormField('presented');
        $field->setType(FieldsEnum::TYPE_RADIO);
        $field->setLabel(__('Véhicule présenté'));
        $field->setOptions($options);
        $field->setData(['class' => 'radio-inline']);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('rolling');
        $field->setType(FieldsEnum::TYPE_RADIO);
        $field->setLabel(__('Véhicule roulant'));
        $field->setOptions($options);
        $field->setData(['class' => 'radio-inline']);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('immobilised_not_dismantled');
        $field->setType(FieldsEnum::TYPE_RADIO);
        $field->setLabel(__('Véhicule immobilisé'));
        $field->setOptions($options);
        $field->setData(['class' => 'radio-inline']);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('on_lift');
        $field->setType(FieldsEnum::TYPE_RADIO);
        $field->setLabel(__('Véhicule sur pont élévateur'));
        $field->setOptions($options);
        $field->setData(['class' => 'radio-inline']);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('repaired');
        $field->setType(FieldsEnum::TYPE_RADIO);
        $field->setLabel(__('Véhicule réparé'));
        $field->setOptions($options);
        $field->setData(['class' => 'radio-inline']);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('dismantled');
        $field->setType(FieldsEnum::TYPE_RADIO);
        $field->setLabel(__('Véhicule démonté'));
        $field->setOptions($options);
        $field->setData(['class' => 'radio-inline']);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('partially_dismantled');
        $field->setType(FieldsEnum::TYPE_RADIO);
        $field->setLabel(__('Véhicule partiellement démonté'));
        $field->setOptions($options);
        $field->setData(['class' => 'radio-inline']);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('reassembled');
        $field->setType(FieldsEnum::TYPE_RADIO);
        $field->setLabel(__('Véhicule remonté'));
        $field->setOptions($options);
        $field->setData(['class' => 'radio-inline']);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('engine_working');
        $field->setType(FieldsEnum::TYPE_RADIO);
        $field->setLabel(__('Moteur démarre'));
        $field->setOptions($options);
        $field->setData(['class' => 'radio-inline']);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('draining');
        $field->setType(FieldsEnum::TYPE_RADIO);
        $field->setLabel(__('Vidange des fluides et filtres réalisée'));
        $field->setOptions($options);
        $field->setData(['class' => 'radio-inline']);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('draining_comment');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Si oui, précisez lequel(s) :'));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('part_examination');
        $field->setType(FieldsEnum::TYPE_RADIO);
        $field->setLabel(__('Examen sur pièce'));
        $field->setOptions($options);
        $field->setData(['class' => 'radio-inline']);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('examination_condition');
        $field->setType(FieldsEnum::TYPE_TEXTAREA);
        $field->setLabel(__('Précisions sur les conditions d\'examen'));
        $this->builderFields->addField($field);
    }
}
