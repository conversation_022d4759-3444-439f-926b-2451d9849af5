<?php
namespace <PERSON><PERSON><PERSON>ver\FormsFactory\Dossier\Expertise;

use Mat<PERSON><PERSON>ver\Entity\Dossier\DossierDocument;
use Mat<PERSON>yver\Entity\Dossier\Expertise\DossierExpertise;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Forms\Dossier\Expertise\DossierExpertiseSignaturesForm;
use MatGyver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;

class DossierExpertiseSignaturesFormFactory extends AbstractFormFactory
{
    /**
     * @param DossierExpertise|null $expertise
     * @param DossierDocument|null $dossierDocument
     */
    public function __construct(?DossierExpertise $expertise = null, ?DossierDocument $dossierDocument = null)
    {
        parent::__construct();
        $this->setFormService(DossierExpertiseSignaturesForm::class);

        $field = new BuilderFormField('temp');
        $field->setType(FieldsEnum::TYPE_HIDDEN);
        $field->setValue('temp');
        $this->builderFields->addField($field);

        $this->addSummary($dossierDocument);

        $this->setTitle(__('Signatures'));
        if ($expertise) {
            $this->addHiddenFields('expertise_id', $expertise->getId());
        }
    }

    private function addSummary(DossierDocument $dossierDocument)
    {
        $content = '<iframe class="border-0 w-100 h-1000px" src="' . $dossierDocument->getUrl(true, true) . '"></iframe>';

        $field = new BuilderFormField('document');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($content);
        $field->setStep(1);
        $this->builderFields->addField($field);
    }
}
