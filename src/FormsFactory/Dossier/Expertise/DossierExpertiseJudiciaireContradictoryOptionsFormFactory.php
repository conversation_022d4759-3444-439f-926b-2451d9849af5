<?php
namespace MatGyver\FormsFactory\Dossier\Expertise;

use Mat<PERSON>yver\Entity\Dossier\Expertise\DossierExpertise;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Entity\Dossier\Expertise\DossierExpertiseJudiciaire;
use MatGyver\Forms\Dossier\Expertise\DossierExpertiseJudiciaireContradictoryOptionsForm;
use MatGyver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;

class DossierExpertiseJudiciaireContradictoryOptionsFormFactory extends AbstractFormFactory
{
    public function __construct(?DossierExpertise $expertise = null)
    {
        parent::__construct();
        $this->setFormService(DossierExpertiseJudiciaireContradictoryOptionsForm::class);

        $this->addContradictoryEndText();

        $this->setTitle(__('Observations de fin'));
        $this->setBtnValidateText(__('Valider et passer à l\'étape suivante'));
        if ($expertise) {
            $this->addHiddenFields('expertise_id', $expertise->getId());
        }
    }

    /**
     * @param DossierExpertiseJudiciaire|null $dossierExpertiseJudiciaire
     */
    public function preRenderFields(?DossierExpertiseJudiciaire $dossierExpertiseJudiciaire)
    {
        if ($dossierExpertiseJudiciaire) {
            $content = '<p>Le présent document est transmis aux parties et leur conseils par voie de communication électronique.</p>';
            $content .= '<p>Les conseils devant faire suivre à leur mandant et conseils techniques.</p>';
            if ($dossierExpertiseJudiciaire->getContradictoryEndText() !== null) {
                $content = $dossierExpertiseJudiciaire->getContradictoryEndText();
            }
            $this->builderFields->updateField('contradictory_end_text', 'value', $content);
        }
    }

    private function addContradictoryEndText()
    {
        $field = new BuilderFormField('contradictory_end_text');
        $field->setType(FieldsEnum::TYPE_CKEDITOR_SMALL);
        $field->setLabel(__('Observations de fin'));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('audio_recorder3');
        $field->setType(FieldsEnum::TYPE_AUDIO_RECORDER);
        $field->setLabel(__('Enregistreur audio'));
        $field->setData(['target' => 'contradictory_end_text']);
        $this->builderFields->addField($field);
    }
}
