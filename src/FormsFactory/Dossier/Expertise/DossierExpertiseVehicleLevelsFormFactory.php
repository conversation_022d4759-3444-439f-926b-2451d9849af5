<?php
namespace <PERSON><PERSON><PERSON>ver\FormsFactory\Dossier\Expertise;

use MatGyver\Entity\Dossier\DossierDocument;
use Mat<PERSON>yver\Entity\Dossier\Expertise\DossierExpertise;
use MatG<PERSON>ver\Entity\Dossier\Expertise\DossierExpertisePicture;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Entity\Dossier\Expertise\DossierExpertiseVehicle;
use MatGyver\Forms\Dossier\Expertise\DossierExpertiseVehicleLevelsForm;
use MatGyver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormGroup;
use MatGyver\FormsFactory\Builder\BuilderFormSelect;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Dossier\DossierDocumentService;

class DossierExpertiseVehicleLevelsFormFactory extends AbstractFormFactory
{
    public function __construct(?DossierExpertise $expertise = null)
    {
        parent::__construct();
        $this->setFormService(DossierExpertiseVehicleLevelsForm::class);

        $this->addLevelEngineOil();
        $this->addLevelCoolant();
        $this->addLevelBrake();
        $this->addLevelFuel();
        $this->addTires();
        $this->addTiresCourrier();

        $this->setTitle(__('Niveau d\'huile moteur'));
        $this->setBtnValidateText(__('Valider et passer à l\'étape suivante'));
        if ($expertise) {
            $this->addHiddenFields('expertise_id', $expertise->getId());
            $this->setPrevPageUrl(Tools::makeLink('app', 'dossier', 'expertise/vehicle/' . $expertise->getId()));
        }
    }

    /**
     * @param DossierExpertiseVehicle|null $dossierExpertiseVehicle
     */
    public function preRenderFields(?DossierExpertiseVehicle $dossierExpertiseVehicle)
    {
        Assets::addJs('app/dossier/vehicle.js');

        if (!$dossierExpertiseVehicle) {
            throw new \Exception('Missing expertise vehicle.');
        }

        $this->addLevelEngineOil($dossierExpertiseVehicle);
        $this->addLevelCoolant($dossierExpertiseVehicle);
        $this->addLevelBrake($dossierExpertiseVehicle);
        $this->addLevelFuel($dossierExpertiseVehicle);
        $this->addTires($dossierExpertiseVehicle);
        $this->addTiresCourrier($dossierExpertiseVehicle->getExpertise());

        $expertise = $dossierExpertiseVehicle->getExpertise();
        $picture = $expertise->getPicture(DossierExpertisePicture::TYPE_ENGINE_OIL_LEVEL);
        if ($picture) {
            $this->builderFields->updateField('engine_oil_level_picture', 'value', $picture->getUrl());
        }

        $group = $this->builderFields->getField('group_level_coolant');
        if ($group instanceof BuilderFormGroup) {
            $picture = $expertise->getPicture(DossierExpertisePicture::TYPE_COOLANT_LEVEL);
            if ($picture) {
                $group->updateField('coolant_level_picture', 'value', $picture->getUrl());
            }
        }

        $group = $this->builderFields->getField('group_level_brake');
        if ($group instanceof BuilderFormGroup) {
            $picture = $expertise->getPicture(DossierExpertisePicture::TYPE_BRAKE_LEVEL);
            if ($picture) {
                $group->updateField('brake_level_picture', 'value', $picture->getUrl());
            }
        }

        $group = $this->builderFields->getField('group_level_fuel');
        if ($group instanceof BuilderFormGroup) {
            $picture = $expertise->getPicture(DossierExpertisePicture::TYPE_FUEL_LEVEL);
            if ($picture) {
                $group->updateField('fuel_level_picture', 'value', $picture->getUrl());
            }
        }

        $group = $this->builderFields->getField('group_tires');
        if ($group instanceof BuilderFormGroup) {
            $picture = $expertise->getPicture(DossierExpertisePicture::TYPE_FRONT_TIRE);
            if ($picture) {
                $group->updateField('front_tire_picture', 'value', $picture->getUrl());
            }

            $picture = $expertise->getPicture(DossierExpertisePicture::TYPE_REAR_TIRE);
            if ($picture) {
                $group->updateField('rear_tire_picture', 'value', $picture->getUrl());
            }
        }
    }

    private function addLevelEngineOil(?DossierExpertiseVehicle $dossierExpertiseVehicle = null)
    {
        $field = new BuilderFormField('level_engine_oil');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('
<div class="form-group form-group-level-engine-oil pb-10">
    <label class="control-label" for="nouislider_level_engine_oil">' . __('Niveau d\'huile moteur') . '</label>
    <input type="hidden" class="form-control" id="nouislider_level_engine_oil_input" name="level_engine_oil" value="' . ($dossierExpertiseVehicle ? $dossierExpertiseVehicle->getLevelEngineOil() : 0) . '">
    <div id="nouislider_level_engine_oil" class="nouislider nouislider-car-level" data-value="' . ($dossierExpertiseVehicle ? $dossierExpertiseVehicle->getLevelEngineOil() : 0) . '"></div>
</div>');
        $this->builderFields->addField($field);

        $options = [
            [
                'value' => true,
                'label' => __('Cochez cette case si le niveau d\'huile n\'est pas lisible à la jauge'),
                'checked' => ($dossierExpertiseVehicle and $dossierExpertiseVehicle->getLevelEngineOilUnreadable()),
            ]
        ];
        $field = new BuilderFormField('level_engine_oil_unreadable');
        $field->setType(FieldsEnum::TYPE_CHECKBOX);
        $field->setOptions($options);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('engine_oil_level_picture');
        $field->setType(FieldsEnum::TYPE_DROPZONE);
        $field->setLabel(__('Photo du niveau d\'huile moteur'));
        $field->setData([
            /*'accepted-files' => 'image/*',*/
            'nb-files' => 1,
        ]);
        $this->builderFields->addField($field);
    }

    private function addLevelCoolant(?DossierExpertiseVehicle $dossierExpertiseVehicle = null)
    {
        $group = new BuilderFormGroup('group_level_coolant');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Niveau du liquide de refroidissement'));
        $group->setOpened(true);

        $field = new BuilderFormField('level_coolant');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('
<div class="form-group form-group-level-coolant pb-10">
    <label class="control-label" for="nouislider_level_coolant">' . __('Niveau du liquide de refroidissement') . '</label>
    <input type="hidden" class="form-control" id="nouislider_level_coolant_input" name="level_coolant" value="' . ($dossierExpertiseVehicle ? $dossierExpertiseVehicle->getLevelCoolant() : 1) . '">
    <div id="nouislider_level_coolant" class="nouislider nouislider-car-level" data-value="' . ($dossierExpertiseVehicle ? $dossierExpertiseVehicle->getLevelCoolant() : 1) . '"></div>
</div>');
        $group->addField($field);

        $options = [
            [
                'value' => true,
                'label' => __('Cochez cette case si le niveau du liquide de refroidissement ne peut être observé dans le vase d\'expansion'),
                'checked' => ($dossierExpertiseVehicle and $dossierExpertiseVehicle->getLevelCoolantUnreadable()),
            ]
        ];
        $field = new BuilderFormField('level_coolant_unreadable');
        $field->setType(FieldsEnum::TYPE_CHECKBOX);
        $field->setOptions($options);
        $group->addField($field);

        $field = new BuilderFormField('coolant_level_picture');
        $field->setType(FieldsEnum::TYPE_DROPZONE);
        $field->setLabel(__('Photo du niveau du liquide de refroidissement'));
        $field->setData([
            /*'accepted-files' => 'image/*',*/
            'nb-files' => 1,
        ]);
        $group->addField($field);

        $this->builderFields->addField($group);
    }

    private function addLevelBrake(?DossierExpertiseVehicle $dossierExpertiseVehicle = null)
    {
        $group = new BuilderFormGroup('group_level_brake');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Niveau du liquide de frein'));
        $group->setOpened(true);

        $field = new BuilderFormField('level_brake');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('
<div class="form-group form-group-level-brake pb-10">
    <label class="control-label" for="nouislider_level_brake">' . __('Niveau du liquide de frein') . '</label>
    <input type="hidden" class="form-control" id="nouislider_level_brake_input" name="level_brake" value="' . ($dossierExpertiseVehicle ? $dossierExpertiseVehicle->getLevelBrake() : 1) . '">
    <div id="nouislider_level_brake" class="nouislider nouislider-car-level" data-value="' . ($dossierExpertiseVehicle ? $dossierExpertiseVehicle->getLevelBrake() : 1) . '"></div>
</div>');
        $group->addField($field);

        $options = [
            [
                'value' => true,
                'label' => __('Cochez cette case si le niveau du liquide de frein n\'est pas visible dans le bocal'),
                'checked' => ($dossierExpertiseVehicle and $dossierExpertiseVehicle->getLevelBrakeUnreadable()),
            ]
        ];
        $field = new BuilderFormField('level_brake_unreadable');
        $field->setType(FieldsEnum::TYPE_CHECKBOX);
        $field->setOptions($options);
        $group->addField($field);

        $field = new BuilderFormField('brake_level_picture');
        $field->setType(FieldsEnum::TYPE_DROPZONE);
        $field->setLabel(__('Photo du niveau du liquide de frein'));
        $field->setData([
            /*'accepted-files' => 'image/*',*/
            'nb-files' => 1,
        ]);
        $group->addField($field);

        $this->builderFields->addField($group);
    }

    private function addLevelFuel(?DossierExpertiseVehicle $dossierExpertiseVehicle = null)
    {
        $group = new BuilderFormGroup('group_level_fuel');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Niveau de carburant'));
        $group->setOpened(true);

        $field = new BuilderFormField('level_fuel');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('
<div class="form-group form-group-level-fuel pb-10">
    <label class="control-label" for="nouislider_level_fuel">' . __('Niveau de carburant') . '</label>
    <input type="hidden" class="form-control" id="nouislider_level_fuel_input" name="level_fuel" value="' . ($dossierExpertiseVehicle ? $dossierExpertiseVehicle->getLevelFuel() : 1) . '">
    <div id="nouislider_level_fuel" class="nouislider nouislider-car-level car-level-fuel" data-value="' . ($dossierExpertiseVehicle ? $dossierExpertiseVehicle->getLevelFuel() : 1) . '"></div>
</div>');
        $group->addField($field);

        $field = new BuilderFormField('fuel_level_picture');
        $field->setType(FieldsEnum::TYPE_DROPZONE);
        $field->setLabel(__('Photo du niveau de carburant'));
        $field->setData([
            /*'accepted-files' => 'image/*',*/
            'nb-files' => 1,
        ]);
        $group->addField($field);

        $field = new BuilderFormField('level_comment');
        $field->setType(FieldsEnum::TYPE_TEXTAREA);
        $field->setLabel(__('Commentaire sur les niveaux'));
        $group->addField($field);

        $field = new BuilderFormField('level_ignore');
        $field->setType(FieldsEnum::TYPE_SWITCH);
        $field->setLabel(__('Ignorer les niveaux'));
        $field->setHelp(__('Si vous activez cette option, les niveaux ne seront pas pris en compte et n\'apparaîtront ni dans le PV ni dans le rapport d\'expertise'));
        $field->setOptions([
            'on' => 'valid',
            'off' => 'invalid',
        ]);
        $field->setData([
            'label_active' => __('Ignorer les niveaux'),
            'label_desactive' => __('Ne pas ignorer les niveaux'),
        ]);
        $group->addField($field);

        $this->builderFields->addField($group);
    }

    private function addTires(?DossierExpertiseVehicle $dossierExpertiseVehicle = null)
    {
        $group = new BuilderFormGroup('group_tires');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Roues du véhicule'));
        $group->setOpened(true);

        $nbWheels = ($dossierExpertiseVehicle ? $dossierExpertiseVehicle->getWheelsNumber() : 4);

        $options = [];
        $options[] = ['value' => 2, 'label' => 2];
        $options[] = ['value' => 3, 'label' => 3];
        $options[] = ['value' => 4, 'label' => 4];
        $options[] = ['value' => 6, 'label' => 6];
        $options[] = ['value' => 8, 'label' => 8];
        $field = new BuilderFormField('wheels_number');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Nombre de roues'));
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $select->setValue($nbWheels);
        $field->setSelect($select);
        $field->setData(['rel' => '']);
        $group->addField($field);

        $field = new BuilderFormField('tire_wear_content');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('<div class="form-group"><label class="control-label" for="tire_wear">' . __('Taux d\'usure des pneus') . ' *</label><div class="controls"><div class="d-flex flex-row justify-content-between2">');
        $group->addField($field);

        $tires = [
            'Pneu 1 (avg)',
            'Pneu 2 (avd)',
            'Pneu 3 (arg)',
            'Pneu 4 (ard)',
            'Pneu 5 (intarg)',
            'Pneu 6 (intard)',
            'Pneu 7 (divers)',
            'Pneu 8 (divers)',
        ];
        $tireWears = ($dossierExpertiseVehicle ? explode(';', $dossierExpertiseVehicle->getTireWear()) : []);
        foreach ($tires as $id => $tire) {
            $field = new BuilderFormField('tire_wear_content' . $id);
            $field->setType(FieldsEnum::TYPE_CONTENT);
            $field->setContent('<div class="tire-wear tire-wear-' . $id . ' mr-4 ' . ($id >= $nbWheels ? 'd-none' : '') . '">');
            $group->addField($field);

            $options = [];
            for ($i = 0; $i <= 100; $i += 5) {
                $options[] = ['value' => $i, 'label' => $i . '%'];
            }
            $field = new BuilderFormField('tire_wear[' . $id . ']');
            $field->setType(FieldsEnum::TYPE_SELECT);
            $field->setLabel($tire);
            $select = new BuilderFormSelect();
            $select->setOptions($options);
            $select->setValue($tireWears[$id] ?? 0);
            $field->setSelect($select);
            $field->setData(['rel' => '']);
            $group->addField($field);

            $field = new BuilderFormField('tire_wear_content' . $id . '-1');
            $field->setType(FieldsEnum::TYPE_CONTENT);
            $field->setContent('</div>');
            $group->addField($field);
        }

        $field = new BuilderFormField('tire_wear_content_end');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('</div></div></div>');
        $group->addField($field);


        $field = new BuilderFormField('front_tire_picture');
        $field->setType(FieldsEnum::TYPE_DROPZONE);
        $field->setLabel(__('Photo des pneumatiques avant'));
        $field->setData([
            /*'accepted-files' => 'image/*',*/
            'nb-files' => 1,
        ]);
        $group->addField($field);

        $field = new BuilderFormField('rear_tire_picture');
        $field->setType(FieldsEnum::TYPE_DROPZONE);
        $field->setLabel(__('Photo des pneumatiques arrière'));
        $field->setData([
            /*'accepted-files' => 'image/*',*/
            'nb-files' => 1,
        ]);
        $group->addField($field);


        $field = new BuilderFormField('tire_ignore');
        $field->setType(FieldsEnum::TYPE_SWITCH);
        $field->setLabel(__('Ignorer les pneus'));
        $field->setHelp(__('Si vous activez cette option, les informations concernant les pneus n\'apparaîtront ni dans le PV ni dans le rapport d\'expertise'));
        $field->setOptions([
            'on' => 'valid',
            'off' => 'invalid',
        ]);
        $field->setData([
            'label_active' => __('Ignorer les pneus'),
            'label_desactive' => __('Ne pas ignorer les pneus'),
        ]);
        $group->addField($field);

        $field = new BuilderFormField('additional_wheel_comment');
        $field->setType(FieldsEnum::TYPE_TEXTAREA);
        $field->setLabel(__('Commentaires roues supplémentaires'));
        $group->addField($field);

        $this->builderFields->addField($group);
    }

    private function addTiresCourrier(?DossierExpertise $dossierExpertise = null)
    {
        $subject = '';
        $content = '';
        if ($dossierExpertise) {
            $container = ContainerBuilderService::getInstance();
            $document = $container->get(DossierDocumentService::class)->getRepository()->findOneBy(['dossier' => $dossierExpertise->getDossier(), 'expertise' => $dossierExpertise, 'type' => DossierDocument::TYPE_COURRIER_TIRES]);
            if ($document) {
                $subject = $document->getSubject();
                $content = $document->getFirstPart();
            }
        }

        $group = new BuilderFormGroup('group_tires_courrier');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Courrier de dangerosité'));
        $group->setOpened(true);

        $field = new BuilderFormField('generate_tires_courrier');
        $field->setType(FieldsEnum::TYPE_SWITCH);
        $field->setLabel(__('Générer un courrier de dangerosité'));
        $field->setOptions([
            'on' => 'valid',
            'off' => 'invalid',
        ]);
        $field->setData([
            'label_active' => __('Générer un courrier de dangerosité'),
            'label_desactive' => __('Ne pas générer un courrier de dangerosité'),
        ]);
        $field->setValue('invalid');
        $group->addField($field);

        $field = new BuilderFormField('generate_tires_courrier_content1');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('<div class="generate_tires_courrier_content d-none">');
        $group->addField($field);

        $field = new BuilderFormField('tires_courrier_subject');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Objet'));
        $field->setValue(($subject ?: __('Objet : Dangerosité des pneus')));
        $group->addField($field);

        if (!$content) {
            $content = '';
            if ($dossierExpertise) {
                $vehicleOwner = $dossierExpertise->getDossier()->getVehicleOwner();
                if ($vehicleOwner) {
                    $content = '<p>A l\'attention de ' . $vehicleOwner->getFirstName() . ' ' . $vehicleOwner->getLastName() . '</p>';
                }
            }
            $content .= '<p>Madame, Monsieur</p>';
            $content .= '<p>Dans le cadre de l’expertise du véhicule cité en objet, nous tenons à vous informer ou confirmer ...</p>';
            $content .= '<p>Nous vous conseillons ...</p>';
            $content .= '<p>Nous vous prions d\'agréer, l\'expression de notre considération distinguée.</p>';
        }
        $field = new BuilderFormField('tires_courrier_content');
        $field->setType(FieldsEnum::TYPE_CKEDITOR_SMALL);
        $field->setLabel(__('Contenu du courrier'));
        $field->setValue($content);
        $group->addField($field);

        $field = new BuilderFormField('generate_tires_courrier_content2');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('</div>');
        $group->addField($field);

        $this->builderFields->addField($group);
    }
}
