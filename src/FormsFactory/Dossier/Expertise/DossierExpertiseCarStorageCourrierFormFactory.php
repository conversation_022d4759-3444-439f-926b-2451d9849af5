<?php
namespace Mat<PERSON>yver\FormsFactory\Dossier\Expertise;

use Mat<PERSON>yver\Entity\Dossier\DossierDocument;
use Mat<PERSON>yver\Entity\Dossier\Expertise\DossierExpertise;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Forms\Dossier\Expertise\DossierExpertiseCarStorageCourrierForm;
use MatGyver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormSelect;
use MatGyver\Helpers\Number;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Dossier\Expertise\DossierExpertisePersonService;

class DossierExpertiseCarStorageCourrierFormFactory extends AbstractFormFactory
{
    /**
     * @param DossierExpertise|null $expertise
     */
    public function __construct(?DossierExpertise $expertise = null)
    {
        parent::__construct();
        $this->setFormService(DossierExpertiseCarStorageCourrierForm::class);

        $this->addPerson($expertise);
        $this->addContent($expertise);

        $this->setTitle(__('Création d\'un courrier de frais de gardiennage'));
        if ($expertise) {
            $this->addHiddenFields('expertise_id', $expertise->getId());
            $this->setCancelUrl(Tools::makeLink('app', 'dossier', 'surcharges/' . $expertise->getDossier()->getId()));
        }
    }

    /**
     * @param DossierDocument|null $dossierDocument
     */
    public function preRenderFields(?DossierDocument $dossierDocument)
    {
        if ($dossierDocument) {
            $this->setTitle(__('Modification d\'un courrier de frais de gardiennage'));

            $this->builderFields->updateField('subject', 'value', $dossierDocument->getSubject());
            $this->builderFields->updateField('first_part', 'value', $dossierDocument->getFirstPart());

            if ($dossierDocument->getPerson()) {
                $select = $this->builderFields->getField('person_id')->getSelect();
                $select->setParam2($dossierDocument->getPerson()->getId());
            }
        }
    }

    /**
     * @param DossierExpertise|null $expertise
     * @return void
     */
    private function addPerson(?DossierExpertise $expertise = null)
    {
        $field = new BuilderFormField('person_id');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Destinataire'));
        $field->setHelp(__('Le courrier ne sera pas envoyé automatiquement à ce destinataire, vous pourrez l\'envoyer manuellement quand vous le souhaiterez.'));
        $select = new BuilderFormSelect();
        $select->setClass(DossierExpertisePersonService::class);
        $select->setFunction('generateSelectOptions');
        if ($expertise) {
            $select->setParam($expertise);
        }
        $field->setSelect($select);
        $field->setPlaceholder(__('Sélectionnez un destinataire'));
        $this->builderFields->addField($field);
    }

    /**
     * @param DossierExpertise|null $expertise
     * @return void
     */
    private function addContent(?DossierExpertise $expertise = null)
    {
        $field = new BuilderFormField('subject');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Objet'));
        $field->setValue(__('Objet : Frais de gardiennage'));
        $this->builderFields->addField($field);

        $carStorageDate = '.. /.. /..';
        $carStorageFees = '... €';
        if ($expertise) {
            $vehicle = $expertise->getDossier()->getVehicle();
            if ($vehicle->getCarStorageDate()) {
                $carStorageDate = $vehicle->getCarStorageDate()->format('d/m/Y');
            }
            if ($vehicle->getCarStorageFees()) {
                $carStorageFees = Number::formatAmount($vehicle->getCarStorageFees(), DEFAULT_CURRENCY);
            }
        }
        $content = '
        <p>Madame, Monsieur,</p>
        <p>Dans le cadre de l’expertise qui nous a été confiée, nous avons été informé que le dépositaire (lieu d’expertise) du véhicule en objet facture à partir du ' . $carStorageDate . '. des frais de gardiennage à hauteur de ' . $carStorageFees . ' TTC par jour d’immobilisation dans son établissement.</p>
        <p>Nous tenions à vous transmettre ou rappeler cette information afin que vous puissiez prendre vos dispositions ou décisions.</p>
        <p>Nous vous prions d\'agréer, l\'expression de notre considération distinguée.</p>';

        $field = new BuilderFormField('first_part');
        $field->setType(FieldsEnum::TYPE_CKEDITOR_SMALL);
        $field->setLabel(__('Contenu du courrier'));
        $field->setValue($content);
        $this->builderFields->addField($field);
    }
}
