<?php
namespace MatGyver\FormsFactory\Dossier\Expertise;

use Mat<PERSON><PERSON><PERSON>\Entity\Dossier\Expertise\DossierExpertise;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Entity\Dossier\Expertise\DossierExpertiseJudiciaire;
use MatGyver\Forms\Dossier\Expertise\DossierExpertiseJudiciaireCourtMissionForm;
use MatGyver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;

class DossierExpertiseJudiciaireCourtMissionFormFactory extends AbstractFormFactory
{
    public function __construct(?DossierExpertise $expertise = null)
    {
        parent::__construct();
        $this->setFormService(DossierExpertiseJudiciaireCourtMissionForm::class);

        $this->addMission();

        $this->setTitle(__('Expertise judiciaire - Mission du tribunal'));
        $this->setBtnValidateText(__('Valider et passer à l\'étape suivante'));
        if ($expertise) {
            $this->addHiddenFields('expertise_id', $expertise->getId());
        }
    }

    /**
     * @param DossierExpertiseJudiciaire|null $dossierExpertiseJudiciaire
     */
    public function preRenderFields(?DossierExpertiseJudiciaire $dossierExpertiseJudiciaire)
    {
        if ($dossierExpertiseJudiciaire) {
            //$this->builderFields->updateField('court_mission', 'value', $dossierExpertiseJudiciaire->getCourtMission());

            $interventionComment = $dossierExpertiseJudiciaire->getExpertise()->getDossier()->getInterventionComment();
            if (str_contains($dossierExpertiseJudiciaire->getExpertise()->getDossier()->getInterventionComment(), "\n")) {
                $interventionComment = nl2br($dossierExpertiseJudiciaire->getExpertise()->getDossier()->getInterventionComment());
            }
            $this->builderFields->updateField('court_mission', 'value', $interventionComment);
        }
    }

    private function addMission()
    {
        $field = new BuilderFormField('court_mission');
        $field->setType(FieldsEnum::TYPE_CKEDITOR_SMALL);
        $field->setLabel(__('Mission du tribunal'));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('audio_recorder');
        $field->setType(FieldsEnum::TYPE_AUDIO_RECORDER);
        $field->setLabel(__('Enregistreur audio'));
        $field->setData(['target' => 'court_mission']);
        $this->builderFields->addField($field);
    }
}
