<?php
namespace <PERSON><PERSON><PERSON>ver\FormsFactory\Dossier\Expertise;

use Mat<PERSON>yver\Entity\Dossier\DossierDocument;
use Mat<PERSON>yver\Entity\Dossier\Expertise\DossierExpertise;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Forms\Dossier\Expertise\DossierExpertiseDireIntroductionForm;
use MatGyver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\Helpers\Number;
use MatGyver\Services\Dossier\DossierPaymentService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseVehicleService;

class DossierExpertiseDireIntroductionFormFactory extends AbstractFormFactory
{
    /**
     * @param DossierExpertise|null $expertise
     */
    public function __construct(?DossierExpertise $expertise = null)
    {
        parent::__construct();
        $this->setFormService(DossierExpertiseDireIntroductionForm::class);

        $this->addIntroduction($expertise);

        $this->setTitle(__('Introduction d\'un dire technique'));
        $this->setBtnValidateText(__('Valider et passer à l\'étape suivante'));
        if ($expertise) {
            $this->addHiddenFields('expertise_id', $expertise->getId());
        }
    }

    /**
     * @param DossierDocument|null $dossierDocument
     */
    public function preRenderFields(?DossierDocument $dossierDocument)
    {
        if ($dossierDocument) {
            if ($dossierDocument->getFirstPart()) {
                $data = json_decode($dossierDocument->getFirstPart(), true);
                if (isset($data['introduction']) and $data['introduction']) {
                    $this->builderFields->updateField('introduction', 'value', $data['introduction']);
                }
            }
        }
    }

    /**
     * @param DossierExpertise|null $expertise
     * @return void
     */
    private function addIntroduction(?DossierExpertise $expertise = null)
    {
        $content = '';
        if ($expertise) {
            $dossier = $expertise->getDossier();

            $vehicle = $expertise->getDossier()->getVehicle();
            $vehicleOwner = $expertise->getDossier()->getVehicleOwner();
            $immobilizationPlaceType = $vehicle->getImmobilizationPlaceType();
            if ($immobilizationPlaceType == DossierExpertise::PLACE_TYPE_HOME) {
                $immobilizationPlace = __('au domicile du propriétaire du véhicule');
                $immobilizationPlace .= ' (' . $vehicleOwner->getAddress() . ' - ' . $vehicleOwner->getZip() . ' ' . $vehicleOwner->getCity() . ')';
            } elseif ($immobilizationPlaceType == DossierExpertise::PLACE_TYPE_CONTACT) {
                $contact = $expertise->getDossier()->getContact();
                if ($contact->getPlace()) {
                    $immobilizationPlace = 'à ' . $contact->getPlace()->getName();
                    $immobilizationPlace .= ' (' . $contact->getPlace()->getAddress() . ', ' . $contact->getPlace()->getZip() . ' ' . $contact->getPlace()->getCity() . ')';
                } else {
                    $immobilizationPlace = 'à ' . $contact->getCompanyOrName();
                    $immobilizationPlace .= ' (' . $contact->getAddress() . ', ' . $contact->getZip() . ' ' . $contact->getCity() . ')';
                }
            } elseif ($immobilizationPlaceType == DossierExpertise::PLACE_TYPE_MANDATE) {
                $mandate = $expertise->getDossier()->getMandate();
                if ($mandate->getPlace()) {
                    $immobilizationPlace = 'à ' . $mandate->getPlace()->getName();
                    $immobilizationPlace .= ' (' . $mandate->getPlace()->getAddress() . ', ' . $mandate->getPlace()->getZip() . ' ' . $mandate->getPlace()->getCity() . ')';
                } else {
                    $immobilizationPlace = 'à ' . $mandate->getName();
                    $immobilizationPlace .= ' (' . $mandate->getAddress() . ', ' . $mandate->getZip() . ' ' . $mandate->getCity() . ')';
                }
            } elseif ($vehicle->getImmobilizationPlaceType() == DossierExpertise::PLACE_TYPE_ROLLING) {
                $immobilizationPlace = '...';
            } elseif ($vehicle->getImmobilizationPlace()) {
                $immobilizationPlace = 'à ' . $vehicle->getImmobilizationPlace()->getName() . ' (';
                $immobilizationPlace .= $vehicle->getImmobilizationPlace()->getAddress() . ', ' . $vehicle->getImmobilizationPlace()->getZip() . ' ' . $vehicle->getImmobilizationPlace()->getCity() . ')';
            } else {
                $immobilizationPlace = 'à ' . $vehicle->getImmobilizationPlaceName() . ' (';
                $immobilizationPlace .= $vehicle->getImmobilizationAddress() . ', ' . $vehicle->getImmobilizationZip() . ' ' . $vehicle->getImmobilizationCity() . ')';
            }

            $content .= '<p>Nous faisons suite aux opérations d’expertise sur le véhicule immatriculé ' . $expertise->getDossier()->getVehicle()->getRegistration() . '.<br>';
            $content .= 'Le véhicule est immobilisé ' . $immobilizationPlace . ' depuis le ... suite à ...</p>';

            $content .= '<p><strong>Pour rappel : </strong></p><p>';

            $vehicle = $dossier->getVehicle();
            if ($vehicle->getBuyDate()) {
                $content .= "Date d'achat : " . $vehicle->getBuyDate()->format('d/m/Y') . '<br>';
            }
            if ($vehicle->getBuyPrice()) {
                $content .= "Montant d'achat : " . Number::formatAmount($vehicle->getBuyPrice(), DEFAULT_CURRENCY) . '<br>';
                if ($vehicle->getBuyPayment()) {
                    $content .= "Mode de paiement : " . DossierPaymentService::getType($vehicle->getBuyPayment()) . '<br>';
                }
            }
            if ($vehicle->getBuyMileage()) {
                $content .= "Kilométrage à l'achat : " . $vehicle->getBuyMileage() . " Km<br>";
            }

            //breakdown
            $breakdownsOrigins = [];
            if ($dossier->getBreakdown() and $dossier->getBreakdown()->getOrigins()) {
                foreach ($dossier->getBreakdown()->getOrigins() as $origin) {
                    $breakdownsOrigins[] = $origin->getName();
                }
            }
            if ($breakdownsOrigins) {
                $content .= n__("Objet de la panne ou du litige", "Objets de la panne ou du litige", count($breakdownsOrigins)) . ' : ' . implode(', ', $breakdownsOrigins) . '<br>';
            }
            if ($dossier->getBreakdown() and $dossier->getBreakdown()->getDescription()) {
                $content .= 'Description de la panne ou du litige : ' . nl2br($dossier->getBreakdown()->getDescription()) . '<br>';
            }

            if ($dossier->getBreakdown() and $dossier->getBreakdown()->getDate()) {
                $content .= "Date de la panne ou du litige : " . $dossier->getBreakdown()->getDate()->format('d/m/Y') . '<br>';
            }
            if ($dossier->getBreakdown() and $dossier->getBreakdown()->getMileage()) {
                $content .= "Kilométrage de la panne ou du litige : " . $dossier->getBreakdown()->getMileage() . " Km<br>";
            }
            if ($expertise->getExpertiseVehicle() and $expertise->getExpertiseVehicle()->getMeter()) {
                $content .= 'Kilométrage lors de l\'expertise : ' . $expertise->getExpertiseVehicle()->getMeter() . ' ' . DossierExpertiseVehicleService::getMeterType($expertise->getExpertiseVehicle()->getMeterType()) . ' ' . DossierExpertiseVehicleService::getMeterRead($expertise->getExpertiseVehicle()->getMeterRead()) . '<br>';
            }

            $content .= '</p>';
        }

        $field = new BuilderFormField('introduction');
        $field->setType(FieldsEnum::TYPE_CKEDITOR_SMALL);
        $field->setLabel(__('Introduction du dire technique'));
        $field->setHelp(__('Juste après cette introduction, seront affichés le rappel des faits et les constatations'));
        $field->setValue($content);
        $this->builderFields->addField($field);
    }
}
