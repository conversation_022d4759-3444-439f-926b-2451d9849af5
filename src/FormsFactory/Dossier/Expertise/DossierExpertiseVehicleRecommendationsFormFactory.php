<?php
namespace Mat<PERSON>yver\FormsFactory\Dossier\Expertise;

use Mat<PERSON>yver\Entity\Dossier\DossierDocument;
use Mat<PERSON>yver\Entity\Dossier\Expertise\DossierExpertise;
use MatGyver\Enums\FieldsEnum;
use MatG<PERSON>ver\Entity\Dossier\Expertise\DossierExpertiseVehicle;
use MatGyver\Forms\Dossier\Expertise\DossierExpertiseVehicleRecommendationsForm;
use MatG<PERSON>ver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormSelect;
use MatGyver\Helpers\Tools;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Dossier\DossierDocumentService;

class DossierExpertiseVehicleRecommendationsFormFactory extends AbstractFormFactory
{
    public function __construct(?DossierExpertise $expertise = null)
    {
        parent::__construct();
        $this->setFormService(DossierExpertiseVehicleRecommendationsForm::class);

        $this->addEngineOil();
        $this->addCrankcaseCapacity();
        $this->addRecommendations();
        $this->addDocument();

        $this->setTitle(__('Préconisations du constructeur'));
        if ($expertise) {
            $this->addHiddenFields('expertise_id', $expertise->getId());
            $this->setPrevPageUrl(Tools::makeLink('app', 'dossier', 'expertise/breakdown/' . $expertise->getId()));
        }
        $this->setBtnValidateText(__('Valider et passer à l\'étape suivante'));
    }

    /**
     * @param DossierExpertiseVehicle|null $dossierExpertiseVehicle
     */
    public function preRenderFields(?DossierExpertiseVehicle $dossierExpertiseVehicle)
    {
        if ($dossierExpertiseVehicle->getEngineOil()) {
            $engineOil = $dossierExpertiseVehicle->getEngineOil();
            if (isset($engineOil['change_kms'])) {
                $this->builderFields->getField('engine_oil_change_kms_')->setValue($engineOil['change_kms']);
            }
            if (isset($engineOil['change_km_type']) and $engineOil['change_km_type']) {
                $this->builderFields->getField('engine_oil_change_km_type_')->getSelect()->setValue($engineOil['change_km_type']);
            }
            if (isset($engineOil['change_time'])) {
                $this->builderFields->getField('engine_oil_change_time_')->setValue($engineOil['change_time']);
            }
            if (isset($engineOil['change_time_type'])) {
                $select = $this->builderFields->getField('engine_oil_change_time_type_')->getSelect();
                $select->setValue($engineOil['change_time_type']);
            }
            if (isset($engineOil['first_due_date']) and !$engineOil['first_due_date']) {
                $options = [
                    [
                        'value' => true,
                        'label' => __('au premier terme échu'),
                        'checked' => false
                    ]
                ];
                $this->builderFields->updateField('engine_oil_first_due_date_', 'options', $options);
            }
            if (isset($engineOil['viscosity'])) {
                $this->builderFields->getField('engine_oil_viscosity_')->setValue($engineOil['viscosity']);
            }
            if (isset($engineOil['standard'])) {
                $this->builderFields->getField('engine_oil_standard_')->setValue($engineOil['standard']);
            }
            if (isset($engineOil['crankcase_qty'])) {
                $this->builderFields->getField('engine_oil_crankcase_qty_')->setValue($engineOil['crankcase_qty']);
            }
            if (isset($engineOil['qty_with_filter'])) {
                $this->builderFields->getField('engine_oil_qty_with_filter_')->setValue($engineOil['qty_with_filter']);
            }
        }

        if ($dossierExpertiseVehicle) {
            $container = ContainerBuilderService::getInstance();
            $document = $container->get(DossierDocumentService::class)->getByTypeAndExpertise($dossierExpertiseVehicle->getExpertise(), DossierDocument::TYPE_PRECONISATIONS_DU_CONSTRUCTEUR);
            if ($document) {
                $this->builderFields->getField('document')->setValue($document->getUrl());
            }
        }
    }

    public function addEngineOil()
    {
        $field = new BuilderFormField('engine_oil_content1');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('<div class="d-flex flex-column flex-md-row w-100"><div class="mr-md-2">');
        $this->builderFields->addField($field);

        $field = new BuilderFormField('engine_oil[change_kms]');
        $field->setType(FieldsEnum::TYPE_INT);
        $field->setLabel(__('Vidange moteur et filtre à huile tous les'));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('engine_oil_content1_1');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('</div><div class="d-flex flex-row mr-8">');
        $this->builderFields->addField($field);

        $options = [];
        $options[] = ['value' => DossierDocument::MILEAGE_TYPE_METERS, 'label' => 'km'];
        $options[] = ['value' => DossierDocument::MILEAGE_TYPE_MILES, 'label' => 'miles'];
        $options[] = ['value' => DossierDocument::MILEAGE_TYPE_HOURS, 'label' => __('heures')];
        $field = new BuilderFormField('engine_oil[change_km_type]');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel('&nbsp;');
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $field->setSelect($select);
        $field->setData(['rel' => '']);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('engine_oil_content2');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('</div><div class="d-flex flex-row mr-8">');
        $this->builderFields->addField($field);

        $field = new BuilderFormField('engine_oil[change_time]');
        $field->setType(FieldsEnum::TYPE_FLOAT);
        $field->setLabel(__('ou tous les'));
        $this->builderFields->addField($field);

        /*$field = new BuilderFormField('engine_oil_content3');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('</div><div class="mr-md-8">');
        $this->builderFields->addField($field);*/

        $options = [];
        $options[] = ['value' => 'year', 'label' => __('ans')];
        $options[] = ['value' => 'month', 'label' => __('mois')];

        $field = new BuilderFormField('engine_oil[change_time_type]');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel('&nbsp;');
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $field->setSelect($select);
        $field->setData(['rel' => '']);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('engine_oil_content4');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('</div><div class="pt-md-10">');
        $this->builderFields->addField($field);

        $options = [
            [
                'value' => true,
                'label' => __('au premier terme échu'),
                'checked' => true
            ]
        ];
        $field = new BuilderFormField('engine_oil[first_due_date]');
        $field->setType(FieldsEnum::TYPE_CHECKBOX);
        $field->setOptions($options);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('engine_oil_content5');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('</div></div>');
        $this->builderFields->addField($field);

        $field = new BuilderFormField('engine_oil[viscosity]');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Viscosité Huile'));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('engine_oil[standard]');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Norme Huile'));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('engine_oil[crankcase_qty]');
        $field->setType(FieldsEnum::TYPE_FLOAT);
        $field->setLabel(__('Quantité d\'huile présente dans le carter (L)'));
        $field->setData(['size' => 'input-sm']);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('engine_oil[qty_with_filter]');
        $field->setType(FieldsEnum::TYPE_FLOAT);
        $field->setLabel(__('Quantité d’huile avec filtre'));
        $field->setData(['size' => 'input-sm']);
        $this->builderFields->addField($field);
    }

    public function addCrankcaseCapacity()
    {
        $field = new BuilderFormField('crankcase_capacity');
        $field->setType(FieldsEnum::TYPE_FLOAT);
        $field->setLabel(__('Capacité du carter (L)'));
        $field->setData(['size' => 'input-sm']);
        $this->builderFields->addField($field);
    }

    private function addRecommendations()
    {
        $field = new BuilderFormField('recommendations');
        $field->setType(FieldsEnum::TYPE_TEXTAREA);
        $field->setLabel(__('Autres préconisations du constructeur'));
        $field->setClass('autosize');
        $field->setData([
            'presets' => [
                'Préconisations huile boite à vitesses : ',
                'Capacité huile boite à vitesses : … litres',
                'Remplacement filtre à air tous les … ou … km',
                'Remplacement filtre à carburant tous les … ans ou … km',
                'Remplacement des bougies tous les … ans ou … km',
                'Remplacement liquide de frein tous les … ans ou … km',
                'Remplacement liquide de refroidissement tous les … ans ou … km',
                'Remplacement courroie de distribution au premier terme échu',
                'Kit de distribution tous les … ans ou … km',
            ]
        ]);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('audio_recorder');
        $field->setType(FieldsEnum::TYPE_AUDIO_RECORDER);
        $field->setLabel(__('Enregistreur audio'));
        $field->setData(['target' => 'recommendations']);
        $this->builderFields->addField($field);
    }

    private function addDocument()
    {
        $field = new BuilderFormField('document');
        $field->setType(FieldsEnum::TYPE_DROPZONE);
        $field->setLabel(__('Document'));
        $field->setData([
            'title' => __('Glissez ici votre pièce jointe'),
            'nb-files' => 1,
        ]);
        $this->builderFields->addField($field);
    }
}
