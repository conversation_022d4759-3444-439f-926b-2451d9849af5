<?php
namespace Mat<PERSON>yver\FormsFactory\Dossier\Site;

use Mat<PERSON><PERSON><PERSON>\Entity\Dossier\Dossier;
use MatGyver\Enums\FieldsEnum;
use Mat<PERSON>yver\Entity\Dossier\DossierInstitution;
use MatGyver\Forms\Dossier\Site\SiteDossierInstitutionForm;
use Mat<PERSON><PERSON>ver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormGroup;
use MatGyver\FormsFactory\Builder\BuilderFormSelect;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Dossier\DossierInstitutionService;

class SiteDossierInstitutionFormFactory extends AbstractFormFactory
{
    /**
     * @param Dossier|null $dossier
     * @param DossierInstitution[] $dossierInstitutions
     */
    public function __construct(?Dossier $dossier = null, array $dossierInstitutions = [])
    {
        parent::__construct();
        $this->setFormService(SiteDossierInstitutionForm::class);

        $field = new BuilderFormField('temp');
        $field->setType(FieldsEnum::TYPE_HIDDEN);
        $field->setValue('temp');
        $this->builderFields->addField($field);

        $this->addContent();
        for ($i = 0; $i <= 4; $i++) {
            $this->addInstitution($i, $dossierInstitutions[$i] ?? null);
        }

        $this->setTitle(__('Établissements à convoquer'));
        $this->setBtnValidateText(__('Valider et passer à l\'étape suivante'));
        if ($dossier) {
            $this->addHiddenFields('dossier_reference', $dossier->getReference());
            //$this->setCancelUrl(Tools::makeLink('site', 'dossier', 'institutions/' . $dossier->getReference()));
        }

        Assets::addJs('common/addresses.js');
    }

    /**
     * @param DossierInstitution|null $dossierInstitution
     */
    public function preRenderFields(?DossierInstitution $dossierInstitution)
    {
        if ($dossierInstitution) {

        }
    }

    public function addContent()
    {
        $field = new BuilderFormField('content');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('
        <div class="alert alert-custom alert-white fade show pl-2 mb-0" role="alert">
            <div class="alert-icon align-items-start">
                <span class="svg-icon svg-icon-primary svg-icon-xl">
                   <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <rect x="0" y="0" width="24" height="24"/>
                            <circle fill="#000000" opacity="0.3" cx="12" cy="12" r="10"/>
                            <rect fill="#000000" x="11" y="10" width="2" height="7" rx="1"/>
                            <rect fill="#000000" x="11" y="7" width="2" height="2" rx="1"/>
                        </g>
                    </svg>
                </span>
            </div>
            <div class="alert-text pt-1">
                <p class="mb-0">' . __('Vous pouvez ajouter ci-dessous un ou plusieurs établissements à convoquer.') . '</p>
            </div>
        </div>');
        $this->builderFields->addField($field);
    }

    /**
     * @param int $id
     * @param DossierInstitution|null $dossierInstitution
     * @return void
     */
    private function addInstitution(int $id, ?DossierInstitution $dossierInstitution = null)
    {
        $group = new BuilderFormGroup('group_institution_' . $id);
        $group->setLabel(__('Établissement %s à convoquer', $id + 1));
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setOpened((!$id));

        $institution = $this->getInstitution($id, $dossierInstitution);

        $types = DossierInstitutionService::getTypes();
        $options = [];
        $options[] = ['value' => '', 'label' => __('Sélectionnez un type d\'établissement')];
        foreach ($types as $type => $name) {
            if (in_array($type, [DossierInstitution::TYPE_CONTACT, DossierInstitution::TYPE_VEHICLE_OWNER, DossierInstitution::TYPE_MANDATE, DossierInstitution::TYPE_EXPERT])) {
                continue;
            }
            $options[] = ['value' => $type, 'label' => $name];
        }
        $field = new BuilderFormField('institution[' . $id . '][type]');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Type d\'établissement'));
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $select->setValue($institution['type']);
        $field->setSelect($select);
        $group->addField($field);

        $field = new BuilderFormField('institution[' . $id . '][name]');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Nom de l\'établissement'));
        $field->setValue($institution['name']);
        $group->addField($field);

        $field = new BuilderFormField('institution[' . $id . '][first_name]');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Prénom du représentant'));
        $field->setValue($institution['first_name']);
        $group->addField($field);

        $field = new BuilderFormField('institution[' . $id . '][last_name]');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Nom du représentant'));
        $field->setValue($institution['last_name']);
        $group->addField($field);

        /*$field = new BuilderFormField('institution[' . $id . '][email]');
        $field->setType(FieldsEnum::TYPE_EMAIL);
        $field->setLabel(__('Adresse email'));
        $field->setValue($institution['email']);
        $group->addField($field);*/


        /*$field = new BuilderFormField('content_address');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('<div class="border rounded px-8 pt-8 mb-6"><p class="mb-6 font-weight-bolder">' . __('Adresse postale') . '</p>');
        $group->addField($field);

        $field = new BuilderFormField('institution[' . $id . '][address]');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Adresse'));
        $field->setValue($institution['address']);
        $field->setData([
            'targets' => [
                'zip' => 'institution_' . $id . '__zip_',
                'city' => 'institution_' . $id . '__city_'
            ],
        ]);
        $field->setClass('input-group-address');
        $group->addField($field);

        $field = new BuilderFormField('institution[' . $id . '][address2]');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Adresse (ligne 2)'));
        $field->setValue($institution['address2']);
        $group->addField($field);

        $field = new BuilderFormField('institution[' . $id . '][zip]');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Code postal'));
        $field->setValue($institution['zip']);
        $group->addField($field);

        $field = new BuilderFormField('institution[' . $id . '][city]');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Ville'));
        $field->setValue($institution['city']);
        $group->addField($field);

        $field = new BuilderFormField('institution[' . $id . '][telephone]');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Telephone'));
        $field->setValue($institution['telephone']);
        $group->addField($field);

        $field = new BuilderFormField('content_address2');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('</div>');
        $group->addField($field);*/

        /*$field = new BuilderFormField('content_references');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('<div class="border rounded px-8 pt-8 mb-6"><p class="mb-6 font-weight-bolder">' . __('Références') . '</p>');
        $group->addField($field);

        $field = new BuilderFormField('institution[' . $id . '][reference]');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Référence du dossier'));
        $field->setValue($institution['reference']);
        $group->addField($field);

        $field = new BuilderFormField('institution[' . $id . '][reference_client]');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Référence du mandant'));
        $field->setValue($institution['reference_client']);
        $group->addField($field);

        $field = new BuilderFormField('institution[' . $id . '][reference_company]');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Référence de la compagnie'));
        $field->setValue($institution['reference_company']);
        $group->addField($field);

        $field = new BuilderFormField('content_references2');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('</div>');
        $group->addField($field);*/

        $this->builderFields->addField($group);
    }

    /**
     * @param int                     $id
     * @param DossierInstitution|null $dossierInstitution
     * @return array
     */
    private function getInstitution(int $id, ?DossierInstitution $dossierInstitution = null): array
    {
        $institution = [
            'type' => '',
            'name' => '',
            'first_name' => '',
            'last_name' => '',
            'email' => '',
            'address' => '',
            'address2' => '',
            'zip' => '',
            'city' => '',
            'telephone' => '',
            'reference' => '',
            'reference_client' => '',
            'reference_company' => '',
        ];
        if ($dossierInstitution) {
            $institution['type'] = $dossierInstitution->getType();
            $institution['name'] = $dossierInstitution->getCompany();
            $institution['first_name'] = $dossierInstitution->getFirstName();
            $institution['last_name'] = $dossierInstitution->getLastName();
            $institution['email'] = $dossierInstitution->getEmail();
            if ($dossierInstitution->getPlace()) {
                $institution['address'] = $dossierInstitution->getPlace()->getAddress();
                $institution['address2'] = $dossierInstitution->getPlace()->getAddress2();
                $institution['zip'] = $dossierInstitution->getPlace()->getZip();
                $institution['city'] = $dossierInstitution->getPlace()->getCity();
            }
            $institution['telephone'] = $dossierInstitution->getTelephone();
            $institution['reference'] = $dossierInstitution->getReference();
            $institution['reference_client'] = $dossierInstitution->getReferenceClient();
            $institution['reference_company'] = $dossierInstitution->getReferenceCompany();
        }

        if (isset($_POST['institution'][$id]) and $_POST['institution'][$id]) {
            $institution['type'] = filter_var($_POST['institution'][$id]['type'], FILTER_UNSAFE_RAW);
            $institution['name'] = filter_var($_POST['institution'][$id]['name'], FILTER_UNSAFE_RAW);
            $institution['first_name'] = filter_var($_POST['institution'][$id]['first_name'], FILTER_UNSAFE_RAW);
            $institution['last_name'] = filter_var($_POST['institution'][$id]['last_name'], FILTER_UNSAFE_RAW);
            /*$institution['email'] = filter_var($_POST['institution'][$id]['email'], FILTER_SANITIZE_EMAIL);
            $institution['address'] = filter_var($_POST['institution'][$id]['address'], FILTER_UNSAFE_RAW);
            $institution['address2'] = filter_var($_POST['institution'][$id]['address2'], FILTER_UNSAFE_RAW);
            $institution['zip'] = filter_var($_POST['institution'][$id]['zip'], FILTER_UNSAFE_RAW);
            $institution['city'] = filter_var($_POST['institution'][$id]['city'], FILTER_UNSAFE_RAW);
            $institution['telephone'] = filter_var($_POST['institution'][$id]['telephone'], FILTER_UNSAFE_RAW);
            $institution['reference'] = filter_var($_POST['institution'][$id]['reference'], FILTER_UNSAFE_RAW);
            $institution['reference_client'] = filter_var($_POST['institution'][$id]['reference_client'], FILTER_UNSAFE_RAW);
            $institution['reference_company'] = filter_var($_POST['institution'][$id]['reference_company'], FILTER_UNSAFE_RAW);*/
        }

        return $institution;
    }
}
