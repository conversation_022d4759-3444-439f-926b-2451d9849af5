<?php
namespace Mat<PERSON>yver\FormsFactory\Dossier;

use Mat<PERSON><PERSON>ver\Entity\Dossier\Dossier;
use Mat<PERSON><PERSON>ver\Entity\Dossier\Expertise\DossierExpertise;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Entity\Dossier\DossierDocument;
use MatGyver\Forms\Dossier\DossierDocumentForm;
use MatGyver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormGroup;
use MatGyver\FormsFactory\Builder\BuilderFormSelect;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Dossier\DossierDocumentService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseJudiciaireChronologyService;
use Mat<PERSON><PERSON>ver\Services\Dossier\Expertise\DossierExpertiseJudiciaireDireService;
use MatGyver\Services\Dossier\Expertise\DossierExpertisePersonService;
use Symfony\Component\Validator\Constraints\NotBlank;

class DossierDocumentFormFactory extends AbstractFormFactory
{
    /**
     * @param Dossier|null          $dossier
     * @param DossierExpertise|null $expertise
     */
    public function __construct(?Dossier $dossier = null, ?DossierExpertise $expertise = null)
    {
        parent::__construct();
        $this->setFormService(DossierDocumentForm::class);

        $this->addType();
        $this->addName();
        $this->addFile();
        $this->addOptions($dossier);

        $this->setTitle(__('Ajout d\'un document'));
        if ($dossier) {
            $this->addHiddenFields('dossier_id', $dossier->getId());
            $this->setCancelUrl(Tools::makeLink('app', 'dossier', 'documents/' . $dossier->getId()));
        }
        if ($expertise) {
            $this->setCancelUrl(Tools::makeLink('app', 'dossier', 'expertise/documents/' . $expertise->getId()));
        }
    }

    /**
     * @param DossierDocument|null $dossierDocument
     */
    public function preRenderFields(?DossierDocument $dossierDocument)
    {
        Assets::addJs('app/dossier/document_add.js');

        if ($dossierDocument) {
            $this->setTitle(__('Modification d\'un document'));
            $this->builderFields->updateField('document', 'value', $dossierDocument->getUrl());

            $select = $this->builderFields->getField('type')->getSelect();
            $select->setValue($dossierDocument->getType());

            $this->addOptions($dossierDocument->getDossier(), $dossierDocument);

            $group = $this->builderFields->getField('group_options');
            if ($group instanceof BuilderFormGroup) {
                if ($dossierDocument->getAvailableForContact()) {
                    $group->updateField('available_for_contact', 'value', 'valid');
                }
                if ($dossierDocument->getConfidential()) {
                    $group->updateField('confidential', 'value', 'valid');
                }

                if ($dossierDocument->getLink()) {
                    if (str_contains($dossierDocument->getLink(), '[')) {
                        $link = json_decode($dossierDocument->getLink(), true);
                    } else {
                        $link = [$dossierDocument->getLink()];
                    }

                    $options = [];
                    $options[] = ['value' => DossierDocument::LINK_FACT, 'label' => __('Historique du véhicule'), 'checked' => in_array(DossierDocument::LINK_FACT, $link)];
                    $options[] = ['value' => DossierDocument::LINK_CHRONOLOGY, 'label' => __('Chronologie de l\'expertise'), 'checked' => in_array(DossierDocument::LINK_CHRONOLOGY, $link)];
                    $options[] = ['value' => DossierDocument::LINK_DOCUMENT_RECEIVED, 'label' => __('Déclarations et pièces reçues'), 'checked' => in_array(DossierDocument::LINK_DOCUMENT_RECEIVED, $link)];
                    $options[] = ['value' => DossierDocument::LINK_DOCUMENT_SENT, 'label' => __('Documents communiqués aux parties'), 'checked' => in_array(DossierDocument::LINK_DOCUMENT_SENT, $link)];
                    $options[] = ['value' => DossierDocument::LINK_DIRE, 'label' => __('Dires des parties'), 'checked' => in_array(DossierDocument::LINK_DIRE, $link)];
                    $group->updateField('link', 'options', $options);

                    $container = ContainerBuilderService::getInstance();
                    if (in_array(DossierDocument::LINK_DOCUMENT_RECEIVED, $link)) {
                        $documentReceived = $container->get(DossierExpertiseJudiciaireChronologyService::class)->getRepository()->findOneBy(['document' => $dossierDocument, 'type' => DossierDocument::LINK_DOCUMENT_RECEIVED]);
                        if ($documentReceived) {
                            if ($documentReceived->getPersons()) {
                                $persons = $documentReceived->getPersons();
                                $personsIds = array_map(function ($person) {
                                    return $person->getId();
                                }, $persons);
                                $group->getField('document_received_persons_ids__')->getSelect()->setParam2($personsIds);
                            }
                            $group->updateField('document_received_recipient', 'value', $documentReceived->getRecipient());
                        }
                    }
                    if (in_array(DossierDocument::LINK_DOCUMENT_SENT, $link)) {
                        $documentSent = $container->get(DossierExpertiseJudiciaireChronologyService::class)->getRepository()->findOneBy(['document' => $dossierDocument, 'type' => DossierDocument::LINK_DOCUMENT_SENT]);
                        if ($documentSent) {
                            if ($documentSent->getPersons()) {
                                $persons = $documentSent->getPersons();
                                $personsIds = array_map(function ($person) {
                                    return $person->getId();
                                }, $persons);
                                $group->getField('document_sent_persons_ids__')->getSelect()->setParam2($personsIds);
                            }
                            $group->updateField('document_sent_recipient', 'value', $documentSent->getRecipient());
                        }
                    }
                    if (in_array(DossierDocument::LINK_DIRE, $link)) {
                        $expertise = $dossierDocument->getExpertise();
                        if (!$expertise) {
                            $expertise = $dossierDocument->getDossier()->getLastExpertise();
                        }
                        $dires = $container->get(DossierExpertiseJudiciaireDireService::class)->getRepository()->findBy(['expertise' => $expertise, 'file' => $dossierDocument->getFile()]);
                        if ($dires) {
                            $personsIds = [];
                            foreach ($dires as $dire) {
                                if ($dire->getPerson()) {
                                    $personsIds[] = $dire->getPerson()->getId();
                                }
                            }
                            $group->getField('document_dire_persons_ids__')->getSelect()->setParam2($personsIds);
                        }
                    }
                }

                $expertise = $dossierDocument->getExpertise();
                if (!$expertise) {
                    $expertise = $dossierDocument->getDossier()->getLastExpertise();
                }
                if ($expertise) {
                    $report = $expertise->getExpertiseReport();
                    if ($report) {
                        $appendices = $report->getAppendices();
                        if (in_array($dossierDocument->getId(), $appendices)) {
                            $group->updateField('appendice', 'value', 'valid');
                        }
                    }
                }
            }
        }
    }

    private function addType()
    {
        $types = DossierDocumentService::getTypes();
        $options = [];
        foreach ($types as $type => $name) {
            $options[] = ['value' => $type, 'label' => $name];
        }

        $field = new BuilderFormField('type');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Type de document'));
        $field->addValidation(new NotBlank(['message' => __('Veuillez sélectionner le type de document')]));
        $field->setRequired(true);
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $field->setSelect($select);
        $this->builderFields->addField($field);
    }

    private function addName()
    {
        $field = new BuilderFormField('name');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Nom'));
        $field->setHelp(__('Facultatif'));
        $this->builderFields->addField($field);
    }

    private function addFile()
    {
        $field = new BuilderFormField('document');
        $field->setType(FieldsEnum::TYPE_DROPZONE);
        $field->setLabel(__('Documents'));
        $field->setHelp(__('Max 10 documents'));
        $field->setData([
            'nb-files' => 10
        ]);
        $this->builderFields->addField($field);
    }

    private function addOptions(?Dossier $dossier = null, ?DossierDocument $dossierDocument = null)
    {
        $group = new BuilderFormGroup('group_options');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Options'));
        $group->setOpened(true);

        $field = new BuilderFormField('document_date');
        $field->setType(FieldsEnum::TYPE_DATE_MASK);
        $field->setLabel(__('Date du document'));
        $group->addField($field);

        $field = new BuilderFormField('mileage_content1');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('<div class="d-flex flex-row w-300px"><div class="mr-4">');
        $group->addField($field);

        $field = new BuilderFormField('mileage');
        $field->setType(FieldsEnum::TYPE_INT);
        $field->setLabel(__('Kilométrage ou heures'));
        $group->addField($field);

        $field = new BuilderFormField('mileage_content2');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('</div><div>');
        $group->addField($field);

        $options = [];
        $options[] = ['value' => DossierDocument::MILEAGE_TYPE_METERS, 'label' => 'km'];
        $options[] = ['value' => DossierDocument::MILEAGE_TYPE_MILES, 'label' => 'miles'];
        $options[] = ['value' => DossierDocument::MILEAGE_TYPE_HOURS, 'label' => __('heures')];
        $field = new BuilderFormField('mileage_type');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Type'));
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $field->setSelect($select);
        $field->setData(['rel' => '']);
        $group->addField($field);

        $field = new BuilderFormField('mileage_content3');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('</div></div>');
        $group->addField($field);

        if ($dossier and $dossier->isJudiciaire()) {
            $options = [];
            $options[] = ['value' => DossierDocument::LINK_FACT, 'label' => __('Historique du véhicule')];
            $options[] = ['value' => DossierDocument::LINK_CHRONOLOGY, 'label' => __('Chronologie de l\'expertise')];
            $options[] = ['value' => DossierDocument::LINK_DOCUMENT_RECEIVED, 'label' => __('Déclarations et pièces reçues')];
            $options[] = ['value' => DossierDocument::LINK_DOCUMENT_SENT, 'label' => __('Documents communiqués aux parties')];
            $options[] = ['value' => DossierDocument::LINK_DIRE, 'label' => __('Dires des parties')];
            $field = new BuilderFormField('link');
            $field->setType(FieldsEnum::TYPE_CHECKBOX);
            $field->setLabel(__('Lier ce document à :'));
            if ($dossierDocument and $dossierDocument->getLink()) {
                $field->setValue($dossierDocument->getLink());
            }
            $field->setOptions($options);
            $group->addField($field);

            $field = new BuilderFormField('document_received_person_content1');
            $field->setType(FieldsEnum::TYPE_CONTENT);
            $field->setContent('
<div id="document_received_person_content" class="border p-4 rounded gutter-b d-none">
    <label class="font-weight-bolder">' . __('Déclarations et pièces reçues') . '</label>
    <div class="d-flex flex-row" style="gap: 15px">');
            $group->addField($field);

            $field = new BuilderFormField('document_received_persons_ids[]');
            $field->setType(FieldsEnum::TYPE_SELECT);
            $field->setLabel(__('Sélectionnez une personne'));
            $select = new BuilderFormSelect();
            $select->setClass(DossierExpertisePersonService::class);
            $select->setFunction('generateSelectOptions');
            $select->setParam($dossier->getLastExpertise());
            $field->setSelect($select);
            $field->setPlaceholder(__('Sélectionnez une personne'));
            $field->setData(['multiple' => true]);
            $group->addField($field);

            $field = new BuilderFormField('document_received_recipient');
            $field->setType(FieldsEnum::TYPE_TEXT);
            $field->setLabel(__('Ou indiquez le nom de la personne'));
            $group->addField($field);

            $field = new BuilderFormField('document_received_person_content2');
            $field->setType(FieldsEnum::TYPE_CONTENT);
            $field->setContent('
    </div>
</div>
<div id="document_sent_person_content" class="border p-4 rounded gutter-b d-none">
    <label class="font-weight-bolder">' . __('Documents communiqués aux parties') . '</label>
    <div class="d-flex flex-row" style="gap: 15px">');
            $group->addField($field);

            $field = new BuilderFormField('document_sent_persons_ids[]');
            $field->setType(FieldsEnum::TYPE_SELECT);
            $field->setLabel(__('Sélectionnez une personne'));
            $select = new BuilderFormSelect();
            $select->setClass(DossierExpertisePersonService::class);
            $select->setFunction('generateSelectOptions');
            $select->setParam($dossier->getLastExpertise());
            $field->setSelect($select);
            $field->setPlaceholder(__('Sélectionnez une personne'));
            $field->setData(['multiple' => true]);
            $group->addField($field);

            $field = new BuilderFormField('document_sent_recipient');
            $field->setType(FieldsEnum::TYPE_TEXT);
            $field->setLabel(__('Ou indiquez le nom de la personne'));
            $group->addField($field);

            $field = new BuilderFormField('document_received_person_content3');
            $field->setType(FieldsEnum::TYPE_CONTENT);
            $field->setContent('
    </div>
</div>
<div id="document_dire_person_content" class="border p-4 rounded gutter-b d-none">
    <label class="font-weight-bolder">' . __('Dires des parties') . '</label>
    <div class="d-flex flex-row" style="gap: 15px">');
            $group->addField($field);

            $field = new BuilderFormField('document_dire_persons_ids[]');
            $field->setType(FieldsEnum::TYPE_SELECT);
            $field->setLabel(__('Sélectionnez une personne'));
            $select = new BuilderFormSelect();
            $select->setClass(DossierExpertisePersonService::class);
            $select->setFunction('generateSelectOptions');
            $select->setParam($dossier->getLastExpertise());
            $field->setSelect($select);
            $field->setPlaceholder(__('Sélectionnez une personne'));
            $field->setData(['multiple' => true]);
            $group->addField($field);

            $field = new BuilderFormField('document_received_person_content4');
            $field->setType(FieldsEnum::TYPE_CONTENT);
            $field->setContent('</div></div>');
            $group->addField($field);
        } else {
            $field = new BuilderFormField('link');
            $field->setType(FieldsEnum::TYPE_HIDDEN);
            $field->setValue('');
            $group->addField($field);
        }

        $field = new BuilderFormField('description');
        $field->setType(FieldsEnum::TYPE_CKEDITOR_SMALL);
        $field->setLabel(__('Description'));
        $group->addField($field);

        $field = new BuilderFormField('audio_recorder');
        $field->setType(FieldsEnum::TYPE_AUDIO_RECORDER);
        $field->setLabel(__('Enregistreur audio'));
        $field->setData(['target' => 'description']);
        $group->addField($field);

        if (!$dossier or !$dossier->isJudiciaire()) {
            $field = new BuilderFormField('available_for_contact');
            $field->setType(FieldsEnum::TYPE_SWITCH);
            $field->setLabel(__('Rendre disponible pour le lésé'));
            $field->setOptions([
                'on' => 'valid',
                'off' => 'invalid',
            ]);
            $field->setData([
                'label_active' => __('Document visible par le lésé dans son espace client'),
                'label_desactive' => __('Document non disponible pour le lésé'),
            ]);
            $field->setValue('invalid');
            $field->setHelp(__('Si vous activez cette option, le document sera consultable par le lésé dans son espace client.'));
            $group->addField($field);
        }

        $field = new BuilderFormField('confidential');
        $field->setType(FieldsEnum::TYPE_SWITCH);
        $field->setLabel(__('Document confidentiel'));
        $field->setOptions([
            'on' => 'valid',
            'off' => 'invalid',
        ]);
        $field->setData([
            'label_active' => __('Document confidentiel'),
            'label_desactive' => __('Document non confidentiel'),
        ]);
        $field->setValue('invalid');
        $field->setHelp(__('Si vous activez cette option, le document sera considéré comme confidentiel et n\'apparaîtra pas dans le rappel des faits, dans le PV et dans le rapport d\'expertise.'));
        if ($dossier and $dossier->isJudiciaire()) {
            $field->setHelp(__('Le document n\'apparaîtra pas dans la chronologie, le compte rendu d\'accedit, le pré-rapport et le rapport.'));
        }
        $group->addField($field);

        $field = new BuilderFormField('appendice');
        $field->setType(FieldsEnum::TYPE_SWITCH);
        $field->setLabel(__('Prévoir pour les annexes'));
        $field->setOptions([
            'on' => 'valid',
            'off' => 'invalid',
        ]);
        $field->setData([
            'label_active' => __('Prévoir pour les annexes'),
            'label_desactive' => __('Ne pas prévoir pour les annexes'),
        ]);
        $field->setValue('invalid');
        $field->setHelp(__('Si vous activez cette option, le document sera pré-coché pour générer le rapport d\'annexes.'));
        $group->addField($field);

        $this->builderFields->addField($group);
    }
}
