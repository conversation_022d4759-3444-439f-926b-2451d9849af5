<?php
namespace MatGyver\FormsFactory\Dossier;

use MatGyver\Entity\Dossier\Dossier;
use MatGyver\Entity\Dossier\DossierDocument;
use MatGyver\Entity\Dossier\DossierInstitution;
use MatGyver\Entity\Dossier\Expertise\DossierExpertiseJudiciaireChronology;
use MatGyver\Enums\ConfigEnum;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Forms\Dossier\DossierMailSendForm;
use MatGyver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormSelect;
use MatGyver\Helpers\Number;
use MatGyver\Helpers\Tools;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Dossier\DossierDocumentService;
use MatGyver\Services\Dossier\DossierExpertiseService;
use MatGyver\Services\Dossier\DossierInstitutionService;
use MatGyver\Services\Dossier\DossierPaymentLinkService;
use MatGyver\Services\Dossier\DossierUserService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseJudiciaireService;
use MatGyver\Services\Dossier\Expertise\DossierExpertisePersonService;
use MatGyver\Services\Dossier\Expertise\DossierExpertisePictureService;
use MatGyver\Services\Mail\MailHistoryService;
use MatGyver\Services\Partials\SelectService;
use MatGyver\Services\Users\UsersService;
use Symfony\Component\Validator\Constraints\NotBlank;

class DossierMailSendFormFactory extends AbstractFormFactory
{
    public function __construct(?Dossier $dossier = null)
    {
        parent::__construct();
        $this->setFormService(DossierMailSendForm::class);

        $this->addContents($dossier);
        $this->addRecipients($dossier);
        $this->addOthersUsers();
        $this->addDocuments($dossier);
        $this->addInvoices($dossier);
        $this->addPictures($dossier);
        $this->addMails($dossier);
        $this->addFiles();
        $this->addDate();
        if ($_SESSION['client']['id'] == 2) {
            $this->addConfidential();
        }
        if ($dossier and $dossier->isJudiciaire()) {
            $this->addChronology();
        }

        $this->setTitle(__('Envoi d\'un email'));
        if ($dossier) {
            $this->addHiddenFields('dossier_id', $dossier->getId());
            $this->setCancelUrl(Tools::makeLink('app', 'dossier', 'mails/' . $dossier->getId()));
        }
    }

    public function preRenderFields()
    {
        if (isset($_GET['from_history'])) {
            $container = ContainerBuilderService::getInstance();
            $mailHistoryId = filter_input(INPUT_GET, 'from_history', FILTER_VALIDATE_INT);
            $mailHistory = $container->get(MailHistoryService::class)->getRepository()->find($mailHistoryId);
            if ($mailHistory) {
                $this->builderFields->updateField('subject', 'value', $mailHistory->getSubject());

                $content = $mailHistory->getMessage();
                preg_match_all('/\<td class="container-padding content"(.*?)\>(.*?)\<\/td\>/is', $content, $data);
                if ($data and isset($data[2]) and $data[2][0]) {
                    $content = $data[2][0];

                    //remove attachments
                    preg_match_all('/<div style="padding-top: 10px; padding-bottom: 10px">(.*?)<\/div>/is', $content, $data);
                    if ($data and isset($data[1]) and $data[1][0]) {
                        $content = str_replace('<div style="padding-top: 10px; padding-bottom: 10px">' . $data[1][0] . '</div>', '', $content);
                    }

                    $this->builderFields->updateField('content', 'value', $content);
                }

                if ($mailHistory->getAttachments()) {
                    $mailAttachments = [];
                    $attachments = json_decode($mailHistory->getAttachments(), true);
                    foreach ($attachments as $attachment) {
                        $data = pathinfo($attachment);
                        $baseName = $data['basename'];
                        $document = $container->get(DossierDocumentService::class)->getRepository()->findOneBy(['dossier' => $mailHistory->getDossier(), 'file' => $baseName]);
                        if ($document) {
                            $mailAttachments[] = $document->getId();
                        }
                    }
                    $select = $this->builderFields->getField('documents__')->getSelect();
                    $select->setValue($mailAttachments);
                }
            }
        }
    }

    private function addRecipients(?Dossier $dossier = null)
    {
        $groups = [];
        $container = ContainerBuilderService::getInstance();
        if ($dossier) {
            if ($dossier->isJudiciaire()) {
                $groups = $container->get(DossierInstitutionService::class)->getSelectOptionsJudiciaireGroups($dossier, 'institution-');
                foreach ($groups as $group => $options) {
                    foreach ($options as $key => $option) {
                        $groups[$group]['institution-' . $key] = $option;
                        unset($groups[$group][$key]);
                    }
                }
            } else {
                $groups = [];
                $options = [];
                $institutions = $container->get(DossierInstitutionService::class)->getRepository()->findBy(['dossier' => $dossier]);
                foreach ($institutions as $institution) {
                    if (!$institution->getEmail()) {
                        continue;
                    }
                    $type = DossierInstitutionService::getType($institution->getType());
                    $options['institution-' . $institution->getId()] = $institution->getName() . ' (' . $type . ')';

                    if ($institution->getEmailClient()) {
                        $options['institution-' . $institution->getId() . '-email-client'] = __('Mandant de %s (%s) : %s', $institution->getName(), $type, $institution->getEmailClient());
                    }
                    if ($institution->getLawyerEmail()) {
                        $options['institution-' . $institution->getId() . '-email-lawyer'] = __('Avocat de %s (%s) : %s', $institution->getName(), $type, $institution->getLawyerEmail());
                    }
                }

                if ($options) {
                    $groups[__('Personnes et établissements')] = $options;
                }
            }

            $options = [];
            $dossierUsers = $container->get(DossierUserService::class)->getRepository()->findBy(['dossier' => $dossier]);
            foreach ($dossierUsers as $dossierUser) {
                if ($dossierUser->getUser()->getId() === $_SESSION['user']['id']) {
                    continue;
                }
                $options['dossier-user-' . $dossierUser->getId()] = $dossierUser->getUser();
            }
            if ($options) {
                $groups[__('Utilisateurs du dossier')] = $options;
            }

            $options = [];
            $contact = $dossier->getContact();
            if ($contact->getLegalProtectionEmail()) {
                $options['contact-legal-protection-email'] = __('Protection juridique du lésé (%s)', $contact->getLegalProtectionEmail());
            }

            $mandate = $dossier->getMandate();
            if ($mandate and $mandate->getLegalProtectionEmail()) {
                $options['mandate-legal-protection-email'] = __('Protection juridique du mandant (%s)', $mandate->getLegalProtectionEmail());
            }
            if ($options) {
                $groups[__('Autres')] = $options;
            }
        }

        $selectedUsers = [];
        if (isset($_GET['documentId'])) {
            $container = ContainerBuilderService::getInstance();
            $documentId = filter_input(INPUT_GET, 'documentId', FILTER_VALIDATE_INT);
            $document = $container->get(DossierDocumentService::class)->getRepository()->find($documentId);
            if ($document and ($document->getType() == DossierDocument::TYPE_CLAIM or $document->getType() == DossierDocument::TYPE_CLAIM_TEMPORARY or $document->getType() == DossierDocument::TYPE_COURRIER_CAR_STORAGE) and $document->getPerson() and $document->getPerson()->getInstitution()) {
                $selectedUsers[] = 'institution-' . $document->getPerson()->getInstitution()->getId();
            }
            if ($document and DossierExpertiseJudiciaireService::getDocumentType($document->getType()) !== null) {
                $firstPart = ($document->getFirstPart() ? json_decode($document->getFirstPart(), true) : []);
                if (isset($firstPart['persons']) and $firstPart['persons']) {
                    foreach ($firstPart['persons'] as $personId) {
                        $person = $container->get(DossierExpertisePersonService::class)->getRepository()->find($personId);
                        if ($person and $person->getInstitution()) {
                            $selectedUsers[] = 'institution-' . $person->getInstitution()->getId();
                        }
                    }
                }
            }
        }
        if (isset($_GET['type']) and ($_GET['type'] == 'acceptation_mission' or $_GET['type'] == 'expertise_date') and isset($institutions) and $institutions) {
            foreach ($institutions as $institution) {
                if ($institution->getType() == DossierInstitution::TYPE_LAWYER) {
                    $selectedUsers[] = 'institution-' . $institution->getId();
                }
            }
        }

        $content = SelectService::renderWithGroups($groups, $selectedUsers, true);

        $field = new BuilderFormField('recipients[]');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Destinataires *'));
        $field->setPlaceholder(__('Cliquez pour sélectionner un destinataire'));
        $select = new BuilderFormSelect();
        $select->setContent($content);
        $select->setValue($selectedUsers);
        $field->setSelect($select);
        $field->setData(['multiple' => true, 'keepOnSelect' => true]);
        $field->setStep(2);
        $this->builderFields->addField($field);


        $options = [];
        $users = $container->get(UsersService::class)->getAllUsers();
        foreach ($users as $user) {
            $options[] = ['value' => 'user-' . $user->getId(), 'label' => $user . ($_SESSION['user']['id'] == $user->getId() ? ' (moi)' : ' (collaborateur)')];
        }
        $field = new BuilderFormField('users[]');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Collaborateurs'));
        $field->setPlaceholder(__('Cliquez pour sélectionner un collaborateur'));
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $field->setSelect($select);
        $field->setData(['multiple' => true, 'keepOnSelect' => true]);
        $field->setStep(2);
        $this->builderFields->addField($field);


        /*$field = new BuilderFormField('emails');
        $field->setType(FieldsEnum::TYPE_TEXTAREA);
        $field->setLabel(__('Adresses emails supplémentaires'));
        $field->setHelp(__('1 adresse email par ligne'));
        $field->setPlaceholder("<EMAIL>\<EMAIL>");
        $field->setStep(2);
        $this->builderFields->addField($field);*/
    }

    private function addOthersUsers()
    {
        $content = '
<style>.form-group-repeater .user .form-group { margin-bottom: 0; width: 100%; }</style>
<div class="form-group form-group-repeater">
    <label class="control-label">' . __('Autres destinataires') . '</label>
    <div id="kt_repeater_1">
        <div data-repeater-list="others_users">
            <div data-repeater-item="" class="user border border-1 border-secondary p-4 rounded mb-4">
                <div class="row mb-4">
                    <div class="col-md-6">';
        $field = new BuilderFormField('content1');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($content);
        $field->setStep(2);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('others_users[][first_name]');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Prénom'));
        $field->setStep(2);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('content2');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('</div><div class="col-md-6">');
        $field->setStep(2);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('others_users[][last_name]');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Nom'));
        $field->setStep(2);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('content3');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('</div></div><div class="d-flex flex-row">');
        $field->setStep(2);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('others_users[][email]');
        $field->setType(FieldsEnum::TYPE_EMAIL);
        $field->setLabel(__('Adresse email'));
        $field->setStep(2);
        $this->builderFields->addField($field);

        $content = '
                    <div class="ml-4 pt-12">
                        <a href="javascript:;" data-repeater-delete="" class="btn btn-sm btn-icon btn-clean btn-hover-light-danger">
                            <i class="la la-remove"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div data-repeater-create="" class="btn font-weight-bold btn-light-primary">
            <i class="la la-plus"></i> ' . __('Ajouter un destinataire') . '
        </div>
    </div>
</div>';
        $field = new BuilderFormField('content4');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($content);
        $field->setStep(2);
        $this->builderFields->addField($field);
    }

    private function addContents(?Dossier $dossier = null)
    {
        $field = new BuilderFormField('subject');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Sujet du mail'));
        $field->addValidation(new NotBlank(['message' => __('Veuillez entrer le sujet du mail')]));
        $field->setRequired(true);
        $field->setStep(1);
        if ($dossier) {
            $field->setValue($dossier->getSubject() . ' ');
            if (isset($_GET['type']) and $_GET['type'] == 'convocations') {
                $field->setValue($dossier->getSubject() . ' - ' . __('Convocations'));
            }
            if (isset($_GET['expertise'])) {
                $field->setValue($dossier->getSubject() . ' - ' . __('Convocation à l\'expertise'));
            }
        }
        $this->builderFields->addField($field);


        $content = '';
        if ($dossier) {
            $content = '<p>Bonjour,</p><p></p>';
        }

        if (isset($_GET['paymentLink'])) {
            $paymentLinkId = filter_input(INPUT_GET, 'paymentLink', FILTER_VALIDATE_INT);
            $container = ContainerBuilderService::getInstance();
            $paymentLink = $container->get(DossierPaymentLinkService::class)->getRepository()->find($paymentLinkId);
            if ($paymentLink) {
                $this->addHiddenFields('payment_link_id', $paymentLink->getId());

                $subject = $this->builderFields->getField('subject')->getValue();
                $dossier = $paymentLink->getDossier();
                //$subject = __('%s - Lien CB provision Expertise de votre véhicule %s %s %s', $dossier->getCompany()->getName(), $dossier->getVehicle()->getBrand(), $dossier->getVehicle()->getModel(), $dossier->getVehicle()->getRegistration());
                $subject = $paymentLink->getTitle();
                if ($paymentLink->getUnlockReport()) {
                    $subject = ($dossier->getContact() ? '[' . $dossier->getContact()->getCompanyOrName() . '] ' : '');
                    $subject .= __('Règlement et téléchargement du Rapport d\'Expertise');
                }
                $this->builderFields->updateField('subject', 'value', $subject);

                $lastExpertise = $dossier->getLastExpertise();
                if ($paymentLink->getUnlockReport()) {
                    $content = '<p>' . __('Bonjour,') . '</p>';
                    $content .= '<p>' . __('Vous trouverez ici le lien CB pour le règlement du rapport d\'expertise.') . '</p>';
                    $content .= '<p>' . __('Une fois réglé, le téléchargement sera possible sur la page de confirmation de paiement, ainsi que dans votre espace personnel.') . '</p>';
                } else {
                    $content = '<p>' . __('Bonjour,') . '</p>';
                    $content .= '<p>' . __('Vous trouverez ici le lien CB pour la provision du règlement de l\'expertise de votre véhicule, programmée le %s à %s, pour un montant de %s, tenant compte :', (($lastExpertise and $lastExpertise->getDate()) ? $lastExpertise->getDate()->format('d/m/Y') : ''), (($lastExpertise and $lastExpertise->getDate()) ? $lastExpertise->getDate()->format('H:i') : ''), Number::formatAmount($paymentLink->getProduct()->getPriceTaxIncl(), DEFAULT_CURRENCY)) . '</p>';
                    $content .= '<ul>';
                    $content .= '<li>des frais administratifs de création de dossier</li>';
                    $content .= '<li>création historique</li>';
                    $content .= '<li>organisation de l\'expertise</li>';
                    $content .= '<li>envoi des convocations</li>';
                    $content .= '<li>déplacement à l\'expertise</li>';
                    $content .= '<li>1 expertise contradictoire avec dressage d’un procès-verbal de constatations</li>';
                    $content .= '</ul>';
                    $content .= '<p>' . __('Dans le cas où les besoins seraient plus importants et nécessiteraient d’autres opérations d’analyse ou de diagnostic, ces besoins seront spécifiés sur le procès-verbal ou par email.') . '</p>';
                    $content .= '<p>' . __('La date et l\'heure peuvent varier en fonction de la date de réception de la provision pour l\'expertise, des documents nécessaires et de la disponibilité de chacun.') . '</p>';
                    $content .= '<p>' . __('Les démarches pour l’organisation de l’expertise ne se feront qu’à réception de la provision d’expertise.') . '</p>';
                }
            }
        }

        if (isset($_GET['type']) and $_GET['type'] == 'convocations') {
            $content = '<p>' . __('Bonjour') . '</p>';
            $content .= '<p>' . __('Vous trouverez en pièce jointe les convocations envoyées aux différentes parties prenantes de votre dossier.') . '</p>';
        }

        if (isset($_GET['type']) and $_GET['type'] == 'acceptation_mission') {
            $content = '<p>' . __('Bonjour') . '</p>';
            $content .= '<p>' . __('Dans le cadre de l’acceptation de la mission, merci de m’adresser l’ensemble des pièces déjà versées au contradictoire afin de commencer la gestion administrative du dossier.') . '</p>';
            $content .= '<p>' . __('Je vous remercie également de me communiquer le lieu de dépôt du véhicule pour l’expertise.') . '</p>';
            $content .= '<p>' . __('Nous restons dans l’attente de l’avis de consignation si cela n’a pas déjà été fait.') . '</p>';
        }

        if (isset($_GET['type']) and $_GET['type'] == 'expertise_date') {
            $date = '  /  /  ';
            $address = '_____.';
            if ($dossier) {
                $lastExpertise = $dossier->getLastExpertise();
                if ($lastExpertise and $lastExpertise->getDate()) {
                    $date = '<strong>' . dateFrench("l d F Y", $lastExpertise->getDate()->getTimestamp()) . ' à ' . $lastExpertise->getDate()->format('H:i') . '</strong>';
                }
                if ($lastExpertise) {
                    $address = ':<br>' . $lastExpertise->displayPlace(false);
                }
            }

            $content = '<p>' . __('Bonjour') . '</p>';
            $content .= '<p>' . __('Pour faire suite à la date de convenance, merci de bloquer la date du %s à l’adresse %s', $date, $address) . '</p>';
            $content .= '<p>' . __('Un courrier de convocation confirmera le présent courriel.<br>Nous vous invitons à en informer vos clients respectifs.') . '</p>';
        }

        if (isset($_GET['expertise']) and $_GET['expertise']) {
            $expertiseId = filter_input(INPUT_GET, 'expertise', FILTER_VALIDATE_INT);
            $container = ContainerBuilderService::getInstance();
            $expertise = $container->get(DossierExpertiseService::class)->getRepository()->find($expertiseId);
            if ($expertise) {
                $content = '<p>' . __('Bonjour') . '</p>';
                $content .= '<p>' . __('Dans le cadre de la mission qui nous a été confiée, nous vous confirmons que l’expertise du véhicule en objet se tiendra :') . '</p>';
                $content .= '<p>';

                if ($expertise->getDate()) {
                    $content .= '<strong>' . dateFrench("l d F Y", $expertise->getDate()->getTimestamp()) . ' à ' . $expertise->getDate()->format('H:i') . '</strong><br>';
                }
                $content .= $expertise->displayPlace();
                $content .= '</p>';
            }
        }

        if (isset($_GET['documentId'])) {
            $container = ContainerBuilderService::getInstance();
            $documentId = filter_input(INPUT_GET, 'documentId', FILTER_VALIDATE_INT);
            $document = $container->get(DossierDocumentService::class)->getRepository()->find($documentId);
            if ($document and DossierExpertiseJudiciaireService::getDocumentType($document->getType())) {
                $firstPart = ($document->getFirstPart() ? json_decode($document->getFirstPart(), true) : []);
                if (isset($firstPart['content']) and $firstPart['content']) {
                    $content = $firstPart['content'];
                }
            }
        }

        if ($content and $dossier) {
            $clientSignature = $dossier->getClient()->getClientConfig(ConfigEnum::SITE_EMAIL_SIGNATURE);
            if ($clientSignature) {
                $content .= '<p>' . $clientSignature . '</p>';
            } else {
                $content .= '<p>' . __('Bien cordialement') . '</p>';

                $expert = $dossier->getExpert();
                if ($expert) {
                    $content .= '<p>' . $expert->getFirstName() . ' ' . $expert->getLastName() . '</p>';
                }
            }
        }

        $field = new BuilderFormField('content');
        $field->setType(FieldsEnum::TYPE_CKEDITOR_SMALL);
        $field->setLabel(__('Contenu de l\'email'));
        $field->setStep(1);
        $field->setValue($content);
        if (isset($_GET['paymentLink'])) {
            $field->setHelp(__('Le lien de paiement sera ajouté après le contenu indiqué ci-dessous.'));
        }
        $this->builderFields->addField($field);

        $field = new BuilderFormField('audio_recorder');
        $field->setType(FieldsEnum::TYPE_AUDIO_RECORDER);
        $field->setLabel(__('Enregistreur audio'));
        $field->setData(['target' => 'content']);
        $field->setStep(1);
        $this->builderFields->addField($field);
    }

    private function addDocuments(?Dossier $dossier = null)
    {
        $options = [];
        $selectedDocuments = [];
        if ($dossier) {
            $container = ContainerBuilderService::getInstance();
            $documents = $container->get(DossierDocumentService::class)->getRepository()->findBy(['dossier' => $dossier]);
            foreach ($documents as $document) {
                if ($document->getType() == DossierDocument::TYPE_EXPERT_INVOICE) {
                    continue;
                }
                if ($document->getVirusScan() == DossierDocument::VIRUS_SCAN_ALERT) {
                    continue;
                }
                $options[] = [
                    'value' => $document->getId(),
                    'label' => $document->getName() ?: DossierDocumentService::getType($document->getType()),
                    'data' => ['thumbnail' => ($document->getThumbnail() ? $document->getThumbnailUrl() : '')]
                ];
            }

            if (isset($_GET['type']) and $_GET['type'] == 'convocations') {
                foreach ($documents as $document) {
                    if ($document->getType() == DossierDocument::TYPE_CONVOCATION) {
                        $selectedDocuments[] = $document->getId();
                    }
                }
            }
        }
        if (isset($_GET['documentId'])) {
            $selectedDocuments = [filter_input(INPUT_GET, 'documentId', FILTER_VALIDATE_INT)];
        }

        $field = new BuilderFormField('documents[]');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Documents'));
        $field->setPlaceholder(__('Cliquez pour sélectionner un document'));
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $field->setSelect($select);
        $field->setData(['multiple' => true, 'keepOnSelect' => true]);
        if ($selectedDocuments) {
            $select->setValue($selectedDocuments);
        }
        $field->setStep(3);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('documents_thumbnails');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('<div id="documents_thumbnails"></div>');
        $field->setStep(3);
        $this->builderFields->addField($field);
    }

    private function addInvoices(?Dossier $dossier = null)
    {
        $options = [];
        if ($dossier) {
            $invoices = $dossier->getInvoices();
            if (count($invoices)) {
                foreach ($invoices as $invoice) {
                    $options[] = ['value' => $invoice->getId(), 'label' => __('Facture %s (%s)', $invoice->getPrefix() . $invoice->getNumber(), Number::formatAmount($invoice->getAmountTaxIncl(), $invoice->getCurrency()))];
                }
            }
        }

        if (!$options) {
            return;
        }

        $field = new BuilderFormField('invoices[]');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Factures'));
        $field->setPlaceholder(__('Cliquez pour sélectionner une facture'));
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $field->setSelect($select);
        $field->setData(['multiple' => true, 'keepOnSelect' => true]);
        $field->setStep(3);
        $this->builderFields->addField($field);
    }

    private function addPictures(?Dossier $dossier = null)
    {
        $options = [];
        if ($dossier) {
            $expertise = $dossier->getExpertise();
            if ($expertise) {
                $pictures = $expertise->getPictures();
                if (count($pictures)) {
                    foreach ($pictures as $picture) {
                        $options[] = ['value' => $picture->getId(), 'label' => ($picture->getName() ?: DossierExpertisePictureService::getType($picture->getType()))];
                    }
                }
            }
        }

        if (!$options) {
            return;
        }

        $field = new BuilderFormField('pictures[]');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Photographies'));
        $field->setPlaceholder(__('Cliquez pour sélectionner une photographie'));
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $field->setSelect($select);
        $field->setData(['multiple' => true, 'keepOnSelect' => true]);
        $field->setStep(3);
        $this->builderFields->addField($field);
    }

    private function addMails(?Dossier $dossier = null)
    {
        $options = [];
        if ($dossier) {
            $container = ContainerBuilderService::getInstance();
            $mailsHistories = $container->get(MailHistoryService::class)->getRepository()->findBy(['dossier' => $dossier]);
            if ($mailsHistories) {
                foreach ($mailsHistories as $mailHistory) {
                    if (!$mailHistory->getPreview()) {
                        continue;
                    }
                    $options[] = ['value' => $mailHistory->getId(), 'label' => __('Mail envoyé le %s (objet : %s)', dateFr($mailHistory->getDate()->format('Y-m-d')), $mailHistory->getSubject())];
                }
            }
        }

        if (!$options) {
            return;
        }

        $field = new BuilderFormField('mails[]');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Emails envoyés'));
        $field->setPlaceholder(__('Cliquez pour sélectionner un email'));
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $field->setSelect($select);
        $field->setData(['multiple' => true, 'keepOnSelect' => true]);
        $field->setStep(3);
        $this->builderFields->addField($field);
    }

    private function addFiles()
    {
        $field = new BuilderFormField('files');
        $field->setType(FieldsEnum::TYPE_DROPZONE);
        $field->setLabel(__('Document supplémentaire'));
        $field->setStep(3);
        $field->setData([
            'nb-files' => 10
        ]);
        $this->builderFields->addField($field);
    }

    private function addDate()
    {
        $field = new BuilderFormField('date');
        $field->setType(FieldsEnum::TYPE_DATETIME_MASK);
        $field->setLabel(__('Programmer à une date ultérieure'));
        $field->setHelp(__('Cet email sera envoyé à la date indiquée. Laissez vide pour envoyer cet email tout de suite.'));
        $field->setStep(3);
        $field->setData(['min-date' => date('Y-m-d')]);
        $this->builderFields->addField($field);
    }

    private function addConfidential()
    {
        $field = new BuilderFormField('confidential');
        $field->setType(FieldsEnum::TYPE_SWITCH);
        $field->setLabel(__('Mail confidentiel'));
        $field->setHelp(__('Un email générique sera envoyé aux destinataires avec un bouton leur permettant de consulter le contenu de cet email, sur une page sécurisée de %s. Cette option permet aussi de mieux suivre les ouvertures des emails.', APP_NAME));
        $field->setStep(3);
        $this->builderFields->addField($field);
    }

    public function addChronology()
    {
        $options = [
            ['value' => DossierExpertiseJudiciaireChronology::TYPE_CHRONOLOGY, 'label' => __('Dans la chronologie de l\'expertise')],
            ['value' => DossierExpertiseJudiciaireChronology::TYPE_DOCUMENT_SENT, 'label' => __('Dans les documents envoyés aux parties')],
        ];
        $field = new BuilderFormField('chronology');
        $field->setType(FieldsEnum::TYPE_CHECKBOX);
        $field->setLabel(__('Enregistrer dans les traçabilités'));
        $field->setOptions($options);
        $field->setStep(3);
        $this->builderFields->addField($field);
    }
}
