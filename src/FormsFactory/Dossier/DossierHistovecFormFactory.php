<?php
namespace Mat<PERSON>yver\FormsFactory\Dossier;

use Mat<PERSON><PERSON><PERSON>\Entity\Dossier\Dossier;
use Mat<PERSON><PERSON><PERSON>\Enums\FieldsEnum;
use Mat<PERSON>yver\Forms\Dossier\DossierHistovecForm;
use MatGyver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormGroup;
use Symfony\Component\Validator\Constraints\NotBlank;

class DossierHistovecFormFactory extends AbstractFormFactory
{
    public function __construct(?Dossier $dossier = null)
    {
        parent::__construct();
        $this->setFormService(DossierHistovecForm::class);

        $this->addUser();
        $this->addRegistration();

        $this->setTitle(__('Modification des paramètres pour Histovec'));
        if ($dossier) {
            $this->addHiddenFields('dossier_id', $dossier->getId());

            $this->builderFields->updateField('first_name', 'value', $dossier->getVehicleOwner()->getFirstName());
            $this->builderFields->updateField('last_name', 'value', $dossier->getVehicleOwner()->getLastName());
            $this->builderFields->updateField('company', 'value', $dossier->getVehicleOwner()->getCompany());
            $this->builderFields->updateField('siren', 'value', $dossier->getVehicleOwner()->getSiren());

            $dossierVehicle = $dossier->getVehicle();
            $group = $this->builderFields->getField('group_vehicle');
            if ($dossierVehicle and $group instanceof BuilderFormGroup) {
                $group->updateField('registration', 'value', $dossierVehicle->getRegistration());
                $group->updateField('formule_number', 'value', $dossierVehicle->getFormuleNumber());
                if ($dossierVehicle->getRegistrationDate()) {
                    $group->updateField('registration_date', 'value', $dossierVehicle->getRegistrationDate()->format('d/m/Y'));
                }
            }
        }
    }

    private function addUser()
    {
        $field = new BuilderFormField('first_name');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Prénom'));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('last_name');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Nom'));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('company');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Entreprise'));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('siren');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('SIREN'));
        $this->builderFields->addField($field);
    }

    private function addRegistration()
    {
        $group = new BuilderFormGroup('group_vehicle');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Informations du véhicule'));
        $group->setOpened(true);

        $field = new BuilderFormField('registration');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Immatriculation'));
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez indiquer le numéro d\'immatriculation')]));
        $group->addField($field);

        $field = new BuilderFormField('formule_number');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('N° de formule sur carte grise (si immatriculation depuis 2009)'));
        $group->addField($field);

        $field = new BuilderFormField('registration_date');
        $field->setType(FieldsEnum::TYPE_DATE_MASK);
        $field->setLabel(__('Date de création de la carte grise'));
        $group->addField($field);

        $this->builderFields->addField($group);
    }
}
