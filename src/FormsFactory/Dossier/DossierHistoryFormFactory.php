<?php
namespace Mat<PERSON>yver\FormsFactory\Dossier;

use <PERSON><PERSON><PERSON><PERSON>\Entity\Dossier\Dossier;
use MatG<PERSON>ver\Enums\FieldsEnum;
use MatGyver\Forms\Dossier\DossierHistoryForm;
use MatGyver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;

class DossierHistoryFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(DossierHistoryForm::class);

        $this->addHistory();

        $this->setTitle(__('Historique'));
    }

    /**
     * @param Dossier|null $dossier
     * @throws \Exception
     */
    public function preRenderFields(?Dossier $dossier)
    {
        if (!$dossier) {
            throw new \Exception('Aucun dossier sélectionné');
        }
        $this->builderFields->updateField('history', 'value', $dossier->getHistovec());
    }

    private function addHistory()
    {
        $field = new BuilderFormField('history');
        $field->setType(FieldsEnum::TYPE_TEXTAREA);
        $field->setLabel(__('Historique'));
        $field->setClass('autosize');
        $this->builderFields->addField($field);
    }
}
