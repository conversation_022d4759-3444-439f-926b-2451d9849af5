<?php
namespace MatGyver\FormsFactory\Document;

use MatGyver\Entity\Document\DocumentTemplate;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Forms\Document\DocumentTemplateForm;
use MatGyver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormGroup;
use MatGyver\FormsFactory\Builder\BuilderFormSelect;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Document\DocumentTemplateService;
use Symfony\Component\Validator\Constraints\NotBlank;

class DocumentTemplateFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(DocumentTemplateForm::class);

        $this->addType();
        $this->addPersonType();
        $this->addName();
        $this->addTitle();
        $this->addContent();
        $this->addContent2();

        $this->setTitle(__('Création d\'un modèle de document'));
        $this->setBtnValidateText(__('Valider'));
        $this->setCancelUrl(Tools::makeLink('app', 'settings', 'documents_templates'));
    }

    /**
     * @param DocumentTemplate|null $documentTemplate
     */
    public function preRenderFields(?DocumentTemplate $documentTemplate)
    {
        Assets::addJs('app/documents_templates_create.js');
        if ($documentTemplate) {
            $this->setTitle(__('Modification d\'un modèle de document'));

            $select = $this->builderFields->getField('type')->getSelect();
            $select->setValue($documentTemplate->getType());

            if ($documentTemplate->getPersonType()) {
                $select = $this->builderFields->getField('person_type')->getSelect();
                $select->setValue($documentTemplate->getPersonType());
            }
        }
    }

    private function addType()
    {
        $options = [];
        $options[] = ['value' => '', 'label' => __('Sélectionnez un type de document')];
        $types = DocumentTemplateService::getTypes();
        foreach ($types as $type => $name) {
            $options[] = ['value' => $type, 'label' => $name];
        }
        $field = new BuilderFormField('type');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Type de document'));
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez sélectionner un type de document')]));
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $field->setSelect($select);
        $this->builderFields->addField($field);
    }

    private function addPersonType()
    {
        $field = new BuilderFormField('person_type_content1');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('<div id="person_type_content1" class="d-none">');
        $this->builderFields->addField($field);

        $options = [];
        $options[] = ['value' => 'default', 'label' => __('Aucune personne sélectionnée')];
        $options[] = ['value' => 'contact', 'label' => __('Lésé')];
        $options[] = ['value' => 'mandate', 'label' => __('Mandant')];
        $options[] = ['value' => 'expertise_place', 'label' => __('Lieu d\'expertise')];
        $options[] = ['value' => 'expert', 'label' => __('Expert automobile')];
        $options[] = ['value' => 'insurer', 'label' => __('Assureur')];
        $options[] = ['value' => 'lawyer', 'label' => __('Avocat')];
        $options[] = ['value' => 'manufacturer', 'label' => __('Constructeur')];
        $options[] = ['value' => 'seller', 'label' => __('Vendeur')];
        $options[] = ['value' => 'repairer', 'label' => __('Réparateur')];
        $options[] = ['value' => 'technical_inspector', 'label' => __('Contrôleur technique')];
        $options[] = ['value' => 'other', 'label' => __('Autre')];
        $field = new BuilderFormField('person_type');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Destinataire'));
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $field->setSelect($select);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('person_type_content2');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('</div>');
        $this->builderFields->addField($field);
    }

    private function addName()
    {
        $field = new BuilderFormField('name');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Nom du thème'));
        $this->builderFields->addField($field);
    }

    private function addTitle()
    {
        $field = new BuilderFormField('title');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Titre du document'));
        $this->builderFields->addField($field);
    }

    private function addContent()
    {
        $group = new BuilderFormGroup('group_content');
        $group->setLabel(__('Contenu du document'));
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setOpened(true);

        $field = new BuilderFormField('content');
        $field->setType(FieldsEnum::TYPE_CKEDITOR);
        $field->setLabel(__('Contenu du document'));
        $group->addField($field);

        $this->builderFields->addField($group);
    }

    private function addContent2()
    {
        $group = new BuilderFormGroup('group_content2');
        $group->setLabel(__('Suite du contenu du document'));
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setClass('d-none');
        $group->setOpened(true);

        $field = new BuilderFormField('content2');
        $field->setType(FieldsEnum::TYPE_CKEDITOR);
        $field->setLabel(__('Suite du contenu du document'));
        $field->setHelp(__('Cette seconde partie sera affichée après l\'affichage de la date et du lieu d\'expertise dans une convocation.'));
        $group->addField($field);

        $this->builderFields->addField($group);
    }
}
