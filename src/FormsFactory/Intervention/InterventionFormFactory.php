<?php
namespace <PERSON><PERSON><PERSON>ver\FormsFactory\Intervention;

use Mat<PERSON><PERSON><PERSON>\Enums\FieldsEnum;
use Mat<PERSON><PERSON><PERSON>\Entity\Intervention\Intervention;
use MatGyver\Forms\Intervention\InterventionForm;
use MatGyver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\Helpers\Tools;
use Symfony\Component\Validator\Constraints\NotBlank;

class InterventionFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(InterventionForm::class);

        $this->addName();

        $this->setTitle(__('Création d\'une intervention'));
        $this->setCancelUrl(Tools::makeLink('app', 'interventions'));
    }

    /**
     * @param Intervention|null $intervention
     */
    public function preRenderFields(?Intervention $intervention)
    {
        if ($intervention) {
            $this->setTitle(__('Modification d\'une intervention'));
        }
    }

    private function addName()
    {
        $field = new BuilderFormField('name');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Nom'));
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez entrer le nom')]));
        $this->builderFields->addField($field);
    }


}
