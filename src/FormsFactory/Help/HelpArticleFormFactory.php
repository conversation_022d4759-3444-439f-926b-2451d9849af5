<?php

namespace Mat<PERSON>yver\FormsFactory\Help;

use MatGyver\Entity\Help\Article\HelpArticle;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Forms\Help\HelpArticleForm;
use MatG<PERSON>ver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormGroup;
use MatGyver\FormsFactory\Builder\BuilderFormSelect;
use MatGyver\Helpers\Tools;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\Help\HelpUniverseService;
use Symfony\Component\Validator\Constraints\NotBlank;

class HelpArticleFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(HelpArticleForm::class);

        $this->addUniverse();
        $this->addRoute();
        $this->addActive();

        $languages = HelpArticle::getLangFormChoices();
        foreach ($languages as $lang => $langName) {
            $this->addContent($lang, $langName, ($lang == 'fr'));
        }

        $this->setTitle(__('Création d\'un article'));
        $this->setCancelUrl(Tools::makeLink('admin', 'help'));
    }

    /**
     * @param HelpArticle|null $article
     */
    public function preRenderFields(?HelpArticle $article)
    {
        if ($article) {
            $this->setTitle(__('Modification d\'un article'));

            $select = $this->builderFields->getField('universe_id')->getSelect();
            $select->setParam($article->getUniverse()->getId());

            $select = $this->builderFields->getField('route')->getSelect();
            $select->setValue($article->getRoute());
        }
    }

    private function addUniverse()
    {
        $field = new BuilderFormField('universe_id');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Univers'));
        $select = new BuilderFormSelect();
        $select->setClass(HelpUniverseService::class);
        $select->setFunction('generateSelectUniverse');
        $field->setSelect($select);
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez sélectionner un univers')]));
        $this->builderFields->addField($field);
    }

    private function addRoute()
    {
        $container = ContainerBuilderService::getInstance();
        $router = $container->get(DispatcherService::class)->getRouter();
        $iterator = $router->getRouteCollection()->getIterator();
        $options = [];
        $options[] = ['value' => '', 'label' => __('Aucune')];
        foreach ($iterator as $route => $item) {
            if (!str_contains($item->getPath(), 'app/')) {
                continue;
            }
            $options[] = ['value' => $route, 'label' => $item->getPath()];
        }

        $field = new BuilderFormField('route');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Page (facultatif)'));
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $field->setSelect($select);
        $this->builderFields->addField($field);
    }

    private function addContent(string $lang, string $langName, bool $open = false)
    {
        $group = new BuilderFormGroup('content_group_' . $lang);
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel($langName);
        $group->setOpened($open);

        $field = new BuilderFormField('title' . ($lang != 'fr' ? '_' . $lang : ''));
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Titre'));
        if ($lang == 'fr_FR') {
            $field->setRequired(true);
            $field->addValidation(new NotBlank(['message' => __('Veuillez entrer un titre')]));
        }
        $group->addField($field);

        $field = new BuilderFormField('description' . ($lang != 'fr' ? '_' . $lang : ''));
        $field->setType(FieldsEnum::TYPE_TEXTAREA);
        $field->setLabel(__('Description'));
        $field->setHelp(__('Facultatif'));
        $group->addField($field);

        $field = new BuilderFormField('content' . ($lang != 'fr' ? '_' . $lang : ''));
        $field->setType(FieldsEnum::TYPE_CKEDITOR);
        $field->setLabel(__('Contenu'));
        $group->addField($field);

        $this->builderFields->addField($group);
    }

    private function addActive()
    {
        $field = new BuilderFormField('active');
        $field->setType(FieldsEnum::TYPE_SWITCH);
        $field->setLabel(__('Activé'));
        $field->setData([
            'label_active' => __('Publié'),
            'label_desactive' => __('Brouillon'),
        ]);
        $this->builderFields->addField($field);
    }
}
