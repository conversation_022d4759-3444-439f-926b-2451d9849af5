<?php

namespace <PERSON><PERSON><PERSON><PERSON>\FormsFactory\Chat;

use <PERSON><PERSON><PERSON><PERSON>\Entity\Chat\ChatRoom;
use <PERSON>G<PERSON><PERSON>\Enums\FieldsEnum;
use MatG<PERSON>ver\Forms\Chat\ChatRoomForm;
use Mat<PERSON><PERSON>ver\FormsFactory\AbstractFormFactory;
use Mat<PERSON>yver\FormsFactory\Builder\BuilderFormContentFunction;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Table\Chat\CreateChatRoomUsersViewHelper;

class ChatRoomFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(ChatRoomForm::class);

        $this->addName();
        $this->addUsers();

        $this->setTitle(__('Création d\'un groupe'));
        $this->setCancelUrl(Tools::makeLink($_SESSION['controller'], 'conversations'));
    }

    /**
     * @param ChatRoom|null $chatRoom
     * @throws \Exception
     */
    public function preRenderFields(?ChatRoom $chatRoom)
    {
        if ($chatRoom) {
            throw new \Exception('Unable to edit chat room');
        }
    }

    private function addName()
    {
        $field = new BuilderFormField('group_name');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Nom du groupe'));
        $this->builderFields->addField($field);
    }

    private function addUsers()
    {
        $field = new BuilderFormField('users');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setLabel(__('Utilisateurs'));
        $content = new BuilderFormContentFunction();
        $content->setClass(CreateChatRoomUsersViewHelper::class);
        $content->setFunction('getContent');
        $field->setContentFunction($content);
        $this->builderFields->addField($field);
    }
}
