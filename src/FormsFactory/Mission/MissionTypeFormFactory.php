<?php
namespace <PERSON><PERSON><PERSON><PERSON>\FormsFactory\Mission;

use <PERSON><PERSON><PERSON><PERSON>\Enums\ExpertiseEnum;
use Mat<PERSON><PERSON>ver\Enums\FieldsEnum;
use Mat<PERSON>yver\Entity\Mission\MissionType;
use MatGyver\Forms\Mission\MissionTypeForm;
use Mat<PERSON><PERSON><PERSON>\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Dossier\DossierExpertiseService;
use Symfony\Component\Validator\Constraints\NotBlank;

class MissionTypeFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(MissionTypeForm::class);

        $this->addName();
        $this->addReport();

        $this->setTitle(__('Création d\'un type de mission'));
        $this->setCancelUrl(Tools::makeLink('admin', 'missions', 'types'));
    }

    /**
     * @param MissionType|null $missionType
     */
    public function preRenderFields(?MissionType $missionType)
    {
        if ($missionType) {
            $this->setTitle(__('Modification d\'un type de mission'));
            $this->builderFields->updateField('report', 'value', $missionType->getReport());
        }
    }

    private function addName()
    {
        $field = new BuilderFormField('name');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Nom'));
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez entrer le nom')]));
        $this->builderFields->addField($field);
    }

    private function addReport()
    {
        $reports = DossierExpertiseService::getReports();
        $options = [];
        foreach ($reports as $reportType => $reportName) {
            $options[] = ['value' => $reportType, 'label' => $reportName];
        }

        $field = new BuilderFormField('report');
        $field->setType(FieldsEnum::TYPE_RADIO);
        $field->setLabel(__('Type de rapport d\'expertise'));
        $field->setOptions($options);
        $field->setValue(ExpertiseEnum::REPORT_STANDARD);
        $this->builderFields->addField($field);
    }
}
