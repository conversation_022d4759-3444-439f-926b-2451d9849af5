<?php

namespace Mat<PERSON>yver\FormsFactory\Affiliation;

use Mat<PERSON><PERSON><PERSON>\Entity\Affiliation\Commission\AffiliationCommission;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Forms\Affiliation\AffiliationCommissionsForm;
use Mat<PERSON><PERSON><PERSON>\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormSelect;
use MatGyver\Helpers\Currency;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Affiliation\AffiliationPartnersService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Shop\Transaction\ShopTransactionService;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\PositiveOrZero;

class AffiliationCommissionFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(AffiliationCommissionsForm::class);

        $this->addPartner();
        $this->addReference();
        $this->addProduct();
        $this->addAmount();
        $this->addStatus();

        $this->setTitle(__('Création d\'une commission'));
        $this->setCancelUrl(Tools::makeLink('admin', 'affiliation', 'commissions'));
    }

    /**
     * @param AffiliationCommission|null $commission
     */
    public function preRenderFields(?AffiliationCommission $commission)
    {
        if (isset($_GET['reference'])) {
            $reference = filter_input(INPUT_GET, 'reference', FILTER_UNSAFE_RAW);
            $this->builderFields->updateField('transaction_reference', 'value', $reference);

            $container = ContainerBuilderService::getInstance();
            $transaction = $container->get(ShopTransactionService::class)->getTransactionByReference($reference);
            if ($transaction) {
                $this->builderFields->updateField('product', 'value', stripslashes($transaction->getProductName()));
            }
        }

        if ($commission) {
            $this->setTitle(__('Modification d\'une commission'));

            $select = $this->builderFields->getField('id_partner')->getSelect();
            $select->setParam($commission->getPartner()->getId());
        }
    }

    private function addPartner()
    {
        $field = new BuilderFormField('id_partner');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Affilié'));
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez sélectionner un affilié')]));
        $select = new BuilderFormSelect();
        $select->setClass(AffiliationPartnersService::class);
        $select->setFunction('generateSelectPartners');
        $field->setSelect($select);
        $this->builderFields->addField($field);
    }

    private function addReference()
    {
        $field = new BuilderFormField('transaction_reference');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Numéro de transaction'));
        $field->setHelp(__('Indiquez ici le numéro de transaction'));
        $this->builderFields->addField($field);
    }

    private function addProduct()
    {
        $field = new BuilderFormField('product');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Produit'));
        $field->addValidation(new NotBlank(['message' => __('Veuillez indiquer le nom du produit')]));
        $this->builderFields->addField($field);
    }

    private function addAmount()
    {
        $field = new BuilderFormField('commission');
        $field->setType(FieldsEnum::TYPE_INPUT_GROUP);
        $field->setLabel(__('Montant de la commission'));
        $field->addValidation(new NotBlank(['message' => __('Veuillez indiquer le montant de la commission')]));
        $field->addValidation(new PositiveOrZero(['message' => __('Veuillez indiquer le montant de la commission')]));
        $field->setRequired(true);
        $field->setData([
            'input_type' => 'float',
            'addon' => Currency::displayCurrency(DEFAULT_CURRENCY)
        ]);
        $this->builderFields->addField($field);
    }

    private function addStatus()
    {
        $field = new BuilderFormField('status');
        $field->setType(FieldsEnum::TYPE_SWITCH);
        $field->setLabel(__('Valide'));
        $field->setOptions([
            'on' => 'valid',
            'off' => 'invalid',
        ]);
        $field->setData([
            'label_active' => __('Valide'),
            'label_desactive' => __('Invalide'),
        ]);
        $field->setValue('valid');
        $this->builderFields->addField($field);
    }
}
