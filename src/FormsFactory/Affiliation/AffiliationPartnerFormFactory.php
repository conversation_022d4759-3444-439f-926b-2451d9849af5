<?php

namespace Mat<PERSON>yver\FormsFactory\Affiliation;

use MatG<PERSON>ver\Entity\Affiliation\Partner\AffiliationPartner;
use MatGyver\Entity\Tos\Tos;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Forms\Affiliation\AffiliationPartnerForm;
use Mat<PERSON><PERSON>ver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Tos\TosService;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\NotCompromisedPassword;
use Symfony\Component\Validator\Constraints\NotNull;

class AffiliationPartnerFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(AffiliationPartnerForm::class);

        $this->addIntro();

        $fields = $this->getUserInfosFields();
        foreach ($fields as $field) {
            $field->setStep(1);
            $this->builderFields->addField($field);
        }

        $this->addPassword();
        $this->addAddress();
        $this->addCaptcha();
        $this->addSpamTrap();

        if (isset($_GET['aff'])) {
            $parent = filter_input(INPUT_GET, 'aff', FILTER_UNSAFE_RAW);
            $field = new BuilderFormField('parent');
            $field->setType(FieldsEnum::TYPE_HIDDEN);
            $field->setValue($parent);
            $field->setStep(1);
            $this->builderFields->addField($field);
        }

        $container = ContainerBuilderService::getInstance();
        $tos = $container->get(TosService::class)->getRepository()->getLast();
        if ($tos) {
            $this->addTos($tos);
        }

        $this->setTitle(__('Inscription au programme d\'affiliation'));
    }

    /**
     * @param AffiliationPartner|null $partner
     */
    public function preRenderFields(?AffiliationPartner $partner)
    {
        if ($partner) {
            throw new \Exception(__('Impossible de modifier cet affilié'));
        }
    }

    private function addIntro()
    {
        $content = '
            <div class="pb-10">
                <h3 class="font-weight-bolder text-dark display5">' . __('Inscription au programme d\'affiliation') . '</h3>
            </div>';
        $field = new BuilderFormField('content_step1');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($content);
        $field->setStep(1);
        $this->builderFields->addField($field);
    }

    private function addPassword()
    {
        $field = new BuilderFormField('password');
        $field->setType(FieldsEnum::TYPE_PASSWORD);
        $field->setLabel(__('Mot de passe'));
        $field->setHelp(__('Veuillez indiquer un mot de passe de 12 caractères minimum et comportant au moins une lettre majuscule, un chiffre et un caractère spécial (parmi #@&:.;,+=-_(){}<>/!?).'));
        $field->setRequired(true);
        $field->setStep(1);
        $field->addValidation(new NotBlank(['message' => __('Veuillez entrer un mot de passe')]));
        $field->addValidation(new NotCompromisedPassword(['message' => __('Ce nouveau mot de passe ne peut être utilisé car il est référencé comme mot de passe compromis.')]));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('password2');
        $field->setType(FieldsEnum::TYPE_PASSWORD);
        $field->setLabel(__('Mot de passe (confirmation)'));
        $field->setRequired(true);
        $field->setStep(1);
        $field->addValidation(new NotBlank(['message' => __('Veuillez confirmer le mot de passe')]));
        $this->builderFields->addField($field);
    }

    private function addAddress()
    {
        $addressFields = $this->getAddressFields();
        foreach ($addressFields as $addressField) {
            $addressField->setStep(2);
            $this->builderFields->addField($addressField);
        }

        $field = new BuilderFormField('company');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Société'));
        $field->setStep(3);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('tva_intracom');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Numéro de TVA intracommunautaire'));
        $field->setStep(3);
        $this->builderFields->addField($field);
    }

    private function addCaptcha()
    {
        Assets::addJs('https://www.google.com/recaptcha/api.js');

        $field = new BuilderFormField('content');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setStep(3);
        $field->setContent('
            <div style="width:304px; display:block; margin-top:20px;">
                <div class="g-recaptcha" data-sitekey="' . GOOGLE_RECAPTCHA_PUBLIC_KEY . '" style="margin: 0 auto; display:block;"></div>
            </div>');
        $this->builderFields->addField($field);
    }

    private function addSpamTrap()
    {
        $field = new BuilderFormField('form_time');
        $field->setType(FieldsEnum::TYPE_HIDDEN);
        $field->setValue(time());
        $field->setStep(1);
        $this->builderFields->addField($field);
    }

    /**
     * @param Tos|null $tos
     */
    private function addTos(?Tos $tos)
    {
        if (!$tos) {
            return;
        }

        $field = new BuilderFormField('cgv');
        $field->setType(FieldsEnum::TYPE_HIDDEN);
        $field->setValue(true);
        $field->setStep(3);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('cgv_txt');
        $field->setType(FieldsEnum::TYPE_HIDDEN);
        $field->setValue(__('J\'ai lu les conditions générales de vente et j\'y adhère sans réserve.'));
        $field->setStep(3);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('cgv-accept');
        $field->setType(FieldsEnum::TYPE_SWITCH);
        $field->setLabel(__('Conditions Générales de Vente'));
        $field->setHelp(__('J\'ai lu les conditions générales de vente et j\'y adhère sans réserve.') . ' <a href="' . Tools::makeLink('site', 'tos') . '" target="_blank">' . __('Lire les conditions générales de vente.') . '</a>');
        $field->setRequired(true);
        $field->setStep(3);
        $field->addValidation(new NotNull(['message' => __('Veuillez accepter les conditions générales de vente')]));
        $this->builderFields->addField($field);
    }
}
