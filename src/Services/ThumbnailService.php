<?php

namespace MatGyver\Services;

use MatGyver\Services\Logger\LoggerService;

/**
 * Class ThumbnailService
 * @package MatGyver\Services
 */
class ThumbnailService
{
    /**
     * @param string $source
     * @param string $destination
     * @param int    $height
     * @return void
     * @throws \Exception
     */
    public static function resizeToHeight(string $source, string $destination, int $height)
    {
        list($originalWidth, $originalHeight) = getimagesize($source);
        if (!$originalWidth or !$originalHeight) {
            LoggerService::logError('Unable to get image size for file ' . $source);
            return;
        }
        $ratio = $height / $originalHeight;
        $width = round($originalWidth * $ratio);
        self::resize($source, $destination, $width, $height);
    }

    /**
     * @param string $source
     * @param string $destination
     * @param int    $width
     * @return void
     * @throws \Exception
     */
    public static function resizeToWidth(string $source, string $destination, int $width)
    {
        list($originalWidth, $originalHeight) = getimagesize($source);
        $ratio = $width / $originalWidth;
        $height = round($originalHeight * $ratio);
        self::resize($source, $destination, $width, $height);
    }

    /**
     * @param string $source
     * @param string $destination
     * @param int    $w
     * @param int    $h
     * @return void
     * @throws \Exception
     */
    public static function resize(string $source, string $destination, int $w, int $h): void
    {
        list($originalWidth, $originalHeight) = getimagesize($source);
        $ratio = $originalWidth / $originalHeight;

        if ($w/$h > $ratio) {
            $newWidth = round($h * $ratio);
            $newHeight = $h;
        } else {
            $newHeight = round($w / $ratio);
            $newWidth = $w;
        }

        $pathInfo = pathinfo($source);

        if ($pathInfo['extension'] === 'png') {
            $imageSource = imagecreatefrompng($source);
        } else {
            $imageSource = imagecreatefromjpeg($source);
        }
        if ($imageSource === false) {
            throw new \Exception('Unable to create image source');
        }

        $imageDest = imagecreatetruecolor($newWidth, $newHeight);
        if ($imageDest === false) {
            throw new \Exception('Unable to create image destination');
        }

        if (!imagecopyresampled($imageDest, $imageSource, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight)) {
            throw new \Exception('Unable to copy image');
        }

        if ($pathInfo['extension'] === 'png') {
            imagepng($imageDest, $destination, 0);
        } else {
            imagejpeg($imageDest, $destination, 100);
        }
    }

    public static function fixOrientation(string $source): void
    {
        $extension = getExtension($source);
        if ($extension === 'jpg' or $extension === 'jpeg') {
            $exif = @exif_read_data($source);
            if ($exif and isset($exif['Orientation'])) {
                $orientation = $exif['Orientation'];
                if ($orientation != 1) {
                    $deg = 0;
                    switch ($orientation) {
                        case 3:
                            $deg = 180;
                            break;
                        case 6:
                            $deg = 270;
                            break;
                        case 8:
                            $deg = 90;
                            break;
                    }
                    if ($deg) {
                        $img = imagecreatefromstring(file_get_contents($source));
                        $img = imagerotate($img, $deg, 0);
                        imagejpeg($img, $source);
                    }
                }
            }
        }
    }
}
