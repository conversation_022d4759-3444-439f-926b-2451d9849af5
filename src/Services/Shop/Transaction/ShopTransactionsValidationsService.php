<?php

namespace MatGyver\Services\Shop\Transaction;

use Doctrine\ORM\EntityManager;
use MatGyver\Components\Mailer\MailSender;
use MatGyver\Entity\Notification\Notification;
use MatGyver\Entity\Shop\Invoice\ShopInvoice;
use MatGyver\Entity\Shop\Transaction\ShopTransaction;
use MatGyver\Enums\ConfigEnum;
use MatGyver\Enums\ProductsEnum;
use MatGyver\Enums\UserEmailNotificationTypeEnum;
use MatGyver\Helpers\Chars;
use MatGyver\Helpers\Entities;
use MatGyver\Helpers\Number;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\Transaction;
use MatGyver\Repository\Shop\Transaction\ShopTransactionRepository;
use MatGyver\Services\Clients\ClientsSubscriptionsService;
use MatGyver\Services\Affiliation\AffiliationClicsService;
use MatGyver\Services\Affiliation\AffiliationCommissionErrorService;
use MatGyver\Services\Affiliation\AffiliationCommissionService;
use MatGyver\Services\Affiliation\AffiliationPartnersService;
use MatGyver\Services\Affiliation\AffiliationSubPartnersService;
use MatGyver\Services\Autoresponders\AutorespondersProductPostService;
use MatGyver\Services\BaseEntityService;
use MatGyver\Services\Clients\ClientsService;
use MatGyver\Services\Clients\ClientsTransactionsService;
use MatGyver\Services\ConfigService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Dossier\DossierPaymentLinkService;
use MatGyver\Services\Integration\IntegrationAccountsService;
use MatGyver\Services\Integration\Services\FacebookApiService;
use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\NotificationsService;
use MatGyver\Services\PaymentsMethodsService;
use MatGyver\Services\Shop\CreditNotes\ShopCreditNotesService;
use MatGyver\Services\Shop\Invoices\ShopInvoicesService;
use MatGyver\Services\Shop\Product\ShopProductsService;
use MatGyver\Services\Shop\ShopCartProductsService;
use MatGyver\Services\Shop\ShopCartService;
use MatGyver\Services\Shop\ShopCustomersService;
use MatGyver\Services\Shop\ShopDiscountsService;
use MatGyver\Services\Shop\ShopPaymentsService;
use MatGyver\Services\Shop\Transaction\Error\ShopTransactionErrorService;
use MatGyver\Services\Subscription\SubscriptionService;
use MatGyver\Services\TwigService;
use MatGyver\Services\Users\UsersService;

/**
 * Class ShopTransactionsValidationsService
 * @package MatGyver\Services\Shop\Transaction
 * @property ShopTransactionRepository $repository
 * @method ShopTransactionRepository getRepository()
 */
class ShopTransactionsValidationsService extends BaseEntityService
{
    /**
     * @var MailSender
     */
    private $mailSender;

    /**
     * @param EntityManager $em
     * @param MailSender $mailSender
     */
    public function __construct(
        EntityManager $em,
        MailSender $mailSender
    ) {
        $this->em = $em;
        $this->repository = $em->getRepository(ShopTransaction::class);
        $this->mailSender = $mailSender;
    }

    /**
     * @param string $reference
     * @param string $paymentStatus
     * @param string $paymentName
     * @param string $productName
     * @param float $amount
     * @param string $currency
     * @param string $custom
     * @return array
     */
    public function saveOrder(
        string $reference,
        string $paymentStatus,
        string $paymentName,
        string $productName,
        float $amount,
        string $currency = DEFAULT_CURRENCY,
        string $custom = ''
    ): array {
        $log = "Function saveOrder\n";

        $refF = new \ReflectionMethod($this, 'saveOrder');
        $result = array();
        foreach ($refF->getParameters() as $param) {
            $result[$param->name] = ${$param->name};
        }
        $log .= json_encode($result) . "\n\n";
        $request = @file_get_contents('php://input');

        //check if order already exists
        $checkIfOrderExists = $this->checkIfOrderExists($reference, $paymentStatus, $amount);
        $log .= $checkIfOrderExists['log'];
        if (!$checkIfOrderExists['valid']) {
            LoggerService::logWarning($checkIfOrderExists['message']);
        }
        if ($checkIfOrderExists['action'] == 'exit') {
            return $checkIfOrderExists;
        }

        $transactionValid = 0;
        if ($paymentStatus == ShopTransaction::STATUS_COMPLETED) {
            $transactionValid = 1;
        }

        //champ custom
        $data = Transaction::parseCustom($custom);
        $log .= "Datas from custom : " . json_encode($data) . "\n";
        $cancelUrl = $data['cancel_url'];
        $validUrl = $data['valid_url'];
        $idPartner = $data['id_partner'];
        $idCart = $data['id_cart'];
        $idCustomer = $data['id_customer'];
        $idProduct = $data['id_product'];
        $ip = $data['ip'];
        $idPayment = $data['id_payment'];
        $ecommerce = (isset($data['ecommerce']) and $data['ecommerce']);

        //get products
        if (!$idCart) {
            $errorMessage = 'Transaction ' . $reference . ' : pas de panier';
            LoggerService::logError($errorMessage);
            return ['valid' => false, 'log' => $log . $errorMessage, 'message' => $errorMessage];
        }

        $container = ContainerBuilderService::getInstance();
        $cart = $container->get(ShopCartService::class)->getCartById($idCart);
        if (!$cart) {
            $errorMessage = 'Transaction ' . $reference . ' : le panier ' . $idCart . ' n\'existe pas.';
            LoggerService::logError($errorMessage);
            return ['valid' => false, 'log' => $log . $errorMessage, 'message' => $errorMessage];
        }

        $cartProducts = $container->get(ShopCartProductsService::class)->getProductsByIdCart($idCart);
        if (!$cartProducts) {
            $errorMessage = 'Transaction ' . $reference . ' : pas de produits dans le panier ' . $idCart;
            LoggerService::logError($errorMessage);
            return ['valid' => false, 'log' => $log . $errorMessage, 'message' => $errorMessage];
        }

        //get customer
        if (!$idCustomer) {
            $errorMessage = 'Transaction ' . $reference . ' : pas de customer';
            LoggerService::logError($errorMessage);
            return ['valid' => false, 'log' => $log . $errorMessage, 'message' => $errorMessage];
        }

        $customer = $container->get(ShopCustomersService::class)->getCustomerById($idCustomer);
        if (!$customer) {
            $errorMessage = 'Transaction ' . $reference . ' : le customer ' . $idCustomer . ' n\'existe pas';
            LoggerService::logError($errorMessage);
            return ['valid' => false, 'log' => $log . $errorMessage, 'message' => $errorMessage];
        }

        $account = null;
        $payment = $container->get(ShopPaymentsService::class)->getPaymentById($idPayment);
        if ($payment) {
            $account = $payment->getAccount();
        }
        if (!$account) {
            if ($paymentName == ShopTransaction::METHOD_STRIPE) {
                $charge = $container->get(ShopTransactionStripeService::class)->getRepository()->getChargeByReference($reference);
                if ($charge and $charge->getAccount()) {
                    $account = $charge->getAccount();
                }
            } elseif ($paymentName == ShopTransaction::METHOD_BRAINTREE) {
                $charge = $container->get(ShopTransactionBraintreeService::class)->getRepository()->getChargeByReference($reference);
                if ($charge and $charge->getAccount()) {
                    $account = $charge->getAccount();
                }
            } elseif ($paymentName == ShopTransaction::METHOD_MOLLIE) {
                $charge = $container->get(ShopTransactionMollieService::class)->getRepository()->getChargeByReference($reference);
                if ($charge and $charge->getAccount()) {
                    $account = $charge->getAccount();
                }
            }
        }

        $product = null;
        if (!$ecommerce) {
            $product = $container->get(ShopProductsService::class)->getRepository()->find($idProduct);
            if (!$product) {
                $errorMessage = 'Transaction ' . $reference . ' : impossible de trouver le produit ' . $idProduct;
                LoggerService::logError($errorMessage);
                return ['valid' => false, 'log' => $log . $errorMessage, 'message' => $errorMessage];
            }
        }

        $lastName = $customer->getLastName();
        $firstName = $customer->getFirstName();
        $email = $customer->getEmail();

        if (!$email) {
            $log .= 'Transaction ' . $reference . ' : pas d\'email';
            LoggerService::logError('Transaction ' . $reference . ' : pas d\'email');
            return ['valid' => false, 'log' => $log, 'message' => __('Transaction %s : pas d\'email', $reference)];
        }

        $log .= "Email : $email\n";

        //get user
        $userId = 0;

        $customerData = json_decode($customer->getDatas(), true);
        if (isset($customerData['uniqid']) and $customerData['uniqid']) {
            $uniqid = $customerData['uniqid'];
            $log .= "Uniqid found : $uniqid\n";
            $getClient = $container->get(ClientsService::class)->getClientByUniqId($uniqid);
            if ($getClient) {
                $log .= "Client found : " . $getClient->getId() . "\n";
                $user = $container->get(UsersService::class)->adminGetUserByEmail($email, $getClient->getId());
                if ($user) {
                    $userId = $user->getId();
                }
            }
        }
        $log .= "Search user by email -> user id = $userId\n";

        //affiliation
        $log .= "Recherche des informations manquantes pour l'affiliation\n";

        if (!$idPartner and $ip) {
            $log .= "Recherche du clic par IP $ip\n";
            $click = $container->get(AffiliationClicsService::class)->getRepository()->getClickByIP($ip);
            if ($click) {
                $partner = $click->getPartner();
                if ($partner) {
                    $idPartner = $partner->getId();
                }
            }
        }

        //subscription
        $firstOrderEntity = null;
        $getSubscription = $container->get(SubscriptionService::class)->getSubscriptionByTransaction($reference, $paymentName);
        if ($getSubscription) {
            $firstOrder = $container->get(SubscriptionService::class)->getFirstTransactionBySubscription($getSubscription->getId(), $paymentName);
            if ($firstOrder and $firstOrder->getTransactionReference() != $reference) {
                $firstOrderEntity = $this->repository->findOneBy(['reference' => $firstOrder->getTransactionReference()]);
            }
        }

        $previousTransaction = $container->get(ShopTransactionService::class)->getLastTransactionByCustom($email, $custom);
        if (!$userId and $previousTransaction and $previousTransaction->getUser()) {
            $user = $previousTransaction->getUser();
            $userId = $user->getId();
        }
        $log .= "User ID : $userId\n";

        if ($idPartner and $previousTransaction and !$previousTransaction->getPartner()) {
            $idPartner = 0;
        }
        if (!$idPartner and $previousTransaction and $previousTransaction->getPartner()) {
            $idPartner = $previousTransaction->getPartner()->getId();
        }
        $log .= "Vérification partenaire : idPartner = $idPartner\n";


        $log .= "Récupération des montants\n";
        $amountTaxIncl = $amount;

        $amounts = $container->get(ShopCartService::class)->getCartAmounts($cart, $idPayment);
        $products = $amounts['products'];
        $amountTaxExcl = $amounts['amount_tax_excl'];

        $nbPayments = $amounts['nb_payments'];
        if ($nbPayments > 1) {
            $amountTaxExcl = $amountTaxExcl / $nbPayments;
        }
        if ($amounts['amount_tax_excl'] == $amounts['amount_tax_incl']) {
            $log .= "La commande est passée en HT\n";
            $amountTaxExcl = $amountTaxIncl;
        } elseif ($amounts['trialAmount']) {
            $ratio = $amounts['amount_tax_excl'] / $amounts['amount_tax_incl'];
            $amountTaxExcl = $amountTaxIncl * $ratio;
        }

        if (!$amountTaxIncl) {
            $amountTaxExcl = 0;
        }

        //fix for order with a prorata amount
        if ($amountTaxExcl > $amountTaxIncl) {
            $ratio = $amounts['amount_tax_incl'] / $amounts['amount_tax_excl'];
            $amountTaxExcl = $amountTaxIncl / $ratio;
        }

        $amountTaxIncl = number_format($amountTaxIncl, 2, '.', '');
        $amountTaxExcl = number_format($amountTaxExcl, 2, '.', '');

        $user = $partner = null;
        if ($userId) {
            $user = $container->get(UsersService::class)->getUser($userId);
        }
        if ($idPartner) {
            $partner = $container->get(AffiliationPartnersService::class)->adminGetPartnerById($idPartner);
        }

        $client = $container->get(ClientsService::class)->getClient();

        $customerData = [
            'address' => $customer->getAddress(),
            'address2' => $customer->getAddress2(),
            'city' => $customer->getCity(),
            'zip' => $customer->getZip(),
            'state' => $customer->getState(),
            'country' => $customer->getCountry(),
            'telephone' => $customer->getTelephone(),
        ];
        if ($customer->getDecodedDatas()) {
            $customerData = array_merge($customerData, $customer->getDecodedDatas());
            if (isset($customerData['password'])) {
                unset($customerData['password']);
            }
        }

        //save transaction
        $transactionEntity = new ShopTransaction();
        $transactionEntity->setReference($reference);
        $transactionEntity->setUser($user);
        $transactionEntity->setClient($client);
        $transactionEntity->setShopCustomer($customer);
        $transactionEntity->setAccount($account);
        $transactionEntity->setProduct($product);
        $transactionEntity->setProductName($productName);
        $transactionEntity->setShopCart($cart);
        $transactionEntity->setValid($transactionValid);
        $transactionEntity->setStatus($paymentStatus);
        $transactionEntity->setPaymentMethod($paymentName);
        $transactionEntity->setAmountTaxExcl($amountTaxExcl);
        $transactionEntity->setAmountTaxIncl($amountTaxIncl);
        $transactionEntity->setCurrency($currency);
        $transactionEntity->setLastName($lastName);
        $transactionEntity->setFirstName($firstName);
        $transactionEntity->setEmail($email);
        $transactionEntity->setRequest($request);
        $transactionEntity->setCustom($custom);
        $transactionEntity->setValidUrl($validUrl);
        $transactionEntity->setCancelUrl($cancelUrl);
        $transactionEntity->setPartner($partner);
        $transactionEntity->setIp($ip);
        $transactionEntity->setDatas(json_encode($customerData));
        $transactionEntity->setNbPayments($nbPayments);
        $transactionEntity->setFbLog(false);
        $transactionEntity->setDate(new \DateTime('now'));
        if ($transactionValid) {
            $transactionEntity->setDateValid(new \DateTime('now'));
        }

        if ($getSubscription and $getSubscription->getNbPayments() != 'x') {
            $transactionEntity->setParent($firstOrderEntity);
        }

        $facebookPixelId = $container->get(ConfigService::class)->findByName(ConfigEnum::FACEBOOK_PIXEL_ID, CLIENT_MASTER);
        if ($facebookPixelId and $facebookPixelId->getValue()) {
            $account = $container->get(IntegrationAccountsService::class)->getAccount($facebookPixelId->getValue());
            if ($account) {
                $transactionEntity->setFacebookPixel($account);
            }
        }

        $this->persistAndFlush($transactionEntity);

        //save history
        $insertHistory = $container->get(ShopTransactionHistoryService::class)->insertHistory($transactionEntity->getId(), $paymentStatus, $amountTaxIncl);
        if (!$insertHistory['valid']) {
            $log .= "Impossible de créer un historique pour la commande $reference\n";
            LoggerService::logWarning("Impossible de créer un historique pour la commande $reference");
        }

        //save transaction products
        if ($products) {
            $transactionProducts = $products;

            //si le panier est différent : c'est une nouvelle commande initiée par le client
            if ($firstOrderEntity and $firstOrderEntity->getShopCart() === $transactionEntity->getShopCart()) {
                $log .= "first Order Entity = " . $firstOrderEntity->getReference() . "\n";

                $getTransactionProducts = $firstOrderEntity->getTransactionProducts();
                if ($getTransactionProducts) {
                    $discountProduct = 0;
                    $transactionProducts = [];
                    foreach ($getTransactionProducts as $id => $getTransactionProduct) {
                        //remove trial period
                        if ($getTransactionProduct->getName() == ShopInvoice::NAME_TRIAL_PERIOD) {
                            continue;
                        }

                        $transactionProducts[$id] = [
                            'name' => $getTransactionProduct->getName(),
                            'quantity' => $getTransactionProduct->getQuantity(),
                            'price_tax_excl' => $getTransactionProduct->getPriceTaxExcl(),
                            'price_tax_incl' => $getTransactionProduct->getPriceTaxIncl(),
                            'vat' => $getTransactionProduct->getVat(),
                            'currency' => $getTransactionProduct->getCurrency(),
                            'attributes' => []
                        ];
                        if ($getTransactionProduct->getProduct()) {
                            $transactionProducts[$id]['id'] = $getTransactionProduct->getProduct()->getId();
                        }
                        if ($getTransactionProduct->getAttributes()) {
                            $transactionProducts[$id]['attributes'] = json_decode($getTransactionProduct->getAttributes(), true);
                        }
                        if ($getTransactionProduct->getName() == ShopInvoice::NAME_DISCOUNT) {
                            $discountProduct = $id;
                        }
                    }

                    if ($discountProduct and $cart->getDiscount() and $cart->getDiscount() != '[]') {
                        $discount = json_decode($cart->getDiscount(), true);
                        if ($discount and $discount['code'] == ShopInvoice::NAME_PRORATA) {
                            $log .= "On n'ajoute pas la réduction car on a une commande parente et le code est : " . ShopInvoice::NAME_PRORATA . "\n";
                            unset($transactionProducts[$discountProduct]);
                        }
                    }
                }
            }

            $insertProducts = $container->get(ShopTransactionsProductsService::class)->insertProducts($transactionEntity->getId(), $transactionProducts, true);
            if (!$insertProducts['valid']) {
                $log .= "Erreur lors de l'ajout des produits : " . $insertProducts['message'] . "\n";
            } else {
                $log .= "Ajout des produits OK\n";
            }
        }

        if ($previousTransaction and $previousTransaction->getFacebookPixel()) {
            $container->get(FacebookApiService::class)->handleSubscription($transactionEntity, $previousTransaction->getFacebookPixel()->getId());
        }

        //send mail to client
        $sendMail = $this->sendMailValidateOrder($transactionEntity);
        if (!$sendMail['valid']) {
            $log .= "Erreur lors de l'envoi du mail à l'admin : " . $sendMail['message'] . "\n";
        }

        //validate order
        $completeOrder = $this->completeOrder($transactionEntity);
        $log .= $completeOrder['log'];
        if (!$completeOrder['valid']) {
            LoggerService::logError('Erreur lors de la validation de la transaction ' . $reference . ' : ' . $completeOrder['log']);
        }

        return ['valid' => true, 'log' => $log];
    }

    /**
     * @param ShopTransaction $transaction
     * @return array
     */
    protected function sendMailValidateOrder(ShopTransaction $transaction): array
    {
        $previousTransaction = $this->repository->getPrevTransactionByCustom($transaction->getReference(), $transaction->getCustom(), $transaction->getClient()->getId());

        $subject = '[' . APP_NAME . '] ';
        if ($transaction->getValid()) {
            if ($previousTransaction and $previousTransaction->getValid()) {
                $subject .= __('Nouveau paiement récurrent');
            } else {
                $subject .= __('Nouvelle commande');
            }
        } else {
            switch ($transaction->getStatus()) {
                case ShopTransaction::STATUS_REFUNDED:
                    $subject .= __('Remboursement effectué');
                    break;
                case ShopTransaction::STATUS_WAITING:
                    $subject .= __('Commande en attente de règlement');
                    break;
                case ShopTransaction::STATUS_CANCELED:
                    $subject .= __('Commande annulée');
                    break;
                default:
                    $subject .= __('Erreur de commande');
                    break;
            }
        }

        $customer = $transaction->getShopCustomer();
        $customerDatas = [];
        if ($customer and $customer->getDatas()) {
            $customerDatas = json_decode($customer->getDatas(), true);
        }

        //get products
        $products = array();
        if (count($transaction->getTransactionProducts())) {
            $transactionProducts = $transaction->getTransactionProducts();
            foreach ($transactionProducts as $transactionProduct) {
                if ($transactionProduct->getName() == ShopInvoice::NAME_TRIAL_PERIOD) {
                    foreach ($products as $id => $product) {
                        $products[$id]['name'] .= ' (période d\'essai)';
                    }
                    continue;
                }

                $products[] = [
                    'quantity' => $transactionProduct->getQuantity(),
                    'name' => $transactionProduct->getName()
                ];
            }
        }

        if (!$products) {
            $products[] = ['quantity' => 1, 'name' => $transaction->getProductName()];
        }

        $partner = $transaction->getPartner();

        $type = Transaction::displayTransactionPaymentMethod($transaction->getPaymentMethod());

        $message = TwigService::getInstance()->set('subject', $subject)
            ->set('transaction', $transaction)
            ->set('products', $products)
            ->set('previousTransaction', $previousTransaction)
            ->set('customer', $customer)
            ->set('customerDatas', $customerDatas)
            ->set('partner', $partner)
            ->set('type', $type)
            ->render('common/payments/mails/mail-order.php');
        return $this->mailSender->sendToClient($subject, $message, UserEmailNotificationTypeEnum::TRANSACTIONS_COMMANDE, [], $transaction->getClient()->getId());
    }

    /**
     * @param ShopTransaction $transaction
     * @return array
     */
    protected function completeOrder(ShopTransaction $transaction): array
    {
        $log = '';
        $error = false;

        //validate order
        if ($transaction->getStatus() == ShopTransaction::STATUS_COMPLETED) {
            $log .= "Appel de validateOrder " . $transaction->getReference() . "\n";
            $validateOrder = $this->validateOrder($transaction->getReference());
            if (!$validateOrder['valid']) {
                $error = true;
                $log .= "Erreur lors de la validation de la transaction : " . $validateOrder['message'] . "\n";
            }

            if (isset($validateOrder['log']) and $validateOrder['log']) {
                $log .= "Retour : " . $validateOrder['log'] . "\n";
            }
        } elseif ($transaction->getStatus() == ShopTransaction::STATUS_REFUNDED or $transaction->getStatus() == ShopTransaction::STATUS_ERROR) {
            $log .= "Appel de setOrderInError " . $transaction->getReference() . "\n";
            $setOrderInError = $this->setOrderInError($transaction->getReference());
            if (!$setOrderInError['valid']) {
                $error = true;
                $log .= "Erreur lors de la mise en erreur : " . $setOrderInError['message'] . "\n";
            }

            if (isset($setOrderInError['log']) and $setOrderInError['log']) {
                $log .= "Retour : " . $setOrderInError['log'] . "\n";
            }
        }

        return ['valid' => !$error, 'log' => $log];
    }

    /**
     * @param string $reference
     * @param string $status
     * @param string $amount
     * @return array
     */
    protected function checkIfOrderExists(string $reference, string $status, string $amount): array
    {
        $action = 'continue';
        $log = '';

        $transaction = $this->repository->findOneBy(['reference' => $reference]);
        if (!$transaction) {
            return ['valid' => true, 'message' => '', 'action' => $action, 'log' => $log];
        }

        $log .= "La commande $reference existe déjà\n";

        if ($status == ShopTransaction::STATUS_COMPLETED) {
            $action = 'exit';

            if (!$transaction->getValid()) {
                $log .= "La commande $reference n'est pas valide --> validation\n";

                $transaction->setStatus(ShopTransaction::STATUS_COMPLETED);
                $transaction->setValid(true);
                $this->persistAndFlush($transaction);

                $validateOrder = $this->validateOrder($reference);
                if (!$validateOrder['valid']) {
                    $log .= "Impossible de valider la commande $reference\n";
                    $log .= $validateOrder['message'];
                    return ['valid' => false, 'action' => $action, 'log' => $log, 'message' => __('Impossible de valider la commande %s', $reference)];
                }

                $sendMail = $this->sendMailValidateOrder($transaction);
                if (!$sendMail['valid']) {
                    $log .= "Erreur lors de l'envoi du mail à l'admin : " . $sendMail['message'] . "\n";
                    LoggerService::logError('Erreur lors de l\'envoi du mail à l\'admin : ' . $sendMail['message']);
                }

                $log .= "OK la commande $reference est valide\n";
                return ['valid' => true, 'action' => $action, 'log' => $log];
            }

            return ['valid' => true, 'action' => $action, 'log' => $log, 'message' => __('La commande %s existe déjà.', $reference)];
        }

        if ($status == ShopTransaction::STATUS_REFUNDED) {
            $action = 'exit';
            $log .= "La commande $reference existe déjà : on la met en erreur\n";
            $log .= "Montant de la transaction : " . $transaction->getAmountTaxIncl() . "\n";
            $log .= "Remboursement de $amount\n";

            if ($transaction->getAmountTaxIncl() == $amount) {
                $transaction->setStatus(ShopTransaction::STATUS_REFUNDED);
                $transaction->setValid(false);
                $this->persistAndFlush($transaction);

                $setOrderInError = $this->setOrderInError($reference);
                if (!$setOrderInError['valid']) {
                    $log .= "Impossible de mettre la commande $reference en erreur\n";
                    return ['valid' => false, 'action' => $action, 'log' => $log, 'message' => __('Impossible de mettre la commande en erreur.')];
                }

                $log .= "OK La commande $reference est en erreur\n";
                return ['valid' => true, 'action' => $action, 'log' => $log];
            }

            //partial refund
            $log .= "Remboursement partiel : on génère un avoir\n";
            if (str_contains($amount, ',')) {
                $amount = str_replace(',', '.', $amount);
                $amount = (float) $amount;
            }
            $update = $this->setOrderPartiallyRefunded($reference, $amount);
            if (!$update['valid']) {
                $log .= "Impossible de mettre la commande $reference en erreur\n";
                $log .= $update['log'] . "\n";
                return ['valid' => false, 'action' => $action, 'log' => $log, 'message' => __('Impossible de mettre la commande en erreur.')];
            }

            $log .= "OK La commande $reference a été partiellement remboursée\n";
            $log .= $update['log'] . "\n";

            return ['valid' => true, 'action' => $action, 'log' => $log];
        }

        if ($status == ShopTransaction::STATUS_ERROR) {
            $action = 'exit';

            if (!$transaction->getValid()) {
                $log .= "La commande $reference existe déjà et est déjà en erreur\n";
                return ['valid' => true, 'action' => $action, 'log' => $log, 'message' => __("La commande %s existe déjà et est déjà en erreur.", $reference)];
            }

            $transaction->setStatus(ShopTransaction::STATUS_ERROR);
            $transaction->setValid(false);
            $this->persistAndFlush($transaction);

            $setOrderInError = $this->setOrderInError($reference);
            if (!$setOrderInError['valid']) {
                $log .= "Impossible de mettre la commande $reference en erreur\n";
                return ['valid' => false, 'action' => $action, 'log' => $log, 'message' => __('Impossible de mettre la commande en erreur.')];
            }

            $sendMail = $this->sendMailValidateOrder($transaction);
            if (!$sendMail['valid']) {
                $log .= "Erreur lors de l'envoi du mail à l'admin : " . $sendMail['message'] . "\n";
                LoggerService::logError('Erreur lors de l\'envoi du mail à l\'admin : ' . $sendMail['message']);
            }

            $log .= "OK La commande $reference est en erreur\n";
            return ['valid' => true, 'action' => $action, 'log' => $log];
        }

        return ['valid' => true, 'action' => $action, 'log' => $log];
    }

    /**
     * @param string $reference
     * @param bool $sendEmail
     * @return array
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function validateOrder(string $reference, bool $sendEmail = true): array
    {
        $log = "Appel validateOrder $reference \n";
        $log .= 'Date : ' . date('Y-m-d H:i:s') . "\n";

        $container = ContainerBuilderService::getInstance();
        $transaction = $container->get(ShopTransactionService::class)->getTransactionByReference($reference);
        if (!$transaction) {
            return array('valid' => false, 'message' => __('Cette transaction n\'existe pas'));
        }

        $idPartner = 0;
        if ($transaction->getPartner()) {
            $idPartner = $transaction->getPartner()->getId();
        }

        $previousTransaction = null;
        if ($transaction->getCustom() != 'transaction_add') {
            $previousTransaction = $this->repository->getPrevTransactionByCustom($reference, $transaction->getCustom());
            if (!$previousTransaction or !$previousTransaction->getValid()) {
                $previousTransaction = null;
            }
        }

        //update order
        if ($transaction->getStatus() == ShopTransaction::STATUS_WAITING) {
            $log .= "set transaction valid\n";
            $transaction->setStatus(ShopTransaction::STATUS_COMPLETED);
            $transaction->setValid(true);
            try {
                $this->persistAndFlush($transaction);
            } catch (\Exception $e) {
                $log .= 'Erreur lors de la mise à jour de la transaction : ' . $e->getMessage() . "\n";
                LoggerService::logError('Erreur lors de la mise à jour de la transaction : ' . $e->getMessage());
            }
        }

        $transactionProducts = $transaction->getTransactionProducts();
        if (!$transactionProducts) {
            $log .= "Cette transaction ne contient aucun produit !\n";
            $log .= "validateOrder finished (" . date('Y-m-d H:i:s') . ")\n";
            return ['valid' => true, 'log' => $log];
        }

        $hasDossierPayment = false;
        foreach ($transactionProducts as $transactionProduct) {
            if (!$transactionProduct->getProduct()) {
                continue;
            }
            if ($transactionProduct->getProduct()->getType() == ProductsEnum::TYPE_DOSSIER_PAYMENT) {
                $hasDossierPayment = true;
            }
        }

        //send invoice
        if ($_SESSION['client']['id'] == CLIENT_MASTER) {
            if (!$transaction->getAmountTaxIncl()) {
                $log .= "amount = 0 --> pas de facture\n";
            } elseif ($transaction->getParent() and $transaction->getNbPayments() != 'x') {
                $log .= "Cette transaction a un parent : " . $transaction->getParent()->getId() . " --> pas de facture\n";
            } else {
                $invoicesSettings = $container->get(ConfigService::class)->findByName(ConfigEnum::INVOICE);
                if ($invoicesSettings) {
                    $invoicesSettings = json_decode($invoicesSettings->getValue(), true);
                    if (isset($invoicesSettings['invoice_automatique']) and $invoicesSettings['invoice_automatique']) {
                        $log .= "Génération de la facture\n";

                        $data = array(
                            'transaction_reference' => $transaction->getReference(),
                        );
                        if ($transaction->getCustom() == 'transaction_add') {
                            $data['date_invoice'] = $transaction->getDate()->format('Y-m-d H:i:s');
                        }

                        if ($sendEmail) {
                            if (isset($invoicesSettings['invoice_email']) and $invoicesSettings['invoice_email']) {
                                $data['invoice_email'] = true;
                            }
                            if (isset($invoicesSettings['invoice_email_subscription']) and $invoicesSettings['invoice_email_subscription']) {
                                $data['invoice_email_subscription'] = true;
                            }
                        } else {
                            $data['dont_send_email'] = true;
                        }

                        $log .= 'Insert invoice Date : ' . date('Y-m-d H:i:s') . "\n";
                        $insertInvoice = $container->get(ShopInvoicesService::class)->generateInvoiceFromTransaction($data);
                        $log .= 'Retour insertInvoice Date : ' . date('Y-m-d H:i:s') . "\n";
                        $log .= 'Retour : ' . $insertInvoice['message'] . "\n";
                        if (!$insertInvoice['valid']) {
                            $log .= "Erreur lors de la création de la facture.\n";
                            LoggerService::logError('Erreur lors de la création de la facture : ' . $insertInvoice['message']);
                        } else {
                            $log .= "Création de la facture OK.\n";
                        }
                    }
                }
            }

            //check transaction error
            $container->get(ShopTransactionErrorService::class)->closeByCustom($transaction->getCustom(), 'Transaction #' . $transaction->getReference());
        }

        //handle products
        $cart = $transaction->getShopCart();
        $customer = $transaction->getShopCustomer();

        if (!$cart or !$customer) {
            $log .= "Pas de panier ou de client\n";
            $log .= "validateOrder finished (" . date('Y-m-d H:i:s') . ")\n";
            return array('valid' => true, 'log' => $log);
        }

        $log .= "Analyse du panier " . $cart->getId() . "\n";

        //update discount in cart
        if ($cart->getDiscount() != '' and $cart->getDiscount() != '[]') {
            $container->get(ShopDiscountsService::class)->updateDiscountFromCart($cart);
        }

        if ($transaction->getClient()->getId() != CLIENT_MASTER) {
            $log .= "transaction client_id = " . $transaction->getClient()->getId() . "\n";

            if (!$hasDossierPayment) {
                $log .= "handle transaction : no dossierPayment found\n";
                LoggerService::logError("Transaction " . $transaction->getReference() . " : no dossierPayment found\n");
            } else {
                $log .= "handle dossierPayment\n";
                $transactionProducts = $transaction->getTransactionProducts();
                foreach ($transactionProducts as $transactionProduct) {
                    if (!$transactionProduct->getProduct()) {
                        continue;
                    }
                    if ($transactionProduct->getProduct()->getType() == ProductsEnum::TYPE_DOSSIER_PAYMENT) {
                        $log .= "call handlePaymentLink for order " . $transaction->getId() . "\n";
                        $handleDossierPayment = $container->get(DossierPaymentLinkService::class)->handlePaymentLink($transactionProduct);
                        if (!$handleDossierPayment['valid']) {
                            $log .= "handleDossierPayment error = " . $handleDossierPayment['message'] . "\n";
                            LoggerService::logError("Transaction " . $transaction->getReference() . " : handleDossierPayment error = " . $handleDossierPayment['message'] . "\n");
                        } else {
                            $log .= "handleDossierPayment successful\n";
                        }
                    }
                }
            }

            $log .= "validateOrder finished (" . date('Y-m-d H:i:s') . ")\n";
            return array('valid' => true, 'log' => $log);
        }

        //client uniqid
        $uniqid = '';
        $customerData = json_decode($customer->getDatas(), true);
        if (isset($customerData['uniqid']) and $customerData['uniqid']) {
            $uniqid = $customerData['uniqid'];
        }

        $getClient = [];
        if (!$uniqid and !SUBDOMAIN_ENABLED) {
            $log .= "Pas de subdomain --> génération d'un uniqid\n";
            $uniqid = Chars::generateUniqid();

            $customerData['uniqid'] = $uniqid;
            $customer->setDatas(json_encode($customerData));
            try {
                $container->get(ShopCustomersService::class)->persistAndFlush($customer);
            } catch (\Exception $e) {
                LoggerService::logError($log);
            }
        }

        if (!$uniqid) {
            $log .= "Pas de uniqid spécifié\n";
        } else {
            $getClient = $container->get(ClientsService::class)->getClientByUniqId($uniqid);
            if ($getClient) {
                $log .= "Un client existe : " . $getClient->getId() . "\n";
            } else {
                $log .= "Aucun client n'existe avec le permalink $uniqid\n";
            }
        }

        $log .= "Recherche de l'abonnement associé à la transaction " . $transaction->getReference() . "\n";
        $subscription = $container->get(SubscriptionService::class)->getSubscriptionByTransaction($transaction->getReference(), $transaction->getPaymentMethod());
        if ($subscription) {
            $log .= 'Abonnement trouvé : ' . $subscription->getId() . "\n";
        } else {
            $log .= "Aucun abonnement trouvé\n";
        }

        $transactionProducts = $transaction->getTransactionProducts();
        if (!$transactionProducts) {
            $log .= "Cette transaction ne contient aucun produit !\n";
            $log .= "validateOrder finished (" . date('Y-m-d H:i:s') . ")\n";
            return array('valid' => true, 'log' => $log);
        }

        $log .= "Analyse des produits de la transaction " . $transaction->getReference() . "\n";

        $idClient = ($getClient ? $getClient->getId() : 0);
        $idClientSubscription = 0;

        foreach ($transactionProducts as $transactionProduct) {
            if ($transactionProduct->getName() == ShopInvoice::NAME_PAYMENT_FEES or $transactionProduct->getName() == ShopInvoice::NAME_DISCOUNT) {
                continue;
            }

            $product = $transactionProduct->getProduct();
            if (!$product) {
                $log .= "Le produit n'existe pas\n";
                continue;
            }

            $idProduct = $product->getId();
            $log .= "Analyse du produit $idProduct\n";

            if (!$previousTransaction and $product->getQuantities()) {
                $log .= "Mise à jour du stock\n";
                $container->get(ShopProductsService::class)->updateStock($idProduct, $transactionProduct->getQuantity());
            }

            //autoresponders
            $subscribe = $container->get(AutorespondersProductPostService::class)->executeAutorespondersByProductPost($idProduct, $transaction, $customer, $previousTransaction);
            if (!$subscribe['valid']) {
                $log .= "Errur autorépondeurs : " . $subscribe['message'] . "\n";
            } else {
                $log .= "Inscription autorépondeurs :\n" . $subscribe['log'] . "\n";
            }

            if ($uniqid and ($product->getType() == ProductsEnum::TYPE_SUBSCRIPTION or $product->getType() == ProductsEnum::TYPE_PAUSE)) {
                $idClientSubscription = 0;

                $openAccess = array(
                    'transaction_reference' => $transaction->getReference(),
                    'name' => $transaction->getFirstName() . ' ' . $transaction->getLastName(),
                    'uniqid' => $uniqid,
                    'subscription' => $product->getName(),
                    'date_end_subscription' => date('Y-m-d', strtotime('+' . $product->getDuration())),
                    'duree' => $product->getDuration(),
                    'price_tax_excl' => $transactionProduct->getPriceTaxExcl(),
                    'price_tax_incl' => $transactionProduct->getPriceTaxIncl(),
                    'last_name' => $transaction->getLastName(),
                    'first_name' => $transaction->getFirstName(),
                    'email' => $transaction->getEmail(),
                    'password' => '',
                    'ip' => $transaction->getIp(),
                    'product_id' => $product->getId(),
                    'product' => Entities::asArray($product),
                    'type' => $product->getType(),
                    'affiliation' => $product->getAffiliation(),
                    'id_subscription' => ($subscription ? $subscription->getId() : 0),
                    'quantity' => $transactionProduct->getQuantity(),
                    'customer_id' => ($transaction->getShopCustomer() ? $transaction->getShopCustomer()->getId() : 0),
                    'customer' => Entities::asArray($customer),
                    'customerData' => $customerData,
                    'transactionProduct' => Entities::asArray($transactionProduct),
                );

                if ($subscription) {
                    $log .= 'On a un abonnement --> prochain paiement prévu le ' . $subscription->getDate()->format('Y-m-d') . "\n";
                    $openAccess['id_subscription'] = $subscription->getId();

                    if ($subscription->getNbPaymentsLeft() == 'x') {
                        $nextPayment = $subscription->getDate()->format('Y-m-d');
                        $log .= 'Date fin abonnement : ' . $nextPayment . "\n";
                    } else {
                        //on est sur un abonnement en plusieurs fois, la date de fin d'abonnement a déjà été définie.
                        $log .= "abonnement nb_payments_left != x\n";
                        if ($getClient) {
                            $log .= 'on a un client : ' . $getClient->getId() . "\n";
                            $log .= 'abonnement actuel : ' . $getClient->getSubscription() . "\n";
                            $log .= 'abonnement à ajouter : ' . $openAccess['subscription'] . "\n";
                            if ($getClient->getSubscription() == $openAccess['subscription']) {
                                $openAccess['date_end_subscription'] = $getClient->getDateEndSubscription()->format('Y-m-d H:i:s');
                                $log .= 'Abonnement à durée déterminée --> Date fin abonnement : ' . $openAccess['date_end_subscription'] . "\n";
                            }
                        }
                    }
                }

                if ($getClient) {
                    //mise à jour
                    $log .= "Un client existe ($idClient)\n";

                    if (!$getClient->getActive()) {
                        $log .= "Réactivation du client\n";
                        $active = $container->get(ClientsService::class)->active_client($getClient->getId());
                        if (!$active['valid']) {
                            $log .= "Erreur lors de l'activation du client : " . $active['message'] . "\n\n";
                            LoggerService::logError($log);
                            return array('valid' => false, 'message' => __('Erreur lors de l\'activation du client : ') . $active['message']);
                        }
                    }

                    if ($getClient->getOnPause() and $product->getType() != ProductsEnum::TYPE_PAUSE) {
                        $log .= "Suppression de la mise en pause du client\n";
                        $setClientOffPause = $container->get(ClientsService::class)->setClientOffPause($getClient->getId());
                        if (!$setClientOffPause['valid']) {
                            $log .= "Erreur lors de la suppression de la pause du client : " . $setClientOffPause['message'] . "\n\n";
                            LoggerService::logError($log);
                            return array('valid' => false, 'message' => __('Erreur lors de la suppression de la pause du client : ') . $setClientOffPause['message']);
                        }
                    }

                    //update client
                    $openAccess['id'] = $getClient->getId();
                    $openAccess['name'] = $getClient->getName();

                    if (isset($nextPayment)) {
                        $openAccess['date_end_subscription'] = $nextPayment;
                    }

                    //don't update limits
                    if ($transaction->getParent()) {
                        $openAccess['dont_update_limits'] = false;
                    }

                    $log .= 'Appel de updateClient avec : ' . json_encode($openAccess) . "\n\n";
                    $update = $container->get(ClientsService::class)->updateClient($openAccess);
                    if (!$update['valid']) {
                        $log .= 'Erreur lors de la mise à jour du client : ' . $update['message'] . "\n\n";
                        LoggerService::logError($log);

                        return array('valid' => false, 'log' => $log, 'message' => __('Erreur lors de la mise à jour du client : ') . $update['message']);
                    }

                    $log .= "Mise à jour du client OK\n\n";

                    //on annule les autres abonnements s'il y en a
                    if ($product->getType() == ProductsEnum::TYPE_PAUSE) {
                        $previousSubscriptions = $container->get(ClientsSubscriptionsService::class)->getAllSubscriptionsActive($getClient->getId());
                        if ($previousSubscriptions) {
                            foreach ($previousSubscriptions as $previousSubscription) {
                                if ($previousSubscription->getTypeSubscription() != 'Stripe' and $previousSubscription->getTypeSubscription() != 'Braintree' and $previousSubscription->getTypeSubscription() != 'Mollie') {
                                    continue;
                                }
                                if ($previousSubscription->getProduct() and $previousTransaction->getProduct()->getId() == $idProduct) {
                                    continue;
                                }

                                //cancel current subscription
                                $cancelSubscription = $container->get(ClientsSubscriptionsService::class)->cancelSubscription($previousSubscription->getId(), $getClient->getId());
                                if (!$cancelSubscription['valid']) {
                                    $log .= "Erreur lors de la désactivation de l'abonnement " . $previousSubscription->getId() . " : " . $cancelSubscription['message'] . "\n\n";
                                    LoggerService::logError($log);
                                }
                            }
                        }
                    }

                    //recherche de l'abonnement
                    $log .= "Recherche de l'abonnement pour produit type : " . $openAccess['type'] . "\n";
                    $clientSubscription = $container->get(ClientsSubscriptionsService::class)->getRepository()->getSubscriptionActiveByType($product->getType(), $getClient->getId());
                    if ($clientSubscription) {
                        $idClientSubscription = $clientSubscription->getId();
                        $log .= "Ce client a déjà un abonnement : $idClientSubscription\n";
                        $log .= 'produit de cet abonnement : ' . $clientSubscription->getProductName() . " (" . ($clientSubscription->getProduct() ? $clientSubscription->getProduct()->getId() : '') . ")\n";
                        $log .= 'produit à ajouter : ' . $openAccess['subscription'] . " (" . $openAccess['product_id'] . ")\n";

                        $createNewSubscription = false;
                        if ($clientSubscription->getProduct() and $clientSubscription->getProduct()->getId() != $openAccess['product_id'] or ($transaction->getShopCustomer() and $clientSubscription->getCustomer()->getId() != $transaction->getShopCustomer()->getId())) {
                            $createNewSubscription = true;
                        }
                        if (!$createNewSubscription and $clientSubscription->getProduct()->getId() == $openAccess['product_id']) {
                            //check if subscription renew
                            $charge = null;
                            if ($transaction->getPaymentMethod() == ShopTransaction::METHOD_STRIPE) {
                                $charge = $container->get(ShopTransactionStripeService::class)->getRepository()->getChargeByReference($reference, CLIENT_MASTER);
                            } elseif ($transaction->getPaymentMethod() == ShopTransaction::METHOD_BRAINTREE) {
                                $charge = $container->get(ShopTransactionBraintreeService::class)->getRepository()->getChargeByReference($reference, CLIENT_MASTER);
                            } elseif ($transaction->getPaymentMethod() == ShopTransaction::METHOD_MOLLIE) {
                                $charge = $container->get(ShopTransactionMollieService::class)->getRepository()->getChargeByReference($reference, CLIENT_MASTER);
                            }

                            if ($charge and $charge->getCheckout()) {
                                $checkout = json_decode($charge->getCheckout(), true);
                                if (isset($checkout['upgradeSubscription']) and !$checkout['upgradeSubscription']) {
                                    //if upgradeSubscription = true, we don't create a new subscription
                                    //else : manual order for the same product (subscription renewal)
                                    $createNewSubscription = true;
                                }
                            }
                        }
                        if ($createNewSubscription) {
                            $log .= "produit différent ou id_customer différent : on crée un nouvel abonnement\n";
                            $idClientSubscription = 0;

                            //on désactive l'abonnement actuel
                            if ($clientSubscription->getTypeSubscription() == 'Stripe' or $clientSubscription->getTypeSubscription() == 'Braintree' or $clientSubscription->getTypeSubscription() == 'Mollie') {
                                $log .= "Désactivation de l'abonnement client " . $clientSubscription->getId() . "\n";
                                $cancelSubscription = $container->get(ClientsSubscriptionsService::class)->cancelSubscription($clientSubscription->getId(), $getClient->getId());
                                if (!$cancelSubscription['valid']) {
                                    $log .= "Erreur lors de la désactivation de l'abonnement client " . $clientSubscription->getId() . " : " . $cancelSubscription['message'] . "\n";
                                    LoggerService::logError($log);
                                }
                            }
                        }
                    }
                } else {
                    if (isset($nextPayment)) {
                        $openAccess['date_end_subscription'] = $nextPayment;
                    }

                    $log .= 'Appel de insertClient avec : ' . json_encode($openAccess) . "\n\n";

                    $register = $container->get(ClientsService::class)->insertClient($openAccess);
                    if (!$register['valid']) {
                        $log .= "Erreur lors de l'enregistrement du client : " . $register['message'] . "\n\n";
                        LoggerService::logError($log);
                        return array('valid' => false, 'log' => $log, 'message' => __('Erreur lors de l\'enregistrement du client : ') . $register['message']);
                    }

                    $idClient = $register['client_id'];
                    $clientUserId = $register['user_id'];
                    $uniqid = $register['uniqid'];
                    $log .= "Création du client OK\n\n";
                    $log .= "Client : $idClient\n\n";
                    $log .= "Client User Id : $clientUserId\n\n";

                    if (!$transaction->getUser()) {
                        $transaction->setUser($container->get(UsersService::class)->getUser($clientUserId, $idClient));
                        try {
                            $this->persistAndFlush($transaction);
                        } catch (\Exception $e) {
                            $log .= 'Erreur lors de la mise à jour de la transaction : ' . $e->getMessage() . "\n";
                            LoggerService::logError('Erreur lors de la mise à jour de la transaction : ' . $e->getMessage());
                        }
                    }

                    //save payment method
                    if ($transaction->getPaymentMethod() == ShopTransaction::METHOD_STRIPE or $transaction->getPaymentMethod() == ShopTransaction::METHOD_BRAINTREE or $transaction->getPaymentMethod() == ShopTransaction::METHOD_MOLLIE) {
                        $log .= "Sauvegarde du moyen de paiement\n";
                        $savePaymentMethod = $container->get(PaymentsMethodsService::class)->saveByTransaction($reference, $idClient);
                        if (!$savePaymentMethod['valid']) {
                            $log .= "Erreur lors de la sauvegarde du moyen de paiement : " . $savePaymentMethod['message'] . "\n";
                            LoggerService::logError('Erreur lors de la sauvegarde du moyen de paiement : ' . $savePaymentMethod['message']);
                        } elseif (isset($savePaymentMethod['message']) and $savePaymentMethod['message']) {
                            $log .= $savePaymentMethod['message'] . "\n";
                        }
                    }
                }

                if (!$idClientSubscription) {
                    $log .= "enregistrement d'un nouvel abonnement\n";
                    $arraySubscription = array(
                        'client_id' => $idClient,
                        'product_id' => $idProduct,
                        'product_name' => $product->getName(),
                        'subscription_id' => ($subscription ? $subscription->getId() : 0),
                        'type_subscription' => $transaction->getPaymentMethod(),
                        'transaction_reference' => $transaction->getReference(),
                        'customer_id' => ($transaction->getShopCustomer() ? $transaction->getShopCustomer()->getId() : 0),
                        'amount_tax_excl' => $transactionProduct->getPriceTaxExcl(),
                        'amount_tax_incl' => $transactionProduct->getPriceTaxIncl(),
                    );
                    $insertSubscription = $container->get(ClientsSubscriptionsService::class)->insertSubscription($arraySubscription);
                    if (!$insertSubscription['valid']) {
                        $log .= "Erreur lors de l'enregistrement de l'abonnement : " . $insertSubscription['message'] . "\n\n";
                        LoggerService::logError($log);
                    }

                    $idClientSubscription = $insertSubscription['id_subscription'];
                } else {
                    $log .= "Ce client a déjà un abonnement ($idClientSubscription)\n";
                }
            } else {
                if ($idClient) {
                    $clientSubscription = $container->get(ClientsSubscriptionsService::class)->getSubscriptionActiveByProduct($idProduct, $getClient->getId());
                    if ($clientSubscription) {
                        $idClientSubscription = $clientSubscription->getId();
                    }
                }
            }
        }

        if ($idClient) {
            $insert = $container->get(ClientsTransactionsService::class)->insertTransaction($idClient, $transaction->getId(), $idClientSubscription);
            if (!$insert['valid']) {
                $log .= "Erreur lors de l'enregistrement de la transaction pour ce client : " . $insert['message'] . "\n";
                LoggerService::logError($log);
            }
        }

        //remove password from customer data
        if (isset($customerData['password']) and $customerData['password']) {
            unset($customerData['password']);
            $customer->setDatas(json_encode($customerData));
            try {
                $container->get(ShopCustomersService::class)->persistAndFlush($customer);
            } catch (\Exception $e) {
                LoggerService::logError($log);
            }
        }

        //affiliation
        if ($idPartner) {
            $log .= "Démarrage de l'affiliation (id_partner = $idPartner)\n";

            $totalAmount = 0;
            foreach ($transactionProducts as $transactionProduct) {
                if ($transactionProduct->getName() == ShopInvoice::NAME_PAYMENT_FEES) {
                    continue;
                }
                if ($transactionProduct->getName() == ShopInvoice::NAME_DISCOUNT) {
                    $totalAmount -= $transactionProduct->getPriceTaxExcl();
                    continue;
                }
                $totalAmount += $transactionProduct->getPriceTaxExcl() * $transactionProduct->getQuantity();
            }

            $ratio = $transaction->getAmountTaxExcl() / $totalAmount;

            //insert subpartner
            if ($idClient) {
                $partner = $container->get(AffiliationPartnersService::class)->adminGetPartnerById($idPartner);
                if ($partner) {
                    $log .= "Create subpartner (partner : " . $partner->getClient()->getId() . " / subpartner : $idClient)\n";
                    $data = [
                        'client_id' => $partner->getClient()->getId(),
                        'subpartner_client_id' => $idClient,
                    ];
                    $insertSubPartner = $container->get(AffiliationSubPartnersService::class)->insert($data);
                    if (!$insertSubPartner['valid']) {
                        $log .= "Erreur lors de l'enregistrement du subpartner : " . $insertSubPartner['message'] . "\n";
                    }
                }
            }

            foreach ($transactionProducts as $transactionProduct) {
                if ($transactionProduct->getName() == ShopInvoice::NAME_PAYMENT_FEES or $transactionProduct->getName() == ShopInvoice::NAME_DISCOUNT or $transactionProduct->getName() == ShopInvoice::NAME_TRIAL_PERIOD) {
                    continue;
                }

                $product = $transactionProduct->getProduct();
                if (!$product or !$product->getAffiliation()) {
                    continue;
                }

                $idProduct = $product->getId();
                $log .= "Démarrage de l'affiliation pour le produit $idProduct (" . $product->getName() . ")\n";

                $amount = $transactionProduct->getPriceTaxExcl() * $ratio;
                $commission = array(
                    'partner_id' => $idPartner,
                    'product_id' => $idProduct,
                    'product' => $product->getName(),
                    'client_id' => $idClient,
                    'transaction_reference' => $transaction->getReference(),
                    'amount' => $amount,
                    'ip' => $transaction->getIp(),
                );

                $log .= 'Appel de insertFromTransaction : ' . json_encode($commission) . "\n";
                $insertCommission = $container->get(AffiliationCommissionService::class)->insertFromTransaction($commission);
                if (!$insertCommission['valid']) {
                    $log .= "Erreur lors de l'enregistrement de la commission : " . $insertCommission['message'] . "\n";

                    $commission['error'] = $insertCommission['message'];
                    $container->get(AffiliationCommissionErrorService::class)->insert($commission);
                }
            }
            $log .= "Fin de l'affiliation\n";
        }

        $log .= "validateOrder finished (" . date('Y-m-d H:i:s') . ")\n";
        return array('valid' => true, 'log' => $log);
    }

    /**
     * @param string $reference
     * @return array
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function setOrderInError(string $reference): array
    {
        $log = "appel setOrderInError $reference\n";

        $container = ContainerBuilderService::getInstance();
        $transaction = $container->get(ShopTransactionService::class)->getTransactionByReference($reference);
        if (!$transaction) {
            return array('valid' => false, 'message' => __('Cette transaction n\'existe pas'));
        }

        $insertHistory = $container->get(ShopTransactionHistoryService::class)->insertHistory($transaction->getId(), $transaction->getStatus(), $transaction->getAmountTaxIncl());
        if (!$insertHistory['valid']) {
            $log .= "Impossible de créer un historique pour la commande $reference (etat : " . $transaction->getStatus() . ")\n";
            LoggerService::logWarning('Impossible de créer un historique pour la commande ' . $reference . ' (etat : ' . $transaction->getStatus() . ')');
        }

        if ($_SESSION['client']['id'] != CLIENT_MASTER) {
            return array('valid' => true, 'log' => $log);
        }

        //create credit note
        if ($transaction->getStatus() == ShopTransaction::STATUS_REFUNDED) {
            $invoicesSettings = $container->get(ConfigService::class)->findByName(ConfigEnum::INVOICE, CLIENT_MASTER);
            if ($invoicesSettings) {
                $invoicesSettings = json_decode($invoicesSettings->getValue(), true);
                if (isset($invoicesSettings['invoice_automatique']) and $invoicesSettings['invoice_automatique']) {
                    $log .= "Génération d'un avoir\n";
                    $insertCreditNote = $container->get(ShopCreditNotesService::class)->generateCreditNoteFromTransaction($reference);
                    if (!$insertCreditNote['valid']) {
                        $log .= "Erreur lors de la création de l'avoir.\n";
                        LoggerService::logError("Erreur lors de la création de l'avoir : " . $insertCreditNote['message']);
                    } else {
                        $log .= "Création de l'avoir OK.\n";
                    }
                }
            }
        }

        $transactionClient = $container->get(ClientsTransactionsService::class)->getRepository()->getClientTransactionByTransactionId($transaction->getId());
        if (!$transactionClient) {
            $customer = $transaction->getShopCustomer();

            $uniqId = '';
            $customerData = json_decode($customer->getDatas(), true);
            if (isset($customerData['uniqid']) and $customerData['uniqid']) {
                $uniqId = $customerData['uniqid'];
            }

            $idClient = null;
            if (!$uniqId) {
                $log .= "Pas de uniqid spécifié\n";
            } else {
                $client = $container->get(ClientsService::class)->getClientByUniqId($uniqId);
                if ($client) {
                    $idClient = $client->getId();
                    $log .= "Un client existe : " . $client->getId() . "\n";
                } else {
                    $log .= "Aucun client n'existe avec le permalink $uniqId\n";
                }
            }

            if ($idClient) {
                $idClientSubscription = 0;
                if ($transaction->getProduct()) {
                    $clientSubscription = $container->get(ClientsSubscriptionsService::class)->getSubscriptionActiveByProduct($transaction->getProduct()->getId(), $idClient);
                    if ($clientSubscription) {
                        $idClientSubscription = $clientSubscription->getId();
                    }
                }

                $insert = $container->get(ClientsTransactionsService::class)->insertTransaction($idClient, $transaction->getId(), $idClientSubscription);
                if (!$insert['valid']) {
                    $log .= "setOrderInError : Erreur lors de l'enregistrement de la transaction pour ce client : " . $insert['message'] . "\n";
                    LoggerService::logError($log);
                } else {
                    $transactionClient = $container->get(ClientsTransactionsService::class)->getRepository()->getClientTransactionByTransactionId($transaction->getId());
                }
            }
        }

        if (!$transactionClient) {
            $log .= "La transaction $reference n'est pas associée à un client\n\n";
            LoggerService::logError("La transaction $reference n'est pas associée à un client");
        } else {
            //send notification
            $subject = __('Commande en erreur');
            $message = '<p>' . __('Un problème a été constaté pour le paiement de votre commande sur %s.', APP_NAME) . '</p>';
            $message .= '<p><strong>' . __('Commande') . ' :</strong> ' . $transaction->getReference() . '<br>';
            $message .= '<strong>' . __('Produit') . ' :</strong> ' . $transaction->getProductName() . '<br>';
            $message .= '<strong>' . __('Date') . ' :</strong> ' . dateTimeFr($transaction->getDate()->format('Y-m-d H:i:s')) . '</p>';

            $message .= '<p>' . __('Vérifiez si votre moyen de paiement est toujours actif. Dans le cas contraire, vous pouvez en ajouter un nouveau ici :') . '<br>';
            $paymentMethodLink = Tools::makeClientLink($transactionClient->getClient()->getUniqid(), 'app', 'billing', 'payment_methods');
            $message .= '<a href="' . $paymentMethodLink . '">' . __('Cliquez ici pour ajouter un nouveau moyen de paiement') . '</a></p>';

            $subscription = $container->get(SubscriptionService::class)->getSubscriptionByTransaction($transaction->getReference(), $transaction->getPaymentMethod());
            if ($subscription and $subscription->getValid()) {
                $message .= '<p><strong>' . __('Cette transaction appartient à un abonnement, une nouvelle tentative aura lieu le %s.', dateTimeFr($subscription->getDate()->format('Y-m-d H:i:s'), true, true)) . '</strong></p>';
            }

            $message .= '<p>' . __('Si vous avez besoin d\'aide, vous pouvez contacter le support à tout moment ici :') . '<br>';
            $helpLink = Tools::makeClientLink($transactionClient->getClient()->getUniqid(), 'app', 'help');
            $message .= '<a href="' . $helpLink . '">' . __('Cliquez ici pour consulter l\'aide et contacter le support') . '</a></p>';

            NotificationsService::insert($subject, $message, Notification::TYPE_IMPORTANT, $transactionClient->getClient()->getId());
        }

        //désactivation de la commission si elle existe
        $commissions = $container->get(AffiliationCommissionService::class)->getCommissionsByTransactionReference($reference);
        if (!$commissions and isset($previousTransaction) and $previousTransaction) {
            $log .= "Recherche d'une commission pour la transaction parent " . $previousTransaction->getReference() . "\n";
            $commissions = $container->get(AffiliationCommissionService::class)->getCommissionsByTransactionReference($previousTransaction->getReference());
        }

        if ($commissions) {
            foreach ($commissions as $commission) {
                $log .= 'Commission ' . $commission->getId() . " trouvée pour cette transaction\n";

                $invalid = true;
                if ($transaction->getStatus() == ShopTransaction::STATUS_REFUNDED and isset($previousTransaction) and $previousTransaction) {
                    //comparaison du montant
                    $amountPreviousTransaction = $previousTransaction->getAmountTaxIncl();
                    $amountTaxIncl = $transaction->getAmountTaxIncl();
                    if ($amountTaxIncl < 0) {
                        $amountTaxIncl *= -1;
                    }

                    if ($amountTaxIncl != $amountPreviousTransaction) {
                        $invalid = false;

                        //calcul de la tva
                        $vatRate = $amountPreviousTransaction / $commission->getAmountTaxExcl();
                        $diffCommissionAmount = $amountTaxIncl / $vatRate;

                        $commissionAmount = $commission->getAmountTaxExcl() - $diffCommissionAmount;

                        $diffCommission = $commission->getCommission() / $commission->getAmountTaxExcl();
                        $commissionAmount2 = $commissionAmount * $diffCommission;

                        $log .= "Update commission amount (" . $commission->getId() . ") : from $commissionAmount to $commissionAmount2\n";
                        $updateCommission = $container->get(AffiliationCommissionService::class)->updateCommissionAmount($commission->getId(), $commissionAmount, $commissionAmount2);
                        if (!$updateCommission['valid']) {
                            $log .= 'Erreur lors de la mise à jour de la commission : ' . $updateCommission['message'] . "\n";
                        }
                    }
                }

                if ($invalid) {
                    $invalid = $container->get(AffiliationCommissionService::class)->setInvalid($commission->getId());
                    if (!$invalid['valid']) {
                        $log .= 'Erreur lors de la désactivation de la commission : ' . $invalid['message'] . "\n";
                    }
                }
            }
        }

        //envoi du mail dans le cas d'une erreur liée à un abonnement
        $lastTransaction = $this->repository->getPrevTransactionByCustom($reference, $transaction->getCustom());
        if ($lastTransaction) {
            $settings = $container->get(ConfigService::class)->findByName(ConfigEnum::INVOICE, CLIENT_MASTER);
            if ($settings) {
                $settings = json_decode($settings->getValue(), true);
            }

            if (isset($settings['invoice_email_error']) and $settings['invoice_email_error']) {
                $recipient = [];
                if ($transaction->getUser()) {
                    $recipient[] = ['user_id' => $transaction->getUser()->getId()];
                } else {
                    $recipient[] = ['first_name' => $transaction->getFirstName(), 'last_name' => $transaction->getLastName(), 'email' => $transaction->getEmail()];
                }
                $vars = [
                    'REFERENCE' => $reference,
                    'DATE' => dateTimeFr(date('Y-m-d H:i:s')),
                    'PRODUCTS' => $transaction->getProductName(),
                    'AMOUNT_TAX_EXCL' => Number::formatAmount($transaction->getAmountTaxExcl(), $transaction->getCurrency()),
                    'AMOUNT_TAX_INCL' => Number::formatAmount($transaction->getAmountTaxIncl(), $transaction->getCurrency()),
                    'APP_URL_BILLING' => Tools::makeLink('app', 'billing'),
                ];
                $send = $this->mailSender->sendTemplateToClient('new_order_error', $vars, 0, $recipient, CLIENT_MASTER);
                if (!$send['valid']) {
                    $log .= "Erreur lors de l'envoi du mail d'erreur : " . $send['message'];
                }
            }
        }

        //save transaction error
        if ($transaction->getStatus() != ShopTransaction::STATUS_REFUNDED) {
            $idSubscription = 0;
            $getSubscription = $container->get(SubscriptionService::class)->getSubscriptionByTransaction($transaction->getReference(), $transaction->getPaymentMethod());
            if ($getSubscription) {
                $idSubscription = $getSubscription->getId();
            }

            $arrayInsert = [
                'reference' => $reference,
                'id_subscription' => $idSubscription,
            ];
            $insertError = $container->get(ShopTransactionErrorService::class)->insert($arrayInsert);
            if (!$insertError['valid']) {
                $log .= "Erreur lors de l'enregistrement de la transaction en erreur : " . $insertError['message'];
            }
        }

        return array('valid' => true, 'log' => $log);
    }

    /**
     * @param string $reference
     * @param float $amountRefunded
     * @return array
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function setOrderPartiallyRefunded(string $reference, float $amountRefunded): array
    {
        $log = "appel setOrderPartiallyRefunded $reference \n";

        $container = ContainerBuilderService::getInstance();
        $transaction = $container->get(ShopTransactionService::class)->getTransactionByReference($reference);
        if (!$transaction) {
            return array('valid' => false, 'message' => __('Cette transaction n\'existe pas'));
        }

        $insertHistory = $container->get(ShopTransactionHistoryService::class)->insertHistory($transaction->getId(), ShopTransaction::STATUS_REFUNDED, $amountRefunded);
        if (!$insertHistory['valid']) {
            $log .= "Impossible de créer un historique pour la commande $reference\n";
            LoggerService::logWarning("Impossible de mettre la commande $reference en erreur");
        }

        if ($_SESSION['client']['id'] != CLIENT_MASTER) {
            return array('valid' => true, 'log' => $log);
        }

        //create credit note
        $invoicesSettings = $container->get(ConfigService::class)->findByName(ConfigEnum::INVOICE, CLIENT_MASTER);
        if ($invoicesSettings) {
            $invoicesSettings = json_decode($invoicesSettings->getValue(), true);
            if (isset($invoicesSettings['invoice_automatique']) and $invoicesSettings['invoice_automatique']) {
                $log .= "Génération d'un avoir\n";

                $insertCreditNote = $container->get(ShopCreditNotesService::class)->generateCreditNoteFromTransaction($reference, $amountRefunded);
                if (!$insertCreditNote['valid']) {
                    $log .= "Erreur lors de la création de l'avoir.\n";
                    LoggerService::logError("Erreur lors de la création de l'avoir : " . $insertCreditNote['message']);
                } else {
                    $log .= "Création de l'avoir OK.\n";
                }
            }
        }

        //set commission invalid
        $commissions = $container->get(AffiliationCommissionService::class)->getCommissionsByTransactionReference($reference);
        if (!empty($commissions)) {
            foreach ($commissions as $commission) {
                $log .= 'Commission ' . $commission->getId() . " trouvée pour cette transaction\n";

                $amountPreviousTransaction = $transaction->getAmountTaxIncl();
                if ($amountPreviousTransaction < 0) {
                    $amountPreviousTransaction *= -1;
                }

                if (str_contains($amountRefunded, ',')) {
                    $amountRefunded = (float)str_replace(',', '.', $amountRefunded);
                }

                $absoluteAmountRefunded = $amountRefunded;
                if ($absoluteAmountRefunded < 0) {
                    $absoluteAmountRefunded *= -1;
                }

                //get VAT
                $diff = $amountPreviousTransaction / $commission->getAmountTaxExcl();
                $diffCommissionAmount = $absoluteAmountRefunded / $diff;

                $commissionAmount = $commission->getAmountTaxExcl() - $diffCommissionAmount;

                $diffCommission = $commission->getCommission() / $commission->getAmountTaxExcl();
                $commissionAmount2 = $commissionAmount * $diffCommission;

                $updateCommission = $container->get(AffiliationCommissionService::class)->updateCommissionAmount($commission->getId(), $commissionAmount, $commissionAmount2);
                if (!$updateCommission['valid']) {
                    $log .= 'Erreur lors de la mise à jour de la commission : ' . $updateCommission['message'] . "\n";
                }
            }
        }

        return array('valid' => true, 'log' => $log);
    }
}
