<?php

namespace MatGyver\Services\Shop\Transaction;

use Doctrine\ORM\EntityManager;
use Mat<PERSON>yver\Entity\Client\Client;
use MatGyver\Entity\Shop\Transaction\ShopTransaction;
use MatGyver\Entity\Shop\Transaction\ShopTransactionHistory;
use MatGyver\Entity\User\User;
use MatGyver\Repository\Shop\Transaction\ShopTransactionHistoryRepository;
use MatGyver\Services\BaseEntityService;

/**
 * Class ShopTransactionHistoryService
 * @package MatGyver\Services\Shop\Transaction
 * @property ShopTransactionHistoryRepository $repository
 * @method ShopTransactionHistoryRepository getRepository()
 */
class ShopTransactionHistoryService extends BaseEntityService
{
    /**
     * ShopTransactionHistoryService constructor.
     * @param EntityManager $em
     */
    public function __construct(EntityManager $em)
    {
        $this->em = $em;
        $this->repository = $this->em->getRepository(ShopTransactionHistory::class);
    }

    /**
     * @param int      $idTransaction
     * @param string   $status (completed, pending, error, cancelled, refunded)
     * @param float    $amount
     * @param string   $comment
     * @param int|null $idClient
     * @return array
     */
    public function insertHistory(int $idTransaction, string $status, float $amount = 0, string $comment = '', int $idClient = null): array
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }

        $lastHistory = $this->repository->findOneBy(['transaction' => $idTransaction, 'client' => $idClient], ['date' => 'DESC']);
        if ($lastHistory) {
            $lastHistoryStatus = $lastHistory->getStatus();
            if ($lastHistoryStatus == $status) {
                return ['valid' => true];
            }
        }

        $transaction = $this->em->getRepository(ShopTransaction::class)->findOneBy(['id' => $idTransaction, 'client' => $idClient]);
        if (!$transaction) {
            return ['valid' => false, 'message' => __('Cette transaction n\'existe pas.')];
        }

        //insertion
        $client = $this->em->getRepository(Client::class)->find($idClient);
        $history = new ShopTransactionHistory();
        $history->setClient($client);
        $history->setUser(null);
        $history->setTransaction($transaction);
        $history->setAmount($amount);
        $history->setStatus($status);
        $history->setComment($comment);
        if (isset($_SESSION['user']['id'])) {
            $user = $this->em->getRepository(User::class)->findOneBy(['id' => $_SESSION['user']['id'], 'client' => $idClient]);
            if ($user) {
                $history->setUser($user);
            }
        }
        $this->persistAndFlush($history);

        return array('valid' => true);
    }

    /**
     * @param int $idTransaction
     * @param int|null $idClient
     * @return array
     */
    public function deleteHistories(int $idTransaction, int $idClient = null): array
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }

        $transaction = $this->em->getRepository(ShopTransaction::class)->findOneBy(['transaction' => $idTransaction, 'client' => $idClient]);
        if (!$transaction) {
            return ['valid' => false, 'message' => __('Cette transaction n\'existe pas.')];
        }

        $histories = $this->repository->findBy(['transaction' => $transaction, 'client' => $idClient]);
        if (!$histories) {
            return ['valid' => true];
        }

        foreach ($histories as $history) {
            $this->em->remove($history);
        }
        $this->em->flush();

        return ['valid' => true];
    }
}
