<?php

namespace MatG<PERSON>ver\Services\Shop\Transaction;

use MatG<PERSON>ver\Enums\ProductsEnum;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Services\BaseEntityService;
use MatGyver\Services\Clients\ClientsTransactionsService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Integration\Services\FacebookPixelsService;
use MatGyver\Services\TwigService;
use MatGyver\Services\Users\UsersService;
use MatGyver\Services\Views\ViewSite;

/**
 * Class ShopTransactionsConfirmationService
 * @package MatGyver\Services\Transaction
 */
class ShopTransactionsConfirmationService extends BaseEntityService
{
    /**
     * @var ShopTransactionService
     */
    private $shopTransactionService;

    /**
     * @var ClientsTransactionsService
     */
    private $clientsTransactionsService;

    /**
     * @var UsersService
     */
    private $usersService;

    /**
     * ShopTransactionsConfirmationService constructor.
     * @param ShopTransactionService $shopTransactionService
     * @param ClientsTransactionsService $clientsTransactionsService
     * @param UsersService $usersService
     */
    public function __construct(
        ShopTransactionService $shopTransactionService,
        ClientsTransactionsService $clientsTransactionsService,
        UsersService $usersService
    ) {
        $this->shopTransactionService = $shopTransactionService;
        $this->clientsTransactionsService = $clientsTransactionsService;
        $this->usersService = $usersService;
    }

    public function checkoutConfirmation(): string
    {
        $reference = '';
        if (isset($_GET['reference'])) {
            $reference = filter_input(INPUT_GET, 'reference', FILTER_UNSAFE_RAW);
        } elseif (isset($_GET['transaction_id'])) {
            $reference = filter_input(INPUT_GET, 'transaction_id', FILTER_UNSAFE_RAW);
        } elseif (isset($_GET['tx'])) {
            $reference = filter_input(INPUT_GET, 'tx', FILTER_UNSAFE_RAW);
        } elseif (isset($_POST['tx'])) {
            $reference = filter_input(INPUT_POST, 'tx', FILTER_UNSAFE_RAW);
        } elseif (isset($_GET['txn_id'])) {
            $reference = filter_input(INPUT_GET, 'txn_id', FILTER_UNSAFE_RAW);
        } elseif (isset($_POST['txn_id'])) {
            $reference = filter_input(INPUT_POST, 'txn_id', FILTER_UNSAFE_RAW);
        }

        Assets::addCss('common/blank_state.css');

        if (!$reference) {
            return TwigService::getInstance()->render('common/payments/status/unknown.php');
        }

        $transaction = $this->shopTransactionService->getTransactionByReference($reference);
        if (!$transaction) {
            Assets::addJs('site/check_order.js');
            Assets::addInlineJs('
                <script type="text/javascript">
                    setTimeout(function() { CheckOrder(\'' . $reference . '\'); }, 5000);
                </script>');

            return TwigService::getInstance()->render('common/payments/status/waiting.php');
        }

        /*if ($transaction->getValid()) {
            if ($transaction->getProduct() and isset($_SESSION['customer_product_' . $transaction->getProduct()->getId()])) {
                unset($_SESSION['customer_product_' . $transaction->getProduct()->getId()]);
            }
            if (isset($_SESSION['customer'])) {
                unset($_SESSION['customer']);
            }
        }*/

        $validationOrder = $this->getValidationOrder($reference);
        if (!$validationOrder['valid']) {
            return TwigService::getInstance()->set('link', $validationOrder['link'] ?? '')
                ->render('common/payments/status/error.php');
        }

        if (isset($validationOrder['redirection']) and $validationOrder['redirection']) {
            header('Location: ' . $validationOrder['redirection']);
            exit();
        }

        $dossierPaymentLink = null;
        if ($transaction->getTransactionProducts()) {
            foreach ($transaction->getTransactionProducts() as $transactionProduct) {
                if (!$transactionProduct->getProduct()) {
                    continue;
                }
                if ($transactionProduct->getProduct()->getType() == ProductsEnum::TYPE_DOSSIER_PAYMENT) {
                    $dossierPaymentLink = $transactionProduct->getProduct()->getDossierPaymentLink();
                    break;
                }
            }
        }

        if ($dossierPaymentLink) {
            header('Location: ' . Tools::makeLink('site', 'dossier', 'payment/confirmation/' . $dossierPaymentLink->getPermalink() . '/' . $dossierPaymentLink->getDossier()->getReference()));
            exit();
        }

        $link = '';
        if (isset($validationOrder['link']) and $validationOrder['link']) {
            $link = $validationOrder['link'];
        }

        $container = ContainerBuilderService::getInstance();
        $container->get(ViewSite::class)->set('facebook_pixel_event', FacebookPixelsService::EVENT_PURCHASE);

        return TwigService::getInstance()->set('link', $link)
            ->render('common/payments/status/valid.php');
    }

    /**
     * @param $reference
     * @return array
     */
    public function getValidationOrder($reference): array
    {
        /*if (isset($_SESSION['client']['id']) and $_SESSION['client']['id'] != CLIENT_MASTER) {
            return array('valid' => false, 'message' => __('Une erreur est survenue'));
        }*/

        $transaction = $this->shopTransactionService->getTransactionByReference($reference);
        if (!$transaction) {
            return array('valid' => true, 'action' => 'waiting');
        }
        if (!$transaction->getValid()) {
            $link = '';
            if ($transaction->getCancelUrl() and filter_var($transaction->getCancelUrl(), FILTER_VALIDATE_URL)) {
                $link = $transaction->getCancelUrl();
            }
            return array('valid' => false, 'link' => $link);
        }

        if (!$transaction->getShopCustomer()) {
            return array('valid' => true, 'action' => 'success');
        }

        $customer = $transaction->getShopCustomer();

        if ($transaction->getValidUrl() and $transaction->getValidUrl() == $transaction->getCancelUrl()) {
            $transaction->setValidUrl('');
        }

        if ($transaction->getValidUrl()) {
            $isUrl = filter_var($transaction->getValidUrl(), FILTER_VALIDATE_URL);
            if ($isUrl) {
                $params = [
                    'reference' => $reference,
                    'amount_tax_incl' => $transaction->getAmountTaxIncl(),
                    'amount_tax_excl' => $transaction->getAmountTaxExcl(),
                    'first_name' => $transaction->getFirstName(),
                    'email' => $transaction->getEmail(),
                    'payment_method' => $transaction->getPaymentMethod(),
                    'product' => str_replace("\n", '', $transaction->getProductName()),
                    'is_purchased' => true,
                    'zip' => $customer->getZip(),
                ];

                $getParams = http_build_query($params);

                $link = $transaction->getValidUrl() . (str_contains($transaction->getValidUrl(), '?') ? '&' : '?');
                $link .= $getParams;

                return array('valid' => true, 'redirection' => $link);
            }
        }

        $clientTransaction = $this->clientsTransactionsService->getRepository()->getClientTransactionByTransactionId($transaction->getId());
        if (!$clientTransaction) {
            return array('valid' => true, 'action' => 'success');
        }

        $client = $clientTransaction->getClient();
        if (!$client) {
            return array('valid' => true, 'action' => 'success');
        }

        //autologin only if order has just been recorded
        if ((time() - $transaction->getDate()->getTimestamp()) <= 300) {
            $user = $this->usersService->getMainAdmin($client->getId());
            if ($user) {
                $setAutologinToken = $this->usersService->setAutologinToken($user->getId(), $client->getId(), $client->getUniqid());
                if (!$setAutologinToken['valid']) {
                    return array('valid' => true, 'action' => 'success');
                }

                return array('valid' => true, 'action' => 'success', 'link' => $setAutologinToken['link']);
            }
        }

        return array('valid' => true, 'action' => 'success');
    }
}
