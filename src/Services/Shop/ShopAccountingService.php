<?php

namespace MatGyver\Services\Shop;

use MatGyver\Entity\Shop\Invoice\ShopInvoice;
use MatGyver\Entity\Shop\Transaction\ShopTransaction;
use MatGyver\Services\Shop\CreditNotes\ShopCreditNotesService;
use MatGyver\Services\Shop\Invoices\ShopInvoicesService;
use MatGyver\Services\Shop\Quotes\ShopQuotesService;
use MatGyver\Services\Shop\Transaction\ShopTransactionService;
use MatGyver\Services\Shop\Transaction\ShopTransactionsStatsService;

class ShopAccountingService
{
    /**
     * @var ShopCreditNotesService
     */
    private $shopCreditNotesService;

    /**
     * @var ShopInvoicesService
     */
    private $shopInvoicesService;

    /**
     * @var ShopQuotesService
     */
    private $shopQuotesService;

    /**
     * @var ShopTransactionService
     */
    private $shopTransactionService;

    /**
     * @var ShopTransactionsStatsService
     */
    private $transactionsStatsService;

    /**
     * AccountingService constructor.
     * @param ShopCreditNotesService $shopCreditNotesService
     * @param ShopInvoicesService $shopInvoicesService
     * @param ShopQuotesService $shopQuotesService
     * @param ShopTransactionService $shopTransactionService
     * @param ShopTransactionsStatsService $transactionsStatsService
     */
    public function __construct(
        ShopCreditNotesService $shopCreditNotesService,
        ShopInvoicesService $shopInvoicesService,
        ShopQuotesService $shopQuotesService,
        ShopTransactionService $shopTransactionService,
        ShopTransactionsStatsService $transactionsStatsService
    ) {
        $this->shopCreditNotesService = $shopCreditNotesService;
        $this->shopInvoicesService = $shopInvoicesService;
        $this->shopQuotesService = $shopQuotesService;
        $this->shopTransactionService = $shopTransactionService;
        $this->transactionsStatsService = $transactionsStatsService;
    }

    /**
     * @param int $idTransaction
     * @return array|void
     */
    public function getDetailsForTransaction(int $idTransaction)
    {
        $transaction = $this->shopTransactionService->getRepository()->find($idTransaction);
        if (!$transaction) {
            return;
        }

        $transactionProducts = $transaction->getTransactionProducts();
        if (!count($transactionProducts)) {
            return;
        }

        $paymentDetails = $this->getPaymentDetails($transactionProducts, $transaction);
        $paymentDetails['date'] = $transaction->getDate();

        return $paymentDetails;
    }

    /**
     * @param int $idInvoice
     * @param int|null $clientId
     * @return array|void
     */
    public function getDetailsForInvoice(int $idInvoice, ?int $clientId = null)
    {
        if ($clientId === null) {
            $clientId = $_SESSION['client']['id'];
        }

        $invoice = $this->shopInvoicesService->getRepository()->findOneBy(['id' => $idInvoice, 'client' => $clientId]);
        if (!$invoice) {
            return;
        }

        $invoiceProducts = $invoice->getInvoiceProducts();
        if (!count($invoiceProducts)) {
            return;
        }

        $transaction = $this->shopTransactionService->getRepository()->findOneBy(['reference' => $invoice->getTransactionReference(), 'client' => $clientId]);

        $paymentDetails = $this->getPaymentDetails($invoiceProducts, $transaction, $invoice->getAmountTaxIncl(), true);
        $paymentDetails['date'] = $invoice->getDateCreation();

        return $paymentDetails;
    }

    /**
     * @param int $idCreditNote
     * @return array|void
     */
    public function getDetailsForCreditNote(int $idCreditNote)
    {
        $creditNote = $this->shopCreditNotesService->getRepository()->find($idCreditNote);
        if (!$creditNote) {
            return;
        }

        $creditNoteProducts = $creditNote->getCreditNoteProducts();
        if (!count($creditNoteProducts)) {
            return;
        }

        $transaction = $this->shopTransactionService->getRepository()->findOneBy(['reference' => $creditNote->getTransactionReference(), 'client' => CLIENT_MASTER]);

        $paymentDetails = $this->getPaymentDetails($creditNoteProducts, $transaction, $creditNote->getAmountTaxIncl(), true);
        $paymentDetails['date'] = $creditNote->getDateCreation();

        return $paymentDetails;
    }

    /**
     * @param int $idQuote
     * @return array|void
     */
    public function getDetailsForQuote(int $idQuote, ?int $idClient = null)
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }

        $quote = $this->shopQuotesService->getQuoteById($idQuote, $idClient);
        if (!$quote) {
            return;
        }

        $quoteProducts = $quote->getQuoteProducts();
        if (!count($quoteProducts)) {
            return;
        }

        $paymentDetails = $this->getPaymentDetails($quoteProducts, null, $quote->getAmountTaxIncl(), true);
        $paymentDetails['date'] = $quote->getDateAdd();

        return $paymentDetails;
    }

    /**
     * @param $products
     * @param ShopTransaction|null $transaction
     * @param float|null $ttcPaid
     * @param bool $getAll
     * @return array
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    private function getPaymentDetails($products, ?ShopTransaction $transaction = null, float $ttcPaid = null, bool $getAll = false): array
    {
        $tvas = [];
        $totalProductsHtByTva = [];
        $totalProductsHt = 0;
        $totalProductsTtc = 0;
        $discountTtc = 0;
        $discountName = '';
        $trialPeriodTtc = 0;
        foreach ($products as $product) {
            if ($product->getName() == ShopInvoice::NAME_DISCOUNT) {
                $discountTtc += $product->getPriceTaxIncl();

                if ($transaction and $transaction->getShopCart() and $transaction->getShopCart()->getDiscount() and $transaction->getShopCart()->getDiscount() != '[]') {
                    $discount = json_decode($transaction->getShopCart()->getDiscount(), true);
                    if (isset($discount['code']) and $discount['code']) {
                        $discountName = ' (' . $discount['code'];

                        if (isset($discount['discount_type']) and isset($discount['discount_amount'])) {
                            if ('pourcentage' == $discount['discount_type']) {
                                $discountName .= ' -' . $discount['discount_amount'] . '%';
                            } else {
                                $discountName .= ' -' . \MatGyver\Helpers\Number::formatAmount($discount['discount_amount'], $transaction->getCurrency()) . ' ' . ('ht' == $discount['amount_type'] ? __('HT') : __('TTC'));
                            }
                        }
                        $discountName .= ')';
                    }
                }

                continue;
            }

            if ($product->getName() == ShopInvoice::NAME_TRIAL_PERIOD) {
                $trialPeriodTtc += $product->getPriceTaxIncl();
                continue;
            }

            $tva = $product->getVat();

            //convert 5,5% to 5.5
            $tva = (string) $tva;

            if (!isset($tvas[$tva])) {
                $tvas[$tva] = 0;
            }
            $tvas[$tva] += $product->getPriceTaxIncl() - $product->getPriceTaxExcl();

            if (!isset($totalProductsHtByTva[$tva])) {
                $totalProductsHtByTva[$tva] = 0;
            }
            $totalProductsHtByTva[$tva] += $product->getPriceTaxExcl();

            $totalProductsHt += $product->getPriceTaxExcl();
            $totalProductsTtc += $product->getPriceTaxIncl();
        }

        $percentOfEachTva = [];
        $totalProductsAmountHt = array_sum($totalProductsHtByTva);
        foreach ($totalProductsHtByTva as $tva => $amountHt) {
            if ($totalProductsAmountHt) {
                $percentOfEachTva[$tva] = $amountHt / $totalProductsAmountHt;
            } else {
                $percentOfEachTva[$tva] = 0;
            }
        }

        // Case if discount, remove tva for each rate
        if (0 < $discountTtc) {
            foreach ($tvas as $tva => $amount) {
                $tvaFloat = (float) str_replace(',', '.', $tva);
                $ttcRepartition = $discountTtc * $percentOfEachTva[$tva];
                $htRepartition = $ttcRepartition / (1 + ($tvaFloat / 100));
                $tvas[$tva] -= $ttcRepartition - $htRepartition;
            }
        }

        if ($trialPeriodTtc) {
            $totalTtc = $trialPeriodTtc;
            $totalProductsTtc = $trialPeriodTtc;
            $totalHt = 0;

            // Case if trial period, add tva for each rate
            foreach ($tvas as $tva => $amount) {
                $tvaFloat = (float) str_replace(',', '.', $tva);

                $ttcRepartition = $trialPeriodTtc * $percentOfEachTva[$tva];
                $htRepartition = $ttcRepartition / (1 + ($tvaFloat / 100));
                $tvas[$tva] = $ttcRepartition - $htRepartition;
                $totalHt += $htRepartition;
            }
        } else {
            $totalTtc = $totalProductsTtc + $trialPeriodTtc - $discountTtc;
            $totalHt = $totalTtc - array_sum($tvas);
        }

        $data = [
            'total_products_ttc' => round($totalProductsTtc, 2),
            'trial_period_ttc' => round($trialPeriodTtc, 2),
            'discount_ttc' => round($discountTtc, 2),
            'discount_name' => $discountName,
            'total_ht' => round($totalHt, 2),
            'total_ttc' => round($totalTtc, 2),
            'tvas' => $tvas
        ];

        $ttcPaid = $ttcPaid ?? ($transaction ? $transaction->getAmountTaxIncl() : 0);

        // Multiple payments
        if ($totalTtc and $totalTtc > $ttcPaid) {
            $ratio = $ttcPaid / $totalTtc;
            $data['total_ht'] *= $ratio;
            $data['total_ttc'] *= $ratio;
            foreach ($data['tvas'] as $tva => $amount) {
                $data['tvas'][$tva] *= $ratio;
            }
        }

        $data['subscription_progression'] = [];
        if ($transaction) {
            $data['subscription_progression'] = $this->transactionsStatsService->getSubscriptionProgressionForTransaction($transaction->getReference(), $transaction->getPaymentMethod(), $getAll, $transaction->getClient()->getId());
        }

        return $data;
    }
}
