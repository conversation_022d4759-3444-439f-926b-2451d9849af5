<?php

namespace MatGyver\Services\Shop\Product\Form;

use MatGyver\Entity\Shop\Payment\ShopPayment;
use MatGyver\Entity\Shop\Product\ShopProduct;
use MatGyver\Factories\Shop\FormFactory\ShopFormFactoryBraintree;
use MatGyver\Factories\Shop\FormFactory\ShopFormFactoryMollie;
use MatGyver\Factories\Shop\FormFactory\ShopFormFactoryMolliePayPal;
use MatGyver\Factories\Shop\FormFactory\ShopFormFactoryStripe;
use MatGyver\Helpers\Entities;
use MatGyver\Helpers\Number;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Integration\IntegrationAccountsService;
use MatGyver\Services\Shop\ShopCartProductsService;
use MatGyver\Services\Shop\ShopCartService;
use MatGyver\Services\Shop\ShopCustomersService;
use MatGyver\Services\Shop\ShopPaymentsService;

/**
 * Class ShopProductFormService
 * @package MatGyver\Services\Shop
 */
class ShopProductFormService
{
    /**
     * @var ShopCustomersService
     */
    private $shopCustomersService;

    /**
     * @var ShopCartService
     */
    private $shopCartService;

    /**
     * @var ShopCartProductsService
     */
    private $shopCartProductsService;

    /**
     * @var ShopPaymentsService
     */
    private $shopPaymentsService;

    /**
     * @var IntegrationAccountsService
     */
    private $integrationAccountsService;

    /**
     * ShopProductFormService constructor.
     * @param ShopCustomersService $shopCustomersService
     * @param ShopCartService $shopCartService
     * @param ShopCartProductsService $shopCartProductsService
     * @param ShopPaymentsService $shopPaymentsService
     * @param IntegrationAccountsService $integrationAccountsService
     */
    public function __construct(
        ShopCustomersService $shopCustomersService,
        ShopCartService $shopCartService,
        ShopCartProductsService $shopCartProductsService,
        ShopPaymentsService $shopPaymentsService,
        IntegrationAccountsService $integrationAccountsService
    ) {
        $this->shopCustomersService = $shopCustomersService;
        $this->shopCartService = $shopCartService;
        $this->shopCartProductsService = $shopCartProductsService;
        $this->shopPaymentsService = $shopPaymentsService;
        $this->integrationAccountsService = $integrationAccountsService;
    }

    /**
     * @param ShopProduct $product
     * @param array $cartProducts
     * @return array
     */
    public function prepareProductOrderForm(ShopProduct $product, array $cartProducts = []): array
    {
        $submittedData = getPostData($_POST);

        $tvaIntracom = '';
        $customer = [];
        if (isset($_SESSION['customer'])) {
            $customer = $this->shopCustomersService->getCustomerById($_SESSION['customer']['id']);
            if ($customer) {
                if ($customer->getDatas()) {
                    $datas = json_decode($customer->getDatas(), true);
                    if (isset($datas['tva_intracom']) and $datas['tva_intracom']) {
                        $tvaIntracom = $datas['tva_intracom'];
                    }
                }
                $customer = Entities::asArray($customer);
            }
        }
        if (!$customer) {
            $customer = [
                'first_name' => '',
                'last_name' => '',
                'email' => '',
                'country' => DEFAULT_COUNTRY,
            ];

            if (isset($submittedData['last_name'])) {
                $customer['last_name'] = $submittedData['last_name'];
            } elseif (isset($_GET['last_name'])) {
                $customer['last_name'] = filter_input(INPUT_GET, 'last_name', FILTER_UNSAFE_RAW);
            }

            if (isset($submittedData['first_name'])) {
                $customer['first_name'] = $submittedData['first_name'];
            } elseif (isset($_GET['first_name'])) {
                $customer['first_name'] = filter_input(INPUT_GET, 'first_name', FILTER_UNSAFE_RAW);
            }

            if (isset($submittedData['email'])) {
                $customer['email'] = $submittedData['email'];
            } elseif (isset($_GET['email'])) {
                $customer['email'] = filter_input(INPUT_GET, 'email', FILTER_VALIDATE_EMAIL);
            }

            if (isset($submittedData['country'])) {
                $customer['country'] = $submittedData['country'];
            }

            if (isset($submittedData['tva_intracom'])) {
                $tvaIntracom = $submittedData['tva_intracom'];
            }
        }

        $cart = null;
        $amounts = [];
        if (isset($_SESSION['cart_product_' . $product->getId()])) {
            $cart = $this->shopCartService->getCartById($_SESSION['cart_product_' . $product->getId()]);
        }
        if (!$cart and isset($customer['id']) and isset($_SESSION['cart_customer_' . $customer['id']])) {
            $cart = $this->shopCartService->getCartById($_SESSION['cart_customer_' . $customer['id']]['id']);
        }

        if ($cart) {
            $productsIds = [];
            $shopCartProducts = $cart->getShopCartProducts();
            foreach ($shopCartProducts as $shopCartProduct) {
                if ($shopCartProduct->getShopProduct()) {
                    $productsIds[] = $shopCartProduct->getShopProduct()->getId();
                }
            }
            if (!in_array($product->getId(), $productsIds)) {
                $cart = null;
            }
            if (isset($_SESSION['cart_product_' . $product->getId()])) {
                unset($_SESSION['cart_product_' . $product->getId()]);
            }
        }

        if ($cart) {
            if ($submittedData) {
                //update cart
                $this->shopCartService->updateCart($cart->getId(), $submittedData);

                $quantities = [];
                if (isset($submittedData['quantities'])) {
                    $quantities = filter_var($submittedData['quantities'], FILTER_VALIDATE_INT, FILTER_REQUIRE_ARRAY);
                }

                $attributes = [];
                if (isset($submittedData['product_attributes'][$product->getId()])) {
                    $attributes = filter_var($submittedData['product_attributes'][$product->getId()], FILTER_VALIDATE_INT, FILTER_REQUIRE_ARRAY);
                }

                $products = [];
                $products[] = [
                    'id' => $product->getId(),
                    'qty' => (isset($quantities[$product->getId()]) ? $quantities[$product->getId()] : 1),
                    'attributes' => $attributes,
                ];
                $addProductInCart = $this->shopCartProductsService->addProductInCart($cart, $products);
                if ($addProductInCart['valid']) {
                    //get new cart
                    $cart = $this->shopCartService->getCartById($addProductInCart['id']);
                }
            }

            $amounts = $this->shopCartService->getCartAmounts($cart);
        }

        if (!$amounts) {
            if (!$cartProducts and isset($submittedData['products'])) {
                $productIds = filter_var($submittedData['products'], FILTER_VALIDATE_INT, FILTER_REQUIRE_ARRAY);
                foreach ($productIds as $productId) {
                    $cartProducts[] = ['id_product' => $productId];
                }
            }
            if (!$cartProducts) {
                $cartProducts[] = ['id_product' => $product->getId()];
            }

            $discount = [];
            if (isset($_SESSION['cart_discount_' . $product->getId()])) {
                $discount = $_SESSION['cart_discount_' . $product->getId()];
            }
            $amounts = $this->shopCartService->getProductsAmounts($cartProducts, $customer['country'], $tvaIntracom, $discount);
        }

        $installments = [];
        $selectedPayment = '';
        $selectedPaymentId = 0;

        $payments = $this->shopPaymentsService->getPaymentsByProduct($product->getId());
        if ($payments) {
            $installments = $this->getPaymentsInstallments($payments, $product->getCurrency());
            if ($installments) {
                foreach ($installments as $installment) {
                    if (isset($installment['paymentTypes']) and $installment['paymentTypes']) {
                        $selectedPayment = $installment['paymentTypes'][0]['type'];
                        $selectedPaymentId = $installment['paymentTypes'][0]['id'];
                        break;
                    }
                }
            }
        }

        $arrayPayments = [];
        if ($payments) {
            foreach ($payments as $payment) {
                $arrayPayment = Entities::asArray($payment);
                $arrayPayment['account_id'] = ($payment->getAccount()?->getId());

                $arrayPayments[] = $arrayPayment;

                if ($payment->getType() == 'mollie') {
                    $config = json_decode($payment->getAccount()->getDatas(), true);
                    if (isset($config['paypal']) and $config['paypal']) {
                        $arrayPayment['type'] .= '-paypal';
                        $arrayPayments[] = $arrayPayment;
                    }
                }
            }
        }

        return [
            'customer' => $customer,
            'cart' => ($cart ? Entities::asArray($cart) : []),
            'amounts' => $amounts,
            'payments' => $arrayPayments,
            'installments' => $installments,
            'selectedPayment' => $selectedPayment,
            'selectedPaymentId' => $selectedPaymentId,
        ];
    }

    /**
     * @param ShopPayment[] $payments
     * @param string $currency
     * @return array
     */
    private function getPaymentsInstallments(array $payments, string $currency = DEFAULT_CURRENCY): array
    {
        $installments = [];

        foreach ($payments as $payment) {
            $checkout = json_decode($payment->getCheckout(), true);
            $installmentMd5 = $this->getInstallmentMd5($checkout);

            if (!isset($installments[$installmentMd5])) {
                $installments[$installmentMd5] = $checkout;
                $installments[$installmentMd5]['md5'] = $installmentMd5;

                $paymentEase = $this->getPaymentEase($checkout, $currency);
                $installments[$installmentMd5]['title'] = $paymentEase['title'];
                $installments[$installmentMd5]['subtitle'] = $paymentEase['subtitle'];

                if ($checkout['payment'] == 'onetime') {
                    $installments[$installmentMd5]['nb'] = 1;
                } else {
                    $installments[$installmentMd5]['nb'] = $checkout['nb_payments'];
                }
            }

            $label = __('Régler par CB');
            $installments[$installmentMd5]['paymentTypes'][] = [
                'id' => $payment->getId(),
                'type' => $payment->getType(),
                'label' => $label,
                'subtitle' => __('Paiement sécurisé par') . ' ' . ucfirst($payment->getType()),
            ];

            if ($payment->getType() == 'mollie') {
                $config = json_decode($payment->getAccount()->getDatas(), true);
                if (isset($config['paypal']) and $config['paypal']) {
                    $installments[$installmentMd5]['paymentTypes'][] = [
                        'id' => $payment->getId(),
                        'type' => $payment->getType() . '-paypal',
                        'label' => __('Régler par PayPal'),
                        'subtitle' => __('Paiement sécurisé par PayPal'),
                    ];
                }
            }
        }

        //sort array by nbPayments ("x" must be the last)
        usort($installments, function($a, $b) {
            $result = $a['nb'] <=> $b['nb'];
            if ($a['nb'] == 'x' && $result == -1) {
                $result = 1;
            }
            if ($b['nb'] == 'x' && $result == 1) {
                $result = -1;
            }
            return $result;
        });

        return $installments;
    }

    /**
     * @param array $checkout
     * @param string $currency
     * @return array
     */
    public function getPaymentEase(array $checkout, string $currency = DEFAULT_CURRENCY): array
    {
        $subTitle = '';
        if (isset($checkout['trial_period']) and $checkout['trial_period']) {
            $subTitle .= Number::formatAmount($checkout['trial_amount'], $currency) . ' ';

            if ('days' == $checkout['trial_timetype']) {
                $subTitle .= n__('pour le premier jour', 'pour les %d premiers jours', $checkout['trial_time'], $checkout['trial_time']);
            } else {
                $subTitle .= n__('pour le premier mois', 'pour les %d premiers mois', $checkout['trial_time'], $checkout['trial_time']);
            }

            $subTitle .= ' ' . __('puis') . ' ';
        }

        if ('subscription' == $checkout['payment'] and 'x' != $checkout['nb_payments']) {
            $title =  __('Règlement en %d fois', $checkout['nb_payments']);

            $subTitle .= ($subTitle ? __('règlement en %d fois tous les', $checkout['nb_payments']) : __('Règlement en %d fois tous les', $checkout['nb_payments'])) . ' ';

            if ('days' == $checkout['subscription_timetype']) {
                $subTitle .= n__('%d jour', '%d jours', $checkout['subscription_time'], $checkout['subscription_time']);
            } else {
                $subTitle .= n__('%d mois', '%d mois', $checkout['subscription_time'], $checkout['subscription_time']);
            }
        } elseif ('subscription' == $checkout['payment'] and 'x' == $checkout['nb_payments']) {
            $title =  __('Paiement récurrent');

            $subTitle .= ($subTitle ? __('paiement récurrent tous les') : __('Paiement récurrent tous les')) . ' ';

            if ('days' == $checkout['subscription_timetype']) {
                $subTitle .= n__('%d jour', '%d jours', $checkout['subscription_time'], $checkout['subscription_time']);
            } else {
                $subTitle .= n__('%d mois', '%d mois', $checkout['subscription_time'], $checkout['subscription_time']);
            }
        } else {
            $title = __('Règlement en 1 fois');
            $subTitle .= ($subTitle ? __('règlement en 1 fois') : '');
        }

        return ['title' => $title, 'subtitle' => $subTitle];
    }

    /**
     * @param array $installment
     * @return string
     */
    public function getInstallmentMd5(array $installment): string
    {
        $fields = [
            'payment' => '',
            'nb_payments' => '',
            'subscription_time' => '',
            'subscription_timetype' => '',
            'augmentation' => '',
            'trial_period' => '',
            'trial_amount' => '',
            'trial_time' => '',
            'trial_timetype' => ''
        ];

        $array = array_merge($fields, array_intersect_key($installment, $fields));
        return md5(json_encode($array));
    }

    /**
     * @param array $payments
     * @return array
     */
    public function getPaymentsForms(array $payments): array
    {
        $container = ContainerBuilderService::getInstance();
        $forms = [];
        foreach ($payments as $payment) {
            if (isset($forms[$payment['type']])) {
                continue;
            }

            $account = $this->integrationAccountsService->getAccount($payment['account_id']);
            if (!$account) {
                continue;
            }

            $form = '';
            if ($payment['type'] == 'stripe') {
                $form = $container->get(ShopFormFactoryStripe::class)->render($account);
            } elseif ($payment['type'] == 'braintree') {
                $form = $container->get(ShopFormFactoryBraintree::class)->render($account);
            } elseif ($payment['type'] == 'mollie') {
                $form = $container->get(ShopFormFactoryMollie::class)->render($account);
            } elseif ($payment['type'] == 'mollie-paypal') {
                $form = $container->get(ShopFormFactoryMolliePayPal::class)->render($account);
            }
            if (!$form) {
                continue;
            }

            $forms[$payment['type']] = [
                'id' => $payment['id'],
                'type' => $payment['type'],
                'form' => $form,
            ];
        }

        return $forms;
    }

    /**
     * @param array $payments
     * @return array
     */
    public function addPaymentsFooter(array $payments): array
    {
        foreach ($payments as $id => $payment) {
            $footer = '';
            $checkout = json_decode($payment['checkout'], true);

            if ($checkout['payment'] == 'subscription') {
                $footer .= __('En procédant au règlement, vous autorisez') . ' ' . APP_NAME . ' ' . __('à envoyer des instructions à votre banque pour débiter votre compte selon les échéances indiquées plus haut sur cette page.');
            }

            if (isset($checkout['trial_period']) and $checkout['trial_period']) {
                if (!$checkout['trial_amount'] or $checkout['trial_amount'] == '0.00') {
                    $footer .= ($footer ? '<hr>' : '');
                    $footer .= __('Le montant du premier paiement est de') . ' ' . Number::formatAmount($checkout['trial_amount'], DEFAULT_CURRENCY) . '. ';
                    $footer .= __('Notez que notre processeur de paiement valide les informations de votre carte automatiquement et, de ce fait, vous pourrez constater une autorisation temporaire de %s sur votre relevé de compte.', Number::formatAmount(1, DEFAULT_CURRENCY)) . ' ';
                    $footer .= __('Cette autorisation n\'est que temporaire, cette somme vous sera remboursée automatiquement dans quelques heures.');
                }
            }

            $payments[$id]['footer'] = $footer;
        }

        return $payments;
    }
}
