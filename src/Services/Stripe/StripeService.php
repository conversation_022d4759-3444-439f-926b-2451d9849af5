<?php

namespace MatGyver\Services\Stripe;

use MatGyver\Entity\Integration\Account\IntegrationAccount;
use MatGyver\Helpers\Assets;
use MatGyver\Services\BaseEntityService;
use Stripe;

/**
 * Class StripeService
 * @package MatGyver\Services\Stripe
 */
class StripeService extends BaseEntityService
{
    /**
     * @var string
     */
    private $secretKey;

    /**
     * @var string
     */
    private $publicKey;

    /**
     * @param IntegrationAccount $account
     * @throws \Exception
     */
    public function startStripe(IntegrationAccount $account): void
    {
        $config = json_decode($account->getDatas(), true);
        if (!isset($config['stripe_public_key']) or !$config['stripe_public_key']) {
            throw new \Exception(__('Aucune clé publique détectée'));
        }
        if (!isset($config['stripe_secret_key']) or !$config['stripe_secret_key']) {
            throw new \Exception(__('Aucune clé secrète détectée'));
        }

        $this->setSecretKey($config['stripe_secret_key']);
        $this->setPublicKey($config['stripe_public_key']);

        Stripe\Stripe::setApiKey($config['stripe_secret_key']);

        Stripe\Stripe::setAppInfo(
            APP_NAME,
            '',
            APP_URL
        );
        Stripe\Stripe::setApiVersion('2023-08-16');
    }

    /**
     * @return string
     */
    public function getSecretKey(): string
    {
        return $this->secretKey;
    }

    /**
     * @param string $secretKey
     */
    public function setSecretKey(string $secretKey)
    {
        $this->secretKey = $secretKey;
    }

    /**
     * @return string
     */
    public function getPublicKey(): string
    {
        return $this->publicKey;
    }

    /**
     * @param string $publicKey
     * @return void
     */
    public function setPublicKey(string $publicKey)
    {
        $this->publicKey = $publicKey;
    }

    public function addRequiredFiles()
    {
        Assets::addCss('common/payments/stripe.css');
        Assets::addJs('https://js.stripe.com/v3/');
        Assets::addJs('common/payments/stripe.js');
        Assets::addJs('common/payments/stripe-handler.js');
    }
}
