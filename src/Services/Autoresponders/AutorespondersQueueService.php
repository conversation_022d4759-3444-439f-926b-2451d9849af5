<?php
namespace MatGyver\Services\Autoresponders;

use Doctrine\ORM\EntityManager;
use MatGyver\Entity\Autoresponder\AutoresponderQueue;
use MatGyver\Entity\Integration\Account\IntegrationAccount;
use MatGyver\Entity\Notification\Notification;
use MatGyver\Services\BaseEntityService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\NotificationsService;
use MatGyver\Services\Shop\Product\ShopProductsService;

/**
 * Class AutorespondersQueueService
 * @package MatGyver\Services\Autoresponders
 */
class AutorespondersQueueService extends BaseEntityService
{
    /**
     * AutorespondersQueueService constructor.
     * @param EntityManager $em
     */
    public function __construct(EntityManager $em)
    {
        $this->em = $em;
        $this->repository = $em->getRepository(AutoresponderQueue::class);
    }

    /**
     * @param IntegrationAccount $account
     * @param string $type
     * @param array $autoresponderData
     * @param array $data
     * @return array
     */
    public function insert(IntegrationAccount $account, string $type, array $autoresponderData, array $data): array
    {
        $queue = new AutoresponderQueue();
        $queue->setClient($account->getClient());
        $queue->setAccount($account);
        $queue->setType($type);
        $queue->setAutoresponderData(json_encode($autoresponderData));
        $queue->setData(json_encode($data));
        try {
            $this->persistAndFlush($queue);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => 'Error while inserting data in queue'];
        }

        return ['valid' => true];
    }

    /**
     * @return array
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function execute(): array
    {
        $tasks = $this->repository->findTasks();
        if (!$tasks) {
            return ['valid' => true];
        }

        $container = ContainerBuilderService::getInstance();
        foreach ($tasks as $task) {
            if ($task->getAttempt() == 2) {
                //wait 15 minutes before 3rd attempt
                $diff = time() - $task->getDate()->getTimestamp();
                if ($diff <= 900) {
                    continue;
                }
            }

            $account = $task->getAccount();
            if (!$account) {
                $this->setTaskInError($task, 'account does not exist');
                continue;
            }

            $service = $container->get(AutorespondersService::class)->getAutoresponderService($account->getType());
            if (!$service) {
                LoggerService::logWarning(sprintf('autoresponder %s does not exist', $account->getType()));
                $this->setTaskInError($task, sprintf('autoresponder %s does not exist', $account->getType()));
                continue;
            }

            $autoresponderData = json_decode($task->getAutoresponderData(), true);
            $data = json_decode($task->getData(), true);

            $subscribe = $service->subscribe($account, $autoresponderData, $data);
            if (!$subscribe['valid']) {
                $this->setReattempt($task, $subscribe['message'] ?? '');
                continue;
            }

            $this->deleteTask($task);
        }

        return ['valid' => true];
    }

    /**
     * @param AutoresponderQueue $task
     */
    private function deleteTask(AutoresponderQueue $task)
    {
        try {
            $this->deleteAndFlush($task);
        } catch (\Exception $e) {
            LoggerService::logWarning('Error deleting task : ' . $e->getMessage());
        }
    }

    /**
     * @param AutoresponderQueue $task
     * @param string $error
     */
    private function setTaskInError(AutoresponderQueue $task, string $error = '')
    {
        $task->setProcessed(true);
        $task->setError($error);
        try {
            $this->persistAndFlush($task);
        } catch (\Exception $e) {
            LoggerService::logWarning('Error updating task : ' . $e->getMessage());
        }

        $data = json_decode($task->getData(), true);
        $subject = __('Erreur inscription autorépondeur');
        if ($task->getAccount()) {
            $message = __('Lors de l\'enregistrement d\'un contact, l\'autorépondeur %s a renvoyé une erreur :', ucfirst($task->getAccount()->getType()));
        } else {
            $message = __('Lors de l\'enregistrement d\'un contact, un autorépondeur a renvoyé une erreur :');
        }
        $message .= '<br><strong>' . $error . '</strong>';
        $message .= '<br><strong>' . __('Contact') . '</strong> : ' . $data['email'];

        if (isset($data['id_product'])) {
            $container = ContainerBuilderService::getInstance();
            $product = $container->get(ShopProductsService::class)->getProductById($data['id_product'], $task->getClient()->getId());
            if ($product) {
                $message .= '<br><strong>' . __('Produit') . '</strong> : ' . $product->getName();
            }
        }

        NotificationsService::insert($subject, $message, Notification::TYPE_IMPORTANT, $task->getClient()->getId());
    }

    /**
     * @param AutoresponderQueue $task
     * @param string $error
     */
    private function setReattempt(AutoresponderQueue $task, string $error = '')
    {
        if ($task->getAttempt() == 2) {
            $this->setTaskInError($task, $error);
            return;
        }

        $task->setAttempt($task->getAttempt() + 1);
        $task->setError($error);
        try {
            $this->persistAndFlush($task);
        } catch (\Exception $e) {
            LoggerService::logWarning('Error updating task : ' . $e->getMessage());
        }
    }
}
