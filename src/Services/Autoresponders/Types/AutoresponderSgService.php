<?php
namespace MatGyver\Services\Autoresponders\Types;

use MatGyver\Entity\Integration\Account\IntegrationAccount;
use MatGyver\Services\Autoresponders\AbstractAutorespondersService;
use MatGyver\Services\Logger\LoggerService;

/**
 * Class AutoresponderSgService
 * @package MatGyver\Services\Autoresponders
 */
class AutoresponderSgService extends AbstractAutorespondersService
{
    /**
     * AutoresponderSgService constructor.
     */
    public function __construct()
    {
        include_once 'sgapi.php';

        $this->accountFields = array(
            'sg_idclient' => array(
                'info' => __('Numéro client SG-Autorépondeur'),
                'filter' => FILTER_VALIDATE_INT,
                'error' => __('Veuillez entrer votre numéro client SG-Autorépondeur'),
                'required' => true,
            ),
            'sg_codeactivation' => array(
                'info' => __('Code d\'activation SG-Autorépondeur'),
                'filter' => FILTER_UNSAFE_RAW,
                'error' => __('Veuillez entrer votre code d\'activation SG-Autorépondeur'),
                'required' => true,
            ),
        );

        $this->fields = array(
            'sg_idliste' => array(
                'info' => __('Numéro de liste sur SG-Autorépondeur'),
                'filter' => FILTER_VALIDATE_INT,
                'error' => __('Veuillez sélectionner une liste'),
                'required' => true,
            ),
            'sg_groupes' => array(
                'info' => __('Numéro de liste sur SG-Autorépondeur'),
                'filter' => FILTER_VALIDATE_INT,
                'error' => '',
                'required' => false,
                'is_array' => true,
            ),
            'sg_unsubscribe' => array(
                'info' => __('Désinscription'),
                'filter' => FILTER_UNSAFE_RAW,
                'error' => '',
                'required' => false,
            ),
            'sg_shop_products' => array(
                'info' => __('Numéro de liste sur SG-Autorépondeur'),
                'filter' => FILTER_VALIDATE_INT,
                'error' => '',
                'required' => false,
                'is_array' => true,
            ),
        );
    }

    /**
     * @param IntegrationAccount $account
     * @param array $ar_params
     * @param array $datas
     * @return array
     */
    public function subscribe(IntegrationAccount $account, array $ar_params, array $datas): array
    {
        $accountParams = json_decode($account->getDatas(), true);

        $membreid = $accountParams['sg_idclient'];
        $codeactivationclient = $accountParams['sg_codeactivation'];

        $listeid = $ar_params['sg_idliste'];
        $name = $datas['last_name'];
        $first_name = $datas['first_name'];
        $email = $datas['email'];

        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return array('valid' => true);
        }

        $additionnalData = array();
        if (isset($datas['address']) and $datas['address']) {
            $additionnalData['address'] = $datas['address'];
        }
        if (isset($datas['zip']) and $datas['zip']) {
            $additionnalData['codepostal'] = $datas['zip'];
        }
        if (isset($datas['city']) and $datas['city']) {
            $additionnalData['city'] = $datas['city'];
        }
        if (isset($datas['country']) and $datas['country']) {
            $additionnalData['country'] = $datas['country'];
        }
        if (isset($datas['telephone']) and $datas['telephone']) {
            $additionnalData['telephone'] = $datas['telephone'];
        }
        if (isset($datas['parrain']) and $datas['parrain']) {
            $additionnalData['parrain'] = $datas['parrain'];
        }

        $ip = '';
        if (isset($datas['ip']) and $datas['ip']) {
            $ip = $datas['ip'];
        } elseif (isset($_SERVER['REMOTE_ADDR'])) {
            $ip = $_SERVER['REMOTE_ADDR'];
        }

        $listes = array();

        //gestion des groupes
        if (isset($datas['groupes']) and $datas['groupes'] and isset($ar_params['sg_groupes']) and $ar_params['sg_groupes']) {
            foreach ($datas['groupes'] as $idgroupe) {
                if (isset($ar_params['sg_groupes'][$idgroupe]) and $ar_params['sg_groupes'][$idgroupe]) {
                    array_push($listes, $ar_params['sg_groupes'][$idgroupe]);
                }
            }
        }

        if (isset($datas['cart_products']) and $datas['cart_products'] and isset($ar_params['sg_shop_products']) and $ar_params['sg_shop_products']) {
            foreach ($datas['cart_products'] as $cartProduct) {
                if (isset($ar_params['sg_shop_products'][$cartProduct['id_product']]) and $ar_params['sg_shop_products'][$cartProduct['id_product']]) {
                    array_push($listes, $ar_params['sg_shop_products'][$cartProduct['id_product']]);
                }
            }

            array_push($listes, $listeid);
        }

        if (!$listes) {
            $listes = array($listeid);
        }

        foreach ($listes as $listeid) {
            $sgApi = new \API_SG($membreid, $codeactivationclient);

            $sgApi->set('listeid', $listeid)
                ->set('email', $email)
                ->set('nom', $name)
                ->set('prenom', $first_name)
                ->set('ip', $ip);

            if ($additionnalData) {
                foreach ($additionnalData as $data_name => $data_value) {
                    $sgApi->set($data_name, $data_value);
                }
            }

            try {
                $call = $sgApi->call('set_subscriber');
            } catch (\Exception $e) {
                if ($e->getMessage() == 'Aucun résultat renvoyé par SG-Autorépondeur') {
                    //try again 1s later
                    sleep(1);
                    try {
                        $call = $sgApi->call('set_subscriber');
                    } catch (\Exception $e) {
                        return array('valid' => false, 'message' => $e->getMessage());
                    }
                } else {
                    return array('valid' => false, 'message' => $e->getMessage());
                }
            }

            if (!$call) {
                return array('valid' => false, 'message' => __('Erreur appel SG-Autorépondeur'));
            }

            $result = json_decode($call, true);
            if (isset($result['valid']) and !$result['valid']) {
                //test avec un "0"
                if ((isset($result['reponse'][0]) and str_contains($result['reponse'][0], 'Authentification échouée')) or (isset($result['reponse']['info'][0]) and str_contains($result['reponse']['info'][0], 'Authentification échouée'))) {
                    $sgApi = new \API_SG($membreid, '0' . $codeactivationclient);

                    $sgApi->set('listeid', $listeid)
                        ->set('email', $email)
                        ->set('nom', $name)
                        ->set('prenom', $first_name)
                        ->set('ip', $ip);

                    if ($additionnalData) {
                        foreach ($additionnalData as $data_name => $data_value) {
                            $sgApi->set($data_name, $data_value);
                        }
                    }

                    try {
                        $call = $sgApi->call('set_subscriber');
                    } catch (\Exception $e) {
                        if ($e->getMessage() == 'Aucun résultat renvoyé par SG-Autorépondeur') {
                            //try again 1s later
                            sleep(1);
                            try {
                                $call = $sgApi->call('set_subscriber');
                            } catch (\Exception $e) {
                                return array('valid' => false, 'message' => $e->getMessage());
                            }
                        } else {
                            return array('valid' => false, 'message' => $e->getMessage());
                        }
                    }

                    if (!$call) {
                        return array('valid' => false, 'message' => __('Erreur appel SG-Autorépondeur'));
                    }

                    $result = json_decode($call, true);
                }
            }

            if (isset($result['valid']) and !$result['valid']) {
                $error_message = '';
                if (isset($result['reponse']) and is_array($result['reponse'])) {
                    if (isset($result['reponse']['info'])) {
                        $error_message .= '<strong>' . implode('<br>', $result['reponse']['info']) . '</strong>';
                    } else {
                        $error_message .= '<strong>' . implode('<br>', $result['reponse']) . '</strong>';
                    }
                } elseif (isset($result['error']) and is_array($result['error'])) {
                    $error_message .= '<strong>' . implode('<br>', $result['error']) . '</strong>';
                } elseif (isset($result['error'])) {
                    $error_message .= '<strong>' . $result['error'] . '</strong>';
                }

                if (!$error_message) {
                    LoggerService::logError(__('Pas de réponse de SG') . ' :<br>' . json_encode((array) $result));
                }

                if (str_contains($error_message, 'supérieur à la limite autorisée')) {
                    return array('valid' => true);
                }

                if (isset($result['reponse']) and is_array($result['reponse'])) {
                    if (isset($result['reponse']['info'])) {
                        return array('valid' => false, 'message' => __('Erreur renvoyée par SG-Autorépondeur') . ' : ' . implode('<br>', $result['reponse']['info']));
                    } else {
                        return array('valid' => false, 'message' => __('Erreur renvoyée par SG-Autorépondeur') . ' : ' . implode('<br>', $result['reponse']));
                    }
                } elseif (isset($result['error'])) {
                    return array('valid' => false, 'message' => __('Erreur renvoyée par SG-Autorépondeur') . ' : ' . $result['error']);
                }

                return array('valid' => false, 'message' => __('Erreur renvoyée par SG-Autorépondeur'));
            }
        }

        return array('valid' => true);
    }

    /**
     * @param IntegrationAccount $account
     * @param array $ar_params
     * @param array $datas
     * @return array|bool[]
     */
    public function unsubscribe(IntegrationAccount $account, array $ar_params, array $datas): array
    {
        $accountParams = json_decode($account->getDatas(), true);

        $membreid = $accountParams['sg_idclient'];
        $codeactivationclient = $accountParams['sg_codeactivation'];

        $listeid = $ar_params['sg_idliste'];
        $email = $datas['email'];

        $listes = array();

        //gestion des groupes
        if (isset($datas['groupes']) and $datas['groupes'] and isset($ar_params['sg_groupes']) and $ar_params['sg_groupes']) {
            foreach ($datas['groupes'] as $idgroupe) {
                if (isset($ar_params['sg_groupes'][$idgroupe]) and $ar_params['sg_groupes'][$idgroupe]) {
                    array_push($listes, $ar_params['sg_groupes'][$idgroupe]);
                }
            }
        }

        if (!$listes) {
            $listes = array($listeid);
        }

        foreach ($listes as $listeid) {
            $sgApi = new \API_SG($membreid, $codeactivationclient);
            $sgApi->set('listeid', $listeid)
                ->set('email', $email)
                ->set('motifdesabonnement', 'api_sg');

            try {
                $call = $sgApi->call('del_subscriber');
            } catch (\Exception $e) {
                return array('valid' => false, 'message' => $e->getMessage());
            }

            //retour
            $result = json_decode($call, true);
            if (!$result['valid']) {
                //test avec un "0"
                if ((isset($result['reponse'][0]) and str_contains($result['reponse'][0], 'Authentification échouée')) or (isset($result['reponse']['info'][0]) and str_contains($result['reponse']['info'][0], 'Authentification échouée'))) {
                    $sgApi = new \API_SG($membreid, '0' . $codeactivationclient);

                    $sgApi->set('listeid', $listeid)
                        ->set('email', $email);

                    try {
                        $sgApi->call('del_subscriber');
                    } catch (\Exception $e) {
                        return array('valid' => false, 'message' => $e->getMessage());
                    }
                }
            }
        }

        return array('valid' => true);
    }

    /**
     * @param int $idAccount
     * @return array
     */
    public function updateLists(int $idAccount) :array
    {
        $account = $this->accountsService->getAccount($idAccount);
        if (!$account or !$account->getDatas()) {
            return array('valid' => false, 'message' => __('Autorépondeur non configuré, cliquez sur "Valider" pour enregistrer vos modifications puis réessayez.'));
        }

        $accountParams = json_decode($account->getDatas(), true);
        if (!isset($accountParams['sg_idclient']) or !$accountParams['sg_idclient']) {
            return array('valid' => false, 'message' => __('Une erreur est survenue : Id Client manquant.'));
        }
        if (!isset($accountParams['sg_codeactivation']) or !$accountParams['sg_codeactivation']) {
            return array('valid' => false, 'message' => __('Une erreur est survenue : code d\'activation manquant.'));
        }

        $sgIdClient = $accountParams['sg_idclient'];
        $codeActivation = $accountParams['sg_codeactivation'];

        if (!$sgIdClient or !$codeActivation) {
            return array('valid' => false, 'message' => __('Autorépondeur non configuré.'));
        }

        $sgApi = new \API_SG($sgIdClient, $codeActivation);
        try {
            $lists = $sgApi->call('get_list');
        } catch (\Exception $e) {
            return array('valid' => false, 'message' => $e->getMessage());
        }

        //retour
        $result = json_decode($lists, true);

        if (!$result['valid']) {
            //test avec un "0"
            if ((isset($result['reponse'][0]) and str_contains($result['reponse'][0], 'Authentification échouée')) or (isset($result['reponse']['info'][0]) and str_contains($result['reponse']['info'][0], 'Authentification échouée'))) {
                $sgApi = new \API_SG($sgIdClient, '0' . $codeActivation);

                try {
                    $lists = $sgApi->call('get_list');
                } catch (\Exception $e) {
                    return array('valid' => false, 'message' => $e->getMessage());
                }

                $result = json_decode($lists, true);
                if (!$result['valid']) {
                    if (isset($result['reponse']['info']) and is_array($result['reponse']['info'])) {
                        return array('valid' => false, 'message' => $result['reponse']['info'][0]);
                    }
                    if (isset($result['reponse']) and is_array($result['reponse'])) {
                        return array('valid' => false, 'message' => $result['reponse'][0]);
                    }
                    return array('valid' => false, 'message' => $result['reponse']);
                }
            } else {
                if (isset($result['reponse']['info']) and is_array($result['reponse']['info'])) {
                    return array('valid' => false, 'message' => $result['reponse']['info'][0]);
                }
                if (isset($result['reponse']) and is_array($result['reponse'])) {
                    return array('valid' => false, 'message' => $result['reponse'][0]);
                }

                return array('valid' => false, 'message' => $result['reponse']);
            }
        }

        //remise en forme
        $valid_lists = array();
        if ($result) {
            foreach ($result['reponse'] as $list) {
                $valid_lists[$list['listeid']] = $list['nom'];
            }
        }

        if (!$valid_lists) {
            return array('valid' => false, 'message' => __('Aucune liste trouvée.'));
        }

        //save in bdd
        $update = $this->accountsDataService->setData($idAccount, 'sg_lists', json_encode($valid_lists));
        if (!$update['valid']) {
            return array('valid' => false, 'message' => $update['message']);
        }

        return array('valid' => true);
    }

    /**
     * @param int    $idAccount
     * @param string $selectedList
     * @param bool   $show_button
     * @return string
     */
    public function generateSelectLists(int $idAccount, string $selectedList = '', bool $show_button = true): string
    {
        $account = $this->accountsService->getAccount($idAccount);
        if (!$account or !$account->getDatas()) {
            return __('Ce compte n\'existe pas');
        }

        $lists = $this->accountsDataService->getData($account, 'sg_lists');
        if (!$lists or !$lists->getValue()) {
            return '
                <div class="alert alert-primary mb-5 p-5">
                    <p>' . __('Aucune liste trouvée.') . '</p>
                    <div class="border-bottom border-white opacity-20 mb-5"></div>
                    <a class="btn btn-white btn-sm btn-get-lists" onclick="updateSelect(\'lists\', \'sg\', true);">' . __('Récupérer les listes') . '</a>
                </div>';
        }

        $lists = json_decode($lists->getValue(), true);

        $options = [];
        $options[] = __('Choisissez une liste');
        foreach ($lists as $idList => $list) {
            $options[$idList] = $idList . ' - ' . $list;
        }

        $output = $this->generateSelect('sg_idliste', $options, $selectedList);
        if ($show_button) {
            $output .= '<a class="btn btn-icon btn-clean btn-sm btn-get-lists" onclick="updateSelect(\'lists\', \'sg\');"><i class="fas fa-sync-alt"></i></a>';
        }

        return $output;
    }
}
