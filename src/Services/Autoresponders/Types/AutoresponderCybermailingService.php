<?php
namespace MatGyver\Services\Autoresponders\Types;

use MatGyver\Entity\Integration\Account\IntegrationAccount;
use MatGyver\Services\Autoresponders\AbstractAutorespondersService;
use MatGyver\Services\Logger\LoggerService;

class AutoresponderCybermailingService extends AbstractAutorespondersService
{
    public $returns_error = array();

    public function __construct()
    {
        $this->accountFields = array(
            'cyber_key' => array(
                'info' => __('Clé Privée'),
                'filter' => FILTER_UNSAFE_RAW,
                'error' => __('Veuillez entrer votre clé privée CyberMailing'),
                'required' => true,
            ),
        );

        $this->fields = array(
            'cyber_liste' => array(
                'info' => __('Liste CyberMailing'),
                'filter' => FILTER_VALIDATE_INT,
                'error' => __('Veuillez entrer le numéro de la liste de destination'),
                'required' => true,
            ),
            'cyber_groupes' => array(
                'info' => __('Liste'),
                'filter' => FILTER_VALIDATE_INT,
                'error' => '',
                'required' => false,
                'is_array' => true,
            ),
            'cybermailing_unsubscribe' => array(
                'info' => __('Désinscription'),
                'filter' => FILTER_UNSAFE_RAW,
                'error' => '',
                'required' => false,
            ),
            'cyber_shop_produits' => array(
                'info' => __('Liste'),
                'filter' => FILTER_VALIDATE_INT,
                'error' => '',
                'required' => false,
                'is_array' => true,
            ),
        );
    }

    /**
     * @param IntegrationAccount $account
     * @param array $ar_params
     * @param array $datas
     * @return array
     */
    public function subscribe(IntegrationAccount $account, array $ar_params, array $datas): array
    {
        $accountParams = json_decode($account->getDatas(), true);
        if (!isset($accountParams['cyber_key']) or !$accountParams['cyber_key']) {
            return array('valid' => false, 'message' => __('Une erreur est survenue : aucune clé API définie.'));
        }
        $cyber_key = $accountParams['cyber_key'];
        $cyber_liste = $ar_params['cyber_liste'];

        $first_name = $datas['first_name'];
        $email = $datas['email'];

        $ip = '';
        if (isset($datas['ip'])) {
            $ip = $datas['ip'];
        }

        $listes = array();

        //gestion des groupes
        if (isset($datas['groupes']) and $datas['groupes'] and isset($ar_params['cyber_groupes']) and $ar_params['cyber_groupes']) {
            foreach ($datas['groupes'] as $idgroupe) {
                if (isset($ar_params['cyber_groupes'][$idgroupe]) and $ar_params['cyber_groupes'][$idgroupe]) {
                    array_push($listes, $ar_params['cyber_groupes'][$idgroupe]);
                }
            }
        }

        // gestion des produits
        if (isset($datas['cart_produits']) and $datas['cart_produits'] and isset($ar_params['cyber_shop_produits']) and $ar_params['cyber_shop_produits']) {
            foreach ($datas['cart_produits'] as $cartProduit) {
                if (isset($ar_params['cyber_shop_produits'][$cartProduit['id_produit']]) and $ar_params['cyber_shop_produits'][$cartProduit['id_produit']]) {
                    array_push($listes, $ar_params['cyber_shop_produits'][$cartProduit['id_produit']]);
                }
            }

            if ($cyber_liste) {
                array_push($listes, $cyber_liste);
            }
        }

        if (!$listes) {
            $listes = array($cyber_liste);
        }

        foreach ($listes as $liste) {
            $array_valeurs = array(
                'CyberKey' => $cyber_key,
                'function' => 'subscribe',
                'Email' => $email,
                'Liste' => $liste,
                'Ip' => $ip,
                'Name' => $first_name,
            );

            $ch = curl_init('https://api.cybermailing.com/talk.php');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $array_valeurs);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

            $response = curl_exec($ch);
            $curl_error = curl_error($ch);
            curl_close($ch);

            if ($response) {
                $response = json_decode($response, true);
            }

            if (!$response or (isset($response['type']) and 'error' == $response['type'])) {
                $message = __('CyberMailing a renvoyé une erreur pour la liste') . ' ' . $liste . ' : <strong>' . ($response ? $response['message'] : __('Aucun message renvoyé par CyberMailing')) . '</strong>';
                return array('valid' => false, 'message' => $message);
            }

            if ($curl_error) {
                LoggerService::logError('CURL Error : ' . $curl_error);
                return array('valid' => false, 'message' => __('Erreur renvoyée par CyberMailing'));
            }
        }

        return array('valid' => true);
    }

    /**
     * @param IntegrationAccount $account
     * @param array $ar_params
     * @param array $datas
     * @return array|bool[]
     */
    public function unsubscribe(IntegrationAccount $account, array $ar_params, array $datas): array
    {
        $accountParams = json_decode($account->getDatas(), true);
        if (!isset($accountParams['cyber_key']) or !$accountParams['cyber_key']) {
            return array('valid' => false, 'message' => __('Une erreur est survenue : aucune clé API définie.'));
        }
        $cyber_key = $accountParams['cyber_key'];
        $cyber_liste = $ar_params['cyber_liste'];

        $email = $datas['email'];

        $listes = array();

        //gestion des groupes
        if (isset($datas['groupes']) and $datas['groupes'] and isset($ar_params['cyber_groupes']) and $ar_params['cyber_groupes']) {
            foreach ($datas['groupes'] as $idgroupe) {
                if (isset($ar_params['cyber_groupes'][$idgroupe]) and $ar_params['cyber_groupes'][$idgroupe]) {
                    array_push($listes, $ar_params['cyber_groupes'][$idgroupe]);
                }
            }
        }

        if (!$listes) {
            $listes = array($cyber_liste);
        }

        foreach ($listes as $liste) {
            $array_valeurs = array(
                'CyberKey' => $cyber_key,
                'function' => 'unsubscribe',
                'Email' => $email,
                'Liste' => $liste,
            );

            $ch = curl_init('https://api.cybermailing.com/talk.php');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $array_valeurs);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

            $response = curl_exec($ch);
            $curl_error = curl_error($ch);
            curl_close($ch);

            if ($response) {
                $response = json_decode($response, true);
            }

            if (!$response or (isset($response['type']) and 'error' == $response['type'])) {
                $message = __('CyberMailing a renvoyé une erreur pour la liste') . ' ' . $liste . ' : <strong>' . ($response ? $response['message'] : __('Aucun message renvoyé par CyberMailing')) . '</strong>';
                return array('valid' => false, 'message' => $message);
            }

            if ($curl_error) {
                LoggerService::logError('CURL Error : ' . $curl_error);
                return array('valid' => false, 'message' => __('Erreur renvoyée par CyberMailing'));
            }
        }

        return array('valid' => true);
    }
}
