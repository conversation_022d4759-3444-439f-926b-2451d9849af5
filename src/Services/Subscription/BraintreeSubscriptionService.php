<?php

namespace Mat<PERSON><PERSON>ver\Services\Subscription;

use Doctrine\ORM\EntityManager;
use MatGyver\Entity\Braintree\Charge\BraintreeCharge;
use MatG<PERSON>ver\Entity\Braintree\Subscription\BraintreeSubscription;
use MatG<PERSON>ver\Helpers\Transaction;
use MatG<PERSON><PERSON>\Repository\Braintree\Subscription\BraintreeSubscriptionRepository;
use MatGyver\Services\Braintree\BraintreeChargesService;
use MatGyver\Services\Braintree\BraintreeService;
use MatGyver\Services\ConfigService;
use MatGyver\Services\Shop\Transaction\ShopTransactionBraintreeService;

/**
 * Class BraintreeSubscriptionService
 * @package MatGyver\Services\Subscription
 * @property BraintreeSubscriptionRepository $repository
 * @method BraintreeSubscriptionRepository getRepository()
 */
class BraintreeSubscriptionService extends AbstractSubscriptionService implements SubscriptionInterface
{
    /**
     * @var BraintreeService
     */
    private $braintreeService;

    /**
     * @var BraintreeChargesService
     */
    private $braintreeChargesService;

    /**
     * @var ShopTransactionBraintreeService
     */
    private $transactionBraintreeService;

    /**
     * BraintreeSubscriptionService constructor.
     * @param EntityManager $em
     * @param BraintreeService $braintreeService
     * @param BraintreeChargesService $braintreeChargesService
     * @param ShopTransactionBraintreeService $transactionBraintreeService
     * @param ConfigService $configService
     */
    public function __construct(
        EntityManager $em,
        BraintreeService $braintreeService,
        BraintreeChargesService $braintreeChargesService,
        ShopTransactionBraintreeService $transactionBraintreeService,
        ConfigService $configService
    ) {
        $this->type = 'braintree';
        $this->em = $em;
        $this->repository = $this->em->getRepository(BraintreeSubscription::class);
        $this->braintreeService = $braintreeService;
        $this->braintreeChargesService = $braintreeChargesService;
        $this->transactionBraintreeService = $transactionBraintreeService;
        $this->configService = $configService;
    }

    /**
     * @param array $subscriptions
     * @return array
     */
    public function executeSubscriptions(array $subscriptions): array
    {
        $error = '';
        $log = '';

        $date = date('Y-m-d');

        foreach ($subscriptions as $subscription) {
            $idSubscription = $subscription->getId();
            $idClient = $subscription->getClient()->getId();

            $log .= "Traitement de l'abonnement $idSubscription\n";

            //récupération de l'abonnement au cas où il aurait été traité entre-temps
            $this->em->clear();
            $subscription = $this->repository->findOneBy(['id' => $idSubscription, 'client' => $idClient]);
            if ($subscription->getDate()->format('Y-m-d') != $date) {
                $log .= "Cet abonnement a déjà été traité\n";
                continue;
            }
            if (!$subscription->getValid()) {
                $log .= "Cet abonnement a déjà été traité et n'est plus valide\n";
                continue;
            }
            if (!$subscription->getNbPaymentsLeft()) {
                $log .= "Cet abonnement a déjà été traité et aucun paiement supplémentaire à effectuer\n";
                continue;
            }

            $executeSubscription = $this->executeSubscription($subscription);
            $log .= $executeSubscription['log'];
            $error .= $executeSubscription['error'];
        }

        return array('valid' => !$error, 'log' => $log, 'error' => $error);
    }

    /**
     * @param BraintreeSubscription $subscription
     * @return array
     */
    public function executeSubscription(BraintreeSubscription $subscription): array
    {
        $error = '';
        $log = '';

        $idSubscription = $subscription->getId();
        $idClient = $subscription->getClient()->getId();

        $account = $subscription->getAccount();
        if (!$account) {
            $log .= "Impossible de démarrer Braintree : ce compte n'existe plus : EXIT\n";
            $error .= __('Abonnement %s : Impossible de démarrer Braintree : ce compte n\'existe plus', $idSubscription) . "\n";

            $update = $this->updateSubscriptionInError($idSubscription, $idClient, __('Impossible de démarrer Braintree : ce compte n\'existe plus'));
            if (!$update['valid']) {
                $log .= "Erreur lors de la mise à jour de l'abonnement : " . $update['message'] . "\n";
            }

            return array('valid' => false, 'log' => $log, 'error' => $error);
        }

        try {
            $this->braintreeService->startBraintree($account);
        } catch (\Exception $e) {
            $log .= "Aucune configuration trouvée pour Braintree : EXIT\n";
            $error .= __('Abonnement %s : Aucune configuration trouvée pour Braintree', $idSubscription) . "\n";

            $update = $this->updateSubscriptionInError($idSubscription, $idClient, __('Aucune configuration trouvée pour Braintree'));
            if (!$update['valid']) {
                $log .= "Erreur lors de la mise à jour de l'abonnement : " . $update['message'] . "\n";
            }

            return array('valid' => false, 'log' => $log, 'error' => $error);
        }

        $client = $this->checkClient($idSubscription, $idClient);
        if ($client === null) {
            $log .= __('Client inactif ou en pause') . "\n";
            return array('valid' => false, 'log' => $log, 'error' => $error);
        }

        $paymentToken = $subscription->getPaymentToken();
        $customerId = $subscription->getCustomerId();
        $product = $subscription->getProduct();
        $amount = $subscription->getAmount();
        $currency = $subscription->getCurrency();
        $last_name = $subscription->getLastName();
        $first_name = $subscription->getFirstName();
        $email = $subscription->getEmail();
        $custom = $subscription->getCustom();
        $checkout = $subscription->getCheckout();
        $ip = $subscription->getIp();

        $idTransaction = Transaction::generateTransactionReference();

        //save charge
        $charge = new BraintreeCharge();
        $charge->setClient($client);
        $charge->setAccount($account);
        $charge->setCartId(0);
        $charge->setPaymentToken($paymentToken);
        $charge->setCustomerId($customerId);
        $charge->setTransactionReference($idTransaction);
        $charge->setSubscriptionId($idSubscription);
        $charge->setProduct($product);
        $charge->setAmount($amount);
        $charge->setCurrency($currency);
        $charge->setLastName($last_name);
        $charge->setFirstName($first_name);
        $charge->setEmail($email);
        $charge->setCustom($custom);
        $charge->setCheckout($checkout);
        $charge->setIp($ip);
        $charge->setResult('');
        $charge->setError('');
        $charge->setLast4('');
        $this->persistAndFlush($charge);

        //Process Payment
        $options = ['transactionSource' => 'recurring'];
        $makeCharge = $this->braintreeChargesService->makeCharge($account, $paymentToken, $amount, 0, $options);

        if ($makeCharge['valid']) {
            $status = 'succeeded';
            $result = $makeCharge['charge'];

            if (isset($result->transaction->id)) {
                $idTransaction = $result->transaction->id;
            } elseif (isset($result->subscription->id)) {
                $idTransaction = $result->subscription->id;
            }

            $charge->setTransactionReference($idTransaction);
            $charge->setResult(json_encode((array) $result));
            $this->persistAndFlush($charge);

            //mise à jour de l'abonnement
            $update = $this->updateSubscription($idSubscription, $idClient);
            if (!$update['valid']) {
                $log .= "Erreur lors de la mise à jour de l'abonnement : " . $update['message'] . "\n";
                $error .= __("Erreur lors de la mise à jour de l'abonnement : ") . $update['message'] . "\n";
            }
        } else {
            $status = 'failed';
            $errorMessage = $makeCharge['message'];
            $code = '';
            $type = '';
            if (isset($makeCharge['code'])) {
                $code = $makeCharge['code'];
            }
            if (isset($makeCharge['type'])) {
                $type = $makeCharge['type'];
            }

            $log .= "Braintree Error : $errorMessage\n";

            $charge->setError($errorMessage);
            $charge->setResult(json_encode((array) $makeCharge));
            $this->persistAndFlush($charge);

            //set reattempt
            if ('processor_declined' == $code and 'Hard' == $type) {
                $update = $this->updateSubscriptionInError($idSubscription, $idClient, $errorMessage);
            } else {
                $update = $this->setReattempt($idSubscription, $idClient);
            }
            if (!$update['valid']) {
                $log .= "Erreur lors de la mise à jour de l'abonnement : " . $update['message'] . "\n";
                $error .= __("Erreur lors de la mise à jour de l'abonnement : ") . $update['message'] . "\n";
            }
        }

        $log .= "Call webhook $idTransaction (status = $status)\n";
        $sendWebhook = $this->transactionBraintreeService->sendWebhook($account->getId(), $idTransaction, $status, $client->getUniqid());
        if (!$sendWebhook['valid']) {
            $log .= "Error send webhook : ".$sendWebhook['message'] . "\n";
        } else {
            $log .= "send webhook : OK\n";
            $log .= $sendWebhook['log'] . "\n";
        }

        return array('valid' => !$error, 'log' => $log, 'error' => $error);
    }
}
