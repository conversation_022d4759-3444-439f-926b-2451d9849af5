<?php

namespace MatGyver\Services\News;

use Doctrine\ORM\EntityManager;
use MatGyver\Entity\News\Client\NewsClient;
use MatGyver\Repository\News\Client\NewsClientRepository;
use MatGyver\Services\BaseEntityService;
use MatGyver\Services\Clients\ClientsService;
use MatGyver\Services\Users\UsersService;

/**
 * Class NewsClientsService
 * @package MatGyver\Services\News
 * @property NewsClientRepository $repository
 * @method NewsClientRepository getRepository()
 */
class NewsClientsService extends BaseEntityService
{

    /**
     * @var NewsService
     */
    private $newsService;

    /**
     * @var ClientsService
     */
    private $clientsService;

    /**
     * @var UsersService
     */
    private $usersService;

    /**
     * NewsClientsService constructor.
     * @param EntityManager $entityManager
     * @param NewsService $newsService
     * @param ClientsService $clientsService
     * @param UsersService $usersService
     */
    public function __construct(
        EntityManager $entityManager,
        NewsService $newsService,
        ClientsService $clientsService,
        UsersService $usersService
    ) {
        $this->em = $entityManager;
        $this->repository = $entityManager->getRepository(NewsClient::class);
        $this->newsService = $newsService;
        $this->clientsService = $clientsService;
        $this->usersService = $usersService;
    }

    /**
     * @param int $idNews
     * @param bool $readed
     * @param int|null $userId
     * @param int|null $idClient
     * @return array
     */
    public function setNewsView(int $idNews, bool $readed = false, ?int $userId = null, ?int $idClient = null): array
    {
        if ($userId === null) {
            $userId = $_SESSION['user']['id'];
        }
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }

        if (!$idNews) {
            return ['valid' => false, 'message' => __('Cette news n\'existe pas')];
        }
        $news = $this->newsService->getRepository()->find($idNews);
        if (!$news) {
            return ['valid' => true];
        }

        $newsClient = $this->repository->getNewsViewByUser($news, $userId, $idClient);
        if ($newsClient) {
            $newsClient->setViewed(true);
            if (!$newsClient->getReaded() and $readed) {
                $newsClient->setReaded(true);
            }

            try {
                $this->persistAndFlush($newsClient);
            } catch (\Exception $e) {
                return ['valid' => false, 'message' => __('Erreur lors de la mise à jour de la news.')];
            }
            return ['valid' => true];
        }

        $user = $this->usersService->getRepository()->find($userId);
        if (!$user) {
            $user = $this->usersService->getRepository()->findWoClient($userId);
        }

        $newsClient = new NewsClient();
        $newsClient->setNews($news);
        $newsClient->setClient($this->clientsService->getClientById($idClient));
        $newsClient->setUser($user);
        $newsClient->setViewed(true);
        $newsClient->setReaded($readed);
        try {
            $this->persistAndFlush($newsClient);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Erreur lors de la mise à jour de la news.') . $e->getMessage()];
        }

        $nbViews = $news->getViews();
        $news->setViews(++$nbViews);
        try {
            $this->newsService->persistAndFlush($news);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Erreur lors de la mise à jour de la news.')];
        }

        return ['valid' => true];
    }
}
