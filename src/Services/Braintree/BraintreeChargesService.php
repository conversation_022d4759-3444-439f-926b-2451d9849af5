<?php

namespace Mat<PERSON><PERSON>ver\Services\Braintree;

use Doctrine\ORM\EntityManager;
use Mat<PERSON>yver\Entity\Braintree\Charge\BraintreeCharge;
use MatG<PERSON>ver\Entity\Integration\Account\IntegrationAccount;
use MatG<PERSON>ver\Entity\Shop\Transaction\ShopTransaction;
use MatGyver\Repository\Braintree\Charge\BraintreeChargeRepository;
use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\Shop\ShopCustomersService;

/**
 * Class BraintreeChargesService
 * @package MatGyver\Services\Braintree
 * @property BraintreeChargeRepository $repository
 * @method BraintreeChargeRepository getRepository()
 */
class BraintreeChargesService extends BraintreeService
{
    /**
     * @var ShopCustomersService
     */
    private $shopCustomersService;

    /**
     * BraintreeChargesService constructor.
     * @param EntityManager $em
     * @param ShopCustomersService $shopCustomersService
     */
    public function __construct(
        EntityManager $em,
        ShopCustomersService $shopCustomersService
    ) {
        $this->em = $em;
        $this->repository = $em->getRepository(BraintreeCharge::class);
        $this->shopCustomersService = $shopCustomersService;
        parent::__construct();
    }

    /**
     * @param IntegrationAccount $account
     * @param string $paymentMethodToken
     * @param float $amount
     * @param int $idCustomer
     * @param array $addOptions
     * @return array
     */
    public function makeCharge(IntegrationAccount $account, string $paymentMethodToken, float $amount, int $idCustomer = 0, array $addOptions = []): array
    {
        $amount = number_format($amount, 2, '.', '');

        $options = [
            'paymentMethodToken' => $paymentMethodToken,
            'amount' => $amount,
            'options' => ['submitForSettlement' => true],
        ];

        if (isset($_POST['deviceData']) and $_POST['deviceData']) {
            $options['deviceData'] = filter_input(INPUT_POST, 'deviceData', FILTER_UNSAFE_RAW);
        }

        $options += $addOptions;

        if ($idCustomer) {
            $customer = $this->shopCustomersService->getCustomerById($idCustomer);
            if ($customer) {
                $options['billing'] = [
                    'countryCodeAlpha2' => $customer->getCountry(),
                    'firstName' => $customer->getFirstName(),
                    'lastName' => $customer->getLastName(),
                ];

                if ($customer->getAddress()) {
                    $options['billing']['streetAddress'] = $customer->getAddress();
                }
                if ($customer->getCity()) {
                    $options['billing']['locality'] = $customer->getCity();
                }
                if ($customer->getZip()) {
                    $options['billing']['postalCode'] = $customer->getZip();
                }
            }
        }

        try {
            $this->startBraintree($account);
            $charge = $this->gateway->transaction()->sale($options);
            if (!$charge->success) {
                return $this->handleError($charge);
            }
            return ['valid' => true, 'charge' => $charge];
        } catch (\Exception $e) {
            LoggerService::logWarning('Create Charge Exception : ' . $e->getMessage());
            return ['valid' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * @param ShopTransaction $transaction
     * @param float $amount
     * @return array
     */
    public function refundTransaction(ShopTransaction $transaction, float $amount): array
    {
        $account = $transaction->getAccount();

        try {
            $this->startBraintree($account);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Une erreur est survenue lors du remboursement.')];
        }

        $amount = number_format($amount, 2, '.', '');

        try {
            $result = $this->getGateway()->transaction()->refund($transaction->getReference(), $amount);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Une erreur est survenue lors du remboursement.') . ' ' . $e->getMessage()];
        }

        if (!$result->success) {
            return $this->handleError($result);
        }

        return ['valid' => true];
    }
}
