<?php

namespace MatGyver\Services\Braintree;

use MatGyver\Entity\Integration\Account\IntegrationAccount;
use MatGyver\Helpers\Assets;
use MatGyver\Services\BaseEntityService;
use MatGyver\Services\Logger\LoggerService;
use Mollie\Api\MollieApiClient;

/**
 * Class BraintreeService
 * @package MatGyver\Services\Braintree
 */
class BraintreeService extends BaseEntityService
{
    /**
     * @var MollieApiClient
     */
    protected $mollie;

    /**
     * @var \Braintree\Gateway
     */
    protected $gateway;

    /**
     * @var string
     */
    private $environment;

    /**
     * @var array
     */
    private $errorsCode;

    public function __construct()
    {
        $this->setEnvironment();

        $this->errorsCode = array(
            2000 => array('error' => __('Votre banque a refusé le paiement'), 'type' => 'Soft'),
            2001 => array('error' => __('Fonds insuffisants'), 'type' => 'Soft'),
            2002 => array('error' => __('Limite de paiements atteinte'), 'type' => 'Soft'),
            2003 => array('error' => __('Limite de paiements atteinte'), 'type' => 'Soft'),
            2004 => array('error' => __('Carte expirée'), 'type' => 'Hard'),
            2005 => array('error' => __('Numéro de carte invalide'), 'type' => 'Hard'),
            2006 => array('error' => __('Date d\'expiration invalide'), 'type' => 'Hard'),
            2007 => array('error' => __('No Account'), 'type' => 'Hard'),
            2008 => array('error' => __('Numéro de carte invalide'), 'type' => 'Hard'),
            2009 => array('error' => __('No Such Issuer'), 'type' => 'Soft'),
            2010 => array('error' => __('Cryptogramme invalide'), 'type' => 'Hard'),
            2011 => array('error' => __('Voice Authorization Required'), 'type' => 'Hard'),
            2012 => array('error' => __('Votre banque a refusé le paiement (carte perdue)'), 'type' => 'Hard'),
            2013 => array('error' => __('Votre banque a refusé le paiement (carte volée)'), 'type' => 'Hard'),
            2014 => array('error' => __('Votre banque a refusé le paiement (tentative de fraude)'), 'type' => 'Hard'),
            2015 => array('error' => __('Votre banque a refusé le paiement'), 'type' => 'Hard'),
            2016 => array('error' => __('Duplicate Transaction'), 'type' => 'Soft'),
            2017 => array('error' => __('Cardholder Stopped Billing'), 'type' => 'Hard'),
            2018 => array('error' => __('Cardholder Stopped All Billing'), 'type' => 'Hard'),
            2019 => array('error' => __('Votre banque a refusé le paiement'), 'type' => 'Hard'),
            2020 => array('error' => __('Violation'), 'type' => 'Hard'),
            2021 => array('error' => __('Security Violation'), 'type' => 'Soft'),
            2022 => array('error' => __('Declined – Updated Cardholder Available'), 'type' => 'Hard'),
            2023 => array('error' => __('Processor Does Not Support This Feature'), 'type' => 'Hard'),
            2024 => array('error' => __('Card Type Not Enabled'), 'type' => 'Hard'),
            2025 => array('error' => __('Set Up Error – Merchant'), 'type' => 'Soft'),
            2026 => array('error' => __('Invalid Merchant ID'), 'type' => 'Soft'),
            2027 => array('error' => __('Set Up Error – Amount'), 'type' => 'Hard'),
            2028 => array('error' => __('Set Up Error – Hierarchy'), 'type' => 'Hard'),
            2029 => array('error' => __('Set Up Error – Card'), 'type' => 'Hard'),
            2030 => array('error' => __('Set Up Error – Terminal'), 'type' => 'Hard'),
            2031 => array('error' => __('Encryption Error'), 'type' => 'Hard'),
            2032 => array('error' => __('Surcharge Not Permitted'), 'type' => 'Hard'),
            2033 => array('error' => __('Inconsistent Data'), 'type' => 'Soft'),
            2034 => array('error' => __('No Action Taken'), 'type' => 'Soft'),
            2035 => array('error' => __('Partial Approval For Amount In Group III Version'), 'type' => 'Soft'),
            2036 => array('error' => __('Authorization could not be found to reverse'), 'type' => 'Hard'),
            2037 => array('error' => __('Already Reversed'), 'type' => 'Hard'),
            2038 => array('error' => __('Votre banque a refusé le paiement'), 'type' => 'Soft'),
            2039 => array('error' => __('Invalid Authorization Code'), 'type' => 'Hard'),
            2040 => array('error' => __('Invalid Store'), 'type' => 'Soft'),
            2041 => array('error' => __('Declined – Call For Approval'), 'type' => 'Hard'),
            2042 => array('error' => __('Invalid Client ID'), 'type' => 'Soft'),
            2043 => array('error' => __('Error – Do Not Retry, Call Issuer'), 'type' => 'Hard'),
            2044 => array('error' => __('Declined – Call Issuer'), 'type' => 'Hard'),
            2045 => array('error' => __('Invalid Merchant Number'), 'type' => 'Hard'),
            2046 => array('error' => __('Votre banque a refusé le paiement'), 'type' => 'Soft'),
            2047 => array('error' => __('Call Issuer. Pick Up Card'), 'type' => 'Hard'),
            2048 => array('error' => __('Invalid Amount'), 'type' => 'Soft'),
            2049 => array('error' => __('Invalid SKU Number'), 'type' => 'Hard'),
            2050 => array('error' => __('Invalid Credit Plan'), 'type' => 'Soft'),
            2051 => array('error' => __('Credit Card Number does not match method of payment'), 'type' => 'Hard'),
            2053 => array('error' => __('Card reported as lost or stolen'), 'type' => 'Hard'),
            2054 => array('error' => __('Reversal amount does not match authorization amount'), 'type' => 'Soft'),
            2055 => array('error' => __('Invalid Transaction Division Number'), 'type' => 'Hard'),
            2056 => array('error' => __('Transaction amount exceeds the transaction division limit'), 'type' => 'Hard'),
            2057 => array('error' => __('Issuer or Cardholder has put a restriction on the card'), 'type' => 'Soft'),
            2058 => array('error' => __('Merchant not Mastercard SecureCode enabled'), 'type' => 'Hard'),
            2059 => array('error' => __('Address Verification Failed'), 'type' => 'Hard'),
            2060 => array('error' => __('Address Verification and Card Security Code Failed'), 'type' => 'Hard'),
            2061 => array('error' => __('Invalid Transaction Data'), 'type' => 'Hard'),
            2062 => array('error' => __('Invalid Tax Amount'), 'type' => 'Soft'),
            2063 => array('error' => __('PayPal Business Account preference resulted in the transaction failing'), 'type' => 'Hard'),
            2064 => array('error' => __('Invalid Currency Code'), 'type' => 'Hard'),
            2065 => array('error' => __('Refund Time Limit Exceeded'), 'type' => 'Hard'),
            2066 => array('error' => __('PayPal Business Account Restricted'), 'type' => 'Hard'),
            2067 => array('error' => __('Authorization Expired'), 'type' => 'Hard'),
            2068 => array('error' => __('PayPal Business Account Locked or Closed'), 'type' => 'Hard'),
            2069 => array('error' => __('PayPal Blocking Duplicate Order IDs'), 'type' => 'Hard'),
            2070 => array('error' => __('PayPal Buyer Revoked Future Payment Authorization'), 'type' => 'Hard'),
            2071 => array('error' => __('PayPal Payee Account Invalid Or Does Not Have a Confirmed Email'), 'type' => 'Hard'),
            2072 => array('error' => __('PayPal Payee Email Incorrectly Formatted'), 'type' => 'Hard'),
            2073 => array('error' => __('PayPal Validation Error'), 'type' => 'Hard'),
            2074 => array('error' => __('Funding Instrument In The PayPal Account Was Declined By The Processor Or Bank, Or It Can\'t Be Used For This Payment'), 'type' => 'Hard'),
            2075 => array('error' => __('Payer Account Is Locked Or Closed'), 'type' => 'Hard'),
            2076 => array('error' => __('Payer Cannot Pay For This Transaction With PayPal'), 'type' => 'Hard'),
            2077 => array('error' => __('Transaction Refused Due To PayPal Risk Model'), 'type' => 'Hard'),
            2079 => array('error' => __('PayPal Merchant Account Configuration Error'), 'type' => 'Hard'),
            2081 => array('error' => __('PayPal pending payments are not supported'), 'type' => 'Hard'),
            2082 => array('error' => __('PayPal Domestic Transaction Required'), 'type' => 'Hard'),
            2083 => array('error' => __('PayPal Phone Number Required'), 'type' => 'Hard'),
            2084 => array('error' => __('PayPal Tax Info Required'), 'type' => 'Hard'),
            2085 => array('error' => __('PayPal Payee Blocked Transaction'), 'type' => 'Hard'),
            2086 => array('error' => __('PayPal Transaction Limit Exceeded'), 'type' => 'Hard'),
            2087 => array('error' => __('PayPal reference transactions not enabled for your account'), 'type' => 'Hard'),
            2088 => array('error' => __('Currency not enabled for your PayPal seller account'), 'type' => 'Hard'),
            2089 => array('error' => __('PayPal payee email permission denied for this request'), 'type' => 'Hard'),
            2090 => array('error' => __('PayPal account not configured to refund more than settled amount'), 'type' => 'Hard'),
            2091 => array('error' => __('Currency of this transaction must match currency of your PayPal account'), 'type' => 'Hard'),
            2092 => array('error' => __('Processor Declined'), 'type' => 'Soft'),
            2093 => array('error' => __('Processor Declined'), 'type' => 'Soft'),
            2094 => array('error' => __('Processor Declined'), 'type' => 'Soft'),
            2095 => array('error' => __('Processor Declined'), 'type' => 'Soft'),
            2096 => array('error' => __('Processor Declined'), 'type' => 'Soft'),
            2097 => array('error' => __('Processor Declined'), 'type' => 'Soft'),
            2098 => array('error' => __('Processor Declined'), 'type' => 'Soft'),
            2099 => array('error' => __('Processor Declined'), 'type' => 'Soft'),
            3000 => array('error' => __('Processor Network Unavailable – Try Again'), 'type' => 'Soft'),
        );
    }

    /**
     * @return \Braintree\Gateway
     */
    public function getGateway(): \Braintree\Gateway
    {
        return $this->gateway;
    }

    /**
     * @return string
     */
    public function getEnvironment(): string
    {
        return $this->environment;
    }

    public function setEnvironment(): void
    {
        $this->environment = 'production';
        if (ENV !== ENV_PROD) {
            $this->environment = 'sandbox';
        }
    }

    /**
     * @param IntegrationAccount $account
     * @throws \Exception
     */
    public function startBraintree(IntegrationAccount $account): void
    {
        $config = json_decode($account->getDatas(), true);
        $merchantId = ($config['merchantId'] ?? '');
        $publicKey = ($config['publicKey'] ?? '');
        $privateKey = ($config['privateKey'] ?? '');

        if (!$merchantId or !$publicKey or !$privateKey) {
            throw new \Exception(__('Aucune clé API détectée'));
        }

        $this->setEnvironment();
        $this->setGateway($this->environment, $merchantId, $publicKey, $privateKey);
    }

    public function setGateway($environment, $merchantId, $publicKey, $privateKey)
    {
        $this->gateway = new \Braintree\Gateway([
            'environment' => $environment,
            'merchantId' => $merchantId,
            'publicKey' => $publicKey,
            'privateKey' => $privateKey,
        ]);
    }

    /**
     * @param IntegrationAccount $account
     * @return string|null
     */
    public function getClientToken(IntegrationAccount $account): ?string
    {
        try {
            $this->startBraintree($account);
            return $this->gateway->clientToken()->generate();
        } catch (\Exception $e) {
            LoggerService::logError('clientToken Exception : ' . $e->getMessage());
            return null;
        }
    }

    /**
     * @param bool $addPaypal
     */
    public function addRequiredFiles(bool $addPaypal = false)
    {
        Assets::addCss('common/payments/braintree.css');

        if ($addPaypal) {
            Assets::addJs('https://www.paypalobjects.com/api/checkout.js');
        }

        Assets::addJs('https://js.braintreegateway.com/web/3.78.1/js/client.min.js');
        Assets::addJs('https://js.braintreegateway.com/web/3.78.1/js/hosted-fields.min.js');
        Assets::addJs('https://js.braintreegateway.com/web/3.78.1/js/three-d-secure.min.js');

        if ($addPaypal) {
            Assets::addJs('https://js.braintreegateway.com/web/3.78.1/js/paypal-checkout.min.js');
            Assets::addJs('https://js.braintreegateway.com/web/3.78.1/js/data-collector.min.js');
        }

        Assets::addJs('common/payments/braintree.js');
        Assets::addJs('common/payments/braintree-handler.js');
    }

    /**
     * @param $result
     * @return array
     */
    public function handleError($result): array
    {
        $message = '';
        $code = '';
        $type = 'Soft';
        foreach ($result->errors->deepAll() as $error) {
            if (isset($this->errorsCode[$error->code])) {
                $code = $error->code;
                $message .= $this->errorsCode[$error->code]['error'];
                $type = $this->errorsCode[$error->code]['type'];
            }
        }
        if (!$message and isset($result->message) and $result->message) {
            $message = $result->message;
        }

        if (!$message) {
            $message = __('Une erreur est survenue, merci de réessayer.');
        }

        return ['valid' => false, 'message' => $message, 'code' => $code, 'type' => $type];
    }
}
