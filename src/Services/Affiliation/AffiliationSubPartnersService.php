<?php
namespace MatGyver\Services\Affiliation;

use Doctrine\ORM\EntityManager;
use MatGyver\Entity\Affiliation\SubPartner\AffiliationSubPartner;
use MatGyver\Repository\Affiliation\SubPartner\AffiliationSubPartnerRepository;
use MatGyver\Services\BaseEntityService;
use MatGyver\Services\Clients\ClientsService;
use MatGyver\Services\DI\ContainerBuilderService;

/**
 * Class AffiliationSubPartnersService
 * @package MatGyver\Services\Affiliation
 * @property AffiliationSubPartnerRepository $repository
 * @method AffiliationSubPartnerRepository getRepository()
 */
class AffiliationSubPartnersService extends BaseEntityService
{
    /**
     * @var \DI\Container
     */
    private $container;

    /**
     * AffiliationSubPartnersService constructor.
     * @param EntityManager $em
     */
    public function __construct(EntityManager $em)
    {
        $this->em = $em;
        $this->repository = $em->getRepository(AffiliationSubPartner::class);
        $this->container = ContainerBuilderService::getInstance();
    }

    /**
     * @param int|null $idClientPartner
     * @return AffiliationSubPartner|null
     */
    public function getMyPartner(?int $idClientPartner = null): ?AffiliationSubPartner
    {
        if ($idClientPartner === null) {
            $idClientPartner = $_SESSION['client']['id'];
        }

        return $this->repository->findOneBy(['subPartnerClient' => $idClientPartner]);
    }

    /**
     * @param int $idSubPartner
     * @param int|null $idClient
     * @return AffiliationSubPartner|null
     */
    public function getSubPartnerById(int $idSubPartner, ?int $idClient = null): ?AffiliationSubPartner
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->repository->findOneBy(['id' => $idSubPartner, 'client' => $idClient]);
    }

    /**
     * @param int $clientPartnerId
     * @param int|null $idClient
     * @return AffiliationSubPartner|null
     */
    public function getSubPartnerByClientPartnerId(int $clientPartnerId, ?int $idClient = null): ?AffiliationSubPartner
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->repository->findOneBy(['subPartnerClient' => $clientPartnerId, 'client' => $idClient]);
    }

    /**
     * @param int|null $idClient
     * @return array|null
     */
    public function getSubPartners(?int $idClient = null): ?array
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->repository->findBy(['client' => $idClient]);
    }

    /**
     * @param int|null $idClient
     * @return int
     */
    public function getCountSubPartners(?int $idClient = null): int
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->repository->count(['client' => $idClient]);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function insert(array $submittedData): array
    {
        $idParent = $submittedData['client_id'];
        $idSubPartner = $submittedData['subpartner_client_id'];
        if ($idParent == $idSubPartner) {
            return array('valid' => false, 'message' => __('Le parrain et le partenaire sont identiques'));
        }

        $parent = $this->container->get(ClientsService::class)->getRepository()->find($idParent);
        if (!$parent) {
            return array('valid' => false, 'message' => __('Ce parrain n\'existe pas'));
        }

        $subPartnerClient = $this->container->get(ClientsService::class)->getRepository()->find($idSubPartner);
        if (!$subPartnerClient) {
            return array('valid' => false, 'message' => __('Ce filleul n\'existe pas'));
        }

        //check if subPartner has already a partner
        $getSubPartner = $this->repository->findOneBy(['subPartnerClient' => $idSubPartner], null, false);
        if ($getSubPartner) {
            return array('valid' => false, 'message' => __('Ce filleul a déjà un parrain'));
        }

        //insertion
        $subPartner = new AffiliationSubPartner();
        $subPartner->setClient($parent);
        $subPartner->setPartner($parent->getAffiliationPartner());
        $subPartner->setSubPartnerClient($subPartnerClient);
        $subPartner->setSubPartner($subPartnerClient->getAffiliationPartner());
        $subPartner->setName($subPartnerClient->getName());
        $subPartner->setParent(0);
        $subPartner->setDate(new \DateTime());

        $mainParent = $this->repository->findOneBy(['subPartnerClient' => $idParent], null, false);
        if ($mainParent) {
            $subPartner->setParent($mainParent->getClient()->getId());
        }

        try {
            $this->persistAndFlush($subPartner);
        } catch (\Exception $e) {
            return array('valid' => false, 'message' => __("Erreur lors de l'enregistrement du filleul."));
        }

        $idSubPartner = $subPartner->getId();

        if ($parent->getAffiliationPartner() and $subPartnerClient->getAffiliationPartner()) {
            $partner = $subPartnerClient->getAffiliationPartner();
            if ($partner and !$partner->getParent()) {
                $partner->setParent($parent->getAffiliationPartner()->getId());
                try {
                    $this->persistAndFlush($partner);
                } catch (\Exception $e) {
                    return array('valid' => false, 'message' => __("Erreur lors de l'enregistrement du filleul."));
                }
            }
        }

        return array('valid' => true, 'id_subpartner' => $idSubPartner);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function update(array $submittedData): array
    {
        $idSubPartner = filter_var($submittedData['id'], FILTER_VALIDATE_INT);
        $idClient = filter_var($submittedData['client_id'], FILTER_VALIDATE_INT);
        $newClientId = filter_var($submittedData['new_client_id'], FILTER_VALIDATE_INT);
        $name = filter_var($submittedData['name'], FILTER_UNSAFE_RAW);

        $subPartner = $this->getSubPartnerById($idSubPartner, $idClient);
        if (!$subPartner) {
            return array('valid' => false, 'message' => __('Ce filleul n\'existe pas'));
        }
        $oldPartner = $subPartner->getPartner();

        $newClient = $this->container->get(ClientsService::class)->getRepository()->find($newClientId);
        if (!$newClient) {
            return array('valid' => false, 'message' => __('Ce filleul n\'existe pas'));
        }

        $subPartner->setClient($newClient);
        $subPartner->setPartner($newClient->getAffiliationPartner());
        $subPartner->setName($name);
        try {
            $this->persistAndFlush($subPartner);
        } catch (\Exception $e) {
            return array('valid' => false, 'message' => __("Erreur lors de l'enregistrement du filleul."));
        }

        $container = ContainerBuilderService::getInstance();
        $container->get(AffiliationTransactionsService::class)->getRepository()->changePartner($oldPartner, $newClient->getAffiliationPartner());

        return array('valid' => true);
    }
}
