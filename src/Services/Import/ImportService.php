<?php

namespace MatGyver\Services\Import;

use Doctrine\ORM\EntityManager;
use MatGyver\Components\Mailer\MailSender;
use Mat<PERSON>yver\Entity\Import\Import;
use MatGyver\Enums\UserEmailNotificationTypeEnum;
use MatGyver\Helpers\Tools;
use MatGyver\Repository\Import\ImportRepository;
use MatGyver\Services\BaseEntityService;
use MatGyver\Services\Logger\LoggerService;

/**
 * Class ImportService
 * @package MatGyver\Services
 * @property ImportRepository $repository
 * @method ImportRepository getRepository()
 */
class ImportService extends BaseEntityService
{
    private MailSender $mailSender;

    /**
     * ImportService constructor.
     * @param EntityManager $entityManager
     * @param MailSender $mailSender
     */
    public function __construct(
        EntityManager $entityManager,
        MailSender $mailSender
    ) {
        $this->em = $entityManager;
        $this->repository = $this->em->getRepository(Import::class);
        $this->mailSender = $mailSender;
    }

    /**
     * @param Import $import
     * @param string $status
     * @param string $log
     * @return void
     */
    public function complete(Import $import, string $status = Import::STATUS_PROCESSED, string $log = ''): void
    {
        $import->setStatus($status);
        $import->setDateProcessed(new \DateTime());
        try {
            $this->persistAndFlush($import);
        } catch (\Exception $e) {
            LoggerService::logError('Error when updating import : ' . $e->getMessage());
            return;
        }

        //send mail
        $vars = [
            'APP_ADDRESSES_LINK' => Tools::makeLink('app', 'addresses'),
            'LOG' => $log,
        ];
        $mainAdmin = $import->getClient()->getMainAdmin();
        $recipient = [['user_id' => $mainAdmin->getId()]];
        $sendMail = $this->mailSender->sendTemplateToClient('import_processed', $vars, UserEmailNotificationTypeEnum::IMPORTANT, $recipient, $import->getClient()->getId());
        if (!$sendMail['valid']) {
            LoggerService::logError('error when sending email import_processed : ' . $sendMail['message']);
        }
    }
}
