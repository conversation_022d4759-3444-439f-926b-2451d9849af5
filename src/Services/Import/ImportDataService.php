<?php

namespace MatGyver\Services\Import;

use Doctrine\ORM\EntityManager;
use Mat<PERSON>yver\Entity\Import\Import;
use MatGyver\Entity\Import\ImportData;
use MatGyver\Forms\Address\AddressForm;
use MatGyver\Repository\Import\ImportDataRepository;
use MatGyver\Services\BaseEntityService;

/**
 * Class ImportDataService
 * @package MatGyver\Services
 * @property ImportDataRepository $repository
 * @method ImportDataRepository getRepository()
 */
class ImportDataService extends BaseEntityService
{
    private AddressForm $addressForm;

    /**
     * ImportDataService constructor.
     * @param EntityManager $entityManager
     * @param AddressForm $addressForm
     */
    public function __construct(
        EntityManager $entityManager,
        AddressForm $addressForm
    ) {
        $this->em = $entityManager;
        $this->repository = $this->em->getRepository(ImportData::class);
        $this->addressForm = $addressForm;
    }

    /**
     * @param ImportData $item
     * @return array
     */
    public function process(ImportData $item): array
    {
        if ($item->getStatus() != ImportData::STATUS_PENDING) {
            return ['valid' => false, 'message' => __('Cet objet est déjà en cours de traitement.')];
        }

        $item->setStatus(ImportData::STATUS_PROCESSING);
        try {
            $this->persistAndFlush($item);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Une erreur est survenue lors de la mise à jour du statut de l\'importation.')];
        }

        $type = $item->getImport()->getType();
        if ($type != Import::TYPE_ADDRESS) {
            return $this->setInError($item, __('Type d\'importation non reconnu.'));
        }

        $submittedData = $item->getData();
        if (!isset($submittedData['company'])) {
            $submittedData['company'] = '';
        }

        $insert = $this->addressForm->insert($submittedData);
        if (!$insert['valid']) {
            return $this->setInError($item, $insert['message']);
        }

        return $this->complete($item);
    }

    /**
     * @param ImportData $item
     * @return array
     */
    public function complete(ImportData $item): array
    {
        $item->setStatus(ImportData::STATUS_PROCESSED);
        try {
            $this->persistAndFlush($item);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Une erreur est survenue lors de la mise à jour du statut de l\'importation.')];
        }
        return ['valid' => true];
    }

    /**
     * @param ImportData $item
     * @param string $message
     * @return array
     */
    public function setInError(ImportData $item, string $message = ''): array
    {
        $item->setStatus(ImportData::STATUS_ERROR);
        $item->setResult($message);
        try {
            $this->persistAndFlush($item);
        } catch (\Exception $e) {}
        return ['valid' => false, 'message' => $message];
    }
}
