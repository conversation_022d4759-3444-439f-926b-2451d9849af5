<?php

namespace MatGyver\Services\Activity;

use Doctrine\ORM\EntityManager;
use MatGyver\Entity\Activity\Activity;
use MatGyver\Repository\Activity\ActivityRepository;
use MatGyver\Services\BaseEntityService;

/**
 * Class ActivityService
 * @package MatGyver\Services
 * @property ActivityRepository $repository
 * @method ActivityRepository getRepository()
 */
class ActivityService extends BaseEntityService
{
    /**
     * ActivityService constructor.
     * @param EntityManager $entityManager
     */
    public function __construct(EntityManager $entityManager)
    {
        $this->em = $entityManager;
        $this->repository = $this->em->getRepository(Activity::class);
    }
}
