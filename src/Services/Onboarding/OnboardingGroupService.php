<?php

namespace MatGyver\Services\Onboarding;

use Doctrine\ORM\EntityManager;
use Mat<PERSON>yver\Entity\Onboarding\OnboardingGroup;
use MatGyver\Factories\Onboarding\OnboardingFactory;
use MatGyver\Helpers\Assets;
use MatGyver\Repository\Onboarding\OnboardingGroupRepository;
use MatGyver\Services\BaseEntityService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\TwigService;
use MatGyver\Services\Views\ViewAsideTutoriel;

/**
 * Class OnboardingGroupService
 * @package MatGyver\Services\Onboarding
 * @property OnboardingGroupRepository $repository
 * @method OnboardingGroupRepository getRepository()
 */
class OnboardingGroupService extends BaseEntityService
{
    /**
     * @var OnboardingTaskService
     */
    private $onboardingTaskService;

    /**
     * @var OnboardingTaskClientService
     */
    private $onboardingTaskClientService;

    /**
     * @var OnboardingFactory
     */
    private $onboardingFactory;

    /**
     * OnboardingGroupService constructor.
     * @param EntityManager $entityManager
     * @param OnboardingTaskService $onboardingTaskService
     * @param OnboardingTaskClientService $onboardingTaskClientService
     * @param OnboardingFactory $onboardingFactory
     */
    public function __construct(
        EntityManager $entityManager,
        OnboardingTaskService $onboardingTaskService,
        OnboardingTaskClientService $onboardingTaskClientService,
        OnboardingFactory $onboardingFactory
    ) {
        $this->em = $entityManager;
        $this->repository = $this->em->getRepository(OnboardingGroup::class);
        $this->onboardingTaskService = $onboardingTaskService;
        $this->onboardingTaskClientService = $onboardingTaskClientService;
        $this->onboardingFactory = $onboardingFactory;
    }

    /**
     * @param OnboardingGroup $group
     * @return int
     */
    public function getProgression(OnboardingGroup $group): int
    {
        $nbTasks = count($group->getTasks());
        $nbClientTasks = $this->onboardingTaskClientService->getRepository()->count(['group' => $group]);

        return $nbTasks ? round($nbClientTasks * 100 / $nbTasks) : 0;
    }

    /**
     * @return int|null
     */
    public function getOverallProgression(): ?int
    {
        $groups = $this->repository->findBy(['controller' => $_SESSION['controller'], 'onLogin' => false]);
        if (!$groups) {
            return null;
        }

        $totalProgression = 0;
        foreach ($groups as $group) {
            $totalProgression += $this->getProgression($group);
        }

        return round($totalProgression / count($groups));
    }

    /**
     * @param int|null $idGroup
     * @return string|null
     */
    public function renderLayout(?int $idGroup): ?string
    {
        $groups = $this->repository->findBy(['controller' => $_SESSION['controller'], 'onLogin' => false]);
        if (!$groups) {
            return null;
        }

        $leftColumn = '';
        $activeGroup = null;
        foreach ($groups as $id => $group) {
            $active = false;
            if ($idGroup and $idGroup == $group->getId()) {
                $activeGroup = $group;
                $active = true;
            }
            if (!$idGroup and !$id) {
                $activeGroup = $group;
                $active = true;
            }
            $leftColumn .= $this->renderGroup($group, $active);
        }

        $mainContent = '';
        if ($activeGroup) {
            $mainContent = $this->onboardingTaskService->renderTasks($activeGroup);
        }

        Assets::addCss('pages/wizard-template5.css');
        Assets::addCss('app/onboarding.css');
        Assets::addJs('app/onboarding.js');

        $overallCompletion = $this->getOverallProgression();
        if ($overallCompletion === null) {
            $overallCompletion = 0;
        }
        return TwigService::getInstance()->set('leftColumn', $leftColumn)
            ->set('mainContent', $mainContent)
            ->set('overallCompletion', $overallCompletion)
            ->render('app/onboarding/layout.php');
    }

    /**
     * @param OnboardingGroup $group
     * @param bool $active
     * @return string|null
     */
    public function renderGroup(OnboardingGroup $group, bool $active = false): ?string
    {
        $groupService = $this->onboardingFactory->getGroupService($group);
        if ($groupService === null) {
            return null;
        }

        return TwigService::getInstance()->set('group', $group)
            ->set('title', $groupService->getTitle())
            ->set('description', $groupService->getDescription())
            ->set('icon', $groupService->getIcon())
            ->set('progression', $this->getProgression($group))
            ->set('active', $active)
            ->render('app/onboarding/group.php');
    }

    /**
     * @param OnboardingGroup $onBoardingGroup
     * @return string|null
     */
    public function renderWelcome(OnboardingGroup $onBoardingGroup): ?string
    {
        list($tasksHeader, $tasksContent) = $this->onboardingTaskService->renderWelcomeTasks($onBoardingGroup);

        $container = ContainerBuilderService::getInstance();
        $view = $container->get(ViewAsideTutoriel::class);

        Assets::addCss('pages/wizard-aside.css');
        Assets::addCss('app/onboarding-welcome.css');

        $ajaxAction = '<script>var ajaxAction = \'onboarding/welcome/\';</script>';
        Assets::addInlineJs($ajaxAction);
        Assets::addJs('app/onboarding-welcome.js');
        Assets::addJs('common/tutorial.js');

        $view->setLeftContent($tasksHeader);
        $view->setRightContent($tasksContent);

        $view->render();
        exit();
    }

    /**
     * @param OnboardingGroup $onBoardingGroup
     * @return array
     */
    public function postProcess(OnboardingGroup $onBoardingGroup): array
    {
        $groupService = $this->onboardingFactory->getGroupService($onBoardingGroup);
        if ($groupService === null) {
            return ['valid' => false, 'message' => __('Ce groupe n\'existe pas.')];
        }

        if (!method_exists($groupService, 'postProcess')) {
            return ['valid' => true];
        }

        return $groupService->{'postProcess'}();
    }
}
