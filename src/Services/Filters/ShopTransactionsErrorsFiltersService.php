<?php

namespace MatGyver\Services\Filters;

/**
 * Class ShopTransactionsErrorsFiltersService
 * @package MatGyver\Services\Filters
 */
class ShopTransactionsErrorsFiltersService extends AbstractFiltersService
{
    /**
     * ShopTransactionsErrorsFiltersService constructor.
     */
    public function __construct()
    {
        $this->type = 'transactions_errors';
        $this->filters = [
            'date_start' => '',
            'date_end' => '',
            'status' => '',
            'search' => '',
        ];
    }

    /**
     *
     */
    public function setOptions() :void
    {
        $this->getFilters();

        $selectedDate = [];
        if (isset($this->filters['date_start']) and $this->filters['date_start']) {
            $selectedDate['date_start'] = $this->filters['date_start'];
        }
        if (isset($this->filters['date_end']) and $this->filters['date_end']) {
            $selectedDate['date_end'] = $this->filters['date_end'];
        }

        $search = ($this->filters['search'] ?? '');

        $this->options = [
            [
                'title' => __('Date'),
                'name' => 'date',
                'type' => 'date',
                'placeholder' => __('Séléctionner les dates limites'),
                'selected' => $selectedDate
            ],
            [
                'title' => __('Etat'),
                'name' => 'status',
                'type' => 'select',
                'placeholder' => __('Tous'),
                'options' => [
                    'open' => __('Ouvert'),
                    'to_call' => __('A rappeler'),
                    'waiting_client' => __('En attente de réponse'),
                    'closed' => __('Fermé'),
                    'paid' => __('Payé'),
                    'payment_in_progress' => __('En cours de paiement'),
                    'needs_delay' => __('Demande de délai de paiement')
                ],
                'selected' => ($this->filters['status'] ?? null)
            ],
            [
                'title' => __('Recherche'),
                'name' => 'search',
                'type' => 'search',
                'placeholder' => __('Rechercher un impayé'),
                'value' => $search
            ],
        ];
    }
}
