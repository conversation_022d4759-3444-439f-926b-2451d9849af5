<?php

namespace MatGyver\Services\Filters;

use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Shop\Product\ShopProductsService;

/**
 * Class ShopCustomersFiltersService
 * @package MatGyver\Services\Filters
 */
class ShopCustomersFiltersService extends AbstractFiltersService
{
    /**
     * ShopCustomersFiltersService constructor.
     */
    public function __construct()
    {
        $this->container = ContainerBuilderService::getInstance();
        $this->type = 'customers';
        $this->filters = [
            'date_start' => '',
            'date_end' => '',
            'id_product' => 0,
            'search' => '',
        ];
    }

    /**
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function setOptions() :void
    {
        $this->getFilters();

        $selectProducts = $this->container->get(ShopProductsService::class)->generateSelectProducts(isset($this->filters['id_product']) ? [$this->filters['id_product']] : []);

        $selectedDate = [];
        if (isset($this->filters['date_start']) and $this->filters['date_start']) {
            $selectedDate['date_start'] = $this->filters['date_start'];
        }
        if (isset($this->filters['date_end']) and $this->filters['date_end']) {
            $selectedDate['date_end'] = $this->filters['date_end'];
        }

        $selectedProduct = ($this->filters['id_product'] ?? 0);
        $search = ($this->filters['search'] ?? '');

        $this->options = [
            [
                'title' => __('Date'),
                'name' => 'date',
                'type' => 'date',
                'placeholder' => __('Sélectionnez les dates limites'),
                'selected' => $selectedDate
            ],
            [
                'title' => __('Produit'),
                'name' => 'id_product',
                'type' => 'select',
                'placeholder' => __('Tous'),
                'options' => $selectProducts,
                'selected' => $selectedProduct
            ],
            [
                'title' => __('Recherche'),
                'name' => 'search',
                'type' => 'search',
                'placeholder' => __('Rechercher un client'),
                'value' => $search
            ]
        ];
    }
}
