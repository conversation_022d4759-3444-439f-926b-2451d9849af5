<?php

namespace MatGyver\Services\Dossier\Word;

use MatGyver\Components\Word\ClaimWordComponent;
use MatGyver\Entity\Dossier\DossierDocument;
use MatGyver\Services\Logger\LoggerService;

/**
 * Class DossierWordClaimService
 * @package MatGyver\Services\Dossier\Word
 */
class DossierWordClaimService extends AbstractDossierWordService
{
    public function __construct()
    {
        $this->type = DossierDocument::TYPE_CLAIM;
    }

    /**
     * @param DossierDocument $dossierDocument
     * @return void
     * @throws \Exception
     */
    public function generate(DossierDocument $dossierDocument): void
    {
        $wordComponent = new ClaimWordComponent();
        $wordComponent->setDossier($dossierDocument->getDossier());
        $wordComponent->setDocument($dossierDocument);
        $wordComponent->setExpertise($dossierDocument->getExpertise());
        try {
            $phpWord = $wordComponent->make();
            $phpWord->getCompatibility()->setOoxmlVersion(15);

            $file = VAR_PATH . '/cache/upload/Reclamation ' . $dossierDocument->getDossier()->getVehicle()->getRegistration() . ' ' . $dossierDocument->getDate()->format('d-m-Y') . '.docx';

            $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');
            $objWriter->save($file);
            header('Content-Description: File Transfer');
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="'.basename($file).'"');
            header('Expires: 0');
            header('Cache-Control: must-revalidate');
            header('Pragma: public');
            header('Content-Length: ' . filesize($file));
            readfile($file);
            @unlink($file);
            exit();
        } catch (\Exception $e) {
            LoggerService::logError('Unable to make word : ' . $e->getMessage());
            throw new \Exception($e->getMessage());
        }
    }
}
