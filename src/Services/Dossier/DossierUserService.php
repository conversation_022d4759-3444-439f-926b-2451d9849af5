<?php

namespace MatGyver\Services\Dossier;

use Doctrine\ORM\EntityManager;
use MatGyver\Entity\Dossier\DossierUser;
use MatGyver\Repository\Dossier\DossierUserRepository;
use MatGyver\Services\BaseEntityService;

/**
 * Class DossierUserService
 * @package MatGyver\Services\Dossier
 * @property DossierUserRepository $repository
 * @method DossierUserRepository getRepository()
 */
class DossierUserService extends BaseEntityService
{
    /**
     * DossierUserService constructor.
     * @param EntityManager $em
     */
    public function __construct(EntityManager $em)
    {
        $this->em = $em;
        $this->repository = $em->getRepository(DossierUser::class);
    }
}