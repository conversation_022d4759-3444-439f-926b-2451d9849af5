<?php

namespace MatGyver\Services\Dossier;

use Doctrine\ORM\EntityManager;
use MatGyver\Entity\Dossier\DossierMandate;
use MatGyver\Repository\Dossier\DossierMandateRepository;
use MatGyver\Services\BaseEntityService;

/**
 * Class DossierMandateService
 * @package MatGyver\Services\Dossier
 * @property DossierMandateRepository $repository
 * @method DossierMandateRepository getRepository()
 */
class DossierMandateService extends BaseEntityService
{
    /**
     * DossierMandateService constructor.
     * @param EntityManager $em
     */
    public function __construct(EntityManager $em)
    {
        $this->em = $em;
        $this->repository = $em->getRepository(DossierMandate::class);
    }

    /**
     * @return array
     */
    public static function getTypes(): array
    {
        return [
            DossierMandate::TYPE_BUYER => __('Propriétaire'),
            /*DossierMandate::TYPE_COURT => __('Tribunal'),*/
            DossierMandate::TYPE_SELLER => __('Vendeur'),
            DossierMandate::TYPE_REPAIRER => __('Réparateur'),
            DossierMandate::TYPE_INSURER => __('Assureur'),
            DossierMandate::TYPE_APPRAISAL_FIRM => __('Cabinet d\'expertise'),
            DossierMandate::TYPE_SERVICE_COMPANY => __('Société de service'),
            DossierMandate::TYPE_OTHER => __('Autre'),
        ];
    }

    /**
     * @param string $type
     * @return string|null
     */
    public static function getType(string $type): ?string
    {
        $types = self::getTypes();
        return $types[$type] ?? null;
    }
}
