<?php

namespace MatGyver\Services\Dossier;

use Doctrine\ORM\EntityManager;
use MatGyver\Entity\Dossier\DossierSurcharge;
use MatGyver\Repository\Dossier\DossierSurchargeRepository;
use MatGyver\Services\BaseEntityService;

/**
 * Class DossierSurchargeService
 * @package MatGyver\Services\Dossier
 * @property DossierSurchargeRepository $repository
 * @method DossierSurchargeRepository getRepository()
 */
class DossierSurchargeService extends BaseEntityService
{
    /**
     * DossierSurchargeService constructor.
     * @param EntityManager $em
     */
    public function __construct(EntityManager $em)
    {
        $this->em = $em;
        $this->repository = $em->getRepository(DossierSurcharge::class);
    }
}