<?php

namespace MatGyver\Services\Dossier\Expertise;

use Doctrine\ORM\EntityManager;
use MatG<PERSON>ver\Entity\Dossier\DossierDocument;
use MatGyver\Entity\Dossier\DossierFact;
use MatGyver\Entity\Dossier\Expertise\DossierExpertise;
use MatGyver\Entity\Dossier\Expertise\DossierExpertiseObservations;
use MatGyver\Entity\Dossier\Expertise\DossierExpertiseReport;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\ImagickHelper;
use MatGyver\Repository\Dossier\Expertise\DossierExpertiseReportRepository;
use MatGyver\Services\BaseEntityService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Dossier\DossierFactService;

/**
 * Class DossierExpertiseReportService
 * @package MatGyver\Services\Dossier\Expertise
 * @property DossierExpertiseReportRepository $repository
 * @method DossierExpertiseReportRepository getRepository()
 */
class DossierExpertiseReportService extends BaseEntityService
{
    /**
     * DossierExpertiseReportService constructor.
     * @param EntityManager $em
     */
    public function __construct(EntityManager $em)
    {
        $this->em = $em;
        $this->repository = $em->getRepository(DossierExpertiseReport::class);
    }

    /**
     * @param DossierExpertise $expertise
     * @return DossierExpertiseReport
     * @throws \Exception
     */
    public function createFromExpertise(DossierExpertise $expertise): DossierExpertiseReport
    {
        $report = new DossierExpertiseReport();
        $report->setClient($expertise->getClient());
        $report->setExpertise($expertise);
        try {
            $this->persistAndFlush($report);
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }

        return $report;
    }

    /**
     * @param DossierExpertiseReport $report
     * @param bool $modified
     * @return void
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function setModified(DossierExpertiseReport $report, bool $modified = true)
    {
        $report->setModified($modified);
        $this->persistAndFlush($report);
    }

    /**
     * @param DossierExpertiseReport $report
     * @return void
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function clearModifications(DossierExpertiseReport $report)
    {
        $this->setModified($report, false);
    }

    /**
     * @return array
     */
    public static function getDocumentsToRemove(): array
    {
        return [
            DossierDocument::TYPE_CONVOCATION,
            DossierDocument::TYPE_SUMMARY,
            DossierDocument::TYPE_EXPERTISE,
            DossierDocument::TYPE_CONTRADICTORY,
            DossierDocument::TYPE_CONTRADICTORY_WO_DOCUMENTS,
            DossierDocument::TYPE_CONTRADICTORY_WO_PICTURES,
            DossierDocument::TYPE_POSTAL_MAIL_DEPOSIT,
            DossierDocument::TYPE_POSTAL_MAIL_RECEIPT,
            DossierDocument::TYPE_POSTAL_MAIL_NON_RECEIPT,
            DossierDocument::TYPE_APPRAISED_VALUE
        ];
    }

    /**
     * @param DossierExpertise $expertise
     * @param string $type
     * @param string $prefix
     * @return string
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public static function displayFactsAndObservations(DossierExpertise $expertise, string $type, string $prefix = '- ', bool $displayDocument = false): string
    {
        $content = '<div class="facts">';
        $container = ContainerBuilderService::getInstance();
        $facts = $container->get(DossierFactService::class)->getRepository()->findBy(['dossier' => $expertise->getDossier(), $type => true], ['position' => 'ASC']);
        if ($facts) {
            $content .= '<p><strong>' . __('Il est rappelé que :') . '</strong></p>';
            $factsByDate = DossierFactService::sortFacts($facts);
            foreach ($factsByDate as $date => $facts) {
                array_walk($facts, function (DossierFact $fact) use (&$content, $prefix, $displayDocument) {
                    $content .= $fact->getInlineComment($prefix);
                    if ($displayDocument) {
                        $content .= self::displayFactDocument($fact);
                    }
                });
            }
        }
        $content .= '</div>';

        $content .= '<div class="observations">';
        $observations = $container->get(DossierExpertiseObservationsService::class)->getRepository()->findBy(['expertise' => $expertise, $type => true]);
        if ($observations) {
            $observationsByDate = DossierExpertiseObservationsService::sortObservations($observations);
            foreach ($observationsByDate as $date => $observations) {
                $date = new \DateTime($date);
                $content .= '<p>&nbsp;</p><p><strong>Lors des constatations du ' . $date->format('d/m/Y') . ', nous avons relevé que :</strong></p>';
                array_walk($observations, function (DossierExpertiseObservations $observation) use (&$content, $prefix, $displayDocument) {
                    $content .= $observation->getInlineComment($prefix);
                    if ($displayDocument) {
                        $content .= self::displayObservationDocuments($observation);
                    }
                });
            }
        }
        $content .= '</div>';

        return $content;
    }

    public static function displayFactDocument(DossierFact $fact): string
    {
        if ($fact->getDocument() and $fact->getDocument()->getConfidential()) {
            return '';
        }

        $content = '';
        $file = $fact->getFile();
        if (!$file and $fact->getDocument()) {
            $file = $fact->getDocument()->getFile();
        }
        if ($fact->getDocument() and in_array(getExtension($file), ['jpg', 'jpeg', 'png', 'gif'])) {
            $content .= '<br><br><img class="img-fluid" style="max-height: 800px" src="' . $fact->getDocument()->getUrl() . '">';
        } elseif (in_array(getExtension($file), ['jpg', 'jpeg', 'png'])) {
            $content .= '<br><br><img class="img-fluid" style="max-height: 800px" src="' . Assets::getMediaUrl($fact->getDossier()->getFolder() . $file) . '">';
        } elseif (getExtension($file) == 'pdf') {
            $fullPath = WEB_PATH . '/medias/' . $fact->getDossier()->getFolder();
            $pathInfo = pathinfo($file);
            $fileName = $pathInfo['filename'] . '-thumbnail';

            $images = ImagickHelper::convertPdf($fullPath . $file, true);
            $content .= '<br><br><img class="img-fluid" style="max-height: 800px" src="' . Assets::getMediaUrl($images[0]) . '">';
        }

        return $content;
    }

    public static function displayObservationDocuments(DossierExpertiseObservations $observation): string
    {
        $content = '';
        if ($observation->getFile()) {
            $content .= '<div class="mt-0 mb-12 text-center">';
            $content .= '<img class="img-fluid" src="' . Assets::getMediaUrl($observation->getExpertise()->getDossier()->getFolder() . $observation->getFile()) . '" style="max-height: 800px">';
            if ($observation->getLegend()) {
                $content .= '<p class="text-center mt-6"><em>' . $observation->getLegend() . '</em></p>';
            }
            $content .= '</div>';
        }
        if ($observation->getFile2()) {
            $content .= '<div class="mt-0 mb-12 text-center">';
            $content .= '<img class="img-fluid" src="' . Assets::getMediaUrl($observation->getExpertise()->getDossier()->getFolder() . $observation->getFile2()) . '" style="max-height: 800px">';
            if ($observation->getLegend2()) {
                $content .= '<p class="text-center mt-6"><em>' . $observation->getLegend2() . '</em></p>';
            }
            $content .= '</div>';
        }

        return $content;
    }
}
