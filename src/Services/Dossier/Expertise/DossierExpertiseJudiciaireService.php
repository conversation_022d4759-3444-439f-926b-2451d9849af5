<?php

namespace MatGyver\Services\Dossier\Expertise;

use Doctrine\ORM\EntityManager;
use MatG<PERSON>ver\Entity\Dossier\DossierDocument;
use MatGyver\Entity\Dossier\Expertise\DossierExpertise;
use MatGyver\Entity\Dossier\Expertise\DossierExpertiseJudiciaire;
use MatGyver\Helpers\Assets;
use MatGyver\Repository\Dossier\Expertise\DossierExpertiseJudiciaireRepository;
use MatGyver\Services\BaseEntityService;
use MatGyver\Services\TwigService;

/**
 * Class DossierExpertiseJudiciaireService
 * @package MatGyver\Services\Dossier\Expertise
 * @property DossierExpertiseJudiciaireRepository $repository
 * @method DossierExpertiseJudiciaireRepository getRepository()
 */
class DossierExpertiseJudiciaireService extends BaseEntityService
{
    /**
     * DossierExpertiseJudiciaireService constructor.
     * @param EntityManager $em
     */
    public function __construct(EntityManager $em)
    {
        $this->em = $em;
        $this->repository = $em->getRepository(DossierExpertiseJudiciaire::class);
    }

    public static function getOrderTypes(): array
    {
        return [
            DossierExpertiseJudiciaire::ACKNOWLEDGEMENT_OF_RECEIPT_OF_REQUEST => __('Accusé de réception de la requête'),
            DossierExpertiseJudiciaire::COURT_OF_APPEAL_RULING => __('Arrêt de la Cour d\'Appel'),
            DossierExpertiseJudiciaire::JUDGEMENT => __('Jugement'),
            DossierExpertiseJudiciaire::JUDGEMENT_CONTRADICTORY => __('Jugement contradictoire'),
            DossierExpertiseJudiciaire::PRELIMINARY_RULING => __('Jugement avant dire droit'),
            DossierExpertiseJudiciaire::ORDER => __('Ordonnance'),
            DossierExpertiseJudiciaire::ORDER_TO_EXTEND_TIME_LIMIT => __('Ordonnance de propagation de délai'),
            DossierExpertiseJudiciaire::INTERIM_ORDER => __('Ordonnance de référé'),
            DossierExpertiseJudiciaire::INTERIM_CONSTRUCTION_ORDER => __('Ordonnance de référé construction'),
            DossierExpertiseJudiciaire::ORDER_FOR_ADDITIONAL_ADVANCE_PAYMENT => __('Ordonnance fixant un complément de provision'),
        ];
    }

    public static function getOrderType(string $orderType): ?string
    {
        return self::getOrderTypes()[$orderType] ?? null;
    }

    public static function getDocumentsTypes(): array
    {
        return [
            DossierDocument::TYPE_ACCEPTATION_MISSION => __('Acceptation de mission'),
            DossierDocument::TYPE_ASK_LAWYER_DOCUMENT => __('Demande de documents aux avocats'),
            DossierDocument::TYPE_CONVENANCE_DATE_EXPERTISE_GARAGE => __('Informations convenance de dates d\'expertise au garage'),
            DossierDocument::TYPE_CONVENANCE_DATE_EXPERTISE => __('Convenance de dates d\'expertise'),
            DossierDocument::TYPE_INFORMATION_DATE_LIEU_EXPERTISE => __('Information date et lieu d\'expertise'),
            DossierDocument::TYPE_INFORMATION_DATE_LIEU_EXPERTISE_LAWYER => __('Information à avocat (date et lieu d\'expertise)'),
            DossierDocument::TYPE_ACCEPTATION_DEMATERIALISATION => __('Acceptation de la dématérialisation'),
            DossierDocument::TYPE_DOCUMENTS_REQUIRED_FROM_PARTIES_REMINDER => __('Relance pour l\'envoi des pièces réclamées aux parties'),
            DossierDocument::TYPE_DEMANDE_CONSIGNATION_COMPLEMENTAIRE => __('Demande de consignation complémentaire'),
            DossierDocument::TYPE_PROROGATION_DELAI_IMPARTI => __('Prorogation du délai imparti'),
            DossierDocument::TYPE_DEMANDE_DELAI => __('Demande de délai complémentaire'),
            DossierDocument::TYPE_DEPOT_RAPPORT_ET_REMUNERATION => __('Dépôt de rapport et demande de rémunération'),
            DossierDocument::TYPE_TAX_LETTER => __('Lettre de taxe'),
        ];
    }

    public static function getDocumentType(string $documentType): ?string
    {
        return self::getDocumentsTypes()[$documentType] ?? null;
    }

    public static function getDocumentTypeSubject(string $documentType): string
    {
        $subjects = [
            DossierDocument::TYPE_ACCEPTATION_MISSION => __('Acceptation de la mission'),
            DossierDocument::TYPE_ASK_LAWYER_DOCUMENT => __('Demande de pièces au dossier'),
            DossierDocument::TYPE_CONVENANCE_DATE_EXPERTISE_GARAGE => __('Informations Convenance de dates d\'expertise'),
            DossierDocument::TYPE_CONVENANCE_DATE_EXPERTISE => __('Convenance de dates d\'expertise'),
            DossierDocument::TYPE_PROROGATION_DELAI_IMPARTI => __('Prorogation du délai imparti'),
            DossierDocument::TYPE_DEMANDE_DELAI => __('Demande de délai complémentaire pour dépôt du rapport'),
            DossierDocument::TYPE_DEPOT_RAPPORT_ET_REMUNERATION => __('Dépôt du rapport d\'expertise définitif et demande de rémunération'),
            DossierDocument::TYPE_INFORMATION_DATE_LIEU_EXPERTISE => __('Information date et lieu d\'expertise'),
            DossierDocument::TYPE_ACCEPTATION_DEMATERIALISATION => __('Acceptation de la dématérialisation'),
            DossierDocument::TYPE_DOCUMENTS_REQUIRED_FROM_PARTIES_REMINDER => __('Relance pour l\'envoi des pièces réclamées aux parties'),
            DossierDocument::TYPE_TAX_LETTER => __('Recouvrement des honoraires d’expert (Articles 714 alinéa 2, 715 et 724 du CPC)'),
        ];
        return $subjects[$documentType] ?? '';
    }

    public function createOne(DossierExpertise $expertise): DossierExpertiseJudiciaire
    {
        $expertiseJudiciaire = new DossierExpertiseJudiciaire();
        $expertiseJudiciaire->setClient($expertise->getClient());
        $expertiseJudiciaire->setExpertise($expertise);
        $expertiseJudiciaire->setDate(new \DateTime());
        try {
            $this->persistAndFlush($expertiseJudiciaire);
        } catch (\Exception $e) {
            throw new \Exception(__('Erreur lors de la création de l\'expertise judiciaire.'));
        }

        return $expertiseJudiciaire;
    }

    public static function addModalMissionDocument(): void
    {
        Assets::addJs('common/dropzone.js');
        $content = TwigService::getInstance()->render('app/dossier/modal_document_mission.php');
        Assets::addInlineJs($content);
    }
}
