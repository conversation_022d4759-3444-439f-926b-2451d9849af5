<?php

namespace MatGyver\Services\Dossier\Expertise;

use Doctrine\ORM\EntityManager;
use MatGyver\Entity\Dossier\Expertise\DossierExpertiseAiReport;
use MatGyver\Forms\Dossier\Expertise\DossierExpertiseJudiciaireCourtQuestionAnswerForm;
use MatGyver\Forms\Dossier\Expertise\DossierExpertiseJudiciaireDoubtForm;
use MatGyver\Forms\Dossier\Expertise\DossierExpertiseJudiciaireFactForm;
use MatGyver\Forms\Dossier\Expertise\DossierExpertiseJudiciaireOpinionForm;
use MatGyver\Repository\Dossier\Expertise\DossierExpertiseAiReportRepository;
use MatGyver\Services\BaseEntityService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Dossier\DossierExpertiseService;
use MatGyver\Services\Logger\LoggerService;

/**
 * Class DossierExpertiseAiReportService
 * @package MatGyver\Services\Dossier\Expertise
 * @property DossierExpertiseAiReportRepository $repository
 * @method DossierExpertiseAiReportRepository getRepository()
 */
class DossierExpertiseAiReportService extends BaseEntityService
{
    /**
     * DossierExpertiseAiReportService constructor.
     * @param EntityManager $em
     */
    public function __construct(EntityManager $em)
    {
        $this->em = $em;
        $this->repository = $em->getRepository(DossierExpertiseAiReport::class);
    }

    public static function getTitles(): array
    {
        return [
            'technical_analysis' => __('Analyses techniques'),
            'causes' => __('Causes'),
            'consequences' => __('Conséquences'),
            'responsibilities_text' => __('Responsabilités'),
            'imputability' => __('Imputabilités'),
            'conclusions' => __('Conclusions'),
            'facts' => __('Faits, origines et causes établies'),
            'doubts' => __('Éventuels doutes et incertitudes non levés'),
            'opinions' => __('Avis techniques développés, motivés et argumentés'),
            'answers' => __('Réponses aux questions du Tribunal'),
        ];
    }

    public static function getTitle($key): ?string
    {
        $titles = self::getTitles();
        return $titles[$key] ?? null;
    }

    public function saveResults(DossierExpertiseAiReport $report, array $results): void
    {
        $report->setResult(json_encode($results));
        try {
            $this->persistAndFlush($report);
        } catch (\Exception $e) {
            LoggerService::logError('Error saving results: ' . $e->getMessage());
            throw new \Exception('Error saving results: ' . $e->getMessage());
        }
    }

    public function applyResults(DossierExpertiseAiReport $report): void
    {
        $results = json_decode($report->getResult(), true);
        foreach ($results as $key => $value) {
            if ($key == 'imputability') {
                foreach ($value as $imputability) {
                    $this->applyResult($report, 'imputability', $imputability['content'], $imputability['person']);
                }
                continue;
            } elseif (is_array($value)) {
                foreach ($value as $param => $content) {
                    if (!$content) {
                        continue;
                    }
                    $this->applyResult($report, $key, $content, $param);
                }
                continue;
            }

            $this->applyResult($report, $key, $value);
        }
    }

    public function applyResult(DossierExpertiseAiReport $report, string $key, string $content, ?string $param = null): void
    {
        $expertiseReport = $report->getExpertise()->getExpertiseReport();
        $expertise = $report->getExpertise();
        $container = ContainerBuilderService::getInstance();

        switch ($key) {
            case 'technical_analysis':
                $expertiseReport->setTechnicalAnalyses($content);
                break;
            case 'causes':
                $expertiseReport->setCauses($content);
                break;
            case 'consequences':
                $expertiseReport->setConsequences($content);
                break;
            case 'responsibilities_text':
                $expertiseReport->setResponsibilities($content);
                break;
            case 'conclusions':
                $expertiseReport->setConclusions($content);
                break;
            case 'imputability':
                $damageAttributions = $expertise->getDamageAttribution();
                if (!$damageAttributions) {
                    $damageAttributions = [];
                }
                if ($content) {
                    $damageAttributions[$param] = 'yes';
                    $damageAttributions[$param . '_comment'] = $content;
                } else {
                    $damageAttributions[$param] = 'no';
                }
                $expertise->setDamageAttribution($damageAttributions);
                break;
            case 'answers':
                $question = $container->get(DossierExpertiseJudiciaireCourtQuestionService::class)->getRepository()->findOneBy(['expertise' => $expertise, 'id' => $param]);
                if (!$question) {
                    break;
                }
                $data = [
                    'question_id' => $param,
                    'answer' => $content,
                ];
                $insert = $container->get(DossierExpertiseJudiciaireCourtQuestionAnswerForm::class)->insert($data);
                if (!$insert['valid']) {
                    LoggerService::logError('Error saving answer: ' . $insert['message']);
                }
                break;
            case 'facts':
                $data = [
                    'expertise_id' => $expertise->getId(),
                    'content' => nl2br($content),
                ];
                $insert = $container->get(DossierExpertiseJudiciaireFactForm::class)->insert($data);
                if (!$insert['valid']) {
                    LoggerService::logError('Error saving answer: ' . $insert['message']);
                }
                break;
            case 'doubts':
                $data = [
                    'expertise_id' => $expertise->getId(),
                    'content' => nl2br($content),
                ];
                $insert = $container->get(DossierExpertiseJudiciaireDoubtForm::class)->insert($data);
                if (!$insert['valid']) {
                    LoggerService::logError('Error saving answer: ' . $insert['message']);
                }
                break;
            case 'opinions':
                $data = [
                    'expertise_id' => $expertise->getId(),
                    'content' => nl2br($content),
                ];
                $insert = $container->get(DossierExpertiseJudiciaireOpinionForm::class)->insert($data);
                if (!$insert['valid']) {
                    LoggerService::logError('Error saving answer: ' . $insert['message']);
                }
                break;
        }

        $container->get(DossierExpertiseService::class)->persistAndFlush($expertise);
        $container->get(DossierExpertiseReportService::class)->persistAndFlush($expertiseReport);
    }
}
