<?php

namespace MatGyver\Services\Dossier\Expertise;

use Doctrine\ORM\EntityManager;
use MatGyver\Entity\Dossier\Expertise\DossierExpertiseVehicleEvaluations;
use MatGyver\Repository\Dossier\Expertise\DossierExpertiseVehicleEvaluationsRepository;
use MatGyver\Services\BaseEntityService;

/**
 * Class DossierExpertiseVehicleEvaluationsService
 * @package MatGyver\Services\Dossier\Expertise
 * @property DossierExpertiseVehicleEvaluationsRepository $repository
 * @method DossierExpertiseVehicleEvaluationsRepository getRepository()
 */
class DossierExpertiseVehicleEvaluationsService extends BaseEntityService
{
    /**
     * DossierExpertiseVehicleEvaluationsService constructor.
     * @param EntityManager $em
     */
    public function __construct(EntityManager $em)
    {
        $this->em = $em;
        $this->repository = $em->getRepository(DossierExpertiseVehicleEvaluations::class);
    }

    /**
     * @return array
     */
    public static function getTypes(): array
    {
        return [
            DossierExpertiseVehicleEvaluations::TYPE_GENERAL_PRESENTATION => __('Présentation générale'),
            DossierExpertiseVehicleEvaluations::TYPE_ELECTRICAL_EQUIPMENT => __('Équipements électriques'),
            DossierExpertiseVehicleEvaluations::TYPE_UPHOLSTERY_AND_TRIMMINGS => __('Sellerie et garnitures'),
            DossierExpertiseVehicleEvaluations::TYPE_DRIVER_STATION => __('Poste de conduite'),
            DossierExpertiseVehicleEvaluations::TYPE_BODYWORK => __('Carrosserie'),
            DossierExpertiseVehicleEvaluations::TYPE_UNDERBELT => __('Soubassement'),
            DossierExpertiseVehicleEvaluations::TYPE_PAINTING => __('Peinture'),
            DossierExpertiseVehicleEvaluations::TYPE_ENGINE => __('Moteur'),
            DossierExpertiseVehicleEvaluations::TYPE_TRANSMISSION => __('Transmission'),
            DossierExpertiseVehicleEvaluations::TYPE_DYNAMIC_TEST => __('Essai dynamique'),
            DossierExpertiseVehicleEvaluations::TYPE_TECHNICAL_INSPECTION_REPORT => __('Rapport de contrôle technique'),
            DossierExpertiseVehicleEvaluations::TYPE_HISTORY => __('Historique'),
            DossierExpertiseVehicleEvaluations::TYPE_QUALITY_OF_MAINTENANCE => __('Qualité de l\'entretien'),
            DossierExpertiseVehicleEvaluations::TYPE_QUALITY_OF_RESTORATION => __('Qualité de la restauration'),
            DossierExpertiseVehicleEvaluations::TYPE_WORK_TO_BE_DONE => __('Travaux à effectuer'),
            DossierExpertiseVehicleEvaluations::TYPE_EQUIPMENTS => __('Équipements'),
        ];
    }

    /**
     * @param string $type
     * @return string|null
     */
    public static function getType(string $type): ?string
    {
        $types = self::getTypes();
        return $types[$type] ?? null;
    }

    /**
     * @return array
     */
    public static function getDescriptions(): array
    {
        return [
            DossierExpertiseVehicleEvaluations::TYPE_ELECTRICAL_EQUIPMENT => __('(signalisation, faisceaux, équipements, état, fonctionnement)'),
            DossierExpertiseVehicleEvaluations::TYPE_UPHOLSTERY_AND_TRIMMINGS => __('(matière, couleur, moquettes, état, conforme, observations)'),
            DossierExpertiseVehicleEvaluations::TYPE_DRIVER_STATION => __('(planche de bord, instrumentation, commandes, fonctionnement)'),
            DossierExpertiseVehicleEvaluations::TYPE_BODYWORK => __('(ajustage, fonctionnement, état, modifications, transformation notable)'),
            DossierExpertiseVehicleEvaluations::TYPE_UNDERBELT => __('(oxydation, corrosion, déformation, séquelles, non apparent, état)'),
            DossierExpertiseVehicleEvaluations::TYPE_PAINTING => __('(état, rayures, réfection)'),
            DossierExpertiseVehicleEvaluations::TYPE_ENGINE => __('(état, fonctionnement, fumées, bruit, étanchéité, périphériques)'),
            DossierExpertiseVehicleEvaluations::TYPE_TRANSMISSION => __('(état, fonctionnement, étanchéité, jeux)'),
            DossierExpertiseVehicleEvaluations::TYPE_DYNAMIC_TEST => __('(tenue de cap, freinage, jeux, bruits, accélération, puissance, qualité)'),
            DossierExpertiseVehicleEvaluations::TYPE_TECHNICAL_INSPECTION_REPORT => __('(historique, validité)'),
            DossierExpertiseVehicleEvaluations::TYPE_HISTORY => __('(propriétaires, factures, kilométrage réel)'),
            DossierExpertiseVehicleEvaluations::TYPE_QUALITY_OF_MAINTENANCE => __('(traçabilité, révisions, intervalles)'),
            DossierExpertiseVehicleEvaluations::TYPE_QUALITY_OF_RESTORATION => __('(conforme à l\'origine, règles de l\'art, envergure, auteur)'),
            DossierExpertiseVehicleEvaluations::TYPE_WORK_TO_BE_DONE => __('(immédiatement, à prévoir)'),
        ];
    }

    /**
     * @param string $type
     * @return string|null
     */
    public static function getDescription(string $type): ?string
    {
        $descriptions = self::getDescriptions();
        return $descriptions[$type] ?? null;
    }

    /**
     * @return array
     */
    public static function getStatuses(): array
    {
        return [
            DossierExpertiseVehicleEvaluations::STATUS_LOWER => __('Inférieur'),
            DossierExpertiseVehicleEvaluations::STATUS_STANDARD => __('Standard'),
            DossierExpertiseVehicleEvaluations::STATUS_UPPER => __('Supérieur'),
        ];
    }

    /**
     * @param string $status
     * @return string|null
     */
    public static function getStatus(string $status): ?string
    {
        $statuses = self::getStatuses();
        return $statuses[$status] ?? null;
    }
}