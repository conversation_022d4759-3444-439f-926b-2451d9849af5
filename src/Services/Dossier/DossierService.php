<?php

namespace MatGyver\Services\Dossier;

use Doctrine\ORM\EntityManager;
use MatGyver\Components\Mailer\MailSender;
use MatGyver\Entity\Dossier\Dossier;
use MatGyver\Entity\Dossier\DossierHistory;
use MatG<PERSON>ver\Entity\User\User;
use MatGyver\Enums\UserEmailNotificationTypeEnum;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Encryption;
use MatGyver\Helpers\Tools;
use MatGyver\Repository\Dossier\DossierRepository;
use MatGyver\Services\BaseEntityService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\RightsService;
use MatGyver\Services\TwigService;
use MatGyver\Services\Users\UsersService;

/**
 * Class DossierService
 * @package MatGyver\Services\Dossier
 * @property DossierRepository $repository
 * @method DossierRepository getRepository()
 */
class DossierService extends BaseEntityService
{
    private MailSender $mailSender;

    /**
     * DossierService constructor.
     * @param EntityManager $em
     * @param MailSender $mailSender
     */
    public function __construct(
        EntityManager $em,
        MailSender $mailSender
    ) {
        $this->em = $em;
        $this->repository = $em->getRepository(Dossier::class);
        $this->mailSender = $mailSender;
    }

    /**
     * @param Dossier|null $dossier
     * @return array
     */
    public static function getStatuses(?Dossier $dossier = null): array
    {
        $statuses = [
            Dossier::STATUS_ACCEPTED_BY_EXPERT => __('Accepté par l\'expert'),
            Dossier::STATUS_CANCELED => __('Annulé'),
            Dossier::STATUS_ANALYSIS_TO_BE_SENT => __('Analyse à expédier'),
            Dossier::STATUS_ANALYSIS_TO_BE_COMPLETED => __('Analyse trame à compléter'),
            Dossier::STATUS_ANALYSIS_RESULT_TO_SEND => __('Analyse résultat à transmettre'),
            Dossier::STATUS_CLOSED => __('Archivé'),
            Dossier::STATUS_OTHER => __('Autre'),
            Dossier::STATUS_CONFIRMED => __('Confirmé par le lésé'),
            Dossier::STATUS_CONVOCATION_TO_BE_MADE => __('Convocation à faire'),
            Dossier::STATUS_REQUEST_FOR_ANALYSIS => __('Demande d\'analyse à formuler'),
            Dossier::STATUS_QUOTATION_TO_BE_DRAWN_UP => __('Devis à établir'),
            Dossier::STATUS_INSTRUCTIONS_TO_BE_WRITTEN => __('Dire à rédiger'),
            Dossier::STATUS_EXPERTISE_CANCELED => __('Expertise annulée'),
            Dossier::STATUS_EXPERTISE_COMPLETED => __('Expertise finalisée'),
            Dossier::STATUS_WAITING_FOR_EXPERT => __('En attente d\'acceptation de l\'expert'),
            Dossier::STATUS_WAITING_DOCUMENT => __('En attente d\'un document'),
            Dossier::STATUS_WAITING_REPLY_FROM_LAWYER => __('En attente d\'une réponse de l\'avocat'),
            Dossier::STATUS_WAITING_REPLY_FROM_EXPERT => __('En attente d\'une réponse de l\'expert adverse'),
            Dossier::STATUS_WAITING_REPLY_FROM_CONTACT => __('En attente d\'une réponse du lésé'),
            Dossier::STATUS_WAITING_REPLY_FROM_MANDATE => __('En attente d\'une réponse du mandant'),
            Dossier::STATUS_WAITING_REPLY_FROM_VEHICLE_OWNER => __('En attente d\'une réponse du propriétaire'),
            Dossier::STATUS_WAITING_REPLY_TO_CLAIM => __('En attente d\'une réponse de réclamation'),
            Dossier::STATUS_WAITING_THIRD_PARTY => __('En attente d\'une réponse d\'un tiers'),
            Dossier::STATUS_WAITING_FOR_ESTIMATE => __('En attente de devis'),
            Dossier::STATUS_WAITING_EXPERTISE => __('En attente de l\'expertise'),
            Dossier::STATUS_WAITING_FEES_PROVISION => __('En attente de provision d\'honoraires'),
            Dossier::STATUS_WAITING_CONTACT => __('En attente du lésé'),
            Dossier::STATUS_WAITING_ANALYSIS => __('En attente du résultat d\'une analyse'),
            Dossier::STATUS_WAITING_DIAGNOSTIC => __('En attente du résultat d\'un diagnostic'),
            Dossier::STATUS_WAITING_PAYMENT => __('En attente du paiement'),
            Dossier::STATUS_IN_PROGRESS => __('En cours'),
            Dossier::STATUS_DOSSIER_STUDY => __('Etude du dossier'),
            Dossier::STATUS_VRADE_STUDY => __('Etude de la VRADE'),
            Dossier::STATUS_INVOICE_TO_BE_ISSUED => __('Facture à établir'),
            Dossier::STATUS_INVOICE_TO_BE_SENT => __('Facture à transmettre'),
            Dossier::STATUS_EXPERTISE_APPOINTMENT_MANAGEMENT => __('Gestion de rendez-vous d\'expertise'),
            Dossier::STATUS_POSTPONE_IN_PROGRESS => __('Gestion de report'),
            Dossier::STATUS_PREPARE_HISTORY => __('Historique à préparer'),
            Dossier::STATUS_FEES_TO_BE_CLAIMED => __('Honoraires à réclamer'),
            Dossier::STATUS_MISSION_EDITED => __('Missionnement modifié par le lésé'),
            Dossier::STATUS_SETTLEMENT_AGREEMENT_TO_UPDATE => __('Protocole d\'accord à modifier'),
            Dossier::STATUS_SETTLEMENT_AGREEMENT_TO_BE_MADE => __('Protocole d\'accord à rédiger'),
            Dossier::STATUS_PRELIMINARY_REPORT_TO_BE_FILED => __('Pré-rapport à déposer'),
            Dossier::STATUS_REPORT_TO_BE_FILED => __('Rapport à déposer'),
            Dossier::STATUS_REPORT_TO_UPDATE => __('Rapport à mettre à jour'),
            Dossier::STATUS_REPORT_FILED => __('Rapport déposé'),
            Dossier::STATUS_EXPERTISE_RECEIVED => __('Réception du rapport d\'expertise'),
            Dossier::STATUS_CLAIM_TO_UPDATE => __('Réclamation à modifier'),
            Dossier::STATUS_CLAIM_TO_BE_MADE => __('Réclamation à rédiger'),
            Dossier::STATUS_CLAIM_OR_SETTLEMENT_AGREEMENT => __('Réclamations et protocoles'),
            Dossier::STATUS_CLAIM => __('Réclamer'),
            Dossier::STATUS_REFUSED_BY_EXPERT => __('Refusé par l\'expert'),
            Dossier::STATUS_REMIND => __('Relancer'),
            Dossier::STATUS_REMIND_CONTACT => __('Relancer le lésé'),
            Dossier::STATUS_REMIND_MANDATE => __('Relancer le mandant'),
            Dossier::STATUS_REMIND_REPAIRER => __('Relancer le réparateur'),
            Dossier::STATUS_REPLY_TO_INSURER => __('Répondre à l\'assureur'),
            Dossier::STATUS_REPLY_TO_LAWYER => __('Répondre à l\'avocat'),
            Dossier::STATUS_REPLY_TO_GARAGE => __('Répondre au garage'),
            Dossier::STATUS_REPLY_TO_CONTACT => __('Répondre au lésé'),
            Dossier::STATUS_REPLY_TO_MANDATE => __('Répondre au mandant'),
            Dossier::STATUS_REPLY_TO_VEHICLE_OWNER => __('Répondre au propriétaire du véhicule'),
            Dossier::STATUS_RESCHEDULE_AN_APPOINTMENT => __('Reporter un RDV'),
            Dossier::STATUS_SUSPENDED => __('Suspendu'),
        ];

        if ($dossier and $dossier->isJudiciaire()) {
            $otherStatuses = $statuses;
            $statuses = [
                Dossier::VALIDER_ACCEPTATION_DE_MISSION => __('Valider acceptation de mission'),
                Dossier::INFORMER_AVOCAT_EXPERT_DESIGNE_ET_DEMANDE_DE_PIECES => __('Informer avocat expert désigné et demande de pièces'),
                Dossier::DEMANDER_CONSIGNATION => __('Demander consignation'),
                Dossier::ATTENTE_DU_LIEU_DE_LEXPERTISE_ET_PIECES => __('Attente du lieu d\'expertise et pièces'),
                Dossier::DEMANDER_CONVENANCE_DES_DATES => __('Demander convenance des dates'),
                Dossier::ATTENTE_REPONSE_DE_CONVENANCE_A_5_JOURS_ALERTE => __('Attente réponse de convenance à +5 jours'),
                Dossier::INFORMER_DATE_ET_HEURE_DE_LEXPERTISE => __('Informer date et heure d\'expertise'),
                Dossier::CONVOQUER_LES_PARTIES => __('Convoquer les parties'),
                Dossier::CHRONOLOGIE_DE_LEXPERTISE_ET_HISTORIQUE_A_COMPLETER => __('Chronologie de l\'expertise et historique à compléter'),
                Dossier::DEMANDER_DES_PIECES_AUX_PARTIES => __('Demander des pièces aux parties'),
                Dossier::INTEGRER_LES_PIECES_RECUES => __('Intégrer les pièces reçues'),
                Dossier::ATTENTE_EXPERTISE_JUDICIAIRE => __('Attente expertise judiciaire'),
                Dossier::ENVOYER_COMPTE_RENDU_ACCEDIT => __('Envoyer compte rendu accédit'),
                Dossier::DEMANDER_CONSIGNATION_COMPLEMENTAIRE => __('Demander consignation complémentaire'),
                Dossier::INFORMER_ESTIMATION_DES_FRAIS => __('Informer estimation des frais'),
                Dossier::REDIGER_PRE_RAPPORT => __('Rédiger pré-rapport'),
                Dossier::SOLLICITER_UN_TIERS => __('Solliciter un Tiers'),
                Dossier::ATTENTE_DIRE_DES_PARTIES => __('Attente dire des parties'),
                Dossier::RELANCER_LES_PARTIES => __('Relancer les parties'),
                Dossier::REPONDRE_AUX_DIRE_DES_PARTIES => __('Répondre aux dires des parties'),
                Dossier::REDIGER_RAPPORT => __('Rédiger Rapport'),
                Dossier::REDIGER_LES_FRAIS => __('Rédiger les frais'),
                Dossier::INFORMER_LE_TRIBUNAL => __('Informer le tribunal'),
                Dossier::PROROGATION_DU_DELAI => __('Prorogation du délai'),
                Dossier::DELAI_COMPLEMENTAIRE => __('Délai complémentaire'),
                Dossier::INFORMER_TRIBUNAL_RAPPORT_RECU_PAR_LES_PARTIES => __('Informer tribunal rapport reçu par les parties'),
                Dossier::RECLAMER_LE_REGLEMENT_AU_TRIBUNAL => __('Réclamer le règlement au Tribunal'),
                Dossier::CONSIGNATION_ACCORDEE => __('Consignation accordée'),
                Dossier::CONSIGNATION_COMPLEMENTAIRE_ACCORDEE => __('Consignation complémentaire accordée'),
                Dossier::CONSIGNATION_EN_ATTENTE => __('Consignation en attente'),
            ];
            $statuses = array_merge($statuses, $otherStatuses);
        }

        return $statuses;
    }

    /**
     * @param string $status
     * @param Dossier|null $dossier
     * @return string|null
     */
    public static function getStatus(string $status, ?Dossier $dossier = null): ?string
    {
        $statuses = self::getStatuses($dossier);
        return $statuses[$status] ?? null;
    }

    /**
     * @param Dossier $dossier
     * @param string $identifier
     * @return array
     */
    public function send(Dossier $dossier, string $identifier = 'site_dossier'): array
    {
        $sendMail = $this->sendMailToContact($dossier, $identifier);
        if (!$sendMail['valid']) {
            return $sendMail;
        }

        DossierHistoryService::add($dossier, DossierHistory::ACTION_SENT);

        $this->setStatus($dossier, Dossier::STATUS_WAITING_CONTACT);

        return ['valid' => true];
    }

    /**
     * @param Dossier $dossier
     * @param User $user
     * @return array
     */
    public function setExpert(Dossier $dossier, ?User $user = null): array
    {
        $accepted = false;
        if ($user) {
            if ($dossier->getUser() === $user) {
                return ['valid' => true];
            }

            if ($dossier->getManager() and $dossier->getManager() === $user) {
                $accepted = true;
            }
            if ($user->getId() == $_SESSION['user']['id']) {
                $accepted = true;
            }
            if (!$accepted and RightsService::hasRole(ROLE_ADMIN, $user->getId())) {
                $accepted = true;
            }
        }

        $dossier->setUser($user);
        $dossier->setAccepted($accepted);
        try {
            $this->persistAndFlush($dossier);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => $e->getMessage()];
        }

        if ($accepted) {
            $this->setStatus($dossier, Dossier::STATUS_CONVOCATION_TO_BE_MADE);
            return ['valid' => true];
        }


        if ($user) {
            $recipient = [['user_id' => $dossier->getUser()->getId()]];
        } else {
            $recipient = [['user_id' => $dossier->getClient()->getMainAdmin()->getId()]];
        }
        $vars = [
            'CONTACT' => ($dossier->getContact() ? $dossier->getContact()->getFirstName() . ' ' . $dossier->getContact()->getLastName() : ''),
            'APP_DOSSIER_LINK' => Tools::makeLink('app', 'dossier', $dossier->getId()),
            'VEHICLE' => ($dossier->getVehicle() ? $dossier->getVehicle()->getBrand() . ' ' . $dossier->getVehicle()->getModel() . ' (' . $dossier->getVehicle()->getRegistration() . ')' : ''),
        ];
        $sendMail = $this->mailSender->sendTemplateToClient('dossier_to_expert', $vars, UserEmailNotificationTypeEnum::IMPORTANT, $recipient, $dossier->getClient()->getId(), [], '', '', 0, $dossier);
        if (!$sendMail['valid']) {
            return $sendMail;
        }

        if ($user) {
            DossierHistoryService::add($dossier, DossierHistory::ACTION_SENT_TO_EXPERT, $user->getId());
        }

        $this->setStatus($dossier, Dossier::STATUS_WAITING_FOR_EXPERT);

        return ['valid' => true];
    }

    public function accept(Dossier $dossier)
    {
        if (!$dossier->getUser()) {
            $container = ContainerBuilderService::getInstance();
            $user = $container->get(UsersService::class)->getUser();
            $dossier->setUser($user);
        }
        $dossier->setAccepted(true);
        try {
            $this->persistAndFlush($dossier);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => $e->getMessage()];
        }

        $this->setStatus($dossier, Dossier::STATUS_CONVOCATION_TO_BE_MADE);

        //send mail to manager
        $recipients = [];
        $mainAdmin = $dossier->getClient()->getMainAdmin();
        $recipients[] = ['user_id' => $mainAdmin->getId()];
        if ($dossier->getManager() and $dossier->getManager() !== $mainAdmin) {
            $recipients[] = ['user_id' => $dossier->getManager()->getId()];
        }
        $vars = [
            'EXPERT' => $dossier->getUser(),
            'CONTACT' => ($dossier->getContact() ? $dossier->getContact()->getFirstName() . ' ' . $dossier->getContact()->getLastName() : ''),
            'APP_DOSSIER_LINK' => Tools::makeLink('app', 'dossier', $dossier->getId()),
            'VEHICLE' => ($dossier->getVehicle() ? $dossier->getVehicle()->getBrand() . ' ' . $dossier->getVehicle()->getModel() . ' (' . $dossier->getVehicle()->getRegistration() . ')' : ''),
        ];
        $sendMail = $this->mailSender->sendTemplateToClient('dossier_accepted_by_expert', $vars, UserEmailNotificationTypeEnum::IMPORTANT, $recipients, $dossier->getClient()->getId(), [], '', '', 0, $dossier);
        if (!$sendMail['valid']) {
            return $sendMail;
        }

        DossierHistoryService::add($dossier, DossierHistory::ACTION_ACCEPTED_BY_EXPERT, $dossier->getUser()->getId());

        return ['valid' => true];
    }

    /**
     * @param Dossier $dossier
     * @param string $identifier
     * @return array
     */
    public function sendMailToContact(Dossier $dossier, string $identifier = 'site_dossier'): array
    {
        $expertise = $dossier->getLastExpertise();
        $contact = $dossier->getContact();
        if (!$contact or !$contact->getEmail()) {
            return ['valid' => false, 'message' => __('Aucune adresse email trouvée pour ce contact.')];
        }

        $dossierLink = $dossier->getPublicLink();
        $user = $contact->getUser();
        if (!$user) {
            $container = ContainerBuilderService::getInstance();
            $user = $container->get(UsersService::class)->adminGetUserByEmail($contact->getEmail());
        }
        if (!$user) {
            $dossierLink = Tools::makeLink('site', 'dossier', 'account/create/' . Encryption::encrypt($dossier->getReference()));
        }

        $recipient = [
            [
                'email' => $contact->getEmail(),
                'first_name' => $contact->getFirstName(),
                'last_name' => $contact->getLastName()
            ]
        ];
        $vars = [
            'SUBJECT_DOSSIER' => $dossier->getSubject(),
            'CONTACT' => $contact->getFirstName() . ' ' . $contact->getLastName(),
            'DOSSIER_LINK' => $dossierLink,
            'EXPERTISE_DATE' => (($expertise and $expertise->getDate()) ? dateTimeFr($expertise->getDate()->format('Y-m-d H:i:s')) : ''),
            'LAST_NAME' => $contact->getLastName(),
            'EXPERTISE_PLACE' => ($expertise ? $expertise->displayPlace(false, ', ') : ''),
        ];
        if ($identifier == 'signature_refused') {
            $vars['SIGNATURE_LINK'] = $dossier->getPublicLink('step=signature');
        }

        return $this->mailSender->sendTemplateToClient($identifier, $vars, UserEmailNotificationTypeEnum::IMPORTANT, $recipient, $dossier->getClient()->getId(), [], '', '', 0, $dossier);
    }

    /**
     * @param Dossier $dossier
     * @return array
     */
    public function export(Dossier $dossier): array
    {
        $zipFile = VAR_PATH . '/cache/upload/dossier-' . $dossier->getId() . '.zip';
        $path = WEB_PATH . '/medias/' . $dossier->getFolder();
        if (!is_dir($path)) {
            return ['valid' => false, 'message' => __('Aucun média trouvé dans ce dossier.')];
        }

        try {
            $zip = new \ZipArchive();
            $zip->open($zipFile, \ZipArchive::CREATE | \ZipArchive::OVERWRITE);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Une erreur est survenue.')];
        }

        /** @var \SplFileInfo[] $files */
        $files = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($path),
            \RecursiveIteratorIterator::LEAVES_ONLY
        );
        foreach ($files as $name => $file) {
            if (!$file->isDir()) {
                $filePath = $file->getRealPath();
                if (str_contains($filePath, 'thumbnail')) {
                    continue;
                }
                $zip->addFile($filePath, basename($filePath));
            }
        }
        if (!$zip->count()) {
            $zip->close();
            return ['valid' => false, 'message' => __('Aucun média trouvé dans ce dossier.')];
        }
        $zip->close();

        return ['valid' => true, 'file' => $zipFile];
    }

    /**
     * @param Dossier $dossier
     * @return void
     */
    public function updateFeesAmount(Dossier $dossier)
    {
        $fees = 0;
        $surcharges = $dossier->getSurcharges();
        foreach ($surcharges as $surcharge) {
            $fees += $surcharge->getAmount();
        }

        $dossier->setFeesAmount($fees);
        try {
            $this->persistAndFlush($dossier);
        } catch (\Exception $e) {}
    }

    /**
     * @param Dossier $dossier
     * @return void
     */
    public function addAlert(Dossier $dossier): void
    {
        $dossier->setAlert(true);
        try {
            $this->persistAndFlush($dossier);
        } catch (\Exception $e) {
            LoggerService::logError('Unable to update dossier alert : ' . $e->getMessage());
        }

        $vars = [
            'SUBJECT_DOSSIER' => $dossier->getSubject(),
            'DOSSIER' => $dossier->getContact()->getCompanyOrName(),
            'DOSSIER_LINK' => Tools::makeLink('app', 'dossier', $dossier->getId()),
            'VEHICLE' => ($dossier->getVehicle() ? $dossier->getVehicle()->getBrand() . ' ' . $dossier->getVehicle()->getModel() : ''),
            'VEHICLE_REGISTRATION' => ($dossier->getVehicle() ? $dossier->getVehicle()->getRegistration() : ''),
            'DOSSIER_STATUS' => $dossier->getStatusName(),
        ];
        if ($dossier->getStatusMessage()) {
            $vars['DOSSIER_STATUS'] .= ' (' . $dossier->getStatusMessage() . ')';
        }

        $dossierUsers = $dossier->getUsers();
        $mainAdmin = $dossier->getClient()->getMainAdmin();
        $sendToMainAdmin = false;
        if (count($dossierUsers)) {
            foreach ($dossierUsers as $dossierUser) {
                if ($dossierUser->getUser() === $mainAdmin) {
                    $sendToMainAdmin = true;
                }

                $recipient = [['user_id' => $dossierUser->getUser()->getId()]];
                $send = $this->mailSender->sendTemplateToClient('dossier_alert', $vars, UserEmailNotificationTypeEnum::IMPORTANT, $recipient, $dossier->getClient()->getId(), [], '', '', 0, $dossier);
                if (!$send['valid']) {
                    LoggerService::logError('Unable to send dossier alert email : ' . $send['message']);
                }
            }
        }

        if (!$sendToMainAdmin) {
            $recipient = [['user_id' => $mainAdmin->getId()]];
            $send = $this->mailSender->sendTemplateToClient('dossier_alert', $vars, UserEmailNotificationTypeEnum::IMPORTANT, $recipient, $dossier->getClient()->getId(), [], '', '', 0, $dossier);
            if (!$send['valid']) {
                LoggerService::logError('Unable to send dossier alert email : ' . $send['message']);
            }
        }
    }

    /**
     * @param Dossier $dossier
     * @return void
     */
    public function removeAlert(Dossier $dossier): void
    {
        $dossier->setAlert(false);
        $dossier->setDateAlert(null);
        try {
            $this->persistAndFlush($dossier);
        } catch (\Exception $e) {
            LoggerService::logError('Unable to update dossier alert : ' . $e->getMessage());
        }
    }

    /**
     * @param Dossier $dossier
     * @return bool
     */
    public function compress(Dossier $dossier): bool
    {
        $dossierPath = WEB_PATH . '/medias/' . $dossier->getFolder();
        if (!is_dir($dossierPath)) {
            LoggerService::logError('Directory ' . $dossierPath . ' does no exist');
            return false;
        }

        $zip = new \ZipArchive();
        $zipFile = $dossierPath . 'documents.zip';
        if (!$zip->open($zipFile, \ZipArchive::CREATE | \ZipArchive::OVERWRITE)) {
            LoggerService::logError('Unable to create zip file ' . $dossierPath . 'documents.zip');
            return false;
        }

        $filesToDelete = array();

        /** @var \SplFileInfo[] $files */
        $files = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($dossierPath),
            \RecursiveIteratorIterator::LEAVES_ONLY
        );
        foreach ($files as $name => $file) {
            if ($file->isDir()) {
                continue;
            }

            $filePath = $file->getRealPath();
            $entryName = basename($filePath);
            $zip->addFile($filePath, $entryName);

            $filesToDelete[] = $filePath;
        }
        $zip->close();

        foreach ($filesToDelete as $file) {
            @unlink($file);
        }

        if (file_exists($zipFile)) {
            //encrypt zip file
            $zipEncryptedFile = $dossierPath . 'documents.zip.enc';
            $encryptFile = Encryption::encryptFile($zipFile, $zipEncryptedFile);
            if ($encryptFile) {
                @unlink($zipFile);
            }
        }

        $dossier->setCompressed(true);
        try {
            $this->persistAndFlush($dossier);
        } catch (\Exception $e) {
            LoggerService::logError('Unable to update dossier ' . $dossier->getId() . ' : ' . $e->getMessage());
            return false;
        }

        DossierHistoryService::add($dossier, DossierHistory::ACTION_COMPRESSED);

        return true;
    }

    /**
     * @param Dossier $dossier
     * @return bool
     */
    public function unCompress(Dossier $dossier): bool
    {
        $dossierPath = WEB_PATH . '/medias/' . $dossier->getFolder();
        $zipFile = $dossierPath . 'documents.zip';

        //encrypt zip file
        $zipEncryptedFile = $dossierPath . 'documents.zip.enc';
        if (file_exists($zipEncryptedFile)) {
            $decryptFile = Encryption::decryptFile($zipEncryptedFile, $zipFile);
            if ($decryptFile) {
                @unlink($zipEncryptedFile);
            }
        }

        $zip = new \ZipArchive();
        $zipOpen = $zip->open($zipFile);
        if ($zipOpen !== true) {
            LoggerService::logError('Unable to open zip file ' . $zipFile);
            return false;
        }

        $zip->extractTo($dossierPath);
        $zip->close();

        @unlink($zipFile);

        DossierHistoryService::add($dossier, DossierHistory::ACTION_UNCOMPRESSED);

        return true;
    }

    /**
     * @param Dossier $dossier
     * @param \DateTimeInterface $date
     * @return void
     */
    public function setFirstConvocation(Dossier $dossier, \DateTimeInterface $date): void
    {
        if ($dossier->hasConvocation()) {
            return;
        }

        $dossier->setHasConvocation(true);
        $dossier->setDateConvocation($date);
        try {
            $this->persistAndFlush($dossier);
        } catch (\Exception $e) {
            LoggerService::logError('Unable to update dossier first convocation : ' . $e->getMessage());
        }
    }

    public static function addModalCreateDossier(): void
    {
        $content = TwigService::getInstance()->render('app/dossier/modal.php');
        Assets::addInlineJs($content);
    }

    public function setStatus(Dossier $dossier, string $status, string $statusMessage = ''): array
    {
        $previousStatus = $dossier->getStatus();

        if (($previousStatus == Dossier::STATUS_CANCELED or $previousStatus == Dossier::STATUS_CLOSED) and $dossier->getStatus() != $previousStatus) {
            //reopen dossier
            $dossier->setDate(new \DateTime());
            $dossier->setDateConvocation(null);
        }

        if ($dossier->getStatus() == Dossier::STATUS_CLOSED or $dossier->getStatus() == Dossier::STATUS_CANCELED) {
            $dossier->setDateClosed(new \DateTime());
        }

        $dossier->setStatus($status);
        $dossier->setStatusMessage($statusMessage);
        try {
            $this->persistAndFlush($dossier);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la mise à jour du dossier.")];
        }

        DossierHistoryService::add($dossier, DossierHistory::ACTION_UPDATE_STATUS, $status);

        return ['valid' => true];
    }
}
