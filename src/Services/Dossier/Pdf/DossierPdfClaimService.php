<?php

namespace MatGyver\Services\Dossier\Pdf;

use MatGyver\Entity\Dossier\DossierDocument;
use MatGyver\Helpers\Tools;

/**
 * Class DossierPdfClaimService
 * @package MatGyver\Services\Dossier\Pdf
 */
class DossierPdfClaimService extends AbstractDossierPdfService
{
    public function __construct()
    {
        $this->type = DossierDocument::TYPE_CLAIM;
    }

    /**
     * @param DossierDocument $document
     * @return array
     */
    public function generateClaim(DossierDocument $document): array
    {
        $fileName = 'reclamation-' . $document->getId() . '-' . $document->getExpertise()->getId() . '-' . $document->getPerson()->getId() . '.pdf';
        if ($document->getDossier()->getVehicle() and $document->getDossier()->getVehicle()->getRegistration()) {
            $fileName = 'reclamation-' . $document->getId() . '-' . permalink($document->getDossier()->getVehicle()->getRegistration()) . '-' . $document->getExpertise()->getId() . '-' . $document->getPerson()->getId() . '.pdf';
        }

        $link = Tools::makeLink('site', 'dossier', 'document/generate/' . $document->getType() . '/' . $document->getExpertise()->getId() . '/' . $document->getPerson()->getId(), 'id=' . $document->getId());

        $generatePdf = $this->generatePdf($document->getDossier(), $link, $fileName);
        if (!$generatePdf['valid']) {
            return $generatePdf;
        }

        return ['valid' => true, 'filename' => $fileName];
    }
}
