<?php

namespace MatGyver\Services\Dossier\Pdf;

use MatGyver\Entity\Dossier\DossierDocument;
use MatGyver\Helpers\Tools;

/**
 * Class DossierPdfInformationPartieDocumentService
 * @package MatGyver\Services\Dossier\Pdf
 */
class DossierPdfInformationPartieDocumentService extends AbstractDossierPdfService
{
    /**
     * @param DossierDocument $document
     * @return array
     */
    public function generateDocument(DossierDocument $document): array
    {
        $fileName = $document->getId() . '-' . $document->getDossier()->getId() . '.pdf';
        if ($document->getDossier()->getVehicle() and $document->getDossier()->getVehicle()->getRegistration()) {
            $fileName = permalink($document->getDossier()->getVehicle()->getRegistration()) . '-' . $document->getDossier()->getId() . '-' . $document->getId() . '.pdf';
        }

        $link = Tools::makeLink('site', 'dossier', 'document/generate/' . DossierDocument::TYPE_INFORMATION_PARTIE . '/' . $document->getDossier()->getExpertise()->getId() . '/0', 'documentId=' . $document->getId());

        $generatePdf = $this->generatePdf($document->getDossier(), $link, $fileName);
        if (!$generatePdf['valid']) {
            return $generatePdf;
        }

        return ['valid' => true, 'filename' => $fileName];
    }
}
