<?php

namespace MatGyver\Services\Dossier\Pdf;

use MatGyver\Entity\Dossier\DossierDocument;
use MatGyver\Helpers\Tools;

/**
 * Class DossierPdfDocumentsRequiredFromPartiesDocumentService
 * @package MatGyver\Services\Dossier\Pdf
 */
class DossierPdfDocumentsRequiredFromPartiesDocumentService extends AbstractDossierPdfService
{
    /**
     * @param DossierDocument $document
     * @return array
     */
    public function generateDocument(DossierDocument $document): array
    {
        $fileName = 'pieces-demandees-aux-parties-' . $document->getId() . '-' . $document->getDossier()->getId() . '.pdf';
        if ($document->getDossier()->getVehicle() and $document->getDossier()->getVehicle()->getRegistration()) {
            $fileName = 'pieces-demandees-aux-parties-' . permalink($document->getDossier()->getVehicle()->getRegistration()) . '-' . $document->getDossier()->getId() . '-' . $document->getId() . '.pdf';
        }

        $link = Tools::makeLink('site', 'dossier', 'document/generate/' . DossierDocument::TYPE_DOCUMENTS_REQUIRED_FROM_PARTIES . '/' . $document->getDossier()->getExpertise()->getId() . '/0', 'documentId=' . $document->getId());

        $generatePdf = $this->generatePdf($document->getDossier(), $link, $fileName);
        if (!$generatePdf['valid']) {
            return $generatePdf;
        }

        return ['valid' => true, 'filename' => $fileName];
    }
}
