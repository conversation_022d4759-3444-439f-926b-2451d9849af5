<?php

namespace MatGyver\Services\Dossier\Pdf;

use MatGyver\Entity\Dossier\DossierDocument;
use MatGyver\Entity\Dossier\Expertise\DossierExpertise;
use MatGyver\Helpers\Tools;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Dossier\DossierSignatureService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseJudiciaireChronologyService;
use MatGyver\Services\Integration\Services\ILovePdfService;

/**
 * Class DossierPdfContradictoryService
 * @package MatGyver\Services\Dossier\Pdf
 */
class DossierPdfContradictoryService extends AbstractDossierPdfService
{
    public function __construct()
    {
        $this->type = DossierDocument::TYPE_CONTRADICTORY;
    }

    /**
     * @param DossierExpertise $expertise
     * @param bool $displayPictures
     * @param bool $displayDocuments
     * @param bool $displayPrice
     * @param bool $displayFees
     * @return array
     */
    public function generate(DossierExpertise $expertise, bool $displayPictures = false, bool $displayDocuments = false, bool $displayPrice = false, bool $displayFees = false, bool $displayCourtQuestionsAnswers = false): array
    {
        $prefix = 'PV-';
        $fileName = $expertise->getId() . '.pdf';
        if ($expertise->getDossier()->getVehicle() and $expertise->getDossier()->getVehicle()->getRegistration()) {
            $fileName = permalink($expertise->getDossier()->getVehicle()->getRegistration()) . '-' . ($expertise->getDate() ? $expertise->getDate()->format('d-m-Y') : date('d-m-Y')) . '.pdf';
        }
        if ($expertise->getDossier()->isJudiciaire()) {
            $prefix = 'compte-rendu-accedit-';
        }

        $fileName = $prefix . $fileName;

        $link = $this->getLink($expertise, $displayPictures, $displayDocuments, $displayPrice, $displayFees, $displayCourtQuestionsAnswers);

        $generatePdf = $this->generatePdf($expertise->getDossier(), $link, $fileName);
        if (!$generatePdf['valid']) {
            return $generatePdf;
        }

        if (ENV === ENV_PROD) {
            ILovePdfService::compress(WEB_PATH . '/medias/' . $expertise->getDossier()->getFolder() . $fileName);
        }

        $saveDocument = $this->save($expertise->getDossier(), $fileName, $expertise);
        if (!$saveDocument['valid']) {
            return $saveDocument;
        }

        $documentId = $saveDocument['id'];
        $document = $this->dossierDocumentService->getRepository()->find($documentId);
        if (!$document) {
            return ['valid' => false, 'message' => 'Document introuvable'];
        }

        $firstPart = $document->getFirstPart() ? json_decode($document->getFirstPart(), true) : [];

        $container = ContainerBuilderService::getInstance();
        $signatures = $container->get(DossierSignatureService::class)->getRepository()->findBy(['expertise' => $expertise]);
        $signaturesIds = [];
        foreach ($signatures as $signature) {
            $signaturesIds[] = $signature->getId();
        }
        $firstPart['signatures'] = $signaturesIds;

        $firstPart['displayPictures'] = $displayPictures;
        $firstPart['displayDocuments'] = $displayDocuments;
        $firstPart['displayPrice'] = $displayPrice;
        $firstPart['displayFees'] = $displayFees;
        $firstPart['displayCourtQuestionsAnswers'] = $displayCourtQuestionsAnswers;

        $document->setFirstPart(json_encode($firstPart));
        try {
            $this->dossierDocumentService->persistAndFlush($document);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => $e->getMessage()];
        }

        /*if ($expertise->getDossier()->isJudiciaire()) {
            $expertiseJudiciaire = $expertise->getExpertiseJudiciaire();
            $container->get(DossierExpertiseJudiciaireChronologyService::class)->createFromDocumentGenerated($expertiseJudiciaire, $document);
        }*/

        return $saveDocument;
    }

    public function getLink(DossierExpertise $expertise, bool $displayPictures = false, bool $displayDocuments = false, bool $displayPrice = false, bool $displayFees = false, bool $displayCourtQuestionsAnswers = false): string
    {
        $link = Tools::makeLink('site', 'dossier', 'document/generate/contradictory/' . $expertise->getId() . '/0');
        $query = [
            'display_pictures' => $displayPictures,
            'display_documents' => $displayDocuments,
            'display_price' => $displayPrice,
            'display_fees' => $displayFees,
            'display_court_questions_answers' => $displayCourtQuestionsAnswers,
        ];
        $link .= '?' . http_build_query($query);

        return $link;
    }
}
