<?php

namespace MatGyver\Services\Dossier\Pdf;

use MatGyver\Entity\Dossier\DossierDocument;
use MatGyver\Helpers\Tools;

/**
 * Class DossierPdfPostponementRequestService
 * @package MatGyver\Services\Dossier\Pdf
 */
class DossierPdfSettlementAgreementService extends AbstractDossierPdfService
{
    public function __construct()
    {
        $this->type = DossierDocument::TYPE_SETTLEMENT_AGREEMENT;
    }

    /**
     * @param DossierDocument $document
     * @return array
     */
    public function generateSettlementAgreement(DossierDocument $document): array
    {
        $fileName = 'protocole-accord-' . $document->getExpertise()->getId() . '.pdf';
        if ($document->getDossier()->getVehicle() and $document->getDossier()->getVehicle()->getRegistration()) {
            $fileName = 'protocole-accord-' . permalink($document->getDossier()->getVehicle()->getRegistration()) . '-' . $document->getExpertise()->getId() . '.pdf';
        }

        $link = Tools::makeLink('site', 'dossier', 'document/generate/' . $document->getType() . '/' . $document->getExpertise()->getId() . '/0');

        $generatePdf = $this->generatePdf($document->getDossier(), $link, $fileName);
        if (!$generatePdf['valid']) {
            return $generatePdf;
        }

        return ['valid' => true, 'filename' => $fileName];
    }
}
