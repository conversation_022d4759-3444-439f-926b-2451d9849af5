<?php

namespace MatGyver\Services\Logger;

use MatGyver\Helpers\Tools;

class Logger extends \Monolog\Logger
{
    /**
     * @param  int     $level   The logging level
     * @param  string  $message The log message
     * @param  array   $context The log context
     * @return Boolean Whether the record has been processed
     */
    public function addRecord($level, $message, array $context = array()): bool
    {
        $newContext = [];

        // Add stack trace
        if (!isset($context['trace'])) {
            $backtrace = debug_backtrace();
            $newContext['trace'] = json_encode($backtrace);
        } else {
            unset($context['trace']);
        }

        // Add session data
        if (!isset($context['data'])) {
            $get = $_GET;
            $post = $_POST;
            $this->removePrivateInfo($get);
            $this->removePrivateInfo($post);
            $newContext['data'] = json_encode(['get' => $get, 'post' => $post]);
        } else {
            unset($context['data']);
        }

        // Add route data
        if (!isset($context['route'])) {
            $newContext['route'] = '';
            if (isset($_SERVER['REQUEST_URI'])) {
                $newContext['route'] = Tools::getUrl();
            }
        } else {
            unset($context['route']);
        }

        // Add server data
        if (!isset($context['server'])) {
            $newContext['server'] = '';
            if (isset($_SERVER) and $_SERVER) {
                $newContext['server'] = json_encode($_SERVER);
            }
        } else {
            unset($context['server']);
        }

        // Add session data
        if (!isset($context['session'])) {
            $newContext['session'] = '';
            if (isset($_SESSION) and $_SESSION) {
                $newContext['session'] = json_encode($_SESSION);
            }
        } else {
            unset($context['session']);
        }

        // Add client id
        if (isset($_SESSION['client']['id'])) {
            if (!isset($context['client_id'])) {
                $newContext['client_id'] = $_SESSION['client']['id'];
            } else {
                unset($context['client_id']);
            }
        }

        // Add user's data
        if (!isset($context['user']) and isset($_SESSION['user'])) {
            $newContext['user'] = [
                'user_id' => $_SESSION['user']['id'] ?? null,
                'user_email' => $_SESSION['user']['email'] ?? null,
                'user_first_name' => $_SESSION['user']['first_name'] ?? null,
                'user_last_name' => $_SESSION['user']['last_name'] ?? null,
                'user_ip' => $_SESSION['user']['ip'] ?? null,
                'user_client_id' => $_SESSION['user']['client_id'] ?? null,
            ];
        }

        // Add file & line if sent in context (= from error handler)
        if (!empty($context['file'])) {
            $message .= ' at ' . $context['file'];
        }
        if (!empty($context['line'])) {
            $message .= ' line ' . $context['line'];
        }

        // Add prefix if cli error
        if (php_sapi_name() === 'cli' or php_sapi_name() === 'cgi-fcgi') {
            $message = '[CLI] ' . $message;
        }

        if (!empty($context)) {
            $newContext['extra'] = $context;
        }
        return parent::addRecord($level, $message, $newContext);
    }

    /**
     * @param array $data
     */
    private function removePrivateInfo(array &$data)
    {
        foreach ($data as $name => $value) {
            if (str_contains($name, 'password')) {
                $data[$name] = '***';
            }
        }
    }
}
