<?php

namespace MatGyver\Services\Surcharge;

use Doctrine\ORM\EntityManager;
use MatGyver\Entity\Surcharge\Surcharge;
use MatGyver\Repository\Surcharge\SurchargeRepository;
use MatGyver\Services\BaseEntityService;

/**
 * Class SurchargeService
 * @package MatGyver\Services\Surcharge
 * @property SurchargeRepository $repository
 * @method SurchargeRepository getRepository()
 */
class SurchargeService extends BaseEntityService
{
    /**
     * SurchargeService constructor.
     * @param EntityManager $em
     */
    public function __construct(EntityManager $em)
    {
        $this->em = $em;
        $this->repository = $em->getRepository(Surcharge::class);
    }
}