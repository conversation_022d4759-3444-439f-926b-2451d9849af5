<?php

namespace MatGyver\Services\Users;

use Mat<PERSON><PERSON><PERSON>\Components\Mailer\MailSender;
use <PERSON><PERSON><PERSON><PERSON>\Entity\User\User;
use MatGyver\Enums\ConfigEnum;
use MatGyver\Enums\UserEmailNotificationTypeEnum;
use MatGyver\Helpers\Tools;
use <PERSON><PERSON><PERSON>ee\Auth\Providers\Qr\EndroidQrCodeProvider;
use RobThree\Auth\TwoFactorAuth;

/**
 * Class UserMfaService
 * @package MatGyver\Services\Users
 */
class UserMfaService
{
    private UserConfigService $userConfigService;
    private UsersLoginService $usersLoginService;
    private MailSender $mailSender;

    /**
     * UserConfigService constructor.
     * @param UserConfigService $userConfigService
     * @param UsersLoginService $usersLoginService
     * @param MailSender $mailSender
     */
    public function __construct(
        UserConfigService $userConfigService,
        UsersLoginService $usersLoginService,
        MailSender $mailSender
    ) {
        $this->userConfigService = $userConfigService;
        $this->usersLoginService = $usersLoginService;
        $this->mailSender = $mailSender;
    }

    /**
     * @return array
     */
    public function generateMfaConfig(): array
    {
        //generate secret
        try {
            $qrCodeProvider = new EndroidQrCodeProvider();
            $tfa = new TwoFactorAuth(issuer: APP_NAME, qrcodeprovider: $qrCodeProvider);

            if (isset($_SESSION['mfa_temp_secret'])) {
                $secret = $_SESSION['mfa_temp_secret'];
            } else {
                $secret = $tfa->createSecret();
                //set in session
                $_SESSION['mfa_temp_secret'] = $secret;
            }

            //generate qrCode
            $qrCode = $tfa->getQRCodeImageAsDataUri(APP_NAME, $secret);
            return ['valid' => true, 'secret' => $secret, 'qrCode' => $qrCode];
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * @param User $user
     * @param string $code
     * @return array
     */
    public function checkMfaConfig(User $user, string $code): array
    {
        if (!isset($_SESSION['mfa_temp_secret'])) {
            return ['valid' => false, 'message' => __('Opération annulée, veuillez recommencer.')];
        }

        $tfa = new TwoFactorAuth();
        $mfaSecret = $_SESSION['mfa_temp_secret'];

        //check code
        $checkCode = $tfa->verifyCode($mfaSecret, $code);
        if (!$checkCode) {
            return ['valid' => false, 'message' => __('Code incorrect.')];
        }

        $config = [
            ConfigEnum::TWOFACTOR => ConfigEnum::TWOFACTOR_CODE,
            ConfigEnum::TWOFACTOR_SECRET => $_SESSION['mfa_temp_secret'],
        ];
        $this->userConfigService->insertUserConfig($config, $user->getId());

        unset($_SESSION['mfa_temp_secret']);

        return ['valid' => true];
    }

    /**
     * @param User $user
     * @param array $submittedData
     * @return array
     */
    public function generateMfaConnection(User $user, array $submittedData): array
    {
        $userConfig = $user->getUserConfigsAsArray();
        $mfaConfig = $userConfig[ConfigEnum::TWOFACTOR] ?? null;
        if (!$mfaConfig or $mfaConfig == ConfigEnum::TWOFACTOR_NONE) {
            return ['valid' => false, 'message' => __('MFA non configuré.')];
        }

        $_SESSION['mfa_user_id'] = $user->getId();

        if (isset($submittedData['current_url'])) {
            $_SESSION['mfa_current_url'] = filter_var($submittedData['current_url'], FILTER_VALIDATE_URL);
        }
        if (isset($submittedData['remember_me']) and $submittedData['remember_me']) {
            $_SESSION['mfa_remember_me'] = true;
        }

        if ($mfaConfig == ConfigEnum::TWOFACTOR_MAIL) {
            //generate code
            $code = rand(100000, 999999);
            $this->userConfigService->insertUserConfig([ConfigEnum::TWOFACTOR_CODE => $code], $user->getId(), $user->getClient()->getId());

            //send mail with code
            $recipient = [['user_id' => $user->getId()]];
            $vars = [
                'CODE' => $code,
                'SITE_SUPPORT' => Tools::makeLink('site', 'support', 'ticket/create'),
            ];
            $send = $this->mailSender->sendTemplateToClient('user_mfa_code', $vars, UserEmailNotificationTypeEnum::IMPORTANT, $recipient, CLIENT_MASTER);
            if (!$send['valid']) {
                return ['valid' => false, 'message' => __('Une erreur est survenue, l\'administrateur a été prévenu.')];
            }
        }

        $params = [
            'u' => $user->getId(),
            'r' => $user->getRandomId(),
            'c' => $user->getClient()->getId()
        ];
        $params = http_build_query($params);
        $params = base64_encode($params);
        $redirect = Tools::makeLink('site', 'user', 'mfa/' . $params);

        return ['valid' => true, 'redirect' => $redirect];
    }

    /**
     * @param User $user
     * @param string $code
     * @return array
     */
    public function processMfaConnection(User $user, string $code): array
    {
        $userConfig = $user->getUserConfigsAsArray();
        $mfaConfig = $userConfig[ConfigEnum::TWOFACTOR] ?? null;
        if (!$mfaConfig or $mfaConfig == ConfigEnum::TWOFACTOR_NONE) {
            return ['valid' => false, 'message' => __('MFA non configuré.')];
        }
        if (!isset($_SESSION['mfa_user_id']) or $_SESSION['mfa_user_id'] != $user->getId()) {
            return ['valid' => false, 'message' => __("Opération annulée, veuillez recommencer.")];
        }

        //max 3 tries for 10 minutes
        if (!isset($_SESSION['mfa_tries'])) {
            $_SESSION['mfa_tries'] = 1;
            $_SESSION['mfa_time'] = time();
        } else {
            $_SESSION['mfa_tries']++;
        }
        if ($_SESSION['mfa_tries'] > 3) {
            if (time() - $_SESSION['mfa_time'] < 600) {
                return ['valid' => false, 'message' => __('Trop de tentatives de connexions incorrectes, veuillez patienter.')];
            }
            $_SESSION['mfa_tries'] = 1;
            $_SESSION['mfa_time'] = time();
        }

        if ($mfaConfig == ConfigEnum::TWOFACTOR_MAIL) {
            $codeConfig = $userConfig[ConfigEnum::TWOFACTOR_CODE] ?? null;
            if (!$codeConfig) {
                return ['valid' => false, 'message' => __('Double authentification : configuration incorrecte.')];
            }
            if ($code != $codeConfig) {
                return ['valid' => false, 'message' => __('Code incorrect.')];
            }

            //ok
            $this->userConfigService->deleteUserConfig(ConfigEnum::TWOFACTOR_CODE, $user->getId());
        }

        if ($mfaConfig == ConfigEnum::TWOFACTOR_CODE) {
            $tfa = new TwoFactorAuth();
            $mfaSecret = $userConfig[ConfigEnum::TWOFACTOR_SECRET] ?? null;
            if (!$mfaSecret) {
                return ['valid' => false, 'message' => __('Double authentification : configuration incorrecte.')];
            }

            //check code
            $checkCode = $tfa->verifyCode($mfaSecret, $code);
            if (!$checkCode) {
                return ['valid' => false, 'message' => __('Code incorrect.')];
            }
        }

        unset($_SESSION['mfa_user_id']);
        unset($_SESSION['mfa_tries']);
        unset($_SESSION['mfa_time']);

        //login user
        $data = ['email' => $user->getEmail()];
        if (isset($_SESSION['mfa_remember_me'])) {
            $data['remember_me'] = true;
            unset($_SESSION['mfa_remember_me']);
        }
        return $this->usersLoginService->insertSignIn($data, true, true);
    }
}
