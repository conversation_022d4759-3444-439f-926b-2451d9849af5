<?php

namespace MatGyver\Services;

/**
 * Class TranslationsService
 * @package MatGyver\Services
 */
class TranslationsService
{
    const PO_FILE_PATH = I18N_PATH .'/%s/LC_MESSAGES/%s.po';
    const POT_FILE_PATH = I18N_PATH .'/%s/LC_MESSAGES/%s.pot';
    const MO_FILE_PATH = I18N_PATH .'/%s/LC_MESSAGES/%s.mo';
    const JSON_FILE_PATH = I18N_PATH .'/%s/LC_MESSAGES/%s.json';

    /**
     * @var array
     */
    protected $languages = ['fr_FR', 'en_US'];

    /**
     * @return array
     */
    public function getLanguages() :array
    {
        return $this->languages;
    }

    /**
     * @param string $language
     * @param string $filename
     * @return string
     */
    public function getPoFilePath(string $language, string $filename = 'all') :string
    {
        return sprintf(self::PO_FILE_PATH, $language, $filename);
    }

    /**
     * @param string $language
     * @param string $filename
     * @return string
     */
    public function getPotFilePath(string $language, string $filename = 'all') :string
    {
        return sprintf(self::POT_FILE_PATH, $language, $filename);
    }

    /**
     * @param string $language
     * @param string $filename
     * @return string
     */
    public function getMoFilePath(string $language, string $filename = 'all') :string
    {
        return sprintf(self::MO_FILE_PATH, $language, $filename);
    }

    /**
     * @param string $language
     * @param string $filename
     * @return string
     */
    public function getJsonFilePath(string $language, string $filename = 'all') :string
    {
        return sprintf(self::JSON_FILE_PATH, $language, $filename);
    }

    /**
     * @return bool
     */
    public function generateJsScripts() :bool
    {
        foreach ($this->languages as $language) {
            $script = '';
            if ($language != 'fr_FR') {
                $script .= 'i18next.init({ debug: false, keySeparator: false, nsSeparator: false, returnEmptyString: false, lng: \'' . $language . '\', resources: {';
                $script .= '\'' . $language . '\' : { translation: ' . json_encode(self::getArrayTranslations($language)) . ' }, ';
                $script .= '}});' . "\n\n";
            }
            $script .= 'function __(s) {
    let text = ' . ($language == 'fr_FR' ? 's' : 'i18next.t(s)') . ';
    if (arguments.length == 1) {
        return text;
    }
    let argumentsArray = [];
    for (let i in arguments) {
        if (i == 0) {
            continue;
        }
        argumentsArray.push(arguments[i]);
    }
    return vsprintf(text, argumentsArray);
}' . "\n\n";

            $script .= 'function n__(s, p, c) {
    let text = ' . ($language == 'fr_FR' ? 'c == 1 ? s : p' : 'i18next.t(s, {count: c})') . ';
    if (arguments.length == 3) {
        return text;
    }
    let argumentsArray = [];
    for (let i in arguments) {
        if (i <= 2) {
            continue;
        }
        argumentsArray.push(arguments[i]);
    }
    return vsprintf(text, argumentsArray);
}';

            try {
                file_put_contents(FULL_DOCUMENT_ROOT . '/web/assets/js/common/locales/locale.'. $language .'.js', $script);
            } catch (\Exception $e) {}
        }

        return true;
    }

    /**
     * @param string $language
     * @return array
     */
    protected function getArrayTranslations(string $language) :array
    {
        $data = [];
        try {
            $arrayTranslations = file_get_contents(self::getJsonFilePath($language));
        } catch (\Exception $e) {
            return $data;
        }

        $decode = json_decode($arrayTranslations, true);
        if (isset($decode['messages'][''])) {
            $content = $decode['messages'][''];
            foreach ($content as $key => $val) {
                $data[$key] = $val[0];
                if (isset($val[1])) {
                    $data[$key . '_plural'] = $val[1];
                }
            }
        }

        return $data;
    }
}
