<?php
namespace MatGyver\Services\Aws\Sqs;

use MatGyver\Commands\Affiliation\AffiliationExpireCommissionsCommand;
use MatGyver\Commands\Affiliation\AffiliationExpireCommissionsMailAlertCommand;
use MatGyver\Commands\AutorespondersCommand;
use MatGyver\Commands\Aws\AwsEventsCommand;
use MatGyver\Commands\Aws\AwsNotificationCommand;
use MatGyver\Commands\ChatNotificationsCommand;
use MatGyver\Commands\Clients\CheckDataCommand;
use MatGyver\Commands\Clients\ClientsErrorsCommand;
use MatGyver\Commands\Clients\ClientsLimitsCommand;
use MatGyver\Commands\Clients\ClientsSoonExpireCommand;
use MatGyver\Commands\Clients\ClientsStatsCommand;
use MatGyver\Commands\Clients\ClientsSubscriptionsCommand;
use MatGyver\Commands\Clients\ClientsVerifsCommand;
use MatGyver\Commands\Clients\DeleteDataCommand;
use MatGyver\Commands\Dossier\DossierAlertCommand;
use MatGyver\Commands\Dossier\DossierCanceledCompressCommand;
use MatGyver\Commands\Dossier\DossierConvocationsReminderCommand;
use MatGyver\Commands\Dossier\DossierDocumentResizeCommand;
use MatGyver\Commands\Dossier\DossierDocumentsClamAvCommand;
use MatGyver\Commands\Dossier\DossierDocumentsResizeCommand;
use MatGyver\Commands\Dossier\DossierDocumentsThumbnailsCommand;
use MatGyver\Commands\Dossier\DossierDocumentTranscriptCommand;
use MatGyver\Commands\Dossier\DossierDocumentThumbnailCommand;
use MatGyver\Commands\Dossier\DossierExpertiseInvoiceReminderCommand;
use MatGyver\Commands\Dossier\DossierLastReportDateReminderCommand;
use MatGyver\Commands\Dossier\DossierNewConvocationsCommand;
use MatGyver\Commands\Dossier\DossierPictureResizeCommand;
use MatGyver\Commands\Dossier\DossierRemoveTempFilesCommand;
use MatGyver\Commands\Dossier\HistovecCommand;
use MatGyver\Commands\Game\GameDailyQuestsCommand;
use MatGyver\Commands\Import\ImportCommand;
use MatGyver\Commands\MailboxArchiveCommand;
use MatGyver\Commands\MailboxPublishCommand;
use MatGyver\Commands\MailHistoryPreviewCommand;
use MatGyver\Commands\MailQueueIdCommand;
use MatGyver\Commands\Shop\StripePayoutsCommand;
use MatGyver\Commands\Subscriptions\BraintreeSubscriptionCommand;
use MatGyver\Commands\Subscriptions\MollieSubscriptionCommand;
use MatGyver\Commands\Subscriptions\StripeSubscriptionCommand;
use MatGyver\Commands\Subscriptions\SubscriptionsVerifCommand;
use MatGyver\Commands\TrashCommand;
use MatGyver\Helpers\Aws\Action;
use MatGyver\Helpers\Aws\Message;

class ActionsService
{
    /**
     * @var array
     */
    public array $actions;

    public function __construct()
    {
        $this->actions = [];
        $this->addAction('aws_events', 'cron', AwsEventsCommand::class);
        $this->addAction('aws_notifications', 'bounces', AwsNotificationCommand::class);

        $this->addAction('braintree_subscriptions', 'abonnements', BraintreeSubscriptionCommand::class, '18 * * * *');
        $this->addAction('mollie_subscriptions', 'abonnements', MollieSubscriptionCommand::class, '24 * * * *');
        $this->addAction('stripe_subscriptions', 'abonnements', StripeSubscriptionCommand::class, '12 * * * *');
        $this->addAction('subscriptions_verifs', 'default', SubscriptionsVerifCommand::class, '10 6 * * *');

        $this->addAction('autoresponder_queue', 'default', AutorespondersCommand::class, '* * * * *');
        $this->addAction('check_datas', 'default', CheckDataCommand::class, '12 4 * * *');
        $this->addAction('clients_stats', 'default', ClientsStatsCommand::class, '45 5 * * *');
        $this->addAction('clients_limits', 'default', ClientsLimitsCommand::class, '45 6 * * *');
        $this->addAction('clients_verifs', 'default', ClientsVerifsCommand::class, '44 4 * * *');
        $this->addAction('clients_subscriptions', 'default', ClientsSubscriptionsCommand::class, '44 6 * * *');
        $this->addAction('clients_soon_expire', 'default', ClientsSoonExpireCommand::class, '46 6 * * *');
        $this->addAction('delete_data', 'default', DeleteDataCommand::class, '44 2 * * *');
        $this->addAction('expire_commissions', 'default', AffiliationExpireCommissionsCommand::class, '30 3 * * *');
        $this->addAction('expire_commissions_mail_alert', 'default', AffiliationExpireCommissionsMailAlertCommand::class, '0 4 */15 * *');
        $this->addAction('orders_errors', 'default', ClientsErrorsCommand::class, '55 4 * * *');
        $this->addAction('mail_queue', 'mail_queue', MailQueueIdCommand::class);
        $this->addAction('remove_objects', 'default', TrashCommand::class, '18 2 * * *');

        if (GAME_ACTIVATED) {
            $this->addAction('game_daily_quests', 'default', GameDailyQuestsCommand::class, '0 0 * * *');
        }

        if (CHAT_ACTIVATED) {
            $this->addAction('chat_notifications', 'default', ChatNotificationsCommand::class, '*/10 * * * *');
        }

        $this->addAction('import_to_process', 'default', ImportCommand::class);
        $this->addAction('dossier_documents_resize', 'default', DossierDocumentsResizeCommand::class);
        $this->addAction('dossier_document_transcript', 'default', DossierDocumentTranscriptCommand::class);
        $this->addAction('dossier_canceled_compress', 'default', DossierCanceledCompressCommand::class, '30 6 * * *');

        $this->addAction('histovec', 'default', HistovecCommand::class);
        $this->addAction('dossier_alert', 'default', DossierAlertCommand::class, '0 7 * * *');
        $this->addAction('dossier_reminder', 'default', DossierAlertCommand::class, '5 7 * * *');
        $this->addAction('dossier_new_convocations', 'default', DossierNewConvocationsCommand::class);
        $this->addAction('dossier_document_resize', 'default', DossierDocumentResizeCommand::class);
        $this->addAction('dossier_picture_resize', 'default', DossierPictureResizeCommand::class);
        $this->addAction('mail_history_preview', 'default', MailHistoryPreviewCommand::class);
        $this->addAction('dossier_document_thumbnail', 'default', DossierDocumentThumbnailCommand::class);
        $this->addAction('dossier_documents_thumbnails', 'default', DossierDocumentsThumbnailsCommand::class, '*/5 * * * *');
        $this->addAction('dossier_documents_virus_scans', 'default', DossierDocumentsClamAvCommand::class, '*/10 * * * *');
        $this->addAction('dossier_documents_remove_temp_files', 'default', DossierRemoveTempFilesCommand::class, '10 3 * * *');
        $this->addAction('dossier_convocations_reminder', 'default', DossierConvocationsReminderCommand::class, '15 6 * * *');
        $this->addAction('dossier_last_report_date_reminder', 'default', DossierLastReportDateReminderCommand::class, '30 6 * * *');
        $this->addAction('dossier_expertise_invoice_reminder', 'default', DossierExpertiseInvoiceReminderCommand::class, '35 6 * * *');
        $this->addAction('mailbox_archive', 'default', MailboxArchiveCommand::class, '0 6 * * *');
        $this->addAction('mailbox_publish', 'default', MailboxPublishCommand::class, '*/15 * * * *');

        $this->addAction('stripe_payouts', 'default', StripePayoutsCommand::class, '25 4 * * 2'); //every tuesday at 4:25
    }

    /**
     * @param string $name
     * @param string $queueName
     * @param string $className
     * @param string $cronExpression
     * @return void
     */
    public function addAction(string $name, string $queueName, string $className, string $cronExpression = ''): void
    {
        $this->actions[$name] = new Action($name, $queueName, $className, $cronExpression);
    }

    /**
     * @return Action[]
     */
    public function getActions(): array
    {
        return $this->actions;
    }

    /**
     * @param string $action
     * @return Action|null
     */
    public function getAction(string $action): ?Action
    {
        return ($this->actions[$action] ?? null);
    }

    /**
     * @param int $timestamp
     * @return void
     */
    public function startAllActions(int $timestamp = 0): void
    {
        if (!$timestamp) {
            $timestamp = time();
        }

        $date = new \DateTime("@" . $timestamp);

        $actions = $this->getActions();
        foreach ($actions as $action) {
            if (!$action->getCronExpression()) {
                continue;
            }

            $cron = new \Cron\CronExpression($action->getCronExpression());
            if (!$cron->isDue($date)) {
                continue;
            }

            $queue = $action->getQueue();
            $message = new Message($queue, $action->getName());
            $message->send();
            unset($message);
            unset($queue);
        }
    }
}
