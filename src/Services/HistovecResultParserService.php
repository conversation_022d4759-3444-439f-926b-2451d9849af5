<?php

namespace MatGyver\Services;

use MatGyver\Services\Logger\LoggerService;

/**
 * Class HistovecResultParserService
 * @package MatGyver\Services
 */
class HistovecResultParserService
{
    private \DOMDocument $doc;
    private int $nbColumns;
    private bool $removeTitle = false;
    private bool $removeColumnNames = false;
    private array $results = [];

    public function __construct(string $html = '', int $nbColumns = 3)
    {
        $this->setDoc($html);
        $this->setNbColumns($nbColumns);
    }

    /**
     * @return \DOMDocument
     */
    public function getDoc(): \DOMDocument
    {
        return $this->doc;
    }

    /**
     * @param string $html
     */
    public function setDoc(string $html): void
    {
        $this->doc = new \DOMDocument();
        $this->doc->loadHTML('<?xml encoding="utf-8" ?>' . $html);
    }

    /**
     * @return int
     */
    public function getNbColumns(): int
    {
        return $this->nbColumns;
    }

    /**
     * @param int $nbColumns
     */
    public function setNbColumns(int $nbColumns): void
    {
        $this->nbColumns = $nbColumns;
    }

    /**
     * @return bool
     */
    public function getRemoveTitle(): bool
    {
        return $this->removeTitle;
    }

    /**
     * @param bool $removeTitle
     */
    public function setRemoveTitle(bool $removeTitle): void
    {
        $this->removeTitle = $removeTitle;
    }

    /**
     * @return bool
     */
    public function getRemoveColumnNames(): bool
    {
        return $this->removeColumnNames;
    }

    /**
     * @param bool $removeColumnNames
     */
    public function setRemoveColumnNames(bool $removeColumnNames): void
    {
        $this->removeColumnNames = $removeColumnNames;
    }

    /**
     * @return array
     */
    public function parse(): array
    {
        $this->showDOMNode($this->doc);

        //format results
        $finalResults = [];

        //LoggerService::logError(json_encode($this->results));

        //first value contains full parsed html content
        //array_shift($this->results);

        if ($this->getRemoveTitle()) {
            //remove title "Historique des opérations en France"
            array_shift($this->results);
        }

        while (true) {
            $output = array_splice($this->results, 0, $this->nbColumns);
            if (!$output) {
                break;
            }
            $finalResults[] = $output;
        }

        if ($this->getRemoveColumnNames()) {
            //remove columns names
            array_shift($finalResults);
        }

        return $finalResults;
    }

    /**
     * @param \DOMNode $domNode
     * @return void
     */
    public function showDOMNode(\DOMNode $domNode): void
    {
        foreach ($domNode->childNodes as $node) {
            if (!$this->results and !$node->nodeValue) {
                continue;
            }

            if ($node->nodeName == 'td') {
                $this->results[] = trim($node->nodeValue);
            }

            if ($node->hasChildNodes()) {
                $this->showDOMNode($node);
            }
        }
    }
}
