<?php

namespace MatGyver\Services;

use fXmlRpc\Client;
use fXmlRpc\Transport\PsrTransport;
use GuzzleHttp\Psr7\HttpFactory;
use MatGyver\Services\Logger\LoggerService;
use Supervisor\Supervisor;

/**
 * Class CheckRequirementsService
 * @package MatGyver\Services
 */
class CheckRequirementsService
{
    /**
     *
     */
    const PHP_VERSION = "7.1";

    /**
     *
     */
    public static function checkAll(): void
    {
        self::checkPhpVersion();
        self::checkComposer();
        self::checkGlobalConfig();
        self::checkImagesFolder();
        self::checkRequiredFolders();
        self::checkDomainAccess();
        self::checkSession();
        self::checkViewParser();
        self::checkSupervisord();
    }

    /**
     * @param bool $output
     * @return bool
     */
    public static function healthCheckHttp(bool $output = false): bool
    {
        if (self::checkPhpVersion(self::PHP_VERSION, $output) === true
            && self::checkComposer($output) === true
            && self::checkGlobalConfig($output) === true
            && self::checkImagesFolder($output) === true
            && self::checkSession($output) === true
        ) {
            return true;
        }
        return false;
    }

    /**
     * @param bool $output
     * @return bool
     */
    public static function healthCheckWorker(bool $output = false): bool
    {
        if (self::checkPhpVersion(self::PHP_VERSION, $output) === true
            && self::checkComposer($output) === true
            && self::checkGlobalConfig($output) === true
            && self::checkImagesFolder($output) === true
            && self::checkSupervisord($output) === true
        ) {
            return true;
        }
        return false;
    }

    /**
     * @param string $version
     * @param bool $output
     * @return bool
     */
    public static function checkPhpVersion(string $version = self::PHP_VERSION, bool $output = true): bool
    {
        $result = phpversion() >= $version;
        if ($output === true) {
            echo "\e[33mPHP version\e[0m (required >= " . self::PHP_VERSION . ") : ";
            echo self::formatResult($result, 'OK ' . phpversion());
            echo "\n";
        }
        if (!$result) {
            LoggerService::logError('HealthCheck KO: checkPhpVersion');
        }

        return $result;
    }

    /**
     * @param bool $output
     * @return bool
     */
    public static function checkGlobalConfig(bool $output = true): bool
    {
        $result = is_file(CONFIG_PATH . '/config.php');
        if ($output === true) {
            echo "\e[33mGlobal config file\e[0m : ";
            echo self::formatResult($result, 'OK', 'KO => Please copy ' . CONFIG_PATH . '/config.php.env to config.php and update it');
            echo "\n";
        }
        if (!$result) {
            LoggerService::logError('HealthCheck KO: checkGlobalConfig');
        }
        return $result;
    }

    /**
     * @param bool $output
     * @return bool
     */
    public static function checkComposer(bool $output = true): bool
    {
        $result = is_dir(FULL_DOCUMENT_ROOT . '/vendor');
        if ($output === true) {
            echo "\e[33mComposer\e[0m : ";
            echo self::formatResult($result, 'OK', 'KO => Please run "composer install"');
            echo "\n";
        }
        if (!$result) {
            LoggerService::logError('HealthCheck KO: checkComposer');
        }
        return $result;
    }

    /**
     * @param bool $output
     * @return bool
     */
    public static function checkImagesFolder(bool $output = true): bool
    {
        $result = is_dir(IMAGES_PATH);
        if ($output === true) {
            echo "\e[33mImages folder\e[0m : ";
            echo self::formatResult($result, 'OK', 'KO => Please create images folder ' . IMAGES_PATH);
            echo "\n";
        }
        if (!$result) {
            LoggerService::logError('HealthCheck KO: checkImagesFolder');
        }
        return $result;
    }

    /**
     * @param bool $output
     * @return bool
     */
    public static function checkRequiredFolders(bool $output = true): bool
    {
        $mainResult = true;
        $folders = [
            'var/sessions',
            'var/cache/',
            'var/cache/ckfinder',
            'var/cache/cron_locks',
            'var/logs',
            'web/lib/upload/credit_notes',
            'web/lib/upload/invoices',
            'web/lib/upload/files',
        ];
        $errorMessages = [];
        foreach ($folders as $folder) {
            if ($output === true) {
                echo "\e[33mFolder $folder\e[0m : ";
            }

            $result = is_dir(FULL_DOCUMENT_ROOT . '/' . $folder . '/');
            if (!$result) {
                $errorMessages[] = $folder;
                if ($output === true) {
                    echo self::formatResult($result, 'OK', 'KO => Please create folder ' . $folder);
                    echo "\n";
                }
                $mainResult = false;
            } else {
                $result = substr(sprintf('%o', fileperms(FULL_DOCUMENT_ROOT . '/' . $folder . '/')), -4) == "0777";
                if ($output === true) {
                    echo self::formatResult($result, 'Folder writeable', 'KO => Please make the folder ' . $folder . ' writeable');
                    echo "\n";
                }
                if (!$result) {
                    $errorMessages[] = $folder;
                    $mainResult = false;
                }
            }
        }
        if (!$mainResult) {
            LoggerService::logError('HealthCheck KO: checkRequiredFolders - ' . implode(' - ', $errorMessages));
        }
        return $mainResult;
    }

    /**
     * @param bool $output
     * @return bool
     */
    public static function checkDomainAccess(bool $output = true): bool
    {
        $url = preg_replace('/:[0-9]+/', '', APP_DOMAIN);
        $ping = self::ping($url);
        $result = (bool)$ping;
        if ($output === true) {
            echo "\e[33mDomain\e[0m : ";
            echo self::formatResult($result, 'OK ' . $url . ' is reachable', 'KO => ' . $url . ' is not reachable');
            echo "\n";
        }
        if (!$result) {
            LoggerService::logError('HealthCheck KO: checkDomainAccess');
        }
        return $result;
    }

    /**
     * @param bool $output
     * @return bool
     */
    public static function checkSession(bool $output = true): bool
    {
        $result = (bool)session_id();
        if ($output === true) {
            echo "\e[33mSession\e[0m : ";
            echo self::formatResult($result, 'OK', 'KO => Session do not work');
            echo "\n";
        }
        if (!$result) {
            LoggerService::logError('HealthCheck KO: checkSession');
        }
        return $result;
    }

    /**
     * @param bool $output
     * @return bool
     */
    public static function checkViewParser(bool $output = true): bool
    {
        $parser = TwigService::getInstance();
        $parser->set('var', 'hello');
        $string = $parser->render('common/check_parser.html.twig');
        $result = trim($string) === 'hello';
        if ($output === true) {
            echo "\e[33mView Parser\e[0m : ";
            echo self::formatResult($result, 'OK', 'KO => Parser do not work');
            echo "\n";
        }
        if (!$result) {
            LoggerService::logError('HealthCheck KO: checkViewParser');
        }
        return $result;
    }

    /**
     * @param bool $output
     * @return bool
     */
    public static function checkSupervisord(bool $output = true): bool
    {
        $guzzleClient = new \GuzzleHttp\Client();
        $client = new Client(
            'http://127.0.0.1:9001/RPC2',
            new PsrTransport(
                new HttpFactory(),
                $guzzleClient
            )
        );
        $supervisor = new Supervisor($client);

        $result = true;
        $errorMessage = null;
        foreach ($supervisor->getAllProcesses() as $process) {
            if (!$process->isRunning()) {
                $result = false;
                $errorMessage = "Supervisor's process " . $process->getName() . ' is not running.';
                break;
            }
        }

        if ($output === true) {
            echo "\e[33mSupervisor daemon\e[0m : ";
            echo self::formatResult($result, 'OK', 'KO => ' . $errorMessage);
            echo "\n";
        }
        if (!$result) {
            LoggerService::logError('HealthCheck KO: checkSupervisord - ' . $errorMessage);
        }
        return $result;
    }

    /**
     * @param bool $result
     * @param string $successText
     * @param string $failText
     * @return string
     */
    private static function formatResult(bool $result, string $successText = 'OK', string $failText = 'KO'): string
    {
        return $result === true ? "\e[32m$successText\e[0m" : "\e[41m$failText\e[0m";
    }

    /**
     * @param string $host
     * @return bool
     */
    private static function ping(string $host): bool
    {
        exec(sprintf('ping -c 1 -W 5 %s', escapeshellarg($host)), $res, $rval);
        return $rval === 0;
    }
}
