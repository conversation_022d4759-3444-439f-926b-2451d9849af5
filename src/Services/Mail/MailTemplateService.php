<?php

namespace MatGyver\Services\Mail;

use Doctrine\ORM\EntityManager;
use Mat<PERSON><PERSON><PERSON>\Components\Mailer\MailRender;
use MatG<PERSON><PERSON>\Components\Mailer\MailRenderer;
use MatG<PERSON>ver\Entity\Dossier\Dossier;
use MatG<PERSON>ver\Entity\Mail\Template\MailTemplate;
use MatGyver\Enums\MailTemplateTypeEnum;
use MatGyver\Helpers\TextHelper;
use MatGyver\Repository\Mail\Template\MailTemplateRepository;
use MatGyver\Services\BaseEntityService;
use MatGyver\Services\Logger\LoggerService;

/**
 * Class MailTemplateService
 * @package MatGyver\Services\Mail
 * @property MailTemplateRepository $repository
 * @method MailTemplateRepository getRepository()
 */
class MailTemplateService extends BaseEntityService
{
    const MAIL_TEMPLATES_PATH = '/common/mails/templates/';

    private TextHelper $textHelper;
    private MailRenderer $mailRenderer;

    /**
     * MailTemplateService constructor.
     * @param EntityManager $em
     * @param TextHelper $textHelper
     * @param MailRenderer $mailRenderer
     */
    public function __construct(
        EntityManager $em,
        TextHelper $textHelper,
        MailRenderer $mailRenderer
    ) {
        $this->em = $em;
        $this->repository = $em->getRepository(MailTemplate::class);
        $this->textHelper = $textHelper;
        $this->mailRenderer = $mailRenderer;
    }

    /**
     * @param string $identifier
     * @param array $vars
     * @return MailRender|null
     */
    public function prepareMailTemplate(string $identifier, array $vars = []): ?MailRender
    {
        $template = $this->repository->findOneBy(['identifier' => $identifier]);
        if (!$template) {
            LoggerService::logError(sprintf('Template %s does not exists.', $identifier));
            return null;
        }

        $subject = $template->getSubject();
        $body = $template->getBody();

        $vars = $this->addDefaultVars($vars);
        $vars = $this->addBodyVars($vars, $body);

        $subject = $this->textHelper->replaceVars($subject, $vars);
        $body = $this->textHelper->replaceVars($body, $vars);

        return new MailRender($subject, $body);
    }

    /**
     * @param array $vars
     * @return array
     */
    private function addDefaultVars(array $vars): array
    {
        if (!isset($vars['APP_NAME'])) {
            $vars['APP_NAME'] = APP_NAME;
        }
        if (!isset($vars['APP_URL'])) {
            $vars['APP_URL'] = APP_URL;
        }
        if (!isset($vars['APP_URL_APP']) and defined('APP_CLIENT_URL')) {
            $vars['APP_URL_APP'] = APP_CLIENT_URL . '/app/index/';
        }
        if (!isset($vars['APP_URL_SUPPORT'])) {
            $vars['APP_URL_SUPPORT'] = APP_URL . '/help/';
        }
        if (!isset($vars['APP_URL_RESET_PASSWORD']) and defined('APP_CLIENT_URL')) {
            $vars['APP_URL_RESET_PASSWORD'] = APP_CLIENT_URL . '/reset/';
        }
        return $vars;
    }

    /**
     * @param array $vars
     * @param string $body
     * @return array
     */
    private function addBodyVars(array $vars, string $body): array
    {
        if (str_contains($body, '[[PASSWORD]]') and !isset($vars['PASSWORD'])) {
            $vars['PASSWORD'] = '<em>' . __('Votre mot de passe défini lors de votre inscription') . '</em>';
        }
        return $vars;
    }

    /**
     * @param string $subject
     * @param string $body
     * @param string $type
     * @param array $data
     * @param Dossier|null $dossier
     * @return MailRender
     */
    public function getMailLayout(string $subject, string $body, string $type, array $data = [], ?Dossier $dossier = null): MailRender
    {
        $template = $this->getMailLayoutPath($type);
        $mailVars = $this->getMailVars($type, $dossier);
        $mailVars = array_merge($mailVars, $data);

        return $this->mailRenderer->render($template, $subject, $body, $mailVars);
    }

    /**
     * @param string $type
     * @return string
     */
    public function getMailLayoutPath(string $type): string
    {
        return self::MAIL_TEMPLATES_PATH . $type . '.php';
    }

    /**
     * @param string $type
     * @param Dossier|null $dossier
     * @return array
     */
    public function getMailVars(string $type, ?Dossier $dossier = null): array
    {
        $vars = [];
        if ($dossier and $dossier->isJudiciaire()) {
            $design = [
                'elements' => [
                    'logo' => 'off',
                    'email-poweredby' => 'off'
                ],
            ];
            $vars['design'] = $design;
        } elseif ($type == MailTemplateTypeEnum::MAIL_TEMPLATE_CLIENT or $type == MailTemplateTypeEnum::MAIL_TEMPLATE_SYSTEM) {
            $design = [
                'elements' => [
                    'logo' => 'on',
                    'email-poweredby' => 'on'
                ],
            ];
            $vars['design'] = $design;
        }

        return $vars;
    }
}
