<?php

namespace MatGyver\Services\Pages\Site;

use MatGyver\Helpers\Tools;
use MatGyver\Services\Pages\PagesService;

abstract class AbstractPagesSiteService extends PagesService
{
    /**
     * AbstractPagesSiteService constructor.
     */
    public function __construct()
    {
        parent::__construct();
    }

    protected function addSimilarPages(): void
    {
        $this->setSimilarPages([
            'tos' => [
                'class' => SiteTosService::class,
                'title' => __('Conditions Générales de Vente'),
                'link' => Tools::makeLink('admin', 'page_editor', 'site_tos'),
            ],
            'cgu' => [
                'class' => SiteCguService::class,
                'title' => __('Conditions Générales d\'Utilisation'),
                'link' => Tools::makeLink('admin', 'page_editor', 'site_cgu'),
            ],
            'privacy' => [
                'class' => SitePrivacyService::class,
                'title' => __('Politique de confidentialité'),
                'link' => Tools::makeLink('admin', 'page_editor', 'site_privacy'),
            ],
            'legal_notice' => [
                'class' => SiteLegalNoticeService::class,
                'title' => __('Mentions Légales'),
                'link' => Tools::makeLink('admin', 'page_editor', 'site_legal_notice'),
            ],
            'dpa' => [
                'class' => SiteDpaService::class,
                'title' => __('Traitement des données'),
                'link' => Tools::makeLink('admin', 'page_editor', 'site_dpa'),
            ],
        ]);
    }
}
