<?php

namespace MatGyver\Services\Pages\Users;

use MatGyver\Helpers\Country;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Users\UserConfigService;
use MatGyver\Services\Users\UsersRgpdService;
use MatGyver\Services\Users\UsersService;

class UsersProfilService extends AbstractPagesUsersService
{
    private UsersService $usersService;
    private UsersRgpdService $usersRgpdService;
    private UserConfigService $userConfigService;

    /**
     * UsersProfilService constructor.
     * @param UsersService $usersService
     * @param UsersRgpdService $usersRgpdService
     * @param UserConfigService $userConfigService
     */
    public function __construct(
        UsersService $usersService,
        UsersRgpdService $usersRgpdService,
        UserConfigService $userConfigService
    ) {
        $this->usersService = $usersService;
        $this->usersRgpdService = $usersRgpdService;
        $this->userConfigService = $userConfigService;
        $this->addSimilarPages();
        parent::__construct();

        $this->setTitle(__('Informations personnelles'));
        $this->setReturnLink(Tools::makeLink('admin', 'settings', 'pages'));
        $this->setEditorLink(Tools::makeLink('admin', 'page_editor', 'user'));
    }

    /**
     * @return string
     */
    public function getContent(): string
    {
        $user = $this->getUser();
        if (!$user) {
            return '';
        }

        $rgpdDate = '';
        $rgpdChecked = '';
        $rgpdAffDate = '';
        $rgpdAffChecked = '';

        if ($user->getRgpd()) {
            $rgpdChecked = 'checked';
            $history = $this->usersRgpdService->getFirstHistory($user->getId(), 'normal');
            if ($history) {
                $rgpdDate = $history->getDate()->format('Y-m-d H:i:s');
            }
        }

        if ($user->getRgpdAff()) {
            $rgpdAffChecked = 'checked';
            $history = $this->usersRgpdService->getFirstHistory($user->getId(), 'aff');
            if ($history) {
                $rgpdAffDate = $history->getDate()->format('Y-m-d H:i:s');
            }
        }

        $userConfig = $this->userConfigService->getUserConfig($user);

        $country = DEFAULT_COUNTRY;
        if (isset($userConfig['country'])) {
            $country = $userConfig['country'];
        }
        $selectCountry = Country::generateSelectCountry($country);

        $avatar = $this->usersService->getAvatar($user->getEmail(), 100, $user->getClient()->getId());

        return $this->parser->set('user', $user)
            ->set('rgpdDate', $rgpdDate)
            ->set('rgpdChecked', $rgpdChecked)
            ->set('rgpdAffDate', $rgpdAffDate)
            ->set('rgpdAffChecked', $rgpdAffChecked)
            ->set('userConfig', $userConfig)
            ->set('selectCountry', $selectCountry)
            ->set('avatar', $avatar)
            ->render('pages/users/user-profile.php');
    }
}
