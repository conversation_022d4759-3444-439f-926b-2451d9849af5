<?php

namespace MatGyver\Services\Pages\HelpCenter;

use MatG<PERSON>ver\Entity\Help\Article\HelpArticle;
use MatG<PERSON>ver\Entity\Help\Universe\HelpUniverse;
use MatGyver\Factories\BlankStates\NoResultsBlankState;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Help\HelpArticlesService;
use MatGyver\Services\Help\HelpService;
use MatGyver\Services\Pages\PagesService;
use MatGyver\Services\TwigService;

abstract class AbstractHelpCenterEditorService extends PagesService
{
    /**
     * AbstractHelpCenterEditorService constructor.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @param HelpUniverse|null $universe
     * @param HelpArticle|null $article
     * @return void
     */
    protected function addSimilarPages(?HelpUniverse $universe = null, ?HelpArticle $article = null): void
    {
        $similarPages = [
            'help' => [
                'class' => HelpCenterEditorService::class,
                'title' => __('Centre d\'aide'),
                'link' => Tools::makeLink(($_SESSION['client']['id'] == CLIENT_MASTER ? 'admin' : 'app'), 'help_center', 'editor'),
            ],
        ];
        if ($universe) {
            $similarPages['help_category'] = [
                'class' => HelpCenterUniverseEditorService::class,
                'title' => $universe->getName(),
                'link' => Tools::makeLink(($_SESSION['client']['id'] == CLIENT_MASTER ? 'admin' : 'app'), 'help_center', 'universe/editor/' . $universe->getId()),
            ];
            $similarPages['entity'] = [
                'id' => $universe->getId(),
                'name' => $universe->getName(),
            ];
        }
        if ($article) {
            $similarPages['help_article'] = [
                'class' => HelpCenterArticleEditorService::class,
                'title' => $article->getTitle(),
                'link' => Tools::makeLink(($_SESSION['client']['id'] == CLIENT_MASTER ? 'admin' : 'app'), 'help_center', 'article/editor/' . $article->getId()),
            ];
            $similarPages['entity'] = [
                'id' => $article->getId(),
                'title' => $article->getTitle(),
                'active' => $article->getActive(),
            ];
        }
        $this->setSimilarPages($similarPages);
    }

    /**
     * @return void
     */
    public function addRequiredFiles(): void
    {
        Assets::addCss('plugins/datatables.bundle.css');
        Assets::addJs('plugins/datatables/datatables.bundle.js');
        Assets::addJs('app/editor/help_articles.js');
        Assets::addJs('app/editor/help_categories.js');
        Assets::addInlineJs(TwigService::getInstance()->render('pages/components/modals/help_article.php'));
        Assets::addInlineJs(TwigService::getInstance()->render('pages/components/modals/help_articles.php'));
        Assets::addInlineJs(TwigService::getInstance()->render('pages/components/modals/help_category.php'));
        Assets::addInlineJs(TwigService::getInstance()->render('pages/components/modals/help_categories.php'));
    }

    /**
     * @param string $search
     * @param HelpUniverse|null $universe
     * @param int $idPage
     * @return string
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    protected function renderSearch(string $search, ?HelpUniverse $universe = null, int $idPage = 1): string
    {
        $articles = $this->container->get(HelpArticlesService::class)->getRepository()->searchArticles($search);
        if (!$articles) {
            $title = __('Aucun résultat');
            $message = __('Nous n\'avons trouvé aucun article correspondant. Veuillez poursuivre votre recherche avec une nouvelle requête.');
            $buttonLink = Tools::makeLink('site', 'support', 'ticket/create');
            $buttonText = __('Contacter le support');
            $factory = new NoResultsBlankState($title, $message, $buttonLink, $buttonText);
            $content = '<div class="col-md-6 offset-3">' . $factory->render() . '</div>';

            $toolbar = $this->container->get(HelpService::class)->getInboxToolbar($universe, 'site', 0, $idPage, $search);
            $output = $this->container->get(HelpService::class)->renderLayout($content, $toolbar, 'site', $universe);
            return $output;
        }

        return $this->container->get(HelpService::class)->renderArticles($articles, 'site', $search, $universe);
    }
}
