<?php

namespace MatGyver\Services;

use Doctrine\ORM\EntityManager;
use MatGyver\Entity\Country\Country;
use MatGyver\Repository\Country\CountryRepository;

/**
 * Class CountriesService
 * @package MatGyver\Services
 * @property CountryRepository $repository
 * @method CountryRepository getRepository()
 */
class CountriesService extends BaseEntityService
{
    /**
     * CountriesService constructor.
     * @param EntityManager $entityManager
     */
    public function __construct(EntityManager $entityManager)
    {
        $this->em = $entityManager;
        $this->repository = $this->em->getRepository(Country::class);
    }

    /**
     * @return Country[]
     */
    public function getCountries(): array
    {
        return $this->repository->findBy([], ['name' => 'ASC']);
    }

    /**
     * @param string $countryCode
     * @return Country|null
     */
    public function getCountryByCode(string $countryCode): ?Country
    {
        return $this->repository->findOneBy(['code' => $countryCode]);
    }

    /**
     * @param string $countryName
     * @return Country|null
     */
    public function getCountryByName(string $countryName): ?Country
    {
        return $this->repository->findOneBy(['name' => $countryName]);
    }
}
