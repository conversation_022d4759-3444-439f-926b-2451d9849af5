<?php

namespace MatGyver\Services\Home;

use MatGyver\Entity\News\News;
use MatGyver\Services\News\NewsContentService;
use MatGyver\Services\TwigService;

/**
 * Class HomeNewsService
 * @package MatGyver\Services\Home
 */
class HomeNewsService
{
    /**
     * @var TwigService
     */
    private $parser;

    /**
     * @var NewsContentService
     */
    public $newsContentService;

    /**
     * HomeNewsService constructor.
     * @param NewsContentService $newsContentService
     */
    public function __construct(NewsContentService $newsContentService)
    {
        $this->parser = TwigService::getInstance();
        $this->newsContentService = $newsContentService;
    }

    /**
     * @return string
     */
    public function renderNotification(): ?string
    {
        return $this->newsContentService->renderHomeNews(News::TYPE_NOTIFICATION);
    }

    /**
     * @return string
     */
    public function renderFlash(): ?string
    {
        return $this->newsContentService->renderHomeNews(News::TYPE_FLASH);
    }

    /**
     * @return string
     */
    public function renderNews(): string
    {
        $news = $this->newsContentService->renderHomeNews(News::TYPE_NORMAL, 2, true, 0);
        if ($news) {
            return $this->parser->set('news', $news)
                ->render('app/home/<USER>');
        }
        return '';
    }
}
