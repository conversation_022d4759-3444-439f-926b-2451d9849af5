<?php

namespace MatGyver\Services\Home;

use MatGyver\Entity\PaymentMethod\PaymentMethod;
use MatGyver\Helpers\Encryption;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Clients\ClientsService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\PaymentsMethodsService;
use MatGyver\Services\TwigService;

/**
 * Class HomeAlertsService
 * @package MatGyver\Services\Home
 */
class HomeAlertsService
{
    /**
     * @var ClientsService
     */
    private $clientsService;

    /**
     * @var TwigService
     */
    private $parser;

    /**
     * HomeAlertsService constructor.
     * @param ClientsService $clientsService
     */
    public function __construct(ClientsService $clientsService)
    {
        $this->clientsService = $clientsService;
        $this->parser = TwigService::getInstance();
    }

    /**
     * @return string
     */
    public function render(): string
    {
        $alerts = $this->getAlerts();
        return $this->parser->set('alerts', $alerts)
            ->render('app/home/<USER>');
    }

    /**
     * @return array
     */
    public function getAlerts(): array
    {
        $alerts = [];
        $now = new \DateTime();
        $now->setTime(0, 0, 0);

        $client = $this->clientsService->getClient();

        if ($client->getDateEndSubscription()->getTimestamp() > 0) {
            $dateEndSubscription = $client->getDateEndSubscription();
            $nbDays = $dateEndSubscription->diff($now)->days;

            if ($now > $dateEndSubscription) {
                //date_end_subscription peut être supérieure à la date d'aujourd'hui mais alerte !
                $alerts[] = array(
                    'class' => (!$client->getRecurring() ? 'danger' : ($nbDays <= 2 ? 'warning' : 'danger')),
                    'icon' => 'fas fa-exclamation-triangle',
                    'time' => time(),
                    'title' => __('Votre abonnement est expiré.'),
                    'content' => __('Attention : votre abonnement est expiré.') . ' ' . __('Cliquez sur le bouton ci-dessous pour souscrire à un nouvel abonnement.'),
                    'link' => Tools::makeLink('app', 'billing', 'plans'),
                    'linkText' => __('Souscrire à un nouvel abonnement'),
                );
            } elseif (!$client->getRecurring() and $nbDays <= 5) {
                //on n'est pas en abonnement récurrent : date_fin_abonnement ne peut être supérieure à la date d'aujourd'hui
                $alerts[] = array(
                    'class' => ($nbDays <= 2 ? 'danger' : 'warning'),
                    'icon' => 'fas fa-exclamation-triangle',
                    'time' => time(),
                    'title' => n__('Votre abonnement expire dans %d jour', 'Votre abonnement expire dans %d jours', $nbDays, $nbDays),
                    'content' => n__('Attention : votre abonnement expire dans %d jour', 'Attention : votre abonnement expire dans %d jours', $nbDays, $nbDays) . ' ' . __('Cliquez sur le bouton ci-dessous pour souscrire à un nouvel abonnement.'),
                    'link' => Tools::makeLink('app', 'billing', 'plans'),
                    'linkText' => __('Souscrire à un nouvel abonnement'),
                );
            }
        }

        if (!$client->getActive()) {
            $alerts[] = array(
                'class' => 'danger',
                'icon' => 'fas fa-exclamation-triangle',
                'time' => time(),
                'title' => __('Votre compte est désactivé.'),
                'content' => __('Attention : votre compte est désactivé.') . ' ' . __('Cliquez sur le bouton ci-dessous pour souscrire à un nouvel abonnement.'),
                'link' => Tools::makeLink('app', 'billing', 'plans'),
                'linkText' => __('Souscrire à un nouvel abonnement'),
            );
        }

        $clients = [69];
        if (in_array($_SESSION['client']['id'], $clients)) {
            $container = ContainerBuilderService::getInstance();
            $rcpjAccount = 26;
            $hasRcpjAccount = false;
            $paymentMethods = $container->get(PaymentsMethodsService::class)->getRepository()->findAll();
            if ($paymentMethods) {
                foreach ($paymentMethods as $paymentMethod) {
                    if ($paymentMethod->getAccount()->getId() == $rcpjAccount) {
                        $hasRcpjAccount = true;
                        break;
                    }
                }

                if (!$hasRcpjAccount) {
                    $token = [
                        'id_client' => $_SESSION['client']['id'],
                        'email' => $_SESSION['user']['email'],
                        'id_account' => $rcpjAccount,
                    ];

                    $tokenCrypt = Encryption::encrypt(json_encode($token));
                    $link = Tools::makeLink((SUBDOMAIN_ENABLED ? 'site' : 'app'), 'stripe', 'add_card', 'token=' . $tokenCrypt);

                    $alerts[] = array(
                        'class' => 'primary',
                        'icon' => 'fas fa-exclamation-triangle',
                        'time' => time(),
                        'title' => __('Votre CB doit être mise à jour.'),
                        'content' => __('Votre numéro de carte bancaire doit être mis à jour pour le renouvellement automatiquement de votre abonnement.') . '<br>' . __('Cliquez sur le bouton ci-dessous pour mettre à jour votre CB.'),
                        'link' => $link,
                        'linkText' => __('Mettre à jour ma CB'),
                    );
                }
            }
        }

        return $alerts;
    }
}
