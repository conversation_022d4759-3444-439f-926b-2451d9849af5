<?php

namespace MatG<PERSON>ver\Services\Game;

use Doctrine\ORM\EntityManager;
use Mat<PERSON><PERSON><PERSON>\Entity\Game\GameBadge;
use Mat<PERSON>yver\Entity\Game\GameBadgeClient;
use MatGyver\Repository\Game\GameBadgeClientRepository;
use MatGyver\Services\BaseEntityService;
use MatGyver\Services\Clients\ClientsService;
use MatGyver\Services\TwigService;

/**
 * Class GameBadgeClientService
 * @package MatGyver\Services\Game
 * @property GameBadgeClientRepository $repository
 * @method GameBadgeClientRepository getRepository()
 */
class GameBadgeClientService extends BaseEntityService
{
    /**
     * @var ClientsService
     */
    private $clientsService;

    /**
     * @var GameBagService
     */
    private $gameBagService;

    /**
     * @var GameLevelClientService
     */
    private $gameLevelClientService;

    /**
     * GameBadgeClientService constructor.
     * @param EntityManager $em
     * @param ClientsService $clientsService
     * @param GameBagService $gameBagService
     * @param GameLevelClientService $gameLevelClientService
     */
    public function __construct(
        EntityManager $em,
        ClientsService $clientsService,
        GameBagService $gameBagService,
        GameLevelClientService $gameLevelClientService
    ) {
        $this->em = $em;
        $this->repository = $em->getRepository(GameBadgeClient::class);
        $this->clientsService = $clientsService;
        $this->gameBagService = $gameBagService;
        $this->gameLevelClientService = $gameLevelClientService;
    }

    /**
     * @param GameBadge $gameBadge
     * @return array
     */
    public function insert(GameBadge $gameBadge): array
    {
        $gameBadgeClient = new GameBadgeClient();
        $gameBadgeClient->setClient($this->clientsService->getClient());
        $gameBadgeClient->setBadge($gameBadge);
        $gameBadgeClient->setClaimed(false);
        $gameBadgeClient->setDate(new \DateTime());
        try {
            $this->persistAndFlush($gameBadgeClient);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __("Erreur lors de l'enregistrement du badge.")];
        }

        return ['valid' => true];
    }

    /**
     * @param GameBadge $badge
     * @return string
     */
    public function renderBadgeRewardsModal(GameBadge $badge): string
    {
        return TwigService::getInstance()->set('badge', $badge)
            ->render('app/game/partials/modal-badge.php');
    }

    /**
     * @param GameBadgeClient $badgeClient
     * @return array
     */
    public function claimRewards(GameBadgeClient $badgeClient): array
    {
        $rewards = $badgeClient->getBadge()->getRewards();
        if ($rewards) {
            $addRewards = $this->gameBagService->addRewards($rewards, 'badge ' . $badgeClient->getBadge()->getName());
            if (!$addRewards['valid']) {
                return $addRewards;
            }
        }

        $badgeClient->setClaimed(true);
        $badgeClient->setDateClaimed(new \DateTime());
        try {
            $this->persistAndFlush($badgeClient);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __("Impossible de mettre à jour ce badge.")];
        }

        $items = [];
        $bag = $this->gameBagService->getRepository()->findAll();
        if ($bag) {
            foreach ($bag as $bagItem) {
                $items[$bagItem->getId()] = $bagItem->getNb();
            }
        }

        if ($badgeClient->getBadge()->getNbPoints()) {
            $increase = $this->gameLevelClientService->increase($badgeClient->getBadge()->getNbPoints());
            if (!$increase['valid']) {
                return $increase;
            }
            return array_merge(['items' => $items], $increase);
        }

        return ['valid' => true, 'items' => $items];
    }
}