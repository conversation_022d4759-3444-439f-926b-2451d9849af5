<?php

namespace MatGyver\Services\Game;

use Doctrine\ORM\EntityManager;
use Mat<PERSON><PERSON><PERSON>\Entity\Game\GameLevel;
use Mat<PERSON><PERSON><PERSON>\Entity\Game\GameLevelClient;
use MatGyver\Factories\Game\GameLevelClientFactory;
use MatG<PERSON>ver\Repository\Game\GameLevelClientRepository;
use MatGyver\Services\BaseEntityService;
use MatGyver\Services\TwigService;

/**
 * Class GameLevelClientService
 * @package MatGyver\Services\Game
 * @property GameLevelClientRepository $repository
 * @method GameLevelClientRepository getRepository()
 */
class GameLevelClientService extends BaseEntityService
{
    /**
     * @var GameLevelService
     */
    private $gameLevelService;

    /**
     * GameLevelClientService constructor.
     * @param EntityManager $em
     * @param GameLevelService $gameLevelService
     */
    public function __construct(
        EntityManager $em,
        GameLevelService $gameLevelService
    ) {
        $this->em = $em;
        $this->repository = $em->getRepository(GameLevelClient::class);
        $this->gameLevelService = $gameLevelService;
    }

    /**
     * @return GameLevelClient|null
     */
    public function getCurrent(): ?GameLevelClient
    {
        return $this->repository->findOneBy(['status' => GameLevel::STATUS_CURRENT]);
    }

    /**
     * @param int $nbPoints
     * @return array
     */
    public function increase(int $nbPoints): array
    {
        $gameLevelClient = $this->repository->findOneBy(['status' => GameLevel::STATUS_CURRENT]);
        if (!$gameLevelClient) {
            $lastLevel = $this->gameLevelService->getLast();
            if ($lastLevel) {
                $lastLevelClient = $this->repository->findOneBy(['level' => $lastLevel]);
                if ($lastLevelClient) {
                    //all levels completed
                    return ['valid' => true];
                }
            }

            $firstLevel = $this->gameLevelService->getFirst();
            if (!$firstLevel) {
                return ['valid' => false, 'message' => 'No level found'];
            }
            $gameLevelClient = GameLevelClientFactory::createGameClientLevel($firstLevel);
        }

        $actualPoints = $gameLevelClient->getPoints();
        $newLevel = null;
        $fromLevel = $gameLevelClient->getLevel()->getId();
        while (true) {
            $newPoints = $actualPoints + $nbPoints;
            if ($newPoints < $gameLevelClient->getLevel()->getPoints()) {
                $gameLevelClient->setPoints($newPoints);
                try {
                    $this->persistAndFlush($gameLevelClient);
                } catch (\Exception $e) {
                    return ['valid' => false, 'message' => __('Erreur lors de la mise à jour du niveau')];
                }
                break;
            }


            $gameLevelClient->setPoints($gameLevelClient->getLevel()->getPoints());
            $gameLevelClient->setStatus(GameLevel::STATUS_DONE);
            try {
                $this->persistAndFlush($gameLevelClient);
            } catch (\Exception $e) {
                return ['valid' => false, 'message' => __('Erreur lors de la mise à jour du niveau')];
            }

            $nextLevel = $gameLevelClient->getLevel()->getNext();
            if (!$nextLevel) {
                break;
            }
            $newLevel = $nextLevel->getId();
            $gameLevelClient = GameLevelClientFactory::createGameClientLevel($nextLevel);

            $nbPoints -= ($gameLevelClient->getLevel()->getPoints() - $actualPoints);
            $actualPoints = 0;
        }

        if ($newLevel) {
            return ['valid' => true, 'fromLevel' => $fromLevel, 'newLevel' => $newLevel];
        }

        return ['valid' => true];
    }

    /**
     * @param GameLevel $fromLevel
     * @param GameLevel $newLevel
     * @return string
     */
    public function renderNewLevelModal(GameLevel $fromLevel, GameLevel $newLevel): string
    {
        return TwigService::getInstance()->set('fromLevel', $fromLevel)
            ->set('newLevel', $newLevel)
            ->render('app/game/partials/modal-new-level.php');
    }
}
