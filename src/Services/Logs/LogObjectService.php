<?php

namespace MatGyver\Services\Logs;

use Doctrine\ORM\EntityManager;
use MatGyver\Entity\Log\Object\LogObject;
use MatGyver\Helpers\Tools;
use MatGyver\Repository\Log\Object\LogObjectRepository;
use MatGyver\Services\BaseEntityService;
use MatGyver\Services\Clients\ClientsService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Users\UsersService;

/**
 * Class LogObjectService
 * @package MatGyver\Services\Logs
 * @property LogObjectRepository $repository
 * @method LogObjectRepository getRepository()
 */
class LogObjectService extends BaseEntityService
{
    /**
     * LogObjectService constructor.
     * @param EntityManager $em
     */
    public function __construct(EntityManager $em)
    {
        $this->em = $em;
        $this->repository = $em->getRepository(LogObject::class);
    }

    /**
     * @param string $type
     * @param array $object
     * @param int|null $idClient
     * @return void
     */
    public static function logObject(string $type, array $object, ?int $idClient = null): void
    {
        self::log($type, json_encode($object), $idClient);
    }

    /**
     * @param string $type
     * @param string $content
     * @param int|null $idClient
     * @return void
     */
    public static function log(string $type, string $content, ?int $idClient = null): void
    {
        $container = ContainerBuilderService::getInstance();

        if ($idClient === null and isset($_SESSION['client']['id'])) {
            $idClient = $_SESSION['client']['id'];
        }

        $data = [
            'get' => self::removePrivateInfo($_GET),
            'post' => self::removePrivateInfo($_POST)
        ];

        $route = '';
        if (isset($_SERVER['REQUEST_URI'])) {
            $route = Tools::getUrl();
        }

        $logObject = new LogObject();
        $client = $container->get(ClientsService::class)->getRepository()->find($idClient);
        $logObject->setClient($client);
        $logObject->setUser(null);
        $logObject->setType($type);
        $logObject->setContent($content);
        $logObject->setData($data);
        $logObject->setRoute($route);
        $logObject->setSession(json_encode($_SESSION));
        $logObject->setServer(json_encode($_SERVER));
        $logObject->setDate(new \DateTime('now'));

        if (isset($_SESSION['user']['id'])) {
            $logObject->setUser($container->get(UsersService::class)->getRepository()->find($_SESSION['user']['id']));
        }

        $container->get(LogObjectService::class)->persistAndFlush($logObject);
    }

    /**
     * @param array $data
     * @return array
     */
    private static function removePrivateInfo(array $data): array
    {
        foreach ($data as $name => $value) {
            if (str_contains($name, 'password')) {
                $data[$name] = '***';
            }
        }
        return $data;
    }

    /**
     * @return void
     */
    public function deleteAll(): void
    {
        $this->repository->deleteAll();
    }
}
