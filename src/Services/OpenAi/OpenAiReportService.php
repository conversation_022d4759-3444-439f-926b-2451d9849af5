<?php

namespace MatGyver\Services\OpenAi;

use MatGyver\Entity\Dossier\Expertise\DossierExpertiseAiReport;
use MatGyver\Entity\OpenAi\OpenAiAssistant;
use MatGyver\Services\Logger\LoggerService;

class OpenAiReportService extends OpenAiService
{
    /**
     * @param string $file
     * @return array
     */
    public function generateReport(OpenAiAssistant $assistant, DossierExpertiseAiReport $report, string $file): array
    {
        $client = $this->getOpenAiClient();
        $assistantId = $assistant->getAssistantId();

        //step 1 : upload the file
        try {
            $response = $client->files()->upload([
                'file' => fopen($file, 'r'),
                'purpose' => 'assistants',
            ]);
        } catch (\Exception $e) {
            LoggerService::logError('Error calling OpenAiApi : ' . $e->getMessage());
            return ['valid' => false, 'message' => __('Une erreur est survenue.')];
        }
        $fileId = $response->id;


        //step 2 : create thread
        if ($report->getExpertise()->getDossier()->isJudiciaire()) {
            $prompt = $this->getPromptJudiciaire($report);
        } else {
            $prompt = $this->getPrompt($report);
        }
        $data = [
            'messages' => [
                [
                    'role' => 'user',
                    'content' => $prompt,
                    'attachments' => [
                        [
                            'file_id' => $fileId,
                            'tools' => [['type' => 'file_search']]
                        ],
                    ],
                ],
            ],
        ];
        try {
            $response = $client->threads()->create($data);
        } catch (\Exception $e) {
            LoggerService::logError('Error calling OpenAiApi : ' . $e->getMessage());
            return ['valid' => false, 'message' => __('Une erreur est survenue.')];
        }
        $threadId = $response->id;


        // Step 4 : run the thread
        $data = ['assistant_id' => $assistantId];
        try {
            $response = $client->threads()->runs()->create($threadId, $data);
        } catch (\Exception $e) {
            LoggerService::logError('Error calling OpenAiApi : ' . $e->getMessage());
            return ['valid' => false, 'message' => __('Une erreur est survenue.')];
        }
        $runId = $response->id;


        //get run and check status
        $i = 0;
        $maxLoops = 30;
        while ($i <= $maxLoops) {
            try {
                $response = $client->threads()->runs()->retrieve($threadId, $runId);
            } catch (\Exception $e) {
                LoggerService::logError('Error calling OpenAiApi : ' . $e->getMessage());
                return ['valid' => false, 'message' => __('Une erreur est survenue.')];
            }
            $status = $response->status;
            if ($status == 'completed') {
                break;
            }

            if (in_array($status, ['cancelling', 'cancelled', 'failed', 'incomplete', 'expired'])) {
                LoggerService::logError('Error calling OpenAiApi : run.status = ' . $status);
                return ['valid' => false, 'message' => __('Une erreur est survenue lors de la lecture de votre document.')];
            }

            $i++;
            sleep(1);
        }

        try {
            $response = $client->threads()->messages()->list($threadId);
        } catch (\Exception $e) {
            LoggerService::logError('Error calling OpenAiApi : ' . $e->getMessage());
            return ['valid' => false, 'message' => __('Une erreur est survenue.')];
        }

        $lastMessage = $response->data[0] ?? [];
        if (!$lastMessage) {
            return ['valid' => false, 'message' => __('Une erreur est survenue lors de la lecture de votre document.')];
        }
        $content = $lastMessage->content[0]->text->value;
        $content = str_replace('```json', '', $content);
        $content = str_replace('```', '', $content);
        $content = str_replace('\n', '', $content);
        $content = str_replace("\n", '', $content);
        $content = str_replace('\"', '"', $content);

        $result = [];
        if (str_starts_with($content, '{')) {
            $content = preg_replace('/\{\s+"/', '{"', $content);
            $content = str_replace(': {', ':{', $content);
            $result = json_decode($content, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                LoggerService::logError('JSON decode error: ' . json_last_error_msg());
                return ['valid' => false, 'message' => __('Une erreur est survenue lors de la lecture de votre document.')];
            }

            $pattern = "/【([a-z0-9-].*)】/";
            foreach ($result as $id => $content) {
                if (!is_array($content)) {
                    $content = preg_replace($pattern, '', $content);
                    $result[$id] = $content;
                } else {
                    foreach ($content as $key => $value) {
                        if (!is_array($value)) {
                            $value = preg_replace($pattern, '', $value);
                            $content[$key] = $value;
                        } else {
                            foreach ($value as $subKey => $subValue) {
                                $subValue = preg_replace($pattern, '', $subValue);
                                $content[$key][$subKey] = $subValue;
                            }
                        }
                    }
                    $result[$id] = $content;
                }
            }
        }

        return ['valid' => true, 'result' => $result];
    }

    public function getPrompt(DossierExpertiseAiReport $report): string
    {
        $expertName = $this->getExpertName($report);

        $prompt = "Tu assistes $expertName, expert en automobile en charge de ce dossier d'expertise.\n";
        $prompt .= "**Contexte :**\n";
        $prompt .= "Dans ce dossier, $expertName intervient en tant qu'expert automobile pour le lésé dans le cadre d'une expertise amiable.\n";

        $prompt .= "Voici le PV de l'expertise réalisée pour ce dossier. Il contient toutes les informations nécessaires pour rédiger le rapport d'expertise, y compris la déclaration de la panne ou du litige.\n";
        $prompt .= "**Ta mission :**\n";
        $prompt .= "🔹 Tu dois générer plusieurs sections pour pré-rédiger le rapport d'expertise.\n";
        $prompt .= "🔹 Chaque réponse DOIT OBLIGATOIREMENT contenir au minimum 3 phrases complètes et distinctes, avec au moins 50 mots au total. Une réponse plus courte sera considérée comme invalide.\n";
        $prompt .= "🔹 La réponse doit être uniquement au format JSON et ne contenir aucun texte en dehors de ce format.\n";
        $prompt .= "🔹 Toutes les clés doivent être incluses, même si elles sont vides.\n";
        $prompt .= "**Structure JSON attendue :**\n";

        $result = [
            "technical_analysis" => "Analyse technique détaillée des éléments mécaniques en cause...",
            "causes" => "Explication des raisons de la panne...",
            "consequences" => "Impact sur le véhicule et sur les coûts de réparation... Évite ici de parler de sécurité.",
            "responsibilities_text" => "Description des responsabilités...",
            "imputability" => [
                [
                    "person" => "type de personne",
                    "content" => "Le garagiste a effectué une réparation non conforme aux préconisations constructeur..."
                ]
            ],
            "conclusions" => "Résumé final précisant les responsabilités et recommandations...",
        ];
        $prompt .= json_encode($result, JSON_PRETTY_PRINT) . "\n";

        $prompt .= "**Détails supplémentaires :**\n";
        $prompt .= "La clé `technical_analysis` doit contenir une description détaillée des constats techniques et des pièces concernées.\n";
        $prompt .= "La clé `causes` doit expliquer l'origine de la panne en s’appuyant sur les observations techniques.\n";
        $prompt .= "La clé `consequences` doit détailler l’impact de la panne sur le véhicule, la sécurité et les coûts de réparation.\n";
        $prompt .= "La clé `responsibilities_text` doit décrire les responsabilités en justifiant pourquoi une partie est responsable.\n";
        $prompt .= "La clé `imputability` doit contenir un tableau des personnes responsables avec deux clés :\n";
        $prompt .= "`person` : Peut être : fortuitous, driver, garage_seller, seller, manufacturer ou other.\n";
        $prompt .= "`content` : explication précise de la responsabilité.\n";
        $prompt .= "Si plusieurs parties sont impliquées, ajoute plusieurs objets dans ce tableau.\n";
        $prompt .= "La clé `conclusions` doit :\n";
        $prompt .= "- Rappeler les conditions d’acquisition du véhicule et si les désordres sont liés à cette vente.\n";
        $prompt .= "- Indiquer si les problèmes sont antérieurs à la vente ou à une réparation.\n";
        $prompt .= "- Décrire les dysfonctionnements et leurs causes (défaut de fabrication, mauvais entretien, etc.).\n";

        $prompt .= "❗️ Important :\n";
        $prompt .= "Si une information est absente, la clé correspondante doit être incluse mais laissée vide.\n";
        $prompt .= "Aucun texte ne doit apparaître en dehors du JSON.\n";
        $prompt .= "Réponds uniquement en français.\n";
        $prompt .= "🔥 La réponse doit commencer par { et finir par } sans texte additionnel.\n";

        if ($report->getInstructions()) {
            $prompt .= "Voici des instructions supplémentaires ajoutées par l'expert automobile : " . $report->getInstructions() . "\n";
        }

        return $prompt;
    }

    public function getPromptJudiciaire(DossierExpertiseAiReport $report): string
    {
        $expertName = $this->getExpertName($report);

        $prompt = "Tu assistes $expertName, expert judiciaire en automobile en charge de ce dossier d'expertise.\n";
        $prompt .= "**Contexte :**\n";
        $prompt .= "Dans ce dossier, $expertName intervient en tant qu'expert de justice, à la demande du Tribunal, avec des questions précises évoquées dans l'ordonnance.\n";

        $prompt .= "Voici le compte rendu d'accedit de l'expertise réalisée pour ce dossier. Il contient toutes les informations nécessaires pour rédiger le rapport d'expertise judiciaire.\n";
        $prompt .= "Il contient notamment les circonstances de l'affaire, ainsi que les questions du tribunal dans la section 'libellé de la mission'.\n";

        $prompt .= "**Ta mission :**\n";
        $prompt .= "🔹 Tu dois générer plusieurs sections pour pré-rédiger le rapport d'expertise.\n";
        $prompt .= "🔹 Chaque réponse DOIT OBLIGATOIREMENT contenir au minimum 3 phrases complètes et distinctes, avec au moins 50 mots au total. Une réponse plus courte sera considérée comme invalide.\n";
        $prompt .= "🔹 La réponse doit être uniquement au format JSON et ne contenir aucun texte en dehors de ce format.\n";
        $prompt .= "🔹 Toutes les clés doivent être incluses, même si elles sont vides.\n";

        $prompt .= "**Structure JSON attendue :**\n";
        $result = [
            'facts' => ["liste des faits de l'affaire"],
            'doubts' => ["liste des éventuels doutes restants après l'expertise"],
            'opinions' => ["liste des avis techniques de l'expert"],
            'answers' => ["liste des réponses à chaque question du Tribunal"],
            'conclusions' => 'Résumé final et recommandations techniques ou juridiques...',
        ];
        $prompt .= json_encode($result, JSON_PRETTY_PRINT) . "\n\n";


        $prompt .= "**Détails supplémentaires :**\n";
        $prompt .= "La clé `facts` est un tableau contenant une liste de faits avec à chaque fois une description du fait, de son origine et les causes établies.\n";
        $prompt .= "La clé `doubts` est un tableau contenant une liste d'éventuels doutes et incertitudes non levés.\n";
        $prompt .= "La clé `opinions` est un tableau contenant une liste d'avis techniques développés, motivés et argumentés.\n";

        $expertise = $report->getExpertise();
        $expertiseJudiciaire = $expertise->getExpertiseJudiciaire();
        $courtQuestions = $expertiseJudiciaire->getCourtQuestions();
        if (count($courtQuestions)) {
            $arrayCourtQuestions = [];
            foreach ($courtQuestions as $courtQuestion) {
                $arrayCourtQuestions[$courtQuestion->getId()] = $courtQuestion->getQuestion();
            }

            $prompt .= "Voici toutes les questions du Tribunal avec à chaque fois un id et un texte. Renvoie une clé `answers` qui est un tableau contenant la réponse à chacune des questions du tribunal.\n";
            $prompt .= "Ce tableau doit être sous la forme : [id_question => reponse]. La clé doit toujours être l'id de la question, et la valeur doit être la réponse que tu as généré.\n";
            $prompt .= "Questions du tribunal : " . json_encode($arrayCourtQuestions, JSON_PRETTY_PRINT) . "\n\n";
        }

        $prompt .= "La clé `conclusions` doit :\n";
        $prompt .= "- Rappeler dans quelles conditions le véhicule a été acquis et si les désordres invoqués sont en relation avec cette vente ou une réparation réalisée.\n";
        $prompt .= "- Indiquer si les désordres sont antérieurs à la vente ou à une réparation\n";
        $prompt .= "- Décrire les dysfonctionnements et leurs causes (défaut de fabrication, mauvais entretien, etc.).\n";
        $prompt .= "- Évoquer les causes des dysfonctionnements en précisant s'il s'agit d'un défaut de fabrication, d'entretien, de travaux de réparation réalisés sans respecter les règles de l'art ou les préconisations du constructeur.\n";

        $prompt .= "❗️ Important :\n";
        $prompt .= "Si une information est absente, la clé correspondante doit être incluse mais laissée vide.\n";
        $prompt .= "Aucun texte ne doit apparaître en dehors du JSON.\n";
        $prompt .= "Réponds uniquement en français.\n";
        $prompt .= "🔥 La réponse doit commencer par { et finir par } sans texte additionnel.\n";

        if ($report->getInstructions()) {
            $prompt .= "Voici des instructions supplémentaires ajoutées par l'expert automobile : " . $report->getInstructions() . "\n";
        }

        return $prompt;
    }

    public function getExpertName(DossierExpertiseAiReport $report): string
    {
        $expert = $report->getExpertise()->getDossier()->getExpert();
        if ($expert) {
            $expertName = $expert->getFirstName() . ' ' . $expert->getLastName();
        } else {
            $user = $report->getExpertise()->getDossier()->getUser();
            if (!$user) {
                $user = $report->getExpertise()->getClient()->getMainAdmin();
            }
            $expertName = $user->getFirstName() . ' ' . $user->getLastName();
        }

        return $expertName;
    }
}
