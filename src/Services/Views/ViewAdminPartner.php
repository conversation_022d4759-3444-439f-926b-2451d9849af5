<?php

namespace MatGyver\Services\Views;

use MatGyver\Services\Affiliation\AffiliationClicsService;
use MatGyver\Services\Affiliation\AffiliationCommissionService;
use MatGyver\Services\Affiliation\AffiliationPartnersService;
use MatGyver\Services\Users\UsersLogsService;
use MatGyver\Services\Users\UsersService;
use MatGyver\Services\RightsService;

/**
 * Class ViewAdminPartner
 * @package MatGyver\Services\Views
 */
class ViewAdminPartner extends ViewAdmin
{
    /**
     * @var UsersService
     */
    private $usersService;

    /**
     * @var UsersLogsService
     */
    private $usersLogsService;

    /**
     * @var AffiliationPartnersService
     */
    private $affiliationPartnersService;

    /**
     * @var AffiliationCommissionService
     */
    private $affiliationCommissionService;

    /**
     * @var AffiliationClicsService
     */
    private $affiliationClicsService;

    /**
     * ViewAdminUser constructor.
     * @param UsersService $usersService
     * @param UsersLogsService $usersLogsService
     * @param AffiliationPartnersService $affiliationPartnersService
     * @param AffiliationCommissionService $affiliationCommissionService
     * @param AffiliationClicsService $affiliationClicsService
     */
    public function __construct(
        UsersService $usersService,
        UsersLogsService $usersLogsService,
        AffiliationPartnersService $affiliationPartnersService,
        AffiliationCommissionService $affiliationCommissionService,
        AffiliationClicsService $affiliationClicsService
    ) {
        parent::__construct();

        $this->usersService = $usersService;
        $this->usersLogsService = $usersLogsService;
        $this->affiliationPartnersService = $affiliationPartnersService;
        $this->affiliationCommissionService = $affiliationCommissionService;
        $this->affiliationClicsService = $affiliationClicsService;
    }

    public function initData()
    {
        parent::initData();

        if (!$this->data['param']) {
            return;
        }

        $idPartner = filter_var($this->data['param'], FILTER_VALIDATE_INT);
        $partner = $this->affiliationPartnersService->getRepository()->findWoClient($idPartner);
        if (!$partner) {
            return;
        }

        $user = $this->usersService->adminGetUserById($partner->getUser()->getId());
        if (!$user) {
            return;
        }

        $parent = null;
        if ($partner->getParent()) {
            $parent = $this->affiliationPartnersService->getRepository()->findWoClient($partner->getParent());
        }

        $avatarUrl = $this->usersService->getAvatar($user->getEmail(), 80, $user->getClient()->getId());

        $isUser = RightsService::isUser($user->getId());
        $isEditor = RightsService::isEditor($user->getId());
        $iamAnAdmin = RightsService::isAdmin();

        $lastLogin = $this->usersLogsService->getLastLoginByEmail($user->getEmail(), $user->getClient()->getId());

        $nbCommissions = $this->affiliationCommissionService->getRepository()->getCountAllCommissions($idPartner);
        $nbClicks = $this->affiliationClicsService->getRepository()->getCountClicks(null, $idPartner);
        $balance = $this->affiliationCommissionService->getAvailableCommissionsAmountByPartner($idPartner);

        $headerOutput = $this->parser
            ->set('user', $user)
            ->set('userId', $user->getId())
            ->set('partner', $partner)
            ->set('parent', $parent)
            ->set('nbCommissions', $nbCommissions)
            ->set('nbClicks', $nbClicks)
            ->set('balance', $balance)
            ->set('lastLogin', $lastLogin)
            ->set('isUser', $isUser)
            ->set('isEditor', $isEditor)
            ->set('iamAnAdmin', $iamAnAdmin)
            ->set('avatarUrl', $avatarUrl)
            ->render('layouts/admin/headers/partner.php');
        $headerOutput = $this->csrfService->replaceForm($headerOutput);
        $this->set('universe_menu', $headerOutput);
    }
}
