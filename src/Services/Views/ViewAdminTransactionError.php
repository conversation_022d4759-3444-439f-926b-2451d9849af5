<?php

namespace MatGyver\Services\Views;

use MatGyver\Helpers\Tools;
use MatGyver\Services\Shop\Transaction\Error\ShopTransactionErrorService;
use MatGyver\Services\Shop\Transaction\ShopTransactionService;
use MatGyver\Services\Users\UserConfigService;
use MatGyver\Services\Users\UsersService;

/**
 * Class ViewAdminTransactionError
 * @package MatGyver\Services\Views
 */
class ViewAdminTransactionError extends ViewAdmin
{
    /**
     * @var ShopTransactionService
     */
    protected $shopTransactionService;

    /**
     * @var ShopTransactionErrorService
     */
    private $shopTransactionErrorService;

    /**
     * @var UsersService
     */
    private $usersService;

    /**
     * @var UserConfigService
     */
    private $userConfigService;

    /**
     * ViewAdminTransactionError constructor.
     * @param ShopTransactionService $shopTransactionService
     * @param ShopTransactionErrorService $shopTransactionErrorService
     * @param UsersService $usersService
     * @param UserConfigService $userConfigService
     */
    public function __construct(
        ShopTransactionService $shopTransactionService,
        ShopTransactionErrorService $shopTransactionErrorService,
        UsersService $usersService,
        UserConfigService $userConfigService
    ) {
        parent::__construct();

        $this->shopTransactionService = $shopTransactionService;
        $this->shopTransactionErrorService = $shopTransactionErrorService;
        $this->usersService = $usersService;
        $this->userConfigService = $userConfigService;
    }

    public function initData()
    {
        parent::initData();

        if (!$this->data['param']) {
            return;
        }

        $idTransactionError = filter_var($this->data['param'], FILTER_VALIDATE_INT);
        $transactionError = $this->shopTransactionErrorService->getRepository()->find($idTransactionError);
        if (!$transactionError) {
            return;
        }

        $transaction = $transactionError->getTransaction();

        $user = [];
        $avatarUrl = '';
        if ($transaction and $transaction->getUser()) {
            $user = $transaction->getUser();
        }
        if ($user) {
            $avatarUrl = $this->usersService->getAvatar($user->getEmail(), 150, $user->getClient()->getId());
        }
        if (!$avatarUrl) {
            $avatarUrl = $this->usersService->getGuestAvatar($transaction->getEmail(), 150, $transaction->getFirstName(), $transaction->getLastName());
        }

        $telephone = '';
        $country = '';
        if ($transaction->getShopCustomer()) {
            $customer = $transaction->getShopCustomer();
            $telephone = $customer->getTelephone();
            $country = $customer->getCountry();
        }

        if ('transaction_add' == $transaction->getCustom()) {
            $paymentDatas = json_decode($transaction->getDatas(), true);
            if (isset($paymentDatas['country']) and $paymentDatas['country']) {
                $country = $paymentDatas['country'];
            }
            if (isset($paymentDatas['telephone']) and $paymentDatas['telephone']) {
                $telephone = $paymentDatas['telephone'];
            }
        }
        if (!$telephone and $user) {
            $userConfig = $this->userConfigService->getUserConfig($user);
            if (isset($userConfig['mobile'])) {
                $telephone = $userConfig['mobile'];
            }
            if (!$telephone and isset($userConfig['telephone'])) {
                $telephone = $userConfig['telephone'];
            }
        }
        if ($telephone and $country and strlen($country) == 2) {
            $checkPhoneNumber = Tools::checkPhoneNumber($telephone, $country);
            if ($checkPhoneNumber['valid']) {
                $telephone = str_replace(' ', '', $checkPhoneNumber['phoneNumber']);
            }
        }

        $btnStatusClass = 'btn-light-warning';
        if ($transactionError->getTransactionErrorStatus()->getName() == 'paid' or $transactionError->getTransactionErrorStatus()->getName() == 'closed') {
            $btnStatusClass = 'btn-light-info';
        } elseif ($transactionError->getTransactionErrorStatus()->getName() == 'open') {
            $btnStatusClass = 'btn-light-success';
        }

        $headerOutput = $this->parser
            ->set('transactionError', $transactionError)
            ->set('transaction', $transaction)
            ->set('user', $user)
            ->set('avatarUrl', $avatarUrl)
            ->set('telephone', $telephone)
            ->set('btnStatusClass', $btnStatusClass)
            ->set('orderPageLink', $transaction->getCancelUrl())
            ->render('layouts/admin/headers/transaction_error.php');

        $headerOutput = $this->csrfService->replaceForm($headerOutput);

        $this->set('universe_menu', $headerOutput);
    }
}
