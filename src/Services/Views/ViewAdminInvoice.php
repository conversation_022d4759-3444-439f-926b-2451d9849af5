<?php

namespace MatGyver\Services\Views;

use MatGyver\Services\Shop\Invoices\ShopInvoicesService;
use MatGyver\Services\Shop\Transaction\ShopTransactionService;
use MatGyver\Services\Users\UsersService;

/**
 * Class ViewAdminInvoice
 * @package MatGyver\Services\Views
 */
class ViewAdminInvoice extends ViewAdmin
{
    /**
     * @var UsersService
     */
    private $usersService;

    /**
     * @var ShopInvoicesService
     */
    private $shopInvoicesService;

    /**
     * @var ShopTransactionService
     */
    private $shopTransactionService;

    /**
     * ViewAdminInvoice constructor.
     * @param UsersService $usersService
     * @param ShopInvoicesService $shopInvoicesService
     * @param ShopTransactionService $shopTransactionService
     */
    public function __construct(
        UsersService $usersService,
        ShopInvoicesService $shopInvoicesService,
        ShopTransactionService $shopTransactionService
    ) {
        parent::__construct();

        $this->usersService = $usersService;
        $this->shopInvoicesService = $shopInvoicesService;
        $this->shopTransactionService = $shopTransactionService;
    }

    public function initData()
    {
        parent::initData();

        if (!$this->data['param']) {
            return;
        }

        $idInvoice = filter_var($this->data['param'], FILTER_VALIDATE_INT);
        if (!$idInvoice) {
            return;
        }

        $invoice = $this->shopInvoicesService->getInvoiceById($idInvoice);
        if (!$invoice) {
            return;
        }

        $invoiceContent = json_decode($invoice->getCustomer(), true);

        $transaction = [];
        if ($invoice->getTransactionReference()) {
            $transaction = $this->shopTransactionService->getTransactionByReference($invoice->getTransactionReference());
        }

        $user = [];
        if ($transaction and $transaction->getUser()) {
            $user = $transaction->getUser();
        } elseif ($invoice->getUser()) {
            $user = $invoice->getUser();
        }

        if ($user) {
            try {
                $user->getFirstName();
            } catch (\Exception $e) {
                $user = null;
            }
        }

        if ($user) {
            $avatarUrl = $this->usersService->getAvatar($user->getEmail(), 150);
        } else {
            $avatarUrl = $this->usersService->getGuestAvatar($invoiceContent['email'], 150, $invoiceContent['first_name'], $invoiceContent['last_name']);
        }

        $headerOutput = $this->parser
            ->set('invoice', $invoice)
            ->set('invoiceContent', $invoiceContent)
            ->set('user', $user)
            ->set('avatarUrl', $avatarUrl)
            ->set('transaction', $transaction)
            ->render('layouts/admin/headers/invoice.php');
        $headerOutput = $this->csrfService->replaceForm($headerOutput);
        $this->set('universe_menu', $headerOutput);
    }
}
