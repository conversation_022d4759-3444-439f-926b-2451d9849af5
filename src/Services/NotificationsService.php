<?php

namespace MatGyver\Services;

use Doctrine\ORM\EntityManager;
use MatGyver\Entity\Notification\Notification;
use MatGyver\Repository\Notification\NotificationRepository;
use MatGyver\Services\Clients\ClientsService;
use MatGyver\Services\DI\ContainerBuilderService;

/**
 * Class NotificationsService
 * @package MatGyver\Services
 * @property NotificationRepository $repository
 * @method NotificationRepository getRepository()
 */
class NotificationsService extends BaseEntityService
{

    /**
     * @var ClientsService
     */
    private $clientsService;

    /**
     * NotificationsService constructor.
     * @param EntityManager $em
     * @param ClientsService $clientsService
     */
    public function __construct(
        EntityManager $em,
        ClientsService $clientsService
    ) {
        $this->em = $em;
        $this->repository = $em->getRepository(Notification::class);
        $this->clientsService = $clientsService;
    }

    /**
     * @param string $subject
     * @param string $content
     * @param string $type
     * @param int|null $idClient
     * @return array
     */
    public function insertNotification(string $subject, string $content, string $type = '', ?int $idClient = null): array
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }

        $notification = new Notification();
        $notification->setClient($this->clientsService->getClientById($idClient));
        $notification->setSubject($subject);
        $notification->setContent($content);
        $notification->setSeen(false);
        $notification->setType($type);
        $notification->setDate(new \DateTime('now'));
        try {
            $this->persistAndFlush($notification);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Une erreur est survenue lors de l\'enregistrement de la notification.')];
        }

        return ['valid' => true];
    }

    /**
     * @param string $subject
     * @param string $content
     * @param string $type
     * @param int|null $idClient
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public static function insert(string $subject, string $content, string $type = '', ?int $idClient = null)
    {
        $container = ContainerBuilderService::getInstance();
        $container->get(NotificationsService::class)->insertNotification($subject, $content, $type, $idClient);
    }

    /**
     * @param Notification $notification
     * @return array
     */
    public function setNotificationSeen(Notification $notification): array
    {
        $notification->setSeen(true);
        try {
            $this->persistAndFlush($notification);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Une erreur est survenue lors de la mise à jour de la notification.')];
        }
        return ['valid' => true];
    }

    /**
     * @param Notification $notification
     * @return array
     */
    public function deleteNotification(Notification $notification): array
    {
        try {
            $this->deleteAndFlush($notification);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Une erreur est survenue lors de la suppression de la notification.')];
        }
        return ['valid' => true];
    }
}
