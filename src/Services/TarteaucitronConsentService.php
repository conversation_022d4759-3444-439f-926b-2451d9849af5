<?php

namespace MatGyver\Services;

use Doctrine\ORM\EntityManager;
use MatGyver\Entity\Tarteaucitron\TarteaucitronConsent;
use MatGyver\Helpers\IpAddress;
use MatGyver\Repository\Tarteaucitron\TarteaucitronConsentRepository;
use MatGyver\Services\Clients\ClientsService;
use MatGyver\Services\Users\UsersService;

/**
 * Class TarteaucitronConsentService
 * @package MatGyver\Services\TarteaucitronConsent
 * @property TarteaucitronConsentRepository $repository
 * @method TarteaucitronConsentRepository getRepository()
 */
class TarteaucitronConsentService extends BaseEntityService
{

    /**
     * @var ClientsService
     */
    private $clientsService;

    /**
     * @var UsersService
     */
    private $usersService;

    /**
     * TarteaucitronConsentService constructor.
     * @param EntityManager $entityManager
     * @param ClientsService $clientsService
     * @param UsersService $usersService
     */
    public function __construct(
        EntityManager $entityManager,
        ClientsService $clientsService,
        UsersService $usersService
    ) {
        $this->em = $entityManager;
        $this->repository = $this->em->getRepository(TarteaucitronConsent::class);
        $this->clientsService = $clientsService;
        $this->usersService = $usersService;
    }

    /**
     * @param string $service
     * @param string $response
     * @param string $url
     * @return array
     */
    public function insert(string $service, string $response, string $url): array
    {
        $client = $this->clientsService->getClient();
        $user = $this->usersService->getUser();

        $consent = new TarteaucitronConsent();
        $consent->setClient($client);
        $consent->setUser($user);
        $consent->setUrl($url);
        $consent->setService($service);
        $consent->setResponse($response);
        $consent->setIp(IpAddress::getRealIp());
        try {
            $this->persistAndFlush($consent);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Une erreur est survenue')];
        }

        return ['valid' => true];
    }
}
