<?php
namespace MatGyver\Services\Integration\Services;

use MatGyver\Helpers\Encryption;
use MatGyver\Helpers\Tools;

/**
 * Class AppleCalendarService
 * @package MatGyver\Services\Integration\Services
 */
class OutlookCalendarService
{
    private array $fields;
    private array $accountFields;

    public function __construct()
    {
        $this->fields = array();
        $this->accountFields = array();
    }

    /**
     * @return array
     */
    public function getFields(): array
    {
        return $this->fields;
    }

    /**
     * @return array
     */
    public function getAccountFields(): array
    {
        return $this->accountFields;
    }

    /**
     * @return string
     */
    public function getCalendarLink(): string
    {
        return Tools::makeLink('site', 'dossiers', 'outlook/ics/' . Encryption::encrypt($_SESSION['client']['id']));
    }
}
