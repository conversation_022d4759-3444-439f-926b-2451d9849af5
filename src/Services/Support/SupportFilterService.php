<?php

namespace MatGyver\Services\Support;

use Doctrine\ORM\EntityManager;
use MatGyver\Entity\Support\Filter\SupportFilter;
use MatG<PERSON>ver\Entity\Support\Ticket\SupportTicket;
use MatGyver\Repository\Support\Filter\SupportFilterRepository;
use MatGyver\Services\BaseEntityService;
use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\Partials\SelectService;
use MatGyver\Services\RightsService;

/**
 * Class SupportFilterService
 * @package MatGyver\Services\Support
 * @property SupportFilterRepository $repository
 * @method SupportFilterRepository getRepository()
 */
class SupportFilterService extends BaseEntityService
{
    /**
     * @var SupportUserService
     */
    private $supportUserService;

    /**
     * SupportFilterService constructor.
     * @param EntityManager $em
     * @param SupportUserService $supportUserService
     */
    public function __construct(
        EntityManager $em,
        SupportUserService $supportUserService
    ) {
        $this->em = $em;
        $this->repository = $em->getRepository(SupportFilter::class);
        $this->supportUserService = $supportUserService;
    }

    /**
     * @param int $idFilter
     * @return SupportFilter|null
     */
    public function getFilterById(int $idFilter): ?SupportFilter
    {
        return $this->repository->find($idFilter);
    }

    /**
     * @return SupportFilter[]|null
     */
    public function getAllFilters(): ?array
    {
        return $this->repository->findBy([], ['id' => 'ASC']);
    }

    /**
     * @param SupportTicket $ticket
     * @return SupportTicket|null
     */
    public function applyFilters(SupportTicket $ticket): ?SupportTicket
    {
        $filters = $this->getAllFilters();
        if (!$filters) {
            return $ticket;
        }

        foreach ($filters as $filter) {
            if (!method_exists($ticket, 'get' . ucfirst($filter->getType()))) {
                LoggerService::logError(sprintf('applyFilters : method %s does not exist', 'get' . ucfirst($filter->getType())));
                continue;
            }
            $applyAction = false;
            $ticketContent = $ticket->{'get' . ucfirst($filter->getType())}();
            switch ($filter->getCond()) {
                case "is":
                    if ($ticketContent == $filter->getContent()) {
                        $applyAction = true;
                    }
                    break;
                case "isnot":
                    if ($ticketContent != $filter->getContent()) {
                        $applyAction = true;
                    }
                    break;
                case "contains":
                    if (str_contains($ticketContent, $filter->getContent())) {
                        $applyAction = true;
                    }
                    break;
                case "notcontains":
                    if (!str_contains($ticketContent, $filter->getContent())) {
                        $applyAction = true;
                    }
                    break;
                default:
                    break;
            }

            if ($applyAction) {
                switch ($filter->getAction()) {
                    case "ticket_close":
                        $ticket->setStatus(SupportTicket::STATUS_CLOSE);
                        break;
                    case "ticket_spam":
                        $ticket->setFlag(SupportTicket::FLAG_SPAM);
                        break;
                    case "ticket_important":
                        $ticket->setFlag(SupportTicket::FLAG_IMPORTANT);
                        break;
                    case "ticket_delete":
                        return null;
                    case "ticket_assign_user":
                        $ticket->setAdmin((($filter->getUser() and $filter->getUser()->getUser()) ? $filter->getUser()->getUser() : null));
                        break;
                    case "ticket_assign_department":
                        $ticket->setService($filter->getService());
                        $ticket->setDepartment($filter->getDepartment());
                        break;
                    default:
                        break;
                }
            }
        }

        return $ticket;
    }

    /**
     * @param string $selectedType
     * @return string
     */
    public function generateFilterTypeSelect(string $selectedType = ''): string
    {
        $options = [
            'email' => __('Adresse email'),
            'subject' => __('Sujet du ticket'),
            'message' => __('Contenu du ticket'),
        ];
        return SelectService::render($options, $selectedType);
    }

    /**
     * @param int $selectedUser
     * @return string
     */
    public function generateUserSelect(int $selectedUser = 0): string
    {
        $options = [];

        $admins = $this->supportUserService->getRepository()->getAllIdUsers();
        if ($admins) {
            foreach ($admins as $admin) {
                $user = $admin->getUser();
                if ($user) {
                    $options[$user->getId()] = $user->getFirstName() . ' ' . $user->getLastName();
                }
            }
        }

        return SelectService::render($options, $selectedUser);
    }

    /**
     * @param string $selectedCondition
     * @return string
     */
    public function generateFilterConditionSelect(string $selectedCondition = ''): string
    {
        $options = [
            'is' => __('Est'),
            'isnot' => __('N\'est pas'),
            'contains' => __('Contient'),
            'notcontains' => __('Ne contient pas'),
        ];
        return SelectService::render($options, $selectedCondition);
    }
}
