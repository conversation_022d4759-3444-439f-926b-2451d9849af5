<?php

namespace MatGyver\Services\Support;

use Doctrine\ORM\EntityManager;
use MatGyver\Entity\Support\Service\SupportService;
use MatGyver\Repository\Support\Service\SupportServiceRepository;
use MatGyver\Services\BaseEntityService;
use MatGyver\Services\Partials\SelectService;
use MatGyver\Services\RightsService;

/**
 * Class SupportServiceService
 * @package MatGyver\Services\Support
 * @property SupportServiceRepository $repository
 * @method SupportServiceRepository getRepository()
 */
class SupportServiceService extends BaseEntityService
{
    /**
     * @var SupportUserService
     */
    private $supportUserService;

    /**
     * @var SupportTicketService
     */
    private $supportTicketService;

    /**
     * @var SupportDepartmentService
     */
    private $supportDepartmentService;

    /**
     * SupportServiceService constructor.
     * @param EntityManager $em
     * @param SupportUserService $supportUserService
     * @param SupportTicketService $supportTicketService
     * @param SupportDepartmentService $supportDepartmentService
     */
    public function __construct(
        EntityManager $em,
        SupportUserService $supportUserService,
        SupportTicketService $supportTicketService,
        SupportDepartmentService $supportDepartmentService
    ) {
        $this->em = $em;
        $this->repository = $em->getRepository(SupportService::class);
        $this->supportUserService = $supportUserService;
        $this->supportTicketService = $supportTicketService;
        $this->supportDepartmentService = $supportDepartmentService;
    }

    /**
     * @param int $idService
     * @return SupportService|null
     */
    public function getServiceById(int $idService): ?SupportService
    {
        return $this->repository->find($idService);
    }

    /**
     * @return SupportService[]|null
     */
    public function getAllServices(): ?array
    {
        return $this->repository->findBy([], ['name' => 'ASC']);
    }

    /**
     * @return array|bool
     */
    public function createAdminMenu()
    {
        $userPermissionsArray = $this->supportUserService->getUserPermissionsArray();
        if (!$userPermissionsArray) {
            return false;
        }

        //dashboard
        $nbTicketsOpened = $this->supportTicketService->getRepository()->adminGetCountTicketsOpened();
        $nbMyTicketsOpened = $this->supportTicketService->getRepository()->getCountTicketsOpenedByAdmin();

        $arrayServicesDepartments[] = array(
            'id_service' => 0,
            'name' => __('Accueil'),
            'departments' => array(
                array('id_department' => 0, 'name' => __('Tickets en cours'), 'nb_tickets_open' => $nbTicketsOpened),
                array('id_department' => -2, 'name' => __('Mes tickets'), 'nb_tickets_open' => $nbMyTicketsOpened),
                array('id_department' => -3, 'name' => __('Tickets d\'un utilisateur'), 'nb_tickets_open' => 0)
            )
        );

        foreach ($userPermissionsArray as $serviceId => $departments) {
            $serviceDepartments = array();
            foreach ($departments as $departmentId) {
                $department = $this->supportDepartmentService->getDepartmentById($departmentId);
                $nbTicketsOpened = $this->supportTicketService->getRepository()->adminGetCountTicketsOpenedByDepartment($departmentId);

                $serviceDepartments[] = array(
                    'id_department' => $departmentId,
                    'name' => $department->getName(),
                    'nb_tickets_open' => $nbTicketsOpened
                );
            }

            $service = $this->getServiceById($serviceId);
            $arrayServicesDepartments[] = array(
                'id_service' => $serviceId,
                'name' => $service->getName(),
                'departments' => $serviceDepartments
            );
        }

        return $arrayServicesDepartments;
    }

    /**
     * @return array
     */
    public function createAppMenu(): array
    {
        $services = $this->getAllServices();
        if (!$services) {
            return [];
        }

        //dashboard
        $nbTicketsOpened = $this->supportTicketService->getRepository()->getCountTicketsOpened();
        $nbMyTicketsOpened = $this->supportTicketService->getRepository()->getCountTicketsOpened($_SESSION['user']['id']);

        $arrayServicesDepartments[] = array(
            'id_service' => 0,
            'name' => __('Accueil'),
            'departments' => array(
                array('id_department' => 0, 'name' => __('Tickets en cours'), 'nb_tickets_open' => $nbTicketsOpened),
                array('id_department' => -2, 'name' => __('Mes tickets'), 'nb_tickets_open' => $nbMyTicketsOpened)
            )
        );

        foreach ($services as $service) {
            $departments = $this->supportDepartmentService->getDepartmentsByService($service->getId());
            if (!$departments) {
                continue;
            }

            $serviceDepartments = [];
            foreach ($departments as $department) {
                $nbTicketsOpened = $this->supportTicketService->getCountTicketsOpenedByDepartment($department->getId());
                $serviceDepartments[] = array(
                    'id_department' => $department->getId(),
                    'name' => $department->getName(),
                    'nb_tickets_open' => $nbTicketsOpened
                );
            }

            $arrayServicesDepartments[] = array(
                'id_service' => $service->getId(),
                'name' => $service->getName(),
                'departments' => $serviceDepartments
            );
        }

        return $arrayServicesDepartments;
    }

    /**
     * @param null $idService
     * @return string
     */
    public function generateServiceSelect($idService = null): string
    {
        $options = [];
        $options[] = __('Sélectionnez un service');

        $services = $this->getAllServices();
        if ($services) {
            foreach ($services as $service) {
                $options[$service->getId()] = $service->getName();
            }
        }

        return SelectService::render($options, $idService);
    }
}
