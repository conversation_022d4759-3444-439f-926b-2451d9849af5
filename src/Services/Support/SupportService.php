<?php

namespace MatGyver\Services\Support;

use MatGyver\Entity\Support\Ticket\SupportTicket;
use MatGyver\Factories\BlankStates\SupportBlankState;
use MatGyver\Factories\BlankStates\SupportTicketsBlankState;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Filters\SupportAdminFiltersService;
use MatGyver\Services\RightsService;
use MatGyver\Services\TwigService;

/**
 * Class SupportService
 * @package MatGyver\Services\Support
 */
class SupportService
{
    /**
     * @var SupportServiceService
     */
    private $supportServiceService;

    /**
     * @var SupportUserService
     */
    private $supportUserService;

    /**
     * @var SupportTicketService
     */
    private $supportTicketService;

    /**
     * @var SupportAdminFiltersService
     */
    private $supportAdminFiltersService;

    /**
     * SupportService constructor.
     * @param SupportServiceService $supportServiceService
     * @param SupportUserService $supportUserService
     * @param SupportTicketService $supportTicketService
     * @param SupportAdminFiltersService $supportAdminFiltersService
     */
    public function __construct(
        SupportServiceService $supportServiceService,
        SupportUserService $supportUserService,
        SupportTicketService $supportTicketService,
        SupportAdminFiltersService $supportAdminFiltersService
    ) {
        $this->supportServiceService = $supportServiceService;
        $this->supportUserService = $supportUserService;
        $this->supportTicketService = $supportTicketService;
        $this->supportAdminFiltersService = $supportAdminFiltersService;
    }

    /**
     * @param SupportTicket[] $tickets
     * @param int $nbTickets
     * @param SupportTicket|null $ticketToDisplay
     * @return string
     */
    public function renderAdminLayout(array $tickets = [], int $nbTickets = 0, ?SupportTicket $ticketToDisplay = null): string
    {
        $adminMenu = $this->supportServiceService->createAdminMenu();
        if (!$adminMenu) {
            $factory = new SupportBlankState();
            return $factory->render();
        }

        if ($tickets) {
            $departmentsAccess = $this->supportUserService->getUserPermissionsArray($_SESSION['user']['id']);
            foreach ($tickets as $id => $ticket) {
                $check = $this->supportUserService->hasAccessToDepartment($ticket->getService()->getId(), $ticket->getDepartment()->getId(), null, $departmentsAccess);
                if (!$check) {
                    unset($tickets[$id]);
                }
            }
        }

        $createTicketLink = Tools::makeLink('admin', 'support', 'ticket/create');
        return $this->renderLayout($adminMenu, $createTicketLink, $tickets, $nbTickets, $ticketToDisplay);
    }

    /**
     * @param array $tickets
     * @param int $nbTickets
     * @param SupportTicket|null $ticketToDisplay
     * @return string
     */
    public function renderAppLayout(array $tickets = [], int $nbTickets = 0, ?SupportTicket $ticketToDisplay = null): string
    {
        $appMenu = $this->supportServiceService->createAppMenu();
        if (!$appMenu) {
            $factory = new SupportBlankState();
            return $factory->render();
        }

        $createTicketLink = Tools::makeLink('app', 'support', 'ticket/create');

        return $this->renderLayout($appMenu, $createTicketLink, $tickets, $nbTickets, $ticketToDisplay);
    }

    /**
     * @param array $menu
     * @param string $createTicketLink
     * @param array $tickets
     * @param int $nbTickets
     * @param SupportTicket|null $ticketToDisplay
     * @return string
     */
    public function renderLayout(array $menu, string $createTicketLink, array $tickets = [], int $nbTickets = 0, ?SupportTicket $ticketToDisplay = null): string
    {
        if (!$tickets) {
            $factory = new SupportTicketsBlankState();
            $content = $factory->render();
        } else {
            $content = '';
            foreach ($tickets as $ticket) {
                $content .= $this->supportTicketService->getDesignTicketInBox($ticket);
            }
        }

        $inboxToolbar = $this->getInboxToolbar($nbTickets);

        $parser = TwigService::getInstance();
        $parser->reinit();
        $parser->set('menu', $menu)
            ->set('content', $content)
            ->set('inboxToolbar', $inboxToolbar)
            ->set('createTicketLink', $createTicketLink);

        if ($ticketToDisplay) {
            $ticketDesign = $this->supportTicketService->renderTicket($ticketToDisplay);
            $parser->set('ticket', $ticketToDisplay);
            $parser->set('ticketContent', $ticketDesign['content']);
            $parser->set('ticketToolbar', $ticketDesign['toolbar']);
        }

        return $parser->render('common/support/layout.php');
    }

    /**
     * @param int $nbTickets
     * @param int $nbPage
     * @param bool $opened
     * @param bool $toggleAction
     * @return string
     */
    public function getInboxToolbar(int $nbTickets = 0, int $nbPage = 1, bool $opened = true, bool $toggleAction = true): string
    {
        $nbPerPage = SUPPORT_NB_PER_PAGE;
        $lastPage = ceil($nbTickets / $nbPerPage);

        $firstResult = ($nbPage - 1) * $nbPerPage + 1;
        $lastResult = $nbPage * $nbPerPage;
        if ($lastResult > $nbTickets) {
            $lastResult = $nbTickets;
        }

        $filters = '';
        $buttonFilters = '';
        if (RightsService::hasAccess(UNIVERSE_ADMIN_SAV)) {
            $this->supportAdminFiltersService->setOptions();
            $filters = $this->supportAdminFiltersService->render();
            $buttonFilters = $this->supportAdminFiltersService->renderButton();
        }

        return TwigService::getInstance()->set('nbTickets', $nbTickets)
            ->set('nbPage', $nbPage)
            ->set('lastPage', $lastPage)
            ->set('nbPerPage', $nbPerPage)
            ->set('firstResult', $firstResult)
            ->set('lastResult', $lastResult)
            ->set('opened', $opened)
            ->set('toggleAction', $toggleAction)
            ->set('filters', $filters)
            ->set('buttonFilters', $buttonFilters)
            ->render('common/support/toolbar.php');
    }

    /**
     * @return string
     */
    public function getModalNotHappy(): string
    {
        return TwigService::getInstance()->render('common/support/modal-not-happy.php');
    }
}
