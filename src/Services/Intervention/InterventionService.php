<?php

namespace MatGyver\Services\Intervention;

use Doctrine\ORM\EntityManager;
use MatGyver\Entity\Intervention\Intervention;
use MatGyver\Repository\Intervention\InterventionRepository;
use MatGyver\Services\BaseEntityService;

/**
 * Class InterventionService
 * @package MatGyver\Services\Intervention
 * @property InterventionRepository $repository
 * @method InterventionRepository getRepository()
 */
class InterventionService extends BaseEntityService
{
    /**
     * InterventionService constructor.
     * @param EntityManager $em
     */
    public function __construct(EntityManager $em)
    {
        $this->em = $em;
        $this->repository = $em->getRepository(Intervention::class);
    }
}