<?php

namespace MatGyver\Services\Chat;

use Doctrine\ORM\EntityManager;
use Mat<PERSON>yver\Entity\Chat\ChatRoom;
use MatGyver\Entity\User\User;
use MatGyver\Factories\Chat\ChatRoomFactory;
use MatGyver\Repository\Chat\ChatRoomRepository;
use MatGyver\Services\BaseEntityService;

/**
 * Class ChatRoomService
 * @package MatGyver\Services\Chat
 * @property ChatRoomRepository $repository
 * @method ChatRoomRepository getRepository()
 */
class ChatRoomService extends BaseEntityService
{
    /**
     * ChatRoomService constructor.
     * @param EntityManager $em
     */
    public function __construct(EntityManager $em)
    {
        $this->em = $em;
        $this->repository = $em->getRepository(ChatRoom::class);
    }

    /**
     * @param int $idChatRoom
     * @return ChatRoom|null
     */
    public function getChatRoom(int $idChatRoom): ?ChatRoom
    {
        $chatRoom = $this->repository->find($idChatRoom);
        if (!$chatRoom) {
            return null;
        }
        if (!$chatRoom->findUser()) {
            return null;
        }
        return $chatRoom;
    }

    /**
     * @param User $currentUser
     * @param array $users
     * @param string $name
     * @return array
     */
    public function insert(User $currentUser, array $users, string $name = ''): array
    {
        $chatRoom = ChatRoomFactory::createRoom($currentUser, $users, $name);
        try {
            $this->persistAndFlush($chatRoom);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Une erreur est survenue.')];
        }

        return ['valid' => true, 'id' => $chatRoom->getId()];
    }
}
