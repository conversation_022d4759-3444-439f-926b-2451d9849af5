<?php

namespace MatGyver\Services\Chat;

use Doctrine\ORM\EntityManager;
use MatG<PERSON><PERSON>\Components\Mailer\MailSender;
use Mat<PERSON><PERSON>ver\Entity\Chat\ChatMessage;
use MatGyver\Entity\Chat\ChatMessageSubscriber;
use MatG<PERSON>ver\Entity\Chat\ChatUser;
use MatGyver\Enums\UserEmailNotificationTypeEnum;
use MatGyver\Helpers\Tools;
use MatGyver\Repository\Chat\ChatMessageSubscriberRepository;
use MatGyver\Services\BaseEntityService;
use MatGyver\Services\Users\UsersService;

/**
 * Class ChatMessageSubscriberService
 * @package MatGyver\Services\Chat
 * @property ChatMessageSubscriberRepository $repository
 * @method ChatMessageSubscriberRepository getRepository()
 */
class ChatMessageSubscriberService extends BaseEntityService
{
    /**
     * @var MailSender
     */
    private $mailSender;

    /**
     * @var UsersService
     */
    private $usersService;

    /**
     * ChatMessageSubscriberService constructor.
     * @param EntityManager $entityManager
     * @param MailSender $mailSender
     * @param UsersService $usersService
     */
    public function __construct(
        EntityManager $entityManager,
        MailSender $mailSender,
        UsersService $usersService
    ) {
        $this->em = $entityManager;
        $this->repository = $this->em->getRepository(ChatMessageSubscriber::class);
        $this->mailSender = $mailSender;
        $this->usersService = $usersService;
    }

    /**
     * @return int
     */
    public function getNbUnseenMessages(): int
    {
        $user = $this->usersService->getUser();
        if (!$user) {
            return 0;
        }

        return $this->repository->getNbUnseenMessages($user);

        /*$chatUsers = $this->em->getRepository(ChatUser::class)->findBy(['user' => $user]);
        if (!$chatUsers) {
            return 0;
        }

        $nbUnseenMessages = 0;
        foreach ($chatUsers as $chatUser) {
            $nbUnseenMessages += $this->repository->count(['chatUser' => $chatUser, 'seen' => false]);
        }
        return $nbUnseenMessages;*/
    }

    /**
     * @param ChatMessage $message
     * @return array
     */
    public function add(ChatMessage $message): array
    {
        $messageUser = $message->getChatUser();
        $room = $message->getRoom();
        $roomUsers = $room->getUsers();
        foreach ($roomUsers as $roomUser) {
            if ($roomUser === $messageUser) {
                continue;
            }

            $subscriber = new ChatMessageSubscriber();
            $subscriber->setRoom($room);
            $subscriber->setMessage($message);
            $subscriber->setChatUser($roomUser);
            $subscriber->setSeen(false);
            $subscriber->setNotification(false);
            $subscriber->setDateAdd(new \DateTime());
            try {
                $this->persistAndFlush($subscriber);
            } catch (\Exception $e) {
                return ['valid' => false, 'message' => __('Une erreur est survenue lors de l\'enregistrement du message.')];
            }
        }

        return ['valid' => true];
    }

    /**
     * @return array
     */
    public function sendNotifications(): array
    {
        $messageSubscribers = $this->repository->findBy(['seen' => false, 'notification' => false]);
        if (!$messageSubscribers) {
            return ['valid' => true];
        }

        $chatsMessages = [];
        foreach ($messageSubscribers as $messageSubscriber) {
            if (!$messageSubscriber->getChatUser() or !$messageSubscriber->getChatUser()->getUser()) {
                continue;
            }

            $userId = $messageSubscriber->getChatUser()->getUser()->getId();
            if (!isset($chatsMessages[$userId])) {
                $chatsMessages[$userId] = [];
            }
            $chatsMessages[$userId][] = $messageSubscriber;
        }

        if (!$chatsMessages) {
            return ['valid' => true];
        }

        $log = '';
        foreach ($chatsMessages as $userId => $userMessagesSubscribers) {
            $vars = [
                'URL_CHAT' => Tools::makeLink('app', 'conversation', $userMessagesSubscribers[0]->getRoom()->getId()),
                'MESSAGE' => '',
            ];

            $recipients = [['user_id' => $userId]];
            $sendMail = $this->mailSender->sendTemplateToClient('new_message', $vars, UserEmailNotificationTypeEnum::MESSAGE, $recipients, $userMessagesSubscribers[0]->getChatUser()->getClient()->getId());
            if (!$sendMail['valid']) {
                $log .= sprintf('Error send mail for message %s : %s', $userMessagesSubscribers[0]->getMessage()->getId(), $sendMail['message']) . "\n";
            }

            foreach ($userMessagesSubscribers as $userMessageSubscriber) {
                $userMessageSubscriber->setNotification(true);
                $this->persistAndFlush($userMessageSubscriber);
            }
        }

        return ['valid' => !$log, 'log' => $log];
    }
}