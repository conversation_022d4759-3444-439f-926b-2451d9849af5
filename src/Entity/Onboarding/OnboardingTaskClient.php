<?php

namespace MatGyver\Entity\Onboarding;

use Doctrine\ORM\Mapping as ORM;
use Mat<PERSON><PERSON><PERSON>\Entity\Traits\ClientEntity;
use MatG<PERSON>ver\Entity\Traits\UserEntity;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;

/**
 * @ORM\Entity(repositoryClass="MatGyver\Repository\Onboarding\OnboardingTaskClientRepository")
 * @ORM\Table(name="mg_onboarding_tasks_clients")
 */
class OnboardingTaskClient
{

    use ClientEntity;
    use UserEntity;

    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="datetime")
     * @Gedmo\Timestampable(on="create")
     */
    private $date;

    /**
     * @ORM\ManyToOne(targetEntity="MatGyver\Entity\Onboarding\OnboardingGroup")
     * @ORM\JoinColumn(nullable=false, name="group_id", referencedColumnName="id", onDelete="CASCADE")
     */
    private $group;

    /**
     * @ORM\ManyToOne(targetEntity="MatGyver\Entity\Onboarding\OnboardingTask")
     * @ORM\JoinColumn(nullable=false, name="task_id", referencedColumnName="id", onDelete="CASCADE")
     */
    private $task;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getGroup(): ?OnboardingGroup
    {
        return $this->group;
    }

    public function setGroup(?OnboardingGroup $group): self
    {
        $this->group = $group;

        return $this;
    }

    public function getTask(): ?OnboardingTask
    {
        return $this->task;
    }

    public function setTask(?OnboardingTask $task): self
    {
        $this->task = $task;

        return $this;
    }
}
