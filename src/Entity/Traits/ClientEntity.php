<?php

namespace MatGyver\Entity\Traits;

use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Client\Client;

/**
 * Trait ClientEntity
 * @package MatGyver\Entity\Traits
 */
trait ClientEntity
{
    /**
     * @ORM\ManyToOne(targetEntity="MatGyver\Entity\Client\Client")
     * @ORM\JoinColumn(nullable=false, name="client_id", referencedColumnName="id")
     */
    private $client;

    /**
     * @return Client|null
     */
    public function getClient(): ?Client
    {
        return $this->client;
    }

    /**
     * @param Client|null $client
     * @return $this
     */
    public function setClient(?Client $client)
    {
        $this->client = $client;

        return $this;
    }
}
