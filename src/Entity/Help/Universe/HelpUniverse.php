<?php

namespace MatGyver\Entity\Help\Universe;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Help\Article\HelpArticle;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Help\HelpArticlesService;

/**
 * @ORM\Entity(repositoryClass="MatGyver\Repository\Help\Universe\HelpUniverseRepository")
 * @ORM\Table(name="help_universes")
 */
class HelpUniverse
{

    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=50)
     */
    private $name;

    /**
     * @ORM\Column(type="string", length=20)
     */
    private $permalink;

    /**
     * @ORM\Column(type="text")
     */
    private $description = '';

    /**
     * @ORM\Column(type="text")
     */
    private $image;

    /**
     * @ORM\OneToMany(targetEntity="MatGyver\Entity\Help\Article\HelpArticle", mappedBy="universe", cascade={"persist", "remove"}, orphanRemoval=true)
     */
    private $articles;

    public function __construct()
    {
        $this->articles = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getPermalink(): ?string
    {
        return $this->permalink;
    }

    public function setPermalink(string $permalink): self
    {
        $this->permalink = $permalink;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getImage(): ?string
    {
        return $this->image;
    }

    public function setImage(string $image): self
    {
        $this->image = $image;

        return $this;
    }

    /**
     * @return Collection|HelpArticle[]
     */
    public function getArticles(): Collection
    {
        return $this->articles;
    }

    public function addArticle(HelpArticle $helpArticle): self
    {
        if (!$this->articles->contains($helpArticle)) {
            $this->articles[] = $helpArticle;
            $helpArticle->setUniverse($this);
        }

        return $this;
    }

    public function removeArticle(HelpArticle $helpArticle): self
    {
        if ($this->articles->contains($helpArticle)) {
            $this->articles->removeElement($helpArticle);
            // set the owning side to null (unless already changed)
            if ($helpArticle->getUniverse() === $this) {
                $helpArticle->setUniverse(null);
            }
        }

        return $this;
    }

    /**
     * @return int
     */
    public function getCountArticles(): int
    {
        return ($this->articles ? count($this->articles) : 0);
    }

    /**
     * @return int
     */
    public function getCountActiveArticles(): int
    {
        $container = ContainerBuilderService::getInstance();
        return $container->get(HelpArticlesService::class)->getRepository()->countActiveByUniverse($this);
    }
}
