<?php

namespace MatGyver\Entity\Activity;

use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Support\Reply\SupportReply;

/**
 * @ORM\Entity(repositoryClass="MatGyver\Repository\Activity\ActivitySupportReplyRepository")
 */
class ActivitySupportReply extends Activity
{
    /**
     * @ORM\OneToOne(targetEntity="MatGyver\Entity\Support\Reply\SupportReply", cascade={"persist", "remove"})
     * @ORM\JoinColumn(nullable=true, name="support_reply_id", referencedColumnName="id")
     */
    private $supportReply;

    public function getSupportReply(): ?SupportReply
    {
        return $this->supportReply;
    }

    public function setSupportReply(SupportReply $supportReply): self
    {
        $this->supportReply = $supportReply;

        return $this;
    }
}
