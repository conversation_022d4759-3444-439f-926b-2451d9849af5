<?php

namespace <PERSON><PERSON><PERSON><PERSON>\Entity\Dossier;

use <PERSON>trine\ORM\Mapping as ORM;
use <PERSON><PERSON><PERSON><PERSON>\Entity\Client\Client;
use MatGyver\Repository\Dossier\DossierMissionRepository;

/**
 * @ORM\Entity(repositoryClass=DossierMissionRepository::class)
 * @ORM\Table(name="px_dossiers_missions")
 */
class DossierMission
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=Client::class)
     * @ORM\JoinColumn(nullable=false)
     */
    private $client;

    /**
     * @ORM\OneToOne(targetEntity=Dossier::class, inversedBy="mission", cascade={"persist"})
     * @ORM\JoinColumn(nullable=false)
     */
    private $dossier;

    /**
     * @ORM\Column(type="text")
     */
    private $assignment = '';

    /**
     * @ORM\Column(type="text")
     */
    private $contactAssignment = '';

    /**
     * @ORM\Column(type="boolean")
     */
    private $accepted = false;

    /**
     * @ORM\Column(type="datetime")
     */
    private $date;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $dateAcceptance = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getDossier(): ?Dossier
    {
        return $this->dossier;
    }

    public function setDossier(Dossier $dossier): self
    {
        $this->dossier = $dossier;

        return $this;
    }

    public function getAssignment(): ?string
    {
        return $this->assignment;
    }

    public function setAssignment(?string $assignment): self
    {
        $this->assignment = $assignment;

        return $this;
    }

    public function getContactAssignment(): ?string
    {
        return $this->contactAssignment;
    }

    public function setContactAssignment(?string $contactAssignment): self
    {
        $this->contactAssignment = $contactAssignment;

        return $this;
    }

    public function getAccepted(): bool
    {
        return $this->accepted;
    }

    public function setAccepted(bool $accepted): self
    {
        $this->accepted = $accepted;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getDateAcceptance(): ?\DateTimeInterface
    {
        return $this->dateAcceptance;
    }

    public function setDateAcceptance(\DateTimeInterface $dateAcceptance): self
    {
        $this->dateAcceptance = $dateAcceptance;

        return $this;
    }
}
