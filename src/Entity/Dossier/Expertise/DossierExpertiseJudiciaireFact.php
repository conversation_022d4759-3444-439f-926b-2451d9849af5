<?php

namespace MatGyver\Entity\Dossier\Expertise;

use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON><PERSON><PERSON>\Entity\Client\Client;
use MatGyver\Helpers\Assets;
use MatGyver\Repository\Dossier\Expertise\DossierExpertiseJudiciaireFactRepository;

/**
 * @ORM\Entity(repositoryClass=DossierExpertiseJudiciaireFactRepository::class)
 * @ORM\Table(name="px_dossiers_expertises_judiciaires_facts")
 */
class DossierExpertiseJudiciaireFact
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=Client::class)
     * @ORM\JoinColumn(nullable=false)
     */
    private $client;

    /**
     * @ORM\ManyToOne(targetEntity=DossierExpertise::class)
     * @ORM\JoinColumn(nullable=false)
     */
    private $expertise;

    /**
     * @ORM\ManyToOne(targetEntity=DossierExpertiseJudiciaire::class)
     * @ORM\JoinColumn(nullable=false)
     */
    private $expertiseJudiciaire;

    /**
     * @ORM\Column(type="text")
     */
    private $content = '';

    /**
     * @ORM\Column(type="string")
     */
    private $file = '';

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $legend = '';

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $file2 = '';

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $legend2 = '';

    /**
     * @ORM\Column(type="datetime")
     */
    private $date;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getExpertise(): ?DossierExpertise
    {
        return $this->expertise;
    }

    public function setExpertise(?DossierExpertise $expertise): self
    {
        $this->expertise = $expertise;

        return $this;
    }

    public function getExpertiseJudiciaire(): ?DossierExpertiseJudiciaire
    {
        return $this->expertiseJudiciaire;
    }

    public function setExpertiseJudiciaire(?DossierExpertiseJudiciaire $expertiseJudiciaire): self
    {
        $this->expertiseJudiciaire = $expertiseJudiciaire;

        return $this;
    }

    public function getContent(): ?string
    {
        return $this->content;
    }

    public function setContent(string $content): self
    {
        $this->content = $content;

        return $this;
    }

    public function getFile(): ?string
    {
        return $this->file;
    }

    public function setFile(string $file): self
    {
        $this->file = $file;

        return $this;
    }

    public function getLegend(): ?string
    {
        return $this->legend;
    }

    public function setLegend(string $legend): self
    {
        $this->legend = $legend;

        return $this;
    }

    public function getFile2(): ?string
    {
        return $this->file2;
    }

    public function setFile2(string $file2): self
    {
        $this->file2 = $file2;

        return $this;
    }

    public function getLegend2(): ?string
    {
        return $this->legend2;
    }

    public function setLegend2(string $legend2): self
    {
        $this->legend2 = $legend2;

        return $this;
    }

    public function getFileUrl(): ?string
    {
        return Assets::getMediaUrl($this->getExpertise()->getDossier()->getFolder() . $this->getFile() . '?dossier_ref=' . $this->getExpertise()->getDossier()->getReference());
    }

    public function getFileUrl2(): ?string
    {
        return Assets::getMediaUrl($this->getExpertise()->getDossier()->getFolder() . $this->getFile2() . '?dossier_ref=' . $this->getExpertise()->getDossier()->getReference());
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }
}
