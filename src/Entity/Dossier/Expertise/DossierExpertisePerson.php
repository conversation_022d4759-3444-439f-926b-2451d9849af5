<?php

namespace MatG<PERSON>ver\Entity\Dossier\Expertise;

use MatG<PERSON>ver\Entity\Address\Address;
use MatG<PERSON>ver\Entity\Client\Client;
use MatG<PERSON>ver\Entity\Dossier\DossierConvocation;
use Mat<PERSON>yver\Entity\Dossier\DossierInstitution;
use MatG<PERSON>ver\Entity\Dossier\DossierSignature;
use MatGyver\Entity\User\User;
use MatGyver\Enums\ConfigEnum;
use MatGyver\Repository\Dossier\Expertise\DossierExpertisePersonRepository;
use Doctrine\ORM\Mapping as ORM;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Dossier\DossierInstitutionService;
use MatGyver\Services\Dossier\DossierSignatureService;
use MatGyver\Services\Dossier\Expertise\DossierExpertisePersonService;

/**
 * @ORM\Entity(repositoryClass=DossierExpertisePersonRepository::class)
 * @ORM\Table(name="px_dossiers_expertises_persons")
 */
class DossierExpertisePerson
{
    const STATUS_PRESENT = 'present';
    const STATUS_ABSENT = 'absent';
    const STATUS_UNDEFINED = 'undefined';

    const TYPE_BAILIFF = 'bailiff';
    const TYPE_BODYBUILDER = 'bodybuilder';
    const TYPE_BODY_REPAIRER = 'body_repairer';
    const TYPE_BUYER = 'buyer';
    const TYPE_CLIENT = 'client';
    const TYPE_CONTACT = 'contact';
    const TYPE_CONSULTANT = 'consultant';
    const TYPE_EXPERT = 'expert';
    const TYPE_EXPERT_CUSTOM = 'expert_custom';
    const TYPE_EXPERT_TRAINING = 'expert_training';
    const TYPE_EXPERTISE_PLACE = 'expertise_place';
    const TYPE_IMPORTER = 'importer';
    const TYPE_INSURED = 'insured';
    const TYPE_INSURER = 'insurer';
    const TYPE_INSTITUTION = 'institution';
    const TYPE_INTERMEDIARY = 'intermediary';
    const TYPE_FORENSIC_EXPERT = 'forensic_expert';
    const TYPE_INSURER_EXPERT = 'insurer_expert';
    const TYPE_LAWYER = 'lawyer';
    const TYPE_LESE = 'lese';
    const TYPE_LESSOR = 'lessor';
    const TYPE_MANAGER = 'manager';
    const TYPE_MANDATE = 'mandate';
    const TYPE_MANUFACTURER = 'manufacturer';
    const TYPE_OTHER = 'other';
    const TYPE_REPAIRER = 'repairer';
    const TYPE_SELLER = 'seller';
    const TYPE_SINISTER = 'sinister';
    const TYPE_SPARE_PARTS_SELLER = 'spare_parts_seller';
    const TYPE_SPOUSE = 'spouse';
    const TYPE_SUBCONTRACTOR = 'subcontractor';
    const TYPE_TECHNICAL_INSPECTOR = 'technical_inspector';
    const TYPE_THIRD_PARTY = 'third_party';
    const TYPE_TOWING_COMPANY = 'towing_company';
    const TYPE_USER = 'user';
    const TYPE_VEHICLE_OWNER = 'vehicle_owner';
    const TYPE_VEHICLE_OWNER2 = 'vehicle_owner2';
    const TYPE_VEHICLE_USER = 'vehicle_user';
    const TYPE_WAREHOUSE = 'warehouse';
    const TYPE_WITNESS = 'witness';
    const TYPE_WRECKER = 'wrecker';

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=Client::class)
     * @ORM\JoinColumn(nullable=false)
     */
    private $client;

    /**
     * @ORM\ManyToOne(targetEntity=DossierExpertise::class)
     * @ORM\JoinColumn(nullable=false)
     */
    private $expertise;

    /**
     * @ORM\Column(type="string", length=50)
     */
    private $type;

    /**
     * @ORM\ManyToOne(targetEntity=User::class)
     * @ORM\JoinColumn(nullable=true)
     */
    private $user = null;

    /**
     * @ORM\ManyToOne(targetEntity=DossierInstitution::class)
     * @ORM\JoinColumn(nullable=true)
     */
    private $institution = null;

    /**
     * @ORM\ManyToOne(targetEntity=DossierExpertisePerson::class)
     * @ORM\JoinColumn(nullable=true)
     */
    private $parent = null;

    /**
     * @ORM\ManyToOne(targetEntity=Address::class)
     * @ORM\JoinColumn(nullable=true)
     */
    private $place = null;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $firstName = '';

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $lastName = '';

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $email = '';

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $address = '';

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $address2 = '';

    /**
     * @ORM\Column(type="string", length=50)
     */
    private $zip = '';

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $city = '';

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $company = '';

    /**
     * @ORM\Column(type="string", length=50)
     */
    private $telephone = '';

    /**
     * @ORM\Column(type="string", length=20)
     */
    private $status = self::STATUS_UNDEFINED;

    /**
     * @ORM\Column(type="boolean")
     */
    private $excused = false;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $excuseReason = '';

    /**
     * @ORM\OneToOne(targetEntity=DossierConvocation::class, mappedBy="person")
     */
    private $convocation;

    /**
     * @ORM\Column(type="boolean")
     */
    private $signatureRefused = false;

    /**
     * @ORM\Column(type="text")
     */
    private $signatureRefusedReason = '';

    /**
     * @ORM\Column(type="boolean")
     */
    private $hasLeft = false;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $represent = '';

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $reference = '';

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $nameClient = '';

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $emailClient = '';

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $referenceClient = '';

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $insuranceCompany = '';

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $referenceCompany = '';

    /**
     * @ORM\Column(type="string", length=50)
     */
    private $expertRegistrationNumber = '';

    /**
     * @ORM\Column(type="boolean")
     */
    private $hide = false;

    /**
     * @ORM\Column(type="text")
     */
    private $comment = '';

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getExpertise(): ?DossierExpertise
    {
        return $this->expertise;
    }

    public function setExpertise(DossierExpertise $expertise): self
    {
        $this->expertise = $expertise;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function displayType(): string
    {
        if ($this->getType() == self::TYPE_INSTITUTION and $this->getInstitution()) {
            if ($this->getInstitution()->getType() == DossierInstitution::TYPE_CONTACT and $this->getInstitution()->getDossier()->isJudiciaire()) {
                return __('Demandeur');
            }
            if ($this->getInstitution()->getType() == DossierInstitution::TYPE_EXPERT and $this->getInstitution()->getDossier()->isJudiciaire()) {
                return __('Expert de justice');
            }
            return DossierInstitutionService::getType($this->getInstitution()->getType()) ?? '';
        }
        return DossierExpertisePersonService::getType($this->getType()) ?? '';
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getInstitution(): ?DossierInstitution
    {
        return $this->institution;
    }

    public function setInstitution(?DossierInstitution $institution): self
    {
        $this->institution = $institution;

        return $this;
    }

    public function getParent(): ?DossierExpertisePerson
    {
        return $this->parent;
    }

    public function setParent(?DossierExpertisePerson $parent): self
    {
        $this->parent = $parent;

        return $this;
    }

    public function getPlace(): ?Address
    {
        return $this->place;
    }

    public function setPlace(?Address $place): self
    {
        $this->place = $place;

        return $this;
    }

    public function getRepresentativeName(): string
    {
        $name = $this->getLastName() . ' ' . $this->getFirstName();
        if (!trim($name)) {
            $name = $this->getName();
        }

        if ($name and !str_contains($name, 'Maître') and ($this->getType() == self::TYPE_LAWYER or ($this->getType() == self::TYPE_INSTITUTION and $this->getInstitution() and $this->getInstitution()->getType() == DossierInstitution::TYPE_LAWYER))) {
            return __('Maître %s', $name);
        }

        return $name;
    }

    public function getName(): string
    {
        if ($this->getType() == self::TYPE_INSTITUTION) {
            return $this->getInstitution() ? $this->getInstitution()->getName($this->getExpertise()) : '';
        }
        if ($this->getType() == self::TYPE_EXPERT and !$this->getExpertise()->getPlace()) {
            return $this->getExpertise()->getCompany();
        }

        if ($this->getPlace()) {
            return $this->getPlace()->getName();
        }

        $name = $this->getLastName() . ' ' . $this->getFirstName();
        if (trim($name)) {
            return $name;
        }

        return '';
    }

    public function getFirstName(): ?string
    {
        if ($this->firstName or $this->lastName) {
            return $this->firstName;
        }

        if ($this->getType() == self::TYPE_CONTACT) {
            return $this->getExpertise()->getDossier()->getContact() ? $this->getExpertise()->getDossier()->getContact()->getFirstName() : null;
        }
        if ($this->getType() == self::TYPE_USER) {
            return $this->getUser() ? $this->getUser()->getFirstName() : null;
        }
        if ($this->getType() == self::TYPE_INSTITUTION) {
            return $this->getInstitution() ? $this->getInstitution()->getFirstName($this->getExpertise()) : '';
        }
        if ($this->getType() == self::TYPE_EXPERT) {
            return $this->getExpertise()->getPlace() ? ($this->getExpertise()->getPlace()->getFirstName() ?: $this->getExpertise()->getPlace()->getCompany()) : $this->getExpertise()->getCompany();
        }
        if ($this->getPlace()) {
            return $this->getPlace()->getFirstName();
        }

        return '';
    }

    public function setFirstName(string $firstName): self
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getLastName(): ?string
    {
        if ($this->firstName or $this->lastName) {
            return $this->lastName;
        }

        if ($this->getType() == self::TYPE_CONTACT) {
            return $this->getExpertise()->getDossier()->getContact() ? $this->getExpertise()->getDossier()->getContact()->getLastName() : null;
        }
        if ($this->getType() == self::TYPE_USER) {
            return $this->getUser() ? $this->getUser()->getLastName() : null;
        }
        if ($this->getType() == self::TYPE_INSTITUTION) {
            return $this->getInstitution() ? $this->getInstitution()->getLastName($this->getExpertise()) : '';
        }
        if ($this->getType() == self::TYPE_EXPERT) {
            return $this->getExpertise()->getPlace() ? $this->getExpertise()->getPlace()->getLastName() : '';
        }
        if ($this->getPlace()) {
            return $this->getPlace()->getLastName();
        }

        return '';
    }

    public function setLastName(string $lastName): self
    {
        $this->lastName = $lastName;

        return $this;
    }

    public function getEmail(): ?string
    {
        if ($this->email) {
            return $this->email;
        }

        if ($this->getType() == self::TYPE_CONTACT) {
            return $this->getExpertise()->getDossier()->getContact() ? $this->getExpertise()->getDossier()->getContact()->getEmail() : null;
        }
        if ($this->getType() == self::TYPE_USER) {
            return $this->getUser() ? $this->getUser()->getEmail() : null;
        }
        if ($this->getType() == self::TYPE_INSTITUTION) {
            return $this->getInstitution() ? $this->getInstitution()->getEmail($this->getExpertise()) : null;
        }
        if ($this->getType() == self::TYPE_EXPERT) {
            return $this->getExpertise()->getPlace() ? $this->getExpertise()->getPlace()->getEmail() : $this->getExpertise()->getEmail();
        }
        if ($this->getPlace()) {
            return $this->getPlace()->getEmail();
        }

        return '';
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getAddress(): ?string
    {
        if ($this->address) {
            return $this->address;
        }

        if ($this->getType() == self::TYPE_CONTACT) {
            return $this->getContactConfig(ConfigEnum::ADDRESS);
        }
        if ($this->getType() == self::TYPE_USER) {
            return $this->getUserConfig(ConfigEnum::ADDRESS);
        }
        if ($this->getType() == self::TYPE_INSTITUTION) {
            return $this->getInstitutionConfig(ConfigEnum::ADDRESS);
        }
        if ($this->getType() == self::TYPE_EXPERT) {
            return $this->getExpertise()->getPlace() ? $this->getExpertise()->getPlace()->getAddress() : $this->getExpertise()->getAddress();
        }
        if ($this->getPlace()) {
            return $this->getPlace()->getAddress();
        }

        return '';
    }

    public function setAddress(string $address): self
    {
        $this->address = $address;

        return $this;
    }

    public function getAddress2(): ?string
    {
        if ($this->address2) {
            return $this->address2;
        }

        if ($this->getType() == self::TYPE_CONTACT) {
            return $this->getContactConfig(ConfigEnum::ADDRESS2);
        }
        if ($this->getType() == self::TYPE_USER) {
            return $this->getUserConfig(ConfigEnum::ADDRESS2);
        }
        if ($this->getType() == self::TYPE_INSTITUTION) {
            return $this->getInstitutionConfig(ConfigEnum::ADDRESS2);
        }
        if ($this->getType() == self::TYPE_EXPERT) {
            return $this->getExpertise()->getPlace() ? $this->getExpertise()->getPlace()->getAddress2() : $this->getExpertise()->getAddress2();
        }
        if ($this->getPlace()) {
            return $this->getPlace()->getAddress2();
        }

        return '';
    }

    public function setAddress2(string $address2): self
    {
        $this->address2 = $address2;

        return $this;
    }

    public function getZip(): ?string
    {
        if ($this->zip) {
            return $this->zip;
        }

        if ($this->getType() == self::TYPE_CONTACT) {
            return $this->getContactConfig(ConfigEnum::ZIP);
        }
        if ($this->getType() == self::TYPE_USER) {
            return $this->getUserConfig(ConfigEnum::ZIP);
        }
        if ($this->getType() == self::TYPE_INSTITUTION) {
            return $this->getInstitutionConfig(ConfigEnum::ZIP);
        }
        if ($this->getType() == self::TYPE_EXPERT) {
            return $this->getExpertise()->getPlace() ? $this->getExpertise()->getPlace()->getZip() : $this->getExpertise()->getZip();
        }
        if ($this->getPlace()) {
            return $this->getPlace()->getZip();
        }

        return '';
    }

    public function setZip(string $zip): self
    {
        $this->zip = $zip;

        return $this;
    }

    public function getCity(): ?string
    {
        if ($this->city) {
            return $this->city;
        }

        if ($this->getType() == self::TYPE_CONTACT) {
            return $this->getContactConfig(ConfigEnum::CITY);
        }
        if ($this->getType() == self::TYPE_USER) {
            return $this->getUserConfig(ConfigEnum::CITY);
        }
        if ($this->getType() == self::TYPE_INSTITUTION) {
            return $this->getInstitutionConfig(ConfigEnum::CITY);
        }
        if ($this->getType() == self::TYPE_EXPERT) {
            return $this->getExpertise()->getPlace() ? $this->getExpertise()->getPlace()->getCity() : $this->getExpertise()->getCity();
        }
        if ($this->getPlace()) {
            return $this->getPlace()->getCity();
        }

        return '';
    }

    public function setCity(string $city): self
    {
        $this->city = $city;

        return $this;
    }

    public function getTelephone(): ?string
    {
        if ($this->telephone) {
            return $this->telephone;
        }

        if ($this->getType() == self::TYPE_CONTACT) {
            return $this->getContactConfig(ConfigEnum::TELEPHONE);
        }
        if ($this->getType() == self::TYPE_USER) {
            return $this->getUserConfig(ConfigEnum::TELEPHONE);
        }
        if ($this->getType() == self::TYPE_INSTITUTION) {
            return $this->getInstitutionConfig(ConfigEnum::TELEPHONE);
        }
        if ($this->getType() == self::TYPE_EXPERT) {
            return $this->getExpertise()->getPlace() ? $this->getExpertise()->getPlace()->getTelephone() : $this->getExpertise()->getTelephone();
        }
        if ($this->getPlace()) {
            return $this->getPlace()->getTelephone();
        }

        return '';
    }

    public function setTelephone(string $telephone): self
    {
        $this->telephone = $telephone;

        return $this;
    }

    public function getCompany(): ?string
    {
        if ($this->company) {
            return $this->company;
        }

        if ($this->getType() == self::TYPE_CONTACT) {
            return $this->getExpertise()->getDossier()->getContact() ? $this->getExpertise()->getDossier()->getContact()->getCompany() : null;
        }
        if ($this->getType() == self::TYPE_USER) {
            return $this->getUserConfig(ConfigEnum::COMPANY);
        }
        if ($this->getType() == self::TYPE_INSTITUTION) {
            return $this->getInstitution() ? $this->getInstitution()->getCompany($this->getExpertise()) : null;
        }
        if ($this->getType() == self::TYPE_EXPERT) {
            return $this->getExpertise()->getPlace() ? $this->getExpertise()->getPlace()->getCompany() : $this->getExpertise()->getCompany();
        }
        if ($this->getPlace()) {
            return $this->getPlace()->getCompany();
        }

        return '';
    }

    public function setCompany(string $company): self
    {
        $this->company = $company;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getExcused(): ?bool
    {
        return $this->excused;
    }

    public function setExcused(bool $excused): self
    {
        $this->excused = $excused;

        return $this;
    }

    public function getExcuseReason(): ?string
    {
        return $this->excuseReason;
    }

    public function setExcuseReason(string $excuseReason): self
    {
        $this->excuseReason = $excuseReason;

        return $this;
    }

    public function getSignature(string $type = DossierSignature::TYPE_END): ?DossierSignature
    {
        $container = ContainerBuilderService::getInstance();
        return $container->get(DossierSignatureService::class)->getRepository()->findOneBy(['person' => $this, 'dossier' => $this->getExpertise()->getDossier(), 'type' => $type]);
    }

    public function getSignatureRefused(): ?bool
    {
        return $this->signatureRefused;
    }

    public function setSignatureRefused(bool $signatureRefused): self
    {
        $this->signatureRefused = $signatureRefused;

        return $this;
    }

    public function getSignatureRefusedReason(): ?string
    {
        return $this->signatureRefusedReason;
    }

    public function setSignatureRefusedReason(string $signatureRefusedReason): self
    {
        $this->signatureRefusedReason = $signatureRefusedReason;

        return $this;
    }

    public function getHasLeft(): ?bool
    {
        return $this->hasLeft;
    }

    public function setHasLeft(bool $hasLeft): self
    {
        $this->hasLeft = $hasLeft;

        return $this;
    }

    public function getRepresent(): ?string
    {
        if ($this->represent) {
            return $this->represent;
        }

        if ($this->getType() == self::TYPE_INSTITUTION) {
            return $this->getInstitution() ? $this->getInstitution()->getRepresent() : null;
        }

        return null;
    }

    public function setRepresent(string $represent): self
    {
        $this->represent = $represent;

        return $this;
    }

    public function getReference(): ?string
    {
        if ($this->reference) {
            return $this->reference;
        }

        if ($this->getType() == self::TYPE_INSTITUTION) {
            return $this->getInstitution() ? $this->getInstitution()->getReference() : null;
        }

        return null;
    }

    public function setReference(string $reference): self
    {
        $this->reference = $reference;

        return $this;
    }

    public function getNameClient(): ?string
    {
        if ($this->nameClient) {
            return $this->nameClient;
        }

        if ($this->getType() == self::TYPE_INSTITUTION) {
            return $this->getInstitution() ? $this->getInstitution()->getNameClient() : null;
        }

        return null;
    }

    public function setNameClient(string $nameClient): self
    {
        $this->nameClient = $nameClient;

        return $this;
    }

    public function getEmailClient(): ?string
    {
        if ($this->emailClient) {
            return $this->emailClient;
        }

        if ($this->getType() == self::TYPE_INSTITUTION) {
            return $this->getInstitution() ? $this->getInstitution()->getEmailClient() : null;
        }

        return null;
    }

    public function setEmailClient(string $emailClient): self
    {
        $this->emailClient = $emailClient;

        return $this;
    }

    public function getReferenceClient(): ?string
    {
        if ($this->referenceClient) {
            return $this->referenceClient;
        }

        if ($this->getType() == self::TYPE_INSTITUTION) {
            return $this->getInstitution() ? $this->getInstitution()->getReferenceClient() : null;
        }

        return null;
    }

    public function setReferenceClient(string $referenceClient): self
    {
        $this->referenceClient = $referenceClient;

        return $this;
    }

    public function getInsuranceCompany(): ?string
    {
        if ($this->insuranceCompany) {
            return $this->insuranceCompany;
        }

        if ($this->getType() == self::TYPE_INSTITUTION) {
            return $this->getInstitution() ? $this->getInstitution()->getInsuranceCompany() : null;
        }

        return null;
    }

    public function setInsuranceCompany(string $insuranceCompany): self
    {
        $this->insuranceCompany = $insuranceCompany;

        return $this;
    }

    public function getReferenceCompany(): ?string
    {
        if ($this->referenceCompany) {
            return $this->referenceCompany;
        }

        if ($this->getType() == self::TYPE_INSTITUTION) {
            return $this->getInstitution() ? $this->getInstitution()->getReferenceCompany() : null;
        }

        return null;
    }

    public function setReferenceCompany(string $referenceCompany): self
    {
        $this->referenceCompany = $referenceCompany;

        return $this;
    }

    public function getExpertRegistrationNumber(): ?string
    {
        if ($this->expertRegistrationNumber) {
            return $this->expertRegistrationNumber;
        }

        if ($this->getType() == self::TYPE_INSTITUTION) {
            return $this->getInstitution() ? $this->getInstitution()->getExpertRegistrationNumber() : null;
        }

        return '';
    }

    public function setExpertRegistrationNumber(string $expertRegistrationNumber): self
    {
        $this->expertRegistrationNumber = $expertRegistrationNumber;

        return $this;
    }

    public function getConvocation(): ?DossierConvocation
    {
        return $this->convocation;
    }

    public function setConvocation(DossierConvocation $convocation): self
    {
        // set the owning side of the relation if necessary
        if ($convocation->getPerson() !== $this) {
            $convocation->setPerson($this);
        }

        $this->convocation = $convocation;

        return $this;
    }

    /**
     * @param string $type
     * @return string|null
     */
    public function getContactConfig(string $type): ?string
    {
        if (!$this->getExpertise()->getDossier()->getContact()) {
            return null;
        }
        $contact = $this->getExpertise()->getDossier()->getContact();
        if (!method_exists($contact, 'get' . ucfirst($type))) {
            return null;
        }
        return $contact->{'get' . ucfirst($type)}();
    }

    /**
     * @param string $type
     * @return string|null
     */
    public function getUserConfig(string $type): ?string
    {
        if (!$this->getUser()) {
            return null;
        }
        $userConfig = $this->getUser()->getUserConfigsAsArray();
        return $userConfig[$type] ?? null;
    }

    /**
     * @param string $type
     * @return string|null
     */
    public function getInstitutionConfig(string $type): ?string
    {
        $institution = $this->getInstitution();
        if (!$institution) {
            return null;
        }
        if (!method_exists($institution, 'get' . ucfirst($type))) {
            return null;
        }

        $reflection = new \ReflectionMethod($institution, 'get' . ucfirst($type));
        $params = $reflection->getParameters();
        $args = null;
        if ($params and isset($params[0]) and $params[0]->getName() == 'expertise') {
            $args = $this->getExpertise();
        }
        return call_user_func([$institution, 'get' . ucfirst($type)], $args);
    }

    /**
     * @param string $separator
     * @return string
     */
    public function displayPlace(string $separator = '<br>'): string
    {
        $place = '';
        if ($this->getPlace()) {
            if ($this->getPlace()->getCompany()) {
                $place = $this->getPlace()->getCompany() . $separator;
            }
            $place .= $this->getPlace()->getFirstName() . ' ' . $this->getPlace()->getLastName() . $separator;
            $place .= $this->getPlace()->getAddress() . $separator;
            $place .= $this->getPlace()->getZip() . ' ' . $this->getPlace()->getCity() . $separator;
        } else {
            if ($this->getCompany()) {
                $place = $this->getCompany() . $separator;
            }
            $place .= $this->getFirstName() . ' ' . $this->getLastName() . $separator;
            $place .= $this->getAddress() . $separator;
            $place .= $this->getZip() . ' ' . $this->getCity() . $separator;
        }

        return rtrim($place, $separator);
    }

    public function isHidden(): bool
    {
        return $this->hide;
    }

    public function setHide(bool $hide): self
    {
        $this->hide = $hide;

        return $this;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(string $comment): self
    {
        $this->comment = $comment;

        return $this;
    }

    /**
     * @return DossierExpertisePerson[]
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function getChildren(): array
    {
        $container = ContainerBuilderService::getInstance();
        return $container->get(DossierExpertisePersonService::class)->getRepository()->findBy(['parent' => $this]);
    }
}
