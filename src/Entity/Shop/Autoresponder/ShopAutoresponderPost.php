<?php

namespace MatGyver\Entity\Shop\Autoresponder;

use Doctrine\ORM\Mapping as ORM;
use MatG<PERSON><PERSON>\Entity\Integration\Account\IntegrationAccount;
use MatGyver\Entity\Shop\Product\ShopProduct;
use MatGyver\Entity\Traits\ClientEntity;

/**
 * @ORM\Entity(repositoryClass="MatGyver\Repository\Shop\Autoresponder\ShopAutoresponderPostRepository")
 * @ORM\Table(name="shop_autoresponders_post")
 */
class ShopAutoresponderPost
{
    use ClientEntity;

    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=20)
     */
    private $type;

    /**
     * @ORM\Column(type="text")
     */
    private $datas;

    /**
     * @ORM\Column(type="datetime")
     */
    private $date;

    /**
     * @ORM\ManyToOne(targetEntity="MatGyver\Entity\Integration\Account\IntegrationAccount")
     * @ORM\JoinColumn(nullable=false, name="account_id", referencedColumnName="id")
     */
    private $account;

    /**
     * @ORM\ManyToOne(targetEntity="MatGyver\Entity\Shop\Product\ShopProduct")
     * @ORM\JoinColumn(nullable=false, name="product_id", referencedColumnName="id")
     */
    private $product;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }
    
    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }
    
    public function getDatas(): ?string
    {
        return $this->datas;
    }

    public function setDatas(string $datas): self
    {
        $this->datas = $datas;

        return $this;
    }
    
    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getAccount(): ?IntegrationAccount
    {
        return $this->account;
    }

    public function setAccount(?IntegrationAccount $account): self
    {
        $this->account = $account;

        return $this;
    }

    public function getProduct(): ?ShopProduct
    {
        return $this->product;
    }

    public function setProduct(?ShopProduct $product): self
    {
        $this->product = $product;

        return $this;
    }
}
