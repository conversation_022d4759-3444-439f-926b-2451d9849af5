<?php

namespace MatGyver\Entity\Shop\Cart;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Shop\Customer\ShopCustomer;
use MatGyver\Entity\Traits\ClientEntity;
use Gedmo\Mapping\Annotation as Gedmo;

/**
 * @ORM\Entity(repositoryClass="MatGyver\Repository\Shop\Cart\ShopCartRepository")
 * @ORM\Table(name="shop_cart")
 */
class ShopCart
{
    use ClientEntity;

    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="text")
     */
    private $discount;

    /**
     * @ORM\Column(type="string", length=50)
     */
    private $ip;

    /**
     * @ORM\Column(type="text")
     */
    private $url;

    /**
     * @ORM\Column(type="datetime")
     * @Gedmo\Timestampable(on="create")
     */
    private $date;

    /**
     * @ORM\Column(type="datetime")
     */
    private $dateModification;

    /**
     * @ORM\OneToMany(targetEntity="MatGyver\Entity\Shop\Cart\ShopCartProduct", mappedBy="shopCart", cascade={"persist", "remove"})
     */
    private $shopCartProducts;

    /**
     * @ORM\ManyToOne(targetEntity="MatGyver\Entity\Shop\Customer\ShopCustomer")
     * @ORM\JoinColumn(nullable=false, name="customer_id", referencedColumnName="id")
     */
    private $customer;

    public function __construct()
    {
        $this->shopCartProducts = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getDiscount(): ?string
    {
        return $this->discount;
    }

    public function setDiscount(string $discount): self
    {
        $this->discount = $discount;

        return $this;
    }

    public function getIp(): ?string
    {
        return $this->ip;
    }

    public function setIp(string $ip): self
    {
        $this->ip = $ip;

        return $this;
    }

    public function getUrl(): ?string
    {
        return $this->url;
    }

    public function setUrl(string $url): self
    {
        $this->url = $url;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getDateModification(): ?\DateTimeInterface
    {
        return $this->dateModification;
    }

    public function setDateModification(\DateTimeInterface $dateModification): self
    {
        $this->dateModification = $dateModification;

        return $this;
    }

    /**
     * @return Collection|ShopCartProduct[]
     */
    public function getShopCartProducts(): Collection
    {
        return $this->shopCartProducts;
    }

    public function addShopCartProduct(ShopCartProduct $shopCartProduct): self
    {
        if (!$this->shopCartProducts->contains($shopCartProduct)) {
            $this->shopCartProducts[] = $shopCartProduct;
            $shopCartProduct->setShopCart($this);
        }

        return $this;
    }

    public function removeShopCartProduct(ShopCartProduct $shopCartProduct): self
    {
        if ($this->shopCartProducts->contains($shopCartProduct)) {
            $this->shopCartProducts->removeElement($shopCartProduct);
            // set the owning side to null (unless already changed)
            if ($shopCartProduct->getShopCart() === $this) {
                $shopCartProduct->setShopCart(null);
            }
        }

        return $this;
    }

    public function getCustomer(): ?ShopCustomer
    {
        return $this->customer;
    }

    public function setCustomer(?ShopCustomer $customer): ShopCart
    {
        $this->customer = $customer;

        return $this;
    }

}
