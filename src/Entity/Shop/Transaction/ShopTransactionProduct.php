<?php

namespace MatGyver\Entity\Shop\Transaction;

use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use MatGyver\Entity\Shop\Product\ShopProduct;

/**
 * @ORM\Entity(repositoryClass="MatGyver\Repository\Shop\Transaction\ShopTransactionProductRepository")
 * @ORM\Table(name="shop_transactions_products")
 */
class ShopTransactionProduct
{
    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="integer")
     */
    private $quantity;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $name;

    /**
     * @ORM\Column(type="float")
     */
    private $priceTaxExcl;

    /**
     * @ORM\Column(type="float")
     */
    private $vat;

    /**
     * @ORM\Column(type="float")
     */
    private $priceTaxIncl;

    /**
     * @ORM\Column(type="string", length=10)
     */
    private $currency = 'EUR';

    /**
     * @ORM\Column(type="text")
     */
    private $attributes = '';

    /**
     * @ORM\Column(type="datetime")
     * @Gedmo\Timestampable(on="create")
     */
    private $date;

    /**
     * @ORM\ManyToOne(targetEntity="MatGyver\Entity\Shop\Transaction\ShopTransaction", inversedBy="transactionProducts")
     * @ORM\JoinColumn(nullable=false, name="transaction_id", referencedColumnName="id")
     */
    private $transaction;

    /**
     * @ORM\ManyToOne(targetEntity="MatGyver\Entity\Shop\Product\ShopProduct")
     * @ORM\JoinColumn(nullable=false, name="product_id", referencedColumnName="id")
     */
    private $product;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getQuantity(): ?int
    {
        return $this->quantity;
    }

    public function setQuantity(int $quantity): self
    {
        $this->quantity = $quantity;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getVat(): ?float
    {
        return $this->vat;
    }

    public function setVat(float $vat): self
    {
        $this->vat = $vat;

        return $this;
    }

    public function getPriceTaxExcl(): ?float
    {
        return $this->priceTaxExcl;
    }

    public function setPriceTaxExcl(float $priceTaxExcl): self
    {
        $this->priceTaxExcl = $priceTaxExcl;

        return $this;
    }

    public function getPriceTaxIncl(): ?float
    {
        return $this->priceTaxIncl;
    }

    public function setPriceTaxIncl(float $priceTaxIncl): self
    {
        $this->priceTaxIncl = $priceTaxIncl;

        return $this;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;

        return $this;
    }

    public function getAttributes(): ?string
    {
        return $this->attributes;
    }

    public function setAttributes(string $attributes): self
    {
        $this->attributes = $attributes;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getTransaction(): ?ShopTransaction
    {
        return $this->transaction;
    }

    public function setTransaction(?ShopTransaction $transaction): self
    {
        $this->transaction = $transaction;

        return $this;
    }

    public function getProduct(): ?ShopProduct
    {
        return $this->product;
    }

    public function setProduct(?ShopProduct $product): self
    {
        $this->product = $product;

        return $this;
    }

    /**
     * @return string
     */
    public function getDisplayMontantHt(): string
    {
        return \MatGyver\Helpers\Number::formatAmount($this->priceTaxExcl, $this->currency);
    }

    /**
     * @return string
     */
    public function getDisplayMontantTtc(): string
    {
        return \MatGyver\Helpers\Number::formatAmount($this->priceTaxIncl, $this->currency);
    }

}
