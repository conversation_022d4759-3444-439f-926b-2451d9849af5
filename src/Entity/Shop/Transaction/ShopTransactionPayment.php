<?php

namespace MatGyver\Entity\Shop\Transaction;

use Doctrine\ORM\Mapping as ORM;
use MatG<PERSON><PERSON>\Entity\Traits\ClientEntity;

/**
 * @ORM\Entity(repositoryClass="MatGyver\Repository\Shop\Transaction\ShopTransactionPaymentRepository")
 * @ORM\Table(name="shop_transactions_payments")
 */
class ShopTransactionPayment
{
    use ClientEntity;

    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="integer")
     */
    private $userId;

    /**
     * @ORM\Column(type="text")
     */
    private $comment;

    /**
     * @ORM\Column(type="float")
     */
    private $amount;

    /**
     * @ORM\Column(type="datetime")
     */
    private $date;

    /**
     * @ORM\ManyToOne(targetEntity="MatGyver\Entity\Shop\Transaction\ShopTransaction", inversedBy="transactionPayments")
     * @ORM\JoinColumn(nullable=false, name="transaction_id", referencedColumnName="id")
     */
    private $transaction;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getUserId(): ?int
    {
        return $this->userId;
    }

    public function setUserId(int $userId): self
    {
        $this->userId = $userId;

        return $this;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(string $comment): self
    {
        $this->comment = $comment;

        return $this;
    }

    public function getAmount(): ?float
    {
        return $this->amount;
    }

    public function setAmount(float $amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    public function getTransaction(): ?ShopTransaction
    {
        return $this->transaction;
    }

    public function setTransaction(?ShopTransaction $transaction): self
    {
        $this->transaction = $transaction;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

}
