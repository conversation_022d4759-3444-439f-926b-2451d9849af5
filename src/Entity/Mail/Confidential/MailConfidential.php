<?php

namespace MatGyver\Entity\Mail\Confidential;

use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON><PERSON><PERSON>\Entity\Dossier\Dossier;
use MatG<PERSON>ver\Entity\Traits\ClientEntity;

/**
 * @ORM\Entity(repositoryClass="MatGyver\Repository\Mail\Confidential\MailConfidentialRepository")
 * @ORM\Table(name="mg_mail_confidential")
 */
class MailConfidential
{
    use ClientEntity;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="MatGyver\Entity\Dossier\Dossier")
     * @ORM\JoinColumn(nullable=false)
     */
    private $dossier;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $subject;

    /**
     * @ORM\Column(type="text")
     */
    private $message;

    /**
     * @ORM\Column(type="text")
     */
    private $attachments;

    /**
     * @ORM\Column(type="boolean")
     */
    private $opened = false;

    /**
     * @ORM\Column(type="datetime")
     */
    private $date;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $dateOpened = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getDossier(): ?Dossier
    {
        return $this->dossier;
    }

    public function setDossier(?Dossier $dossier): self
    {
        $this->dossier = $dossier;

        return $this;
    }

    public function getSubject(): ?string
    {
        return $this->subject;
    }

    public function setSubject(string $subject): self
    {
        $this->subject = $subject;

        return $this;
    }

    public function getMessage(): ?string
    {
        return $this->message;
    }

    public function setMessage(string $message): self
    {
        $this->message = $message;

        return $this;
    }

    public function getAttachments(): ?string
    {
        return $this->attachments;
    }

    public function setAttachments(string $attachments): self
    {
        $this->attachments = $attachments;

        return $this;
    }

    public function getOpened(): ?bool
    {
        return $this->opened;
    }

    public function setOpened(bool $opened): self
    {
        $this->opened = $opened;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getDateOpened(): ?\DateTimeInterface
    {
        return $this->dateOpened;
    }

    public function setDateOpened(\DateTimeInterface $dateOpened): self
    {
        $this->dateOpened = $dateOpened;

        return $this;
    }
}