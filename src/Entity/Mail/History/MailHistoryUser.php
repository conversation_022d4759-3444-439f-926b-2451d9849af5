<?php

namespace MatGyver\Entity\Mail\History;

use Doctrine\ORM\Mapping as ORM;
use Mat<PERSON><PERSON><PERSON>\Entity\Traits\ClientEntity;
use Gedmo\Mapping\Annotation as Gedmo;
use MatG<PERSON>ver\Entity\User\User;

/**
 * @ORM\Entity(repositoryClass="MatGyver\Repository\Mail\History\MailHistoryRepository")
 * @ORM\Table(name="mg_mail_history_users")
 */
class MailHistoryUser
{
    use ClientEntity;

    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="MatGyver\Entity\Mail\History\MailHistory")
     * @ORM\JoinColumn(nullable=false, name="history_id", referencedColumnName="id")
     */
    private $mail;

    /**
     * @ORM\ManyToOne(targetEntity="MatGyver\Entity\User\User")
     * @ORM\JoinColumn(nullable=true, name="user_id", referencedColumnName="id")
     */
    private $user;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $firstName;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $lastName;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $email;

    /**
     * @ORM\Column(type="boolean")
     */
    private $sent = false;

    /**
     * @ORM\Column(type="boolean")
     */
    private $error = false;

    /**
     * @ORM\Column(type="string")
     */
    private $errorMessage = '';

    /**
     * @ORM\Column(type="boolean")
     */
    private $open = false;

    /**
     * @ORM\Column(type="datetime")
     * @Gedmo\Timestampable(on="create")
     */
    private $date;

    /**
     * @ORM\Column(type="datetime")
     */
    private $dateOpen = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getMail(): ?MailHistory
    {
        return $this->mail;
    }

    public function setMail(?MailHistory $mail): self
    {
        $this->mail = $mail;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user)
    {
        $this->user = $user;

        return $this;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(string $firstName): self
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName): self
    {
        $this->lastName = $lastName;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getSent(): ?bool
    {
        return $this->sent;
    }

    public function setSent(bool $sent): self
    {
        $this->sent = $sent;

        return $this;
    }

    public function getOpen(): ?bool
    {
        return $this->open;
    }

    public function setOpen(bool $open): self
    {
        $this->open = $open;

        return $this;
    }

    public function getError(): ?bool
    {
        return $this->error;
    }

    public function setError(bool $error): self
    {
        $this->error = $error;

        return $this;
    }

    public function getErrorMessage(): ?string
    {
        return $this->errorMessage;
    }

    public function setErrorMessage(string $errorMessage): self
    {
        $this->errorMessage = $errorMessage;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getDateOpen(): ?\DateTimeInterface
    {
        return $this->dateOpen;
    }

    public function setDateOpen(\DateTimeInterface $dateOpen): self
    {
        $this->dateOpen = $dateOpen;

        return $this;
    }
}
