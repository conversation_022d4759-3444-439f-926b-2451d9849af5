<?php

namespace MatGyver\Entity\Support\Reply;

use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Support\Ticket\SupportTicket;
use MatGyver\Entity\Traits\ClientEntity;
use MatGyver\Entity\Traits\UserEntity;
use MatGyver\Entity\User\User;

/**
 * @ORM\Entity(repositoryClass="MatGyver\Repository\Support\Reply\SupportReplyRepository")
 * @ORM\Table(name="hdk_replies")
 */
class SupportReply
{

    use ClientEntity;
    use UserEntity;

    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="text")
     */
    private $message;

    /**
     * @ORM\Column(type="text")
     */
    private $attachments;

    /**
     * @ORM\Column(type="boolean")
     */
    private $seen;

    /**
     * @ORM\Column(type="boolean")
     */
    private $seenAdmin;

    /**
     * @ORM\Column(type="datetime")
     */
    private $date;

    /**
     * @ORM\ManyToOne(targetEntity="MatGyver\Entity\User\User")
     * @ORM\JoinColumn(nullable=false, name="admin_id", referencedColumnName="id")
     */
    private $admin;

    /**
     * @ORM\ManyToOne(targetEntity="MatGyver\Entity\Support\Ticket\SupportTicket")
     * @ORM\JoinColumn(nullable=false, name="ticket_id", referencedColumnName="id")
     */
    private $ticket;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getMessage(): ?string
    {
        return $this->message;
    }

    public function setMessage(string $message): self
    {
        $this->message = $message;

        return $this;
    }

    public function getAttachments(): ?string
    {
        return $this->attachments;
    }

    public function setAttachments(string $attachments): self
    {
        $this->attachments = $attachments;

        return $this;
    }

    public function getSeen(): ?bool
    {
        return $this->seen;
    }

    public function setSeen(bool $seen): self
    {
        $this->seen = $seen;

        return $this;
    }

    public function getSeenAdmin(): ?bool
    {
        return $this->seenAdmin;
    }

    public function setSeenAdmin(bool $seenAdmin): self
    {
        $this->seenAdmin = $seenAdmin;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    /**
     * @return User|null
     */
    public function getAdmin(): ?User
    {
        return $this->admin;
    }

    /**
     * @param User|null $admin
     * @return $this
     */
    public function setAdmin(?User $admin): SupportReply
    {
        $this->admin = $admin;

        return $this;
    }

    public function getTicket(): ?SupportTicket
    {
        return $this->ticket;
    }

    public function setTicket(?SupportTicket $ticket): self
    {
        $this->ticket = $ticket;

        return $this;
    }

}
