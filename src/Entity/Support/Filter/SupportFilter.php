<?php

namespace MatGyver\Entity\Support\Filter;

use Doctrine\ORM\Mapping as ORM;
use MatG<PERSON>ver\Entity\Support\Department\SupportDepartment;
use MatGyver\Entity\Support\Service\SupportService;
use MatGyver\Entity\Support\User\SupportUser;

/**
 * @ORM\Entity(repositoryClass="MatGyver\Repository\Support\Filter\SupportFilterRepository")
 * @ORM\Table(name="hdk_filters")
 */
class SupportFilter
{

    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=200, nullable=true)
     */
    private $name;

    /**
     * @ORM\Column(type="string", length=50, nullable=true)
     */
    private $type;

    /**
     * @ORM\Column(type="string", length=20, nullable=true)
     */
    private $cond;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private $content;

    /**
     * @ORM\Column(type="string", length=50, nullable=true)
     */
    private $action;

    /**
     * @ORM\ManyToOne(targetEntity="MatGyver\Entity\Support\User\SupportUser")
     * @ORM\JoinColumn(nullable=true, name="user_id", referencedColumnName="id")
     */
    private $user;

    /**
     * @ORM\ManyToOne(targetEntity="MatGyver\Entity\Support\Service\SupportService")
     * @ORM\JoinColumn(nullable=true, name="service_id", referencedColumnName="id")
     */
    private $service;

    /**
     * @ORM\ManyToOne(targetEntity="MatGyver\Entity\Support\Department\SupportDepartment")
     * @ORM\JoinColumn(nullable=true, name="department_id", referencedColumnName="id")
     */
    private $department;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $date;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getCond(): ?string
    {
        return $this->cond;
    }

    public function setCond(?string $cond): self
    {
        $this->cond = $cond;

        return $this;
    }

    public function getContent(): ?string
    {
        return $this->content;
    }

    public function setContent(?string $content): self
    {
        $this->content = $content;

        return $this;
    }

    public function getAction(): ?string
    {
        return $this->action;
    }

    public function setAction(?string $action): self
    {
        $this->action = $action;

        return $this;
    }

    public function getUser(): ?SupportUser
    {
        return $this->user;
    }

    public function setUser(?SupportUser $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getService(): ?SupportService
    {
        return $this->service;
    }

    public function setService(?SupportService $service): self
    {
        $this->service = $service;

        return $this;
    }

    public function getDepartment(): ?SupportDepartment
    {
        return $this->department;
    }

    public function setDepartment(?SupportDepartment $department): self
    {
        $this->department = $department;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(?\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

}
