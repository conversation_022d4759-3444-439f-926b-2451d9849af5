<?php

namespace <PERSON><PERSON><PERSON>ver\Entity\Tos\Client;

use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON><PERSON><PERSON>\Entity\Tos\Tos;
use MatGyver\Entity\Traits\ClientEntity;
use MatGyver\Entity\Traits\UserEntity;

/**
 * @ORM\Entity(repositoryClass="MatGyver\Repository\Tos\Client\TosClientRepository")
 * @ORM\Table(name="mg_tos_clients")
 */
class TosClient
{
    use ClientEntity;
    use UserEntity;

    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="MatGyver\Entity\Tos\Tos")
     * @ORM\JoinColumn(name="tos_id", referencedColumnName="id")
     */
    private $tos;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $email;

    /**
     * @ORM\Column(type="string", length=50)
     */
    private $ip;

    /**
     * @ORM\Column(type="datetime")
     */
    private $date;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getTos(): ?Tos
    {
        return $this->tos;
    }

    public function setTos(?Tos $tos): self
    {
        $this->tos = $tos;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getIp(): ?string
    {
        return $this->ip;
    }

    public function setIp(string $ip): self
    {
        $this->ip = $ip;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

}
