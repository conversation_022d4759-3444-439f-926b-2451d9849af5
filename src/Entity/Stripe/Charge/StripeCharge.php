<?php

namespace MatGyver\Entity\Stripe\Charge;

use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\PaymentMethod\PaymentMethodCharge;
use MatGyver\Entity\Stripe\Payout\StripePayout;

/**
 * @ORM\Entity(repositoryClass="MatGyver\Repository\Stripe\Charge\StripeChargeRepository")
 * @ORM\Table(name="mg_stripe_charges")
 */
class StripeCharge extends PaymentMethodCharge
{
    /**
     * @ORM\Column(type="string", length=100)
     */
    private $token;

    /**
     * @ORM\Column(type="string", length=100, nullable=true)
     */
    private $customerId;

    /**
     * @ORM\Column(type="string", length=100)
     */
    private $paymentMethod;

    /**
     * @ORM\Column(type="string", length=100)
     */
    private $secretKey;

    /**
     * @ORM\Column(type="string", length=100, nullable=true)
     */
    private $idStripeCharge;

    /**
     * @ORM\Column(type="string", length=100, nullable=true)
     */
    private $idStripePaymentIntent;

    /**
     * @ORM\Column(type="integer")
     */
    private $amount;

    /**
     * @ORM\Column(type="float")
     */
    private $fees = 0;

    /**
     * @ORM\ManyToOne(targetEntity="MatGyver\Entity\Stripe\Payout\StripePayout")
     * @ORM\JoinColumn(nullable=true, name="payout_id", referencedColumnName="id")
     */
    private $payout = null;

    public function getToken(): ?string
    {
        return $this->token;
    }

    public function setToken(string $token): self
    {
        $this->token = $token;

        return $this;
    }

    public function getCustomerId(): ?string
    {
        return $this->customerId;
    }

    public function setCustomerId(?string $customerId): self
    {
        $this->customerId = $customerId;

        return $this;
    }

    public function getPaymentMethod(): ?string
    {
        return $this->paymentMethod;
    }

    public function setPaymentMethod(string $paymentMethod): self
    {
        $this->paymentMethod = $paymentMethod;

        return $this;
    }

    public function getSecretKey(): ?string
    {
        return $this->secretKey;
    }

    public function setSecretKey(string $secretKey): self
    {
        $this->secretKey = $secretKey;

        return $this;
    }

    public function getIdStripeCharge(): ?string
    {
        return $this->idStripeCharge;
    }

    public function setIdStripeCharge(?string $idStripeCharge): self
    {
        $this->idStripeCharge = $idStripeCharge;

        return $this;
    }

    public function getIdStripePaymentIntent(): ?string
    {
        return $this->idStripePaymentIntent;
    }

    public function setIdStripePaymentIntent(?string $idStripePaymentIntent): self
    {
        $this->idStripePaymentIntent = $idStripePaymentIntent;

        return $this;
    }

    public function getAmount(): ?int
    {
        return $this->amount;
    }

    public function setAmount(int $amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    public function getFees(): ?float
    {
        return $this->fees;
    }

    public function setFees(float $fees): self
    {
        $this->fees = $fees;

        return $this;
    }

    public function getPayout(): ?StripePayout
    {
        return $this->payout;
    }

    public function setPayout(?StripePayout $payout): self
    {
        $this->payout = $payout;

        return $this;
    }
}
