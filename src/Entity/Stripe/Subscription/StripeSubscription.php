<?php

namespace MatGyver\Entity\Stripe\Subscription;

use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use MatGyver\Entity\PaymentMethod\PaymentMethodSubscription;

/**
 * @ORM\Entity(repositoryClass="MatGyver\Repository\Stripe\Subscription\StripeSubscriptionRepository")
 * @ORM\Table(name="mg_stripe_subscriptions")
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false)
 */
class StripeSubscription extends PaymentMethodSubscription
{
    /**
     * @ORM\Column(type="string", length=100)
     */
    private $customerId;

    /**
     * @ORM\Column(type="string", length=100)
     */
    private $paymentMethod;

    /**
     * @ORM\Column(type="string", length=100)
     */
    private $secretKey;

    /**
     * @ORM\Column(type="integer")
     */
    private $amount;

    public function getCustomerId(): ?string
    {
        return $this->customerId;
    }

    public function setCustomerId(string $customerId): self
    {
        $this->customerId = $customerId;

        return $this;
    }

    public function getPaymentMethod(): ?string
    {
        return $this->paymentMethod;
    }

    public function setPaymentMethod(string $paymentMethod): self
    {
        $this->paymentMethod = $paymentMethod;

        return $this;
    }

    public function getSecretKey(): ?string
    {
        return $this->secretKey;
    }

    public function setSecretKey(string $secretKey): self
    {
        $this->secretKey = $secretKey;

        return $this;
    }

    public function getAmount(): ?int
    {
        return $this->amount;
    }

    public function setAmount(int $amount): self
    {
        $this->amount = $amount;

        return $this;
    }
}
