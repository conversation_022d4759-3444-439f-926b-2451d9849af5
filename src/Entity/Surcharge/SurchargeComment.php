<?php

namespace <PERSON><PERSON><PERSON><PERSON>\Entity\Surcharge;

use <PERSON><PERSON><PERSON><PERSON>\Entity\Client\Client;
use <PERSON>G<PERSON><PERSON>\Repository\Surcharge\SurchargeCommentRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=SurchargeCommentRepository::class)
 * @ORM\Table(name="px_surcharges_comments")
 */
class SurchargeComment
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=Client::class)
     * @ORM\JoinColumn(nullable=false)
     */
    private $client;

    /**
     * @ORM\Column(type="text")
     */
    private $comment;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(string $comment): self
    {
        $this->comment = $comment;

        return $this;
    }
}
