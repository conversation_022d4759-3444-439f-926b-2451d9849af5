<?php

namespace Mat<PERSON><PERSON><PERSON>\Entity\Game;

use Doctrine\ORM\Mapping as ORM;
use Mat<PERSON><PERSON><PERSON>\Helpers\Game\GameCondition;
use Mat<PERSON>yver\Helpers\Game\GameReward;
use MatGyver\Repository\Game\GameDailyQuestRepository;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Game\GameDailyQuestClientService;

/**
 * @ORM\Entity(repositoryClass=GameDailyQuestRepository::class)
 * @ORM\Table(name="game_daily_quests")
 */
class GameDailyQuest
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $name;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $image;

    /**
     * @ORM\Column(type="text")
     */
    private $description;

    /**
     * @ORM\Column(type="text")
     */
    private $conditions;

    /**
     * @ORM\Column(type="text")
     */
    private $rewards;

    /**
     * @ORM\Column(type="integer")
     */
    private $nbPoints;

    /**
     * @ORM\Column(type="boolean")
     */
    private $active;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getImage(): ?string
    {
        return $this->image;
    }

    public function setImage(string $image): self
    {
        $this->image = $image;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(string $description): self
    {
        $this->description = $description;

        return $this;
    }

    /**
     * @return GameCondition[]|null
     */
    public function getConditions(): ?array
    {
        if (!$this->conditions) {
            return null;
        }

        $conditions = json_decode($this->conditions, true);
        $result = [];
        foreach ($conditions as $type => $nb) {
            if (!$nb) {
                continue;
            }
            $result[] = new GameCondition($type, $nb);
        }
        return $result;
    }

    public function setConditions(array $conditions): self
    {
        $this->conditions = json_encode($conditions);

        return $this;
    }

    /**
     * @return GameReward[]|null
     */
    public function getRewards(): ?array
    {
        if (!$this->rewards) {
            return null;
        }

        $rewards = json_decode($this->rewards, true);
        $result = [];
        foreach ($rewards as $id => $nb) {
            if (!$nb) {
                continue;
            }
            $result[] = new GameReward($id, $nb);
        }
        return $result;
    }

    public function setRewards(array $rewards): self
    {
        $this->rewards = json_encode($rewards);

        return $this;
    }

    public function getNbPoints(): ?int
    {
        return $this->nbPoints;
    }

    public function setNbPoints(int $nbPoints): self
    {
        $this->nbPoints = $nbPoints;

        return $this;
    }

    public function getActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    /**
     * @return GameDailyQuestClient|null
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function getQuestClient(): ?GameDailyQuestClient
    {
        $container = ContainerBuilderService::getInstance();
        return $container->get(GameDailyQuestClientService::class)->getRepository()->findOneBy(['quest' => $this]);
    }
}
