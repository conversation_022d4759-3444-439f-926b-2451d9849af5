<?php

namespace MatG<PERSON>ver\Entity\Client\Relance;

use Doctrine\ORM\Mapping as ORM;
use Mat<PERSON><PERSON><PERSON>\Entity\Traits\ClientEntity;

/**
 * @ORM\Entity(repositoryClass="MatGyver\Repository\Client\Relance\ClientRelanceRepository")
 * @ORM\Table(name="mg_clients_relances")
 */
class ClientRelance
{
    use ClientEntity;

    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=20)
     */
    private $type;

    /**
     * @ORM\Column(type="integer")
     */
    private $relance;

    /**
     * @ORM\Column(type="datetime")
     */
    private $date;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getRelance(): ?int
    {
        return $this->relance;
    }

    public function setRelance(int $relance): self
    {
        $this->relance = $relance;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

}
