<?php

namespace MatGyver\Entity\Client\Subscription;

use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Shop\Customer\ShopCustomer;
use MatGyver\Entity\Shop\Invoice\ShopInvoice;
use MatGyver\Entity\Shop\Product\ShopProduct;
use MatGyver\Entity\Traits\ClientEntity;
use MatGyver\Services\Clients\ClientsTransactionsService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Shop\Transaction\ShopTransactionService;

/**
 * @ORM\Entity(repositoryClass="MatGyver\Repository\Client\Subscription\ClientSubscriptionRepository")
 * @ORM\Table(name="mg_clients_subscriptions")
 */
class ClientSubscription
{
    const STATUS_ACTIVE = 'active';
    const STATUS_CANCELLED = 'cancelled';

    use ClientEntity;

    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\OneToOne(targetEntity="MatGyver\Entity\Shop\Product\ShopProduct")
     * @ORM\JoinColumn(nullable=true, referencedColumnName="id")
     */
    private $product;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $productName;

    /**
     * @ORM\Column(type="integer")
     */
    private $subscriptionId;

    /**
     * @ORM\Column(type="string", length=100)
     */
    private $typeSubscription;

    /**
     * @ORM\Column(type="string", length=100)
     */
    private $transactionReference;

    /**
     * @ORM\ManyToOne(targetEntity="MatGyver\Entity\Shop\Customer\ShopCustomer")
     * @ORM\JoinColumn(nullable=true, name="customer_id", referencedColumnName="id")
     */
    private $customer;

    /**
     * @ORM\Column(type="float")
     */
    private $amountTaxExcl;

    /**
     * @ORM\Column(type="float")
     */
    private $amountTaxIncl;

    /**
     * @ORM\Column(type="string", length=20)
     */
    private $status;

    /**
     * @ORM\Column(type="date")
     */
    private $date;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getProduct(): ?ShopProduct
    {
        return $this->product;
    }

    public function setProduct(?ShopProduct $product): self
    {
        $this->product = $product;

        return $this;
    }

    public function getProductName(): ?string
    {
        return $this->productName;
    }

    public function setProductName(string $productName): self
    {
        $this->productName = $productName;

        return $this;
    }

    public function getSubscriptionId(): ?int
    {
        return $this->subscriptionId;
    }

    public function setSubscriptionId(int $subscriptionId): self
    {
        $this->subscriptionId = $subscriptionId;

        return $this;
    }

    public function getTypeSubscription(): ?string
    {
        return $this->typeSubscription;
    }

    public function setTypeSubscription(string $typeSubscription): self
    {
        $this->typeSubscription = $typeSubscription;

        return $this;
    }

    public function getTransactionReference(): ?string
    {
        return $this->transactionReference;
    }

    public function setTransactionReference(string $transactionReference): self
    {
        $this->transactionReference = $transactionReference;

        return $this;
    }

    public function getCustomer(): ?ShopCustomer
    {
        return $this->customer;
    }

    public function setCustomer(?ShopCustomer $customer): ClientSubscription
    {
        $this->customer = $customer;

        return $this;
    }

    public function getAmountTaxExcl(): ?float
    {
        return $this->amountTaxExcl;
    }

    public function setAmountTaxExcl(float $amountTaxExcl): self
    {
        $this->amountTaxExcl = $amountTaxExcl;

        return $this;
    }

    public function getAmountTaxIncl(): ?float
    {
        return $this->amountTaxIncl;
    }

    public function setAmountTaxIncl(float $amountTaxIncl): self
    {
        $this->amountTaxIncl = $amountTaxIncl;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function isInTrial(): bool
    {
        if (!$this->getTransactionReference()) {
            return false;
        }

        $container = ContainerBuilderService::getInstance();
        $clientTransaction = $container->get(ClientsTransactionsService::class)->getRepository()->findOneBy(['subscription' => $this, 'client' => $this->getClient()], ['id' => 'DESC']);
        if (!$clientTransaction) {
            return false;
        }

        $transaction = $clientTransaction->getTransaction();
        if (!$transaction) {
            return false;
        }

        if (!$transaction->getAmountTaxIncl()) {
            return true;
        }

        $transactionProducts = $transaction->getTransactionProducts();
        foreach ($transactionProducts as $transactionProduct) {
            if ($transactionProduct->getName() == ShopInvoice::NAME_TRIAL_PERIOD) {
                return true;
            }
        }

        return false;
    }

    public function hadTrial(): bool
    {
        if (!$this->getTransactionReference()) {
            return false;
        }

        $container = ContainerBuilderService::getInstance();
        $clientTransactions = $container->get(ClientsTransactionsService::class)->getRepository()->findBy(['subscription' => $this, 'client' => $this->getClient()], ['id' => 'DESC']);
        if (!$clientTransactions) {
            return false;
        }

        foreach ($clientTransactions as $clientTransaction) {
            $transaction = $clientTransaction->getTransaction();
            if (!$transaction) {
                return false;
            }

            if (!$transaction->getAmountTaxIncl()) {
                return true;
            }

            $transactionProducts = $transaction->getTransactionProducts();
            foreach ($transactionProducts as $transactionProduct) {
                if ($transactionProduct->getName() == ShopInvoice::NAME_TRIAL_PERIOD) {
                    return true;
                }
            }
        }

        return false;
    }
}
