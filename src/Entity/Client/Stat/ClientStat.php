<?php

namespace MatGyver\Entity\Client\Stat;

use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="MatGyver\Repository\Client\Stat\ClientStatRepository")
 * @ORM\Table(name="mg_clients_stats")
 */
class ClientStat
{

    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="integer")
     */
    private $nbClients;

    /**
     * @ORM\Column(type="integer")
     */
    private $nbPaidClients;

    /**
     * @ORM\Column(type="integer")
     */
    private $nbDailyClients;

    /**
     * @ORM\Column(type="integer")
     */
    private $nbMonthlyClients;

    /**
     * @ORM\Column(type="integer")
     */
    private $nbDailyPaidClients;

    /**
     * @ORM\Column(type="integer")
     */
    private $nbMonthlyPaidClients;

    /**
     * @ORM\Column(type="float")
     */
    private $turnover;

    /**
     * @ORM\Column(type="float")
     */
    private $turnoverDaily;

    /**
     * @ORM\Column(type="float")
     */
    private $turnoverMonthly;

    /**
     * @ORM\Column(type="integer")
     */
    private $nbMonthly;

    /**
     * @ORM\Column(type="integer")
     */
    private $nbYearly;

    /**
     * @ORM\Column(type="integer")
     */
    private $nbFreemium;

    /**
     * @ORM\Column(type="integer")
     */
    private $nbDailyFreemium;

    /**
     * @ORM\Column(type="integer")
     */
    private $nbMonthlyFreemium;

    /**
     * @ORM\Column(type="integer")
     */
    private $nbDailyFreemiumConversions;

    /**
     * @ORM\Column(type="integer")
     */
    private $nbMonthlyFreemiumConversions;

    /**
     * @ORM\Column(type="integer")
     */
    private $nbDesactives;

    /**
     * @ORM\Column(type="integer")
     */
    private $nbPause;

    /**
     * @ORM\Column(type="integer")
     */
    private $nbMonthlyCancel;

    /**
     * @ORM\Column(type="integer")
     */
    private $nbClientsFromAffiliates;

    /**
     * @ORM\Column(type="float")
     */
    private $churn;

    /**
     * @ORM\Column(type="float")
     */
    private $mrr;

    /**
     * @ORM\Column(type="float")
     */
    private $arpu;

    /**
     * @ORM\Column(type="float")
     */
    private $newMrr;

    /**
     * @ORM\Column(type="float")
     */
    private $expansionMrr;

    /**
     * @ORM\Column(type="float")
     */
    private $reactivationMrr;

    /**
     * @ORM\Column(type="float")
     */
    private $contractionMrr;

    /**
     * @ORM\Column(type="float")
     */
    private $churnMrr;

    /**
     * @ORM\Column(type="float")
     */
    private $newMrrNet;

    /**
     * @ORM\Column(type="float")
     */
    private $arr;

    /**
     * @ORM\Column(type="float")
     */
    private $avgCart;

    /**
     * @ORM\Column(type="integer")
     */
    private $avgDuration;

    /**
     * @ORM\Column(type="integer")
     */
    private $clv;

    /**
     * @ORM\Column(type="text")
     */
    private $products;

    /**
     * @ORM\Column(type="date")
     */
    private $date;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getNbClients(): ?int
    {
        return $this->nbClients;
    }

    public function setNbClients(int $nbClients): self
    {
        $this->nbClients = $nbClients;

        return $this;
    }

    public function getNbPaidClients(): ?int
    {
        return $this->nbPaidClients;
    }

    public function setNbPaidClients(int $nbPaidClients): self
    {
        $this->nbPaidClients = $nbPaidClients;

        return $this;
    }

    public function getNbDailyClients(): ?int
    {
        return $this->nbDailyClients;
    }

    public function setNbDailyClients(int $nbDailyClients): self
    {
        $this->nbDailyClients = $nbDailyClients;

        return $this;
    }

    public function getNbMonthlyClients(): ?int
    {
        return $this->nbMonthlyClients;
    }

    public function setNbMonthlyClients(int $nbMonthlyClients): self
    {
        $this->nbMonthlyClients = $nbMonthlyClients;

        return $this;
    }

    public function getNbDailyPaidClients(): ?int
    {
        return $this->nbDailyPaidClients;
    }

    public function setNbDailyPaidClients(int $nbDailyPaidClients): self
    {
        $this->nbDailyPaidClients = $nbDailyPaidClients;

        return $this;
    }

    public function getNbMonthlyPaidClients(): ?int
    {
        return $this->nbMonthlyPaidClients;
    }

    public function setNbMonthlyPaidClients(int $nbMonthlyPaidClients): self
    {
        $this->nbMonthlyPaidClients = $nbMonthlyPaidClients;

        return $this;
    }

    public function getTurnover(): ?float
    {
        return $this->turnover;
    }

    public function setTurnover(float $turnover): self
    {
        $this->turnover = $turnover;

        return $this;
    }

    public function getTurnoverDaily(): ?float
    {
        return $this->turnoverDaily;
    }

    public function setTurnoverDaily(float $turnoverDaily): self
    {
        $this->turnoverDaily = $turnoverDaily;

        return $this;
    }

    public function getTurnoverMonthly(): ?float
    {
        return $this->turnoverMonthly;
    }

    public function setTurnoverMonthly(float $turnoverMonthly): self
    {
        $this->turnoverMonthly = $turnoverMonthly;

        return $this;
    }

    public function getNbMonthly(): ?int
    {
        return $this->nbMonthly;
    }

    public function setNbMonthly(int $nbMonthly): self
    {
        $this->nbMonthly = $nbMonthly;

        return $this;
    }

    public function getNbYearly(): ?int
    {
        return $this->nbYearly;
    }

    public function setNbYearly(int $nbYearly): self
    {
        $this->nbYearly = $nbYearly;

        return $this;
    }

    public function getNbFreemium(): ?int
    {
        return $this->nbFreemium;
    }

    public function setNbFreemium(int $nbFreemium): self
    {
        $this->nbFreemium = $nbFreemium;

        return $this;
    }

    public function getNbDailyFreemium(): ?int
    {
        return $this->nbDailyFreemium;
    }

    public function setNbDailyFreemium(int $nbDailyFreemium): self
    {
        $this->nbDailyFreemium = $nbDailyFreemium;

        return $this;
    }

    public function getNbMonthlyFreemium(): ?int
    {
        return $this->nbMonthlyFreemium;
    }

    public function setNbMonthlyFreemium(int $nbMonthlyFreemium): self
    {
        $this->nbMonthlyFreemium = $nbMonthlyFreemium;

        return $this;
    }

    public function getNbDailyFreemiumConversions(): ?int
    {
        return $this->nbDailyFreemiumConversions;
    }

    public function setNbDailyFreemiumConversions(int $nbDailyFreemiumConversions): self
    {
        $this->nbDailyFreemiumConversions = $nbDailyFreemiumConversions;

        return $this;
    }

    public function getNbMonthlyFreemiumConversions(): ?int
    {
        return $this->nbMonthlyFreemiumConversions;
    }

    public function setNbMonthlyFreemiumConversions(int $nbMonthlyFreemiumConversions): self
    {
        $this->nbMonthlyFreemiumConversions = $nbMonthlyFreemiumConversions;

        return $this;
    }

    public function getNbDesactives(): ?int
    {
        return $this->nbDesactives;
    }

    public function setNbDesactives(int $nbDesactives): self
    {
        $this->nbDesactives = $nbDesactives;

        return $this;
    }

    public function getNbPause(): ?int
    {
        return $this->nbPause;
    }

    public function setNbPause(int $nbPause): self
    {
        $this->nbPause = $nbPause;

        return $this;
    }

    public function getNbMonthlyCancel(): ?int
    {
        return $this->nbMonthlyCancel;
    }

    public function setNbMonthlyCancel(int $nbMonthlyCancel): self
    {
        $this->nbMonthlyCancel = $nbMonthlyCancel;

        return $this;
    }

    public function getNbClientsFromAffiliates(): ?int
    {
        return $this->nbClientsFromAffiliates;
    }

    public function setNbClientsFromAffiliates(int $nbClientsFromAffiliates): self
    {
        $this->nbClientsFromAffiliates = $nbClientsFromAffiliates;

        return $this;
    }

    public function getChurn(): ?float
    {
        return $this->churn;
    }

    public function setChurn(float $churn): self
    {
        $this->churn = $churn;

        return $this;
    }

    public function getMrr(): ?float
    {
        return $this->mrr;
    }

    public function setMrr(float $mrr): self
    {
        $this->mrr = $mrr;

        return $this;
    }

    public function getArpu(): ?float
    {
        return $this->arpu;
    }

    public function setArpu(float $arpu): self
    {
        $this->arpu = $arpu;

        return $this;
    }

    public function getNewMrr(): ?float
    {
        return $this->newMrr;
    }

    public function setNewMrr(float $newMrr): self
    {
        $this->newMrr = $newMrr;

        return $this;
    }

    public function getExpansionMrr(): ?float
    {
        return $this->expansionMrr;
    }

    public function setExpansionMrr(float $expansionMrr): self
    {
        $this->expansionMrr = $expansionMrr;

        return $this;
    }

    public function getReactivationMrr(): ?float
    {
        return $this->reactivationMrr;
    }

    public function setReactivationMrr(float $reactivationMrr): self
    {
        $this->reactivationMrr = $reactivationMrr;

        return $this;
    }

    public function getContractionMrr(): ?float
    {
        return $this->contractionMrr;
    }

    public function setContractionMrr(float $contractionMrr): self
    {
        $this->contractionMrr = $contractionMrr;

        return $this;
    }

    public function getChurnMrr(): ?float
    {
        return $this->churnMrr;
    }

    public function setChurnMrr(float $churnMrr): self
    {
        $this->churnMrr = $churnMrr;

        return $this;
    }

    public function getNewMrrNet(): ?float
    {
        return $this->newMrrNet;
    }

    public function setNewMrrNet(float $newMrrNet): self
    {
        $this->newMrrNet = $newMrrNet;

        return $this;
    }

    public function getArr(): ?float
    {
        return $this->arr;
    }

    public function setArr(float $arr): self
    {
        $this->arr = $arr;

        return $this;
    }

    public function getAvgCart(): ?float
    {
        return $this->avgCart;
    }

    public function setAvgCart(float $avgCart): self
    {
        $this->avgCart = $avgCart;

        return $this;
    }

    public function getAvgDuration(): ?int
    {
        return $this->avgDuration;
    }

    public function setAvgDuration(int $avgDuration): self
    {
        $this->avgDuration = $avgDuration;

        return $this;
    }

    public function getClv(): ?int
    {
        return $this->clv;
    }

    public function setClv(int $clv): self
    {
        $this->clv = $clv;

        return $this;
    }

    public function getProducts(): ?string
    {
        return $this->products;
    }

    public function setProducts(string $products): self
    {
        $this->products = $products;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

}
