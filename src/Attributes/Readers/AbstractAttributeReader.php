<?php
namespace MatGyver\Attributes\Readers;

/**
 * Class AbstractAttributeReader
 * @package MatGyver\Attributes\Readers
 */
abstract class AbstractAttributeReader
{
    /**
     * @param string $className
     * @return null|object
     * @throws \ReflectionException
     */
    public function getClassAttribute(string $className): ?object
    {
        $reflectionClass = new \ReflectionClass($className);
        $attributeClass = $this->getAttributeClass();
        $attributes = $reflectionClass->getAttributes($attributeClass);
        if (!$attributes) {
            while ($parent = $reflectionClass->getParentClass()) {
                $attributes = $parent->getAttributes($attributeClass);
                if ($attributes) {
                    break;
                }
                $reflectionClass = $parent;
            }
        }
        if ($attributes) {
            foreach ($attributes as $attribute) {
                return $attribute->newInstance();
            }
        }
        return null;
    }

    /**
     * @param string $className
     * @param string $method
     * @return null|object
     * @throws \ReflectionException
     */
    public function getMethodAttribute(string $className, string $method): ?object
    {
        $reflectionMethod = new \ReflectionMethod($className, $method);
        $attributeClass = $this->getAttributeClass();
        $attributes = $reflectionMethod->getAttributes($attributeClass);
        if ($attributes) {
            foreach ($attributes as $attribute) {
                return $attribute->newInstance();
            }
        }
        return null;
    }

    /**
     * @return string
     */
    abstract public function getAttributeClass(): string;
}
