<?php
namespace MatGyver\Helpers;

use MatGyver\Services\Partials\SelectService;

/**
 * Class TimeZone
 * @package MatGyver\Helpers
 */
class TimeZone
{
    /**
     * @return array
     */
    public static function timezoneList(): array
    {
        $timezoneIdentifiers = \DateTimeZone::listIdentifiers();
        $utcTime = new \DateTime('now', new \DateTimeZone('UTC'));

        $tempTimezones = array();
        foreach ($timezoneIdentifiers as $timezoneIdentifier) {
            $currentTimezone = new \DateTimeZone($timezoneIdentifier);

            $tempTimezones[] = array(
                'offset' => $currentTimezone->getOffset($utcTime),
                'identifier' => $timezoneIdentifier,
            );
        }

        // Sort the array by offset, identifier ascending
        usort($tempTimezones, function ($a, $b) {
            return ($a['offset'] == $b['offset'])
                ? strcmp($a['identifier'], $b['identifier'])
                : $a['offset'] - $b['offset'];
        });

        $timezoneList = array();
        foreach ($tempTimezones as $tz) {
            $sign = ($tz['offset'] > 0) ? '+' : '-';
            $offset = gmdate('H:i', abs($tz['offset']));
            $timezoneList[$tz['identifier']] = '(GMT' . $sign . $offset . ') ' . str_replace('_', ' ', $tz['identifier']);
        }

        return $timezoneList;
    }

    public static function generateSelectTimeZone($selectedTimeZone = ''): string
    {
        $timezoneList = self::timezoneList();

        $options = [];
        foreach ($timezoneList as $timezone => $name) {
            $options[$timezone] = $name;
        }

        return SelectService::render($options, $selectedTimeZone);
    }
}
