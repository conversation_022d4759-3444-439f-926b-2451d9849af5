<?php

namespace MatGyver\Helpers;

class Chars
{
    /**
     * @param string $string
     * @return string
     */
    public static function stripAccents(string $string): string
    {
        return strtr($string,'àáâãäçèéêëìíîïñòóôõöùúûüýÿÀÁÂÃÄÇÈÉÊËÌÍÎÏÑÒÓÔÕÖÙÚÛÜÝ', 'aaaaaceee<PERSON>iiinooooouuuuyyAAAAACEEEEIIIINOOOOOUUUUY');
    }

    /**
     * @return string
     */
    public static function generateUniqid(): string
    {
        return permalink(uniqid('', true));
    }

    /**
     * @param int $limit
     * @return int
     */
    public static function generateIntegers(int $limit = 9): int
    {
        $uniqId = abs(crc32(uniqid()));
        return substr($uniqId, 0, $limit);
    }

    /**
     * @param string $text
     * @param int    $maxCharacters
     * @return string
     */
    public static function friendlyLimitText(string $text, int $maxCharacters): string
    {
        if (strlen($text) > $maxCharacters) {
            $text = substr($text, 0, ($maxCharacters - 3));
            $words = explode(' ', $text);
            array_pop($words);
            $text = implode(' ', $words) . '...';
        }
        return $text;
    }

    public static function guidv4()
    {
        $data = random_bytes(16);
        assert(strlen($data) == 16);

        // Set version to 0100
        $data[6] = chr(ord($data[6]) & 0x0f | 0x40);
        // Set bits 6-7 to 10
        $data[8] = chr(ord($data[8]) & 0x3f | 0x80);

        // Output the 36 character UUID.
        return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
    }
}
