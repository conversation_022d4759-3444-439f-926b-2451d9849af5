<?php
namespace MatGyver\Helpers;

use Imagick;
use ImagickException;
use MatGyver\Services\Logger\LoggerService;

/**
 * Class Heic
 * @package MatGyver\Helpers
 */
class Heic
{
    /**
     * @param string $heicFile Full path to the HEIC file
     * @return string Path to the converted JPEG file or empty string on failure
     */
    public static function convert(string $heicFile): string
    {
        // Check if Imagick extension is loaded
        if (!extension_loaded('imagick')) {
            LoggerService::logError('Imagick extension is not loaded');
            return '';
        }

        if (!file_exists($heicFile)) {
            LoggerService::logError('HEIC file does not exist: ' . $heicFile);
            return '';
        }

        $pathInfo = pathinfo($heicFile);
        $outputFile = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '.jpg';

        try {
            $imagick = new Imagick();
            $imagick->setCompressionQuality(90);
            $imagick->readImage($heicFile);
            $imagick->setImageFormat('jpeg');
            $imagick->writeImage($outputFile);
            $imagick->clear();

            return $outputFile;
        } catch (ImagickException $e) {
            LoggerService::logError('Imagick error converting HEIC file: ' . $e->getMessage());
            return '';
        } catch (\Exception $e) {
            LoggerService::logError('Failed to convert HEIC file: ' . $e->getMessage());
            return '';
        }
    }

    public static function convertUploadedFile(string $heicFile): string
    {
        if (FILE_SYSTEM == 'files') {
            $heicFile = UPLOAD_PATH . '/files/' . $heicFile;
        } else {
            $heicFile = 'upload/' . $heicFile;
        }

        $newFile = self::convert($heicFile);

        if (FILE_SYSTEM == 'files') {
            $newFile = str_replace(UPLOAD_PATH . '/files/', '', $newFile);
        } else {
            $newFile = str_replace('upload/', '', $newFile);
        }

        return $newFile;
    }
}
