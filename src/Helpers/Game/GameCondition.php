<?php

namespace MatGyver\Helpers\Game;

use MatGyver\Helpers\Number;

class GameCondition
{
    const TYPE_NB_SUBSCRIPTIONS = 'nb_subscriptions';
    const TYPE_NB_SALES = 'nb_sales';
    const TYPE_AMOUNT_SALES = 'amount_sales';

    const TYPE_DAILY_QUEST = 'daily_quest';
    const TYPE_BADGE = 'badge';

    /**
     * @var string
     */
    private string $type;

    /**
     * @var int
     */
    private int $nb;

    /**
     * @param string $type
     * @param int $nb
     */
    public function __construct(string $type, int $nb)
    {
        $this->setType($type);
        $this->setNb($nb);
    }

    /**
     * @return string
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * @param string $type
     */
    public function setType(string $type): void
    {
        $this->type = $type;
    }

    /**
     * @return int
     */
    public function getNb(): int
    {
        return $this->nb;
    }

    /**
     * @param int $nb
     */
    public function setNb(int $nb): void
    {
        $this->nb = $nb;
    }

    /**
     * @return string
     */
    public function render(): string
    {
        if ($this->getType() == self::TYPE_NB_SUBSCRIPTIONS) {
            return n__('%d inscription', '%d inscriptions', $this->getNb(), $this->getNb());
        }
        if ($this->getType() == self::TYPE_NB_SALES) {
            return n__('%d vente', '%d ventes', $this->getNb(), $this->getNb());
        }
        if ($this->getType() == self::TYPE_AMOUNT_SALES) {
            return __('%s de ventes', Number::formatAmount($this->getNb(), DEFAULT_CURRENCY));
        }
        return '';
    }
}
