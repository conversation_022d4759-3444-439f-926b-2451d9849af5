<?php

namespace MatGyver\Helpers;

use setasign\Fpdi;

class Pdf {

    public static function rotatePdf(string $sourceFile, string $outputFile, int $degrees): void
    {
        $pdf = new Fpdi\Fpdi;
        $pageCount = $pdf->setSourceFile($sourceFile);

        for ($i = 1; $i <= $pageCount; $i++) {
            $tPage = $pdf->importPage($i);
            $size = $pdf->getTemplateSize($tPage);
            $orientation = '';
            $newSize = '';

            if (isset($size['orientation'])) {
                $orientation = $size['orientation'];
            }

            if (isset($size['width'], $size['height'])) {
                if ($orientation === '') {
                    $orientation = ($size['width'] > $size['height'] ? 'L' : 'P');
                }

                $newSize = [$size['width'], $size['height']];
            }

            $pdf->AddPage($orientation, $newSize, $degrees);
            $pdf->useTemplate($tPage);
        }

        $pdf->Output($outputFile, "F");
    }
}
