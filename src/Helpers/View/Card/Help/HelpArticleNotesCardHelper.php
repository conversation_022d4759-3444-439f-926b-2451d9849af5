<?php

namespace MatG<PERSON>ver\Helpers\View\Card\Help;

use Doctrine\Common\Collections\Collection;
use MatGyver\Helpers\View\Alert\AlertLightInfo;
use MatGyver\Helpers\View\Card\Card;
use MatGyver\Helpers\View\Card\ViewCardHelper;
use MatGyver\Helpers\View\Table\Help\HelpArticleNotesViewHelper;
use MatGyver\Services\DI\ContainerBuilderService;

/**
 * Class HelpArticleNotesCardHelper.
 */
class HelpArticleNotesCardHelper extends ViewCardHelper
{

    /**
     * @param Collection $notes
     */
    public function __construct(Collection $notes)
    {
        $this->card = new Card();
        $this->card->setTitle(__('Notes'));

        if (!$notes or !count($notes)) {
            $alert = new AlertLightInfo(__('Aucune note pour l\'instant.'));
            $this->card->setBody($alert->getContent());
        } else {
            $container = ContainerBuilderService::getInstance();
            $table = $container->get(HelpArticleNotesViewHelper::class)->getContent($notes);
            $this->card->setBody($table);
        }
    }
}
