<?php

namespace MatGyver\Helpers\View\Card\Affiliation;

use MatGyver\Helpers\View\Alert\AlertLightInfo;
use MatGyver\Helpers\View\Card\Card;
use MatGyver\Helpers\View\Card\ViewCardHelper;
use MatGyver\Helpers\View\Table\Affiliation\AppAffiliationPaymentsViewHelper;
use MatGyver\Services\DI\ContainerBuilderService;

/**
 * Class AppAffiliationPaymentsCardHelper.
 */
class AppAffiliationPaymentsCardHelper extends ViewCardHelper
{

    /**
     * @param array $payments
     */
    public function __construct(array $payments = [])
    {
        $this->card = new Card();
        $this->card->setTitle(__('Paiements'));

        if (!$payments) {
            $alert = new AlertLightInfo(__('Aucun paiement enregistré.'));
            $this->card->setBody($alert->getContent());
        } else {
            $container = ContainerBuilderService::getInstance();
            $tableInvoices = $container->get(AppAffiliationPaymentsViewHelper::class)->getContent($payments);
            $this->card->setBody($tableInvoices);
        }
    }
}
