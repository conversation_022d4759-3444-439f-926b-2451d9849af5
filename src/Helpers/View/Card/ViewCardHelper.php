<?php

namespace MatGyver\Helpers\View\Card;

use DI\Attribute\Inject;

/**
 * Class ViewCardHelper.
 */
abstract class ViewCardHelper
{
    #[Inject]
    protected \DI\Container $container;

    /**
     * @var Card
     */
    protected $card;

    /**
     * @return Card
     */
    public function getCard(): Card
    {
        return $this->card;
    }

    /**
     * @param Card $card
     */
    public function setCard(Card $card): void
    {
        $this->card = $card;
    }

    /**
     * @return string
     */
    public function getContent(): string
    {
        return $this->card->getContent();
    }
}
