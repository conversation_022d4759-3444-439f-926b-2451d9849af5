<?php

namespace MatGyver\Helpers\View\Card\Client;

use MatG<PERSON>ver\Entity\Client\Client;
use MatG<PERSON>ver\Helpers\View\Card\Card;
use MatGyver\Helpers\View\Card\ViewCardHelper;
use MatGyver\Services\Affiliation\AffiliationCommissionService;
use MatGyver\Services\Affiliation\AffiliationSubPartnersService;
use MatGyver\Services\Affiliation\AffiliationTransactionsService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\TwigService;

/**
 * Class ClientAffiliationCardHelper.
 */
class ClientAffiliationCardHelper extends ViewCardHelper
{
    /**
     * @param Client $client
     */
    public function __construct(Client $client)
    {
        $container = ContainerBuilderService::getInstance();
        $subPartners = $container->get(AffiliationSubPartnersService::class)->getSubPartners($client->getId());
        $nbSubPartners = ($subPartners ? count($subPartners) : 0);

        $totalAmount = 0;
        $balance = 0;
        $affiliationTransactions = null;
        if ($client->getAffiliationPartner()) {
            $totalAmount = $container->get(AffiliationCommissionService::class)->getCommissionsAmountByPartner($client->getAffiliationPartner()->getId());
            $balance = $container->get(AffiliationCommissionService::class)->getAvailableCommissionsAmountByPartner($client->getAffiliationPartner()->getId());
            $affiliationTransactions = $container->get(AffiliationTransactionsService::class)->getAllTransactions($client->getAffiliationPartner()->getId());
        }

        $this->card = new Card();
        $this->card->setTitle(__('Affiliation'));

        $body = TwigService::getInstance()->set('subPartners', $subPartners)
            ->set('nbSubPartners', $nbSubPartners)
            ->set('totalAmount', $totalAmount)
            ->set('balance', $balance)
            ->set('affiliationTransactions', $affiliationTransactions)
            ->render('admin/clients/client_affiliation.php');
        $this->card->setBody($body);
    }
}
