<?php
namespace Mat<PERSON>yver\Helpers\View\CardRow\Dossier\Expertise;

use Mat<PERSON>yver\Entity\Dossier\DossierInstitution;
use MatGyver\Entity\Dossier\Expertise\DossierExpertisePerson;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Action;
use MatGyver\Helpers\View\CardRow\ViewCardRowHelper;
use MatGyver\Helpers\View\CardRow\CardRow;
use MatGyver\Services\TwigService;

class DossierExpertisePersonViewHelper extends ViewCardRowHelper
{
    /**
     * @param DossierExpertisePerson[] $dossierExpertisePersons
     * @return string
     */
    public function getContent(array $dossierExpertisePersons): string
    {
        Assets::addJs('common/clipboard.min.js');
        Assets::addJs('common/clipboard-copy.js');

        $this->setCards($dossierExpertisePersons);
        return $this->getCardsContent();
    }

    /**
     * @param DossierExpertisePerson[] $dossierExpertisePersons
     * @return void
     */
    public function setCards(array $dossierExpertisePersons): void
    {
        foreach ($dossierExpertisePersons as $dossierExpertisePerson) {
            $cardRow = $this->getCardRow($dossierExpertisePerson);
            $this->addCard($cardRow);

            $children = $dossierExpertisePerson->getChildren();
            if ($children) {
                foreach ($children as $child) {
                    if ($child->isHidden()) {
                        continue;
                    }
                    $cardRow = $this->getCardRow($child);
                    $this->addCard($cardRow);
                }
            }
        }
    }

    public function getCardRow(DossierExpertisePerson $dossierExpertisePerson): CardRow
    {
        $expertise = $dossierExpertisePerson->getExpertise();
        $cardRow = new CardRow();

        $title = $dossierExpertisePerson->getRepresentativeName() . '<br>';
        $title .= '<div class="d-flex flex-row align-items-center pt-2 mb-0">';
        $title .= '<span class="label label-inline label-outline-primary">' . $dossierExpertisePerson->displayType() . '</span>';
        if (!$dossierExpertisePerson->getParent() and $dossierExpertisePerson->getInstitution() and $dossierExpertisePerson->getInstitution()->isDemandeur()) {
            $title .= '<span class="label label-inline label-outline-primary ml-2">' . __('Demandeur %s', $dossierExpertisePerson->getInstitution()->getDefendantNumber() ?: '') . '</span>';
        }
        if (!$dossierExpertisePerson->getParent() and $dossierExpertisePerson->getInstitution() and $dossierExpertisePerson->getInstitution()->isDefendant()) {
            $title .= '<span class="label label-inline label-outline-primary ml-2">' . __('Défendeur %s', $dossierExpertisePerson->getInstitution()->getDefendantNumber() ?: '') . '</span>';
        }
        $title .= '</div>';

        $cardRow->setTitle($title);
        $cardRow->setClass('card gutter-b');
        if ($dossierExpertisePerson->getParent()) {
            $cardRow->setClass('card gutter-b card-child');
        }

        if ($dossierExpertisePerson->getStatus() == DossierExpertisePerson::STATUS_PRESENT) {
            $cardRow->setIcon('
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <polygon points="0 0 24 0 24 24 0 24"/>
                        <path d="M18,8 L16,8 C15.4477153,8 15,7.55228475 15,7 C15,6.44771525 15.4477153,6 16,6 L18,6 L18,4 C18,3.44771525 18.4477153,3 19,3 C19.5522847,3 20,3.44771525 20,4 L20,6 L22,6 C22.5522847,6 23,6.44771525 23,7 C23,7.55228475 22.5522847,8 22,8 L20,8 L20,10 C20,10.5522847 19.5522847,11 19,11 C18.4477153,11 18,10.5522847 18,10 L18,8 Z M9,11 C6.790861,11 5,9.209139 5,7 C5,4.790861 6.790861,3 9,3 C11.209139,3 13,4.790861 13,7 C13,9.209139 11.209139,11 9,11 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                        <path d="M0.00065168429,20.1992055 C0.388258525,15.4265159 4.26191235,13 8.98334134,13 C13.7712164,13 17.7048837,15.2931929 17.9979143,20.2 C18.0095879,20.3954741 17.9979143,21 17.2466999,21 C13.541124,21 8.03472472,21 0.727502227,21 C0.476712155,21 -0.0204617505,20.45918 0.00065168429,20.1992055 Z" fill="#000000" fill-rule="nonzero"/>
                    </g>
                </svg>');
            $cardRow->setIconClass('success');
        } elseif ($dossierExpertisePerson->getStatus() == DossierExpertisePerson::STATUS_ABSENT) {
            $cardRow->setIcon('
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <polygon points="0 0 24 0 24 24 0 24"/>
                        <path d="M9,11 C6.790861,11 5,9.209139 5,7 C5,4.790861 6.790861,3 9,3 C11.209139,3 13,4.790861 13,7 C13,9.209139 11.209139,11 9,11 Z M21,8 L17,8 C16.4477153,8 16,7.55228475 16,7 C16,6.44771525 16.4477153,6 17,6 L21,6 C21.5522847,6 22,6.44771525 22,7 C22,7.55228475 21.5522847,8 21,8 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                        <path d="M0.00065168429,20.1992055 C0.388258525,15.4265159 4.26191235,13 8.98334134,13 C13.7712164,13 17.7048837,15.2931929 17.9979143,20.2 C18.0095879,20.3954741 17.9979143,21 17.2466999,21 C13.541124,21 8.03472472,21 0.727502227,21 C0.476712155,21 -0.0204617505,20.45918 0.00065168429,20.1992055 Z" fill="#000000" fill-rule="nonzero"/>
                    </g>
                </svg>');
            $cardRow->setIconClass('danger');
        } elseif ($dossierExpertisePerson->getStatus() == DossierExpertisePerson::STATUS_UNDEFINED) {
            $cardRow->setIcon('
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <polygon points="0 0 24 0 24 24 0 24"/>
                        <path d="M12,11 C9.790861,11 8,9.209139 8,7 C8,4.790861 9.790861,3 12,3 C14.209139,3 16,4.790861 16,7 C16,9.209139 14.209139,11 12,11 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                        <path d="M3.00065168,20.1992055 C3.38825852,15.4265159 7.26191235,13 11.9833413,13 C16.7712164,13 20.7048837,15.2931929 20.9979143,20.2 C21.0095879,20.3954741 20.9979143,21 20.2466999,21 C16.541124,21 11.0347247,21 3.72750223,21 C3.47671215,21 2.97953825,20.45918 3.00065168,20.1992055 Z" fill="#000000" fill-rule="nonzero"/>
                    </g>
                </svg>');
            $cardRow->setIconClass('muted');
        }

        //actions
        $cardRow->setActions($this->getActions($dossierExpertisePerson));

        $mainContent = '<div class="d-flex flex-column">';
        if ($dossierExpertisePerson->getCompany() and $dossierExpertisePerson->getCompany() != $dossierExpertisePerson->getRepresentativeName()) {
            $mainContent .= '<span class="text-dark-50 font-weight-bolder">' . __('Société : %s', $dossierExpertisePerson->getCompany()) . '</span>';
        }
        if ($dossierExpertisePerson->getAddress()) {
            $mainContent .= '<span class="text-dark-50"><i class="flaticon-home mr-1 font-size-lg"></i> ' . $dossierExpertisePerson->getAddress() . ($dossierExpertisePerson->getAddress2() ? '<br>' . $dossierExpertisePerson->getAddress2() : '') . '<br>' . $dossierExpertisePerson->getZip() . ' ' . $dossierExpertisePerson->getCity() . '</span>';
        }
        if ($dossierExpertisePerson->getEmail()) {
            $mainContent .= '
            <div class="d-flex flex-row">
                <span class="text-dark-50"><i class="flaticon2-mail mr-1 font-size-lg"></i> <a href="mailto:' . $dossierExpertisePerson->getEmail() . '">' . $dossierExpertisePerson->getEmail() . '</a></span>
                <a class="btn-copy text-dark-50 text-hover-primary cursor-pointer ml-2" id="btn-copy-' . $dossierExpertisePerson->getId() . '-email" data-clipboard-text="' . $dossierExpertisePerson->getEmail() . '"><i class="fas fa-copy icon-nm"></i></a>
            </div>';
        }
        if ($dossierExpertisePerson->getTelephone()) {
            $mainContent .= '<span class="text-dark-50"><i class="flaticon2-phone mr-1 font-size-lg"></i> ' . $dossierExpertisePerson->getTelephone() . '</span>';
        }

        $representativeName = '';
        if ($dossierExpertisePerson->getInstitution() and $dossierExpertisePerson->getInstitution()->getCompany($expertise) and ($dossierExpertisePerson->getInstitution()->getFirstName($expertise) or $dossierExpertisePerson->getInstitution()->getLastName($expertise))) {
            $representativeName = $dossierExpertisePerson->getInstitution()->getFirstName($expertise) . ' ' . $dossierExpertisePerson->getInstitution()->getLastName($expertise);
        } elseif ($dossierExpertisePerson->getFirstName()) {
            $representativeName = $dossierExpertisePerson->getFirstName() . ' ' . $dossierExpertisePerson->getLastName();
        }
        if ($representativeName and $representativeName != $dossierExpertisePerson->getRepresentativeName()) {
            if (!$dossierExpertisePerson->getInstitution() or $dossierExpertisePerson->getInstitution()->getType() != DossierInstitution::TYPE_LAWYER) {
                $mainContent .= '<span class="text-dark-50">' . __('Représentant : %s', $representativeName) . '</span>';
            }
        }
        if ($dossierExpertisePerson->getRepresent()) {
            $mainContent .= '<span class="text-dark-50">' . __('Partie représentée : %s', $dossierExpertisePerson->getRepresent()) . '</span>';
        }
        if ($dossierExpertisePerson->getReference()) {
            $mainContent .= '<span class="text-dark-50">' . __('Réf. Dossier : %s', $dossierExpertisePerson->getReference()) . '</span>';
        }
        if ($dossierExpertisePerson->getReferenceClient()) {
            $mainContent .= '<span class="text-dark-50">' . __('Réf. Mandant : %s', $dossierExpertisePerson->getReferenceClient()) . '</span>';
        } elseif (($dossierExpertisePerson->getType() == DossierInstitution::TYPE_EXPERT or ($dossierExpertisePerson->getInstitution() and $dossierExpertisePerson->getInstitution()->getType() == DossierInstitution::TYPE_EXPERT)) and $dossierExpertisePerson->getExpertise()->getDossier()->getMandate() and $dossierExpertisePerson->getExpertise()->getDossier()->getMandate()->getReference()) {
            $mainContent .= '<span class="text-dark-50">' . __('Réf. Mandant : %s', $dossierExpertisePerson->getExpertise()->getDossier()->getMandate()->getReference()) . '</span>';
        }
        if ($dossierExpertisePerson->getReferenceCompany()) {
            $mainContent .= '<span class="text-dark-50">' . __('Réf. Compagnie : %s', $dossierExpertisePerson->getReferenceCompany()) . '</span>';
        }
        if ($dossierExpertisePerson->getInstitution() and $dossierExpertisePerson->getInstitution()->getLawyer() and $dossierExpertisePerson->getInstitution()->getLawyerName()) {
            $mainContent .= '<span class="text-dark-50">' . __('Avocat : %s', $dossierExpertisePerson->getInstitution()->getLawyerName()) . '<br>';
            if ($dossierExpertisePerson->getInstitution()->getLawyerEmail()) {
                $mainContent .= __('Adresse email de l\'avocat : %s', $dossierExpertisePerson->getInstitution()->getLawyerEmail()) . '<br>';
            }
            if ($dossierExpertisePerson->getInstitution()->getLawyerMandate()) {
                $mainContent .= __('Mandant de l\'avocat : %s', $dossierExpertisePerson->getInstitution()->getLawyerMandate()) . '<br>';
            }
            if ($dossierExpertisePerson->getInstitution()->getLawyerNumber()) {
                $mainContent .= __('Numéro de sinistre du mandant : %s', $dossierExpertisePerson->getInstitution()->getLawyerNumber()) . '<br>';
            }
            $mainContent .= '</span>';
        }

        $mainContent .= TwigService::getInstance()->set('dossierExpertisePerson', $dossierExpertisePerson)
            ->render('app/dossier/expertise_person_presence.php');
        $mainContent .= '</div>';

        $cardRow->setMainContent($mainContent);

        $cardRow->setButtonText(__('Modifier'));
        $cardRow->setButtonLink(Tools::makeLink('app', 'dossier', 'expertise/person/edit/' . $dossierExpertisePerson->getId() . '/' . $dossierExpertisePerson->getExpertise()->getId()));

        return $cardRow;
    }

    /**
     * @param DossierExpertisePerson $dossierExpertisePerson
     * @return Action[]
     */
    public function getActions(DossierExpertisePerson $dossierExpertisePerson): array
    {
        if ($dossierExpertisePerson->getInstitution() and $dossierExpertisePerson->getInstitution()->getType() == DossierInstitution::TYPE_CONTACT) {
            //contact cannot be deleted
            //return [];
        }

        $actions = [];

        $action = new Action();
        $action->setTitle(__('Supprimer'));
        $action->setIcon('fas fa-trash');
        $action->setHref(Tools::makeLink('app', 'dossier', 'expertise/person/delete/' . $dossierExpertisePerson->getId()));
        $action->setClass('text-danger');
        $actions[] = $action;

        return $actions;
    }
}
