<?php
namespace MatGyver\Helpers\View\CardObject\Dossier;

use Mat<PERSON><PERSON>ver\Entity\Dossier\Dossier;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Action;
use MatGyver\Helpers\View\CardObject\CardObject;
use MatGyver\Helpers\View\CardObject\ViewCardObjectHelper;

class DossierViewHelper extends ViewCardObjectHelper
{
    /**
     * @param Dossier[] $dossiers
     * @return string
     */
    public function getContent(array $dossiers): string
    {
        $this->setCards($dossiers);
        return $this->getCardsContent();
    }

    /**
     * @param Dossier[] $dossiers
     * @return void
     */
    public function setCards(array $dossiers): void
    {
        foreach ($dossiers as $dossier) {
            $card = new CardObject();
            $card->setCardSize('col-xl-4');
            $card->setTitle($dossier->getContact()->getFirstName() . ' ' . $dossier->getContact()->getLastName());

            if ($dossier->getVehicle()) {
                $card->setIcon('
<h3 class="text-white">' . $dossier->getVehicle()->getBrand() . ' ' . $dossier->getVehicle()->getModel() . '</h3>
<h3 class="text-white">' . $dossier->getVehicle()->getRegistration() . '</h3>
            ');
            }

            //actions
            $card->setActions($this->getActions($dossier));

            $card->setMainContent('
            <div class="d-flex justify-content-center align-items-center w-100">
                <span class="label label-inline label-lg label-outline-' . $dossier->getStatusLabel() . '">' . $dossier->getStatusName() . '</span>
            </div>');

            $card->setButtonText(__('Voir le dossier'));
            $card->setButtonLink(Tools::makeLink('app', 'dossier', $dossier->getId()));

            $this->addCard($card);
        }
    }

    /**
     * @param Dossier $dossier
     * @return Action[]
     */
    public function getActions(Dossier $dossier): array
    {
        $actions = [];

        $action = new Action();
        $action->setTitle(__('Modifier'));
        $action->setIcon('fas fa-pen');
        $action->setHref(Tools::makeLink('app', 'dossier', 'edit/' . $dossier->getId()));
        $actions[] = $action;

        $action = new Action();
        $action->setDivider(true);
        $actions[] = $action;

        $action = new Action();
        $action->setTitle(__('Supprimer'));
        $action->setIcon('fas fa-trash');
        $action->setOnClickDelete('dossier', $dossier->getId(), __('Êtes-vous sûr de vouloir supprimer ce dossier ?'), __('Supprimer'), Tools::makeLink('app', 'dossier', 'delete/' . $dossier->getId()));
        $action->setClass('text-danger');
        $actions[] = $action;

        return $actions;
    }
}
