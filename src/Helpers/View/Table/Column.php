<?php

namespace MatGyver\Helpers\View\Table;

class Column
{
    /**
     * @var string
     */
    protected $id;

    /**
     * @var string
     */
    protected $value;

    /**
     * @var string
     */
    protected $sort = '';

    /**
     * @var string
     */
    protected $class = '';

    /**
     * @var ColumnAction[]
     */
    protected $actions = [];

    /**
     * @param string $id
     * @param string $value
     * @param string $class
     * @param string $sort
     */
    public function __construct(string $id = '', string $value = '', string $class = '', string $sort = '')
    {
        $this->id = $id;
        $this->value = $value;
        $this->class = $class;
        $this->sort = $sort;
    }

    /**
     * @return string
     */
    public function getId(): string
    {
        return $this->id;
    }

    /**
     * @param string $id
     */
    public function setId(string $id): void
    {
        $this->id = $id;
    }

    /**
     * @return string
     */
    public function getValue(): string
    {
        return $this->value;
    }

    /**
     * @param string $value
     */
    public function setValue(string $value): void
    {
        $this->value = $value;
    }

    /**
     * @return string
     */
    public function getSort(): string
    {
        return $this->sort;
    }

    /**
     * @param string $sort
     */
    public function setSort(string $sort): void
    {
        $this->sort = $sort;
    }

    /**
     * @return string
     */
    public function getClass(): string
    {
        return $this->class;
    }

    /**
     * @param string $class
     */
    public function setClass(string $class): void
    {
        $this->class = $class;
    }

    /**
     * @return ColumnAction[]
     */
    public function getActions(): array
    {
        return $this->actions;
    }

    /**
     * @param ColumnAction[] $actions
     */
    public function setActions(array $actions): self
    {
        $this->actions = $actions;
        return $this;
    }
}
