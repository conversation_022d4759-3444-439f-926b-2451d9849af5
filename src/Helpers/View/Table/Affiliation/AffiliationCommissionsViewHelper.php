<?php

namespace Mat<PERSON><PERSON>ver\Helpers\View\Table\Affiliation;

use MatG<PERSON><PERSON>\Entity\Affiliation\Commission\AffiliationCommission;
use MatGyver\Helpers\Number;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Card\Card;
use MatGyver\Helpers\View\Table\Column;
use MatGyver\Helpers\View\Table\ColumnActionDelete;
use MatGyver\Helpers\View\Table\ColumnActionEdit;
use MatGyver\Helpers\View\Table\ColumnActionView;
use MatGyver\Helpers\View\Table\Table;
use MatGyver\Helpers\View\Table\ViewTableHelper;
use MatGyver\Helpers\View\Table\Row;
use MatGyver\Services\Filters\CommissionsFiltersService;
use MatGyver\Services\RightsService;

/**
 * Class AffiliationCommissionsViewHelper.
 */
class AffiliationCommissionsViewHelper extends ViewTableHelper
{

    public function __construct()
    {
        $this->table = new Table('table_commissions');
        $this->table->setDataTable(false);
        $this->table->addTableColumn(new Column('id', __('ID')));
        $this->table->addTableColumn(new Column('date', __('Date')));
        $this->table->addTableColumn(new Column('partner', __('Affilié')));
        $this->table->addTableColumn(new Column('type', __('Type')));
        $this->table->addTableColumn(new Column('product', __('Produit')));
        $this->table->addTableColumn(new Column('amount', __('Commission')));
        $this->table->addTableColumn(new Column('status', __('Etat')));
        $this->table->addTableColumn(new Column('actions', __('Actions')));
    }

    /**
     * @param AffiliationCommission[]|null $commissions
     * @param int|null $idPartner
     * @return string
     */
    public function getContent(?array $commissions = null, ?int $idPartner = null): string
    {
        if ($commissions) {
            $this->setCommissionsRows($commissions);
        }
        if ($idPartner) {
            $this->table->setTableData('idpartner', $idPartner);
        }
        $table = $this->table->getContent();

        $card = new Card();
        $card->setTitle(__('Commissions'));
        $card->setFilterService($this->container->get(CommissionsFiltersService::class));
        $card->setBody($table);
        return $card->getContent();
    }

    /**
     * @param AffiliationCommission[] $commissions
     * @return void
     */
    public function setCommissionsRows(array $commissions): void
    {
        $canEdit = RightsService::isGranted(ATTRIBUTE_EDIT, UNIVERSE_ADMIN_AFFILIATION);
        $canDelete = RightsService::isGranted(ATTRIBUTE_DELETE, UNIVERSE_ADMIN_AFFILIATION);
        foreach ($commissions as $commission) {
            if ($commission->getPartner()) {
                $partner = '<strong>' . $commission->getPartner()->getLastName() . ' ' . $commission->getPartner()->getFirstName() . '</strong>';
            } else {
                $partner = '<span class="label label-danger label-inline">' . __('Affilié inconnu') . '</span>';
            }

            $commissionStatus = $commission->getCommissionStatus();
            $status = '<span class="label label-inline label-' . $commissionStatus['label'] . '">' . $commissionStatus['name'] . '</span>';

            //actions
            $actions = [new ColumnActionView(Tools::makeLink('admin', 'affiliation', 'commission/' . $commission->getId()))];
            if ($canEdit) {
                $actions[] = new ColumnActionEdit(Tools::makeLink('admin', 'affiliation', 'commission/edit/' . $commission->getId()));
            }
            if ($canDelete) {
                $actions[] = new ColumnActionDelete('commission', $commission->getId(), __('Etes-vous sûr de vouloir supprimer cette commission ?'), '', Tools::makeLink('admin', 'affiliation', 'commission/delete/' . $commission->getId()));
            }

            $row = new Row();
            $row->addColumn(new Column('id', $commission->getId()));
            $row->addColumn(new Column('date', dateTimeFr($commission->getDate()->format('Y-m-d H:i:s'))));
            $row->addColumn(new Column('partner', $partner));
            $row->addColumn(new Column('type', $commission->displayCommissionType()));
            $row->addColumn(new Column('product', $commission->getProduct()));
            $row->addColumn(new Column('amount', Number::formatAmount($commission->getCommission(), DEFAULT_CURRENCY)));
            $row->addColumn(new Column('status', $status));
            $row->addColumn((new Column('actions'))->setActions($actions));
            $this->table->addRow($row);
        }
    }
}
