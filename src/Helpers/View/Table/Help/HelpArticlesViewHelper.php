<?php

namespace Mat<PERSON><PERSON>ver\Helpers\View\Table\Help;

use MatG<PERSON>ver\Entity\Help\Article\HelpArticle;
use MatG<PERSON>ver\Entity\Help\Note\HelpNote;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Table\Column;
use MatGyver\Helpers\View\Table\ColumnActionDelete;
use MatGyver\Helpers\View\Table\ColumnActionEdit;
use MatGyver\Helpers\View\Table\Table;
use MatGyver\Helpers\View\Table\ViewTableHelper;
use MatGyver\Helpers\View\Table\Row;
use MatGyver\Services\RightsService;

/**
 * Class HelpArticlesViewHelper.
 */
class HelpArticlesViewHelper extends ViewTableHelper
{

    public function __construct()
    {
        $this->table = new Table('table_articles');
        $this->table->addTableColumn(new Column('id', __('ID'), 'hidden-phone'));
        $this->table->addTableColumn(new Column('title', __('Titre')));
        $this->table->addTableColumn(new Column('status', __('État'), 'hidden-phone'));
        $this->table->addTableColumn(new Column('stats', __('Statistiques')));
        $this->table->addTableColumn(new Column('actions', __('Actions')));
    }

    /**
     * @param HelpArticle[] $articles
     * @return string
     */
    public function getContent(array $articles): string
    {
        $this->setArticlesRows($articles);
        return $this->table->getContent();
    }

    /**
     * @param HelpArticle[] $articles
     * @return void
     */
    private function setArticlesRows(array $articles): void
    {
        foreach ($articles as $article) {
            $row = new Row();
            $row->addColumn(new Column('id', $article->getId(), 'hidden-phone'));

            $title = '';
            if ($article->getActive()) {
                $title .= '<a href="' . Tools::makeLink('site', 'help', $article->getUniverse()->getPermalink() . '/' . $article->getPermalink()) . '" target="_blank">';
            }
            $title .= '<span class="font-weight-bolder">' . limit_text($article->getTitle(), 14) . '</span>';
            if ($article->getActive()) {
                $title .= '</a>';
            }
            $title .= '<br><span class="label label-inline label-light">' . $article->getUniverse()->getName() . '</span>';
            $row->addColumn(new Column('title', $title));

            if (!$article->getActive()) {
                $status = '<span class="label label-inline mr-2 label-light">' . __('Brouillon') . '</span>';
            } else {
                $status = '<span class="label label-inline mr-2 label-success">' . __('Publié') . '</span>';
            }
            $row->addColumn(new Column('status', $status));

            //$row->addColumn(new Column('category', $article->getUniverse()->getName(), 'hidden-phone'));
            //$row->addColumn(new Column('views', '<span class="font-weight-bolder text-primary mb-0">' . $article->getViews() . '</span>'));

            $stats = '
                <a href="' . Tools::makeLink('admin', 'help', 'article/stats/' . $article->getId()) . '" class="btn btn-icon-danger btn-sm btn-text-dark-50 bg-hover-light-danger btn-hover-text-danger rounded font-weight-bolder font-size-sm p-2">
                    <span style="font-size:14px"><i class="fas fa-eye icon-nm mr-0 pr-0 text-dark-50"></i></span>
                    <br>' . $article->getViews() . '
                </a>
                <a href="' . Tools::makeLink('admin', 'help', 'article/stats/' . $article->getId()) . '" class="btn btn-icon-danger btn-sm btn-text-dark-50 bg-hover-light-danger btn-hover-text-danger rounded font-weight-bolder font-size-sm p-2">
                    <span style="font-size:14px">' . HelpNote::SMILEY_NOT_HAPPY . '</span>
                    <br>' . $article->getCountNotesByType(HelpNote::TYPE_NOT_HAPPY) . '
                </a>
                <a href="' . Tools::makeLink('admin', 'help', 'article/stats/' . $article->getId()) . '" class="btn btn-icon-danger btn-sm btn-text-dark-50 bg-hover-light-danger btn-hover-text-danger rounded font-weight-bolder font-size-sm p-2">
                    <span style="font-size:14px">' . HelpNote::SMILEY_NEUTRAL . '</span>
                    <br>' . $article->getCountNotesByType(HelpNote::TYPE_NEUTRAL) . '
                </a>
                <a href="' . Tools::makeLink('admin', 'help', 'article/stats/' . $article->getId()) . '" class="btn btn-icon-danger btn-sm btn-text-dark-50 bg-hover-light-danger btn-hover-text-danger rounded font-weight-bolder font-size-sm p-2">
                    <span style="font-size:14px">' . HelpNote::SMILEY_HAPPY . '</span>
                    <br>' . $article->getCountNotesByType(HelpNote::TYPE_HAPPY) . '
                </a>';
            $row->addColumn(new Column('stats', $stats));

            $actions = [];
            if (RightsService::isGranted(ATTRIBUTE_EDIT, UNIVERSE_ADMIN_NEWS)) {
                $actions[] = new ColumnActionEdit(Tools::makeLink('admin', 'help', 'article/' . $article->getId()));
            }
            if (RightsService::isGranted(ATTRIBUTE_DELETE, UNIVERSE_ADMIN_NEWS)) {
                $actions[] = new ColumnActionDelete('article', $article->getId(), __('Etes-vous sûr de vouloir supprimer cet article ?'), '', Tools::makeLink('admin', 'help', 'article/delete/' . $article->getId()));
            }
            $row->addColumn((new Column('actions'))->setActions($actions));

            $this->table->addRow($row);
        }
    }
}
