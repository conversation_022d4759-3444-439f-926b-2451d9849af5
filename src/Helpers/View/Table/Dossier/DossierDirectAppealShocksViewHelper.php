<?php
namespace MatG<PERSON>ver\Helpers\View\Table\Dossier;

use Mat<PERSON><PERSON><PERSON>\Entity\Dossier\DirectAppeal\DossierDirectAppealShock;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Table\Column;
use Mat<PERSON><PERSON>ver\Helpers\View\Table\ColumnAction;
use MatGyver\Helpers\View\Table\ColumnActionDelete;
use MatGyver\Helpers\View\Table\ColumnActionEdit;
use MatGyver\Helpers\View\Table\ColumnActionView;
use MatGyver\Helpers\View\Table\Row;
use MatGyver\Helpers\View\Table\Table;
use MatGyver\Helpers\View\Table\ViewTableHelper;

class DossierDirectAppealShocksViewHelper extends ViewTableHelper
{
    public function __construct()
    {
        $this->table = new Table('table_dossier_direct_appeals_shocks');
        $this->table->addTableColumn(new Column('id', __('Id')));
        $this->table->addTableColumn(new Column('title', __('Titre')));
        $this->table->addTableColumn(new Column('date', __('Date')));
        $this->table->addTableColumn(new Column('operations', __('Opérations')));
        $this->table->addTableColumn(new Column('actions', __('Action')));
        $this->table->setTableSort(0, 'desc');
    }

    /**
     * @param DossierDirectAppealShock[] $directAppealShocks
     */
    public function getContent(array $directAppealShocks)
    {
        $this->setContentRows($directAppealShocks);
        return $this->table->getContent();
    }

    /**
     * @param DossierDirectAppealShock[] $directAppealShocks
     */
    public function setContentRows(array $directAppealShocks)
    {
        foreach ($directAppealShocks as $directAppealShock) {
            $row = new Row();
            $row->addColumn(new Column('id', $directAppealShock->getId()));
            $row->addColumn(new Column('title', $directAppealShock->getTitle()));
            $row->addColumn(new Column('date', dateTimeFr($directAppealShock->getDate()->format('Y-m-d H:i:s')), '', $directAppealShock->getDate()->getTimestamp()));

            if (!$directAppealShock->getOperations()) {
                $row->addColumn(new Column('operations', '<em>' . __('Aucune opération') . '</em>'));
            } else {
                $row->addColumn(new Column('operations', n__('1 opération', '%d opérations', count($directAppealShock->getOperations()), count($directAppealShock->getOperations()))));
            }

            $row->addColumn((new Column('actions'))->setActions($this->getActions($directAppealShock)));
            $this->table->addRow($row);
        }
    }

    /**
     * @param DossierDirectAppealShock $directAppealShock
     * @return ColumnAction[]
     */
    public function getActions(DossierDirectAppealShock $directAppealShock): array
    {
        return [
            new ColumnActionEdit(Tools::makeLink('app', 'dossier', 'expertise/direct_appeal/shock/edit/' . $directAppealShock->getDirectAppeal()->getId() . '/' . $directAppealShock->getId() . '/' . $directAppealShock->getDirectAppeal()->getExpertise()->getId())),
            new ColumnActionDelete('direct_appeal_shock', $directAppealShock->getId(), __('Êtes-vous sûr de vouloir supprimer ce choc ?'), __('Supprimer'), Tools::makeLink('app', 'dossier', 'expertise/direct_appeal/shock/delete/' . $directAppealShock->getDirectAppeal()->getId() . '/' . $directAppealShock->getId() . '/' . $directAppealShock->getDirectAppeal()->getExpertise()->getId())),
        ];
    }
}
