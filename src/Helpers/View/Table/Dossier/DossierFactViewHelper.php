<?php
namespace <PERSON><PERSON><PERSON><PERSON>\Helpers\View\Table\Dossier;

use <PERSON><PERSON><PERSON><PERSON>\Entity\Dossier\Dossier;
use Mat<PERSON><PERSON><PERSON>\Entity\Dossier\DossierFact;
use MatGyver\Entity\Dossier\Expertise\DossierExpertise;
use Mat<PERSON><PERSON>ver\Helpers\Assets;
use Mat<PERSON>yver\Helpers\ImagickHelper;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Table\Column;
use MatGyver\Helpers\View\Table\ColumnAction;
use MatGyver\Helpers\View\Table\ColumnActionDelete;
use MatGyver\Helpers\View\Table\ColumnActionEdit;
use MatGyver\Helpers\View\Table\Row;
use MatGyver\Helpers\View\Table\Table;
use MatGyver\Helpers\View\Table\ViewTableHelper;
use MatGyver\Services\Dossier\DossierFactService;

class DossierFactViewHelper extends ViewTableHelper
{
    public function __construct()
    {
        $this->table = new Table('table_dossier_expertise_observations');
        $this->table->setClass('sortable-facts-table sortable-table');
        $this->table->addTableColumn(new Column('sort', ''));
        $this->table->addTableColumn(new Column('checkbox', '<label class="checkbox"><input type="checkbox" id="select_all"><span></span></label>', 'th-checkbox'));
        //$this->table->addTableColumn(new Column('id', __('Id'), 'min-phone-l'));
        $this->table->addTableColumn(new Column('date', __('Date')));
        $this->table->addTableColumn(new Column('mileage', __('Km'), 'w-60px'));
        $this->table->addTableColumn(new Column('comment', __('Commentaire')));
        $this->table->addTableColumn(new Column('actions', __('Actions'), 'w-120px'));
        $this->table->setTableSort(0, 'asc');
        //$this->table->setDataTable(false);
    }

    /**
     * @param array $factsByDate
     * @param Dossier $dossier
     * @param DossierExpertise|null $expertise
     * @param array $selectedFacts
     */
    public function getContent(array $factsByDate, Dossier $dossier, ?DossierExpertise $expertise = null, array $selectedFacts = [])
    {
        Assets::addJs('plugins/jquery-ui-sortable.js');
        Assets::addJs('plugins/jquery.ui.touch-punch.min.js');
        Assets::addJs('app/dossier/facts.js');

        $this->table->setTableData('dossier', $dossier->getId());
        if ($expertise) {
            $this->table->setTableData('expertise', $expertise->getId());
        } else {
            $this->table->removeTableColumn('checkbox');
        }
        $this->setContentRows($factsByDate, $expertise, $selectedFacts);
        return $this->table->getContent();
    }

    /**
     * @param array $factsByDate
     * @param DossierExpertise|null $expertise
     * @param array $selectedFacts
     */
    public function setContentRows(array $factsByDate, ?DossierExpertise $expertise = null, array $selectedFacts = [])
    {
        $i = 0;
        foreach ($factsByDate as $date => $facts) {
            /** @var DossierFact[] $facts */
            foreach ($facts as $fact) {
                $i++;
                $row = new Row();
                $row->setId($fact->getId());
                $row->addColumn(new Column('sort', '<i class="fa fa-sort"></i>', 'reorder'));

                $checkbox = '
                <div class="checkbox-list" style="width: 18px;">
                    <label class="checkbox" for="fact' . $fact->getId() . '">
                        <input type="checkbox" name="facts[]" id="fact' . $fact->getId() . '" value="' . $fact->getId() . '" ' . (($selectedFacts and in_array($fact->getId(), $selectedFacts)) ? 'checked' : '') . '>
                        <span class="mr-0"></span>
                    </label>
                </div>';
                $row->addColumn(new Column('checkbox', $checkbox));

                //$row->addColumn(new Column('id', $i, 'min-phone-l'));
                $row->addColumn(new Column('date', ($fact->getDate() ? dateFr($fact->getDate()->format('Y-m-d H:i:s')) : ''), '', ($fact->getDate() ? $fact->getDate()->getTimestamp() : '')));
                $row->addColumn(new Column('mileage', ($fact->getMileage() ? $fact->getMileage() . ' ' . DossierFactService::getMileageType($fact->getMileageType()) : ''), '', ($fact->getMileage() ?: 0)));

                if ($fact->getComment() != strip_tags($fact->getComment())) {
                    $comment = $fact->getComment();
                } else {
                    $comment = nl2br($fact->getComment());
                }
                if ($fact->getDocument()) {
                    if (!$fact->getDocument()->getConfidential()) {
                        $image = $fact->getDocument()->getThumbnailUrl();
                        $thumbnail = $fact->getDocument()->getThumbnailUrl();
                        if (in_array(getExtension($fact->getDocument()->getFile()), ['jpg', 'jpeg', 'png', 'gif'])) {
                            $image = $fact->getDocument()->getUrl();
                            if (!$fact->getDocument()->getThumbnail()) {
                                $thumbnail = $fact->getDocument()->getUrl();
                            }
                        }
                        $comment .= '<br><br><a class="cursor-pointer" onclick="createModalImg(\'' . $image . '\', \'\');"><img class="w-140px" src="' . $thumbnail . '"></a>';
                    }
                } elseif ($fact->getFile() and in_array(getExtension($fact->getFile()), ['jpg', 'jpeg', 'png', 'gif'])) {
                    $comment .= '<br><br><a class="cursor-pointer" onclick="createModalImg(\'' . $fact->getUrl() . '\', \'\');"><img class="w-140px" src="' . $fact->getUrl() . '"></a>';
                } elseif ($fact->getFile() and getExtension($fact->getFile()) == 'pdf') {
                    $fullPath = WEB_PATH . '/medias/' . $fact->getDossier()->getFolder();
                    $pathInfo = pathinfo($fact->getFile());
                    $fileName = $pathInfo['filename'] . '-thumbnail';

                    $images = ImagickHelper::convertPdf($fullPath . $fact->getFile(), true);
                    $comment .= '<br><br><a class="cursor-pointer" onclick="createModalImg(\'' . Assets::getMediaUrl($images[0]) . '\', \'\');"><img class="w-140px" src="' . Assets::getMediaUrl($images[0]) . '"></a>';
                }
                $comment = '<div class="show-more" data-nb-lines="25"><div class="show-more-content">' . $comment . '</div></div>';
                $row->addColumn(new Column('comment', $comment));

                $row->addColumn((new Column('actions'))->setActions($this->getActions($fact, $expertise)));
                $this->table->addRow($row);
            }
        }
    }

    /**
     * @param DossierFact $dossierFact
     * @param DossierExpertise|null $expertise
     * @return ColumnAction[]
     */
    public function getActions(DossierFact $dossierFact, ?DossierExpertise $expertise = null): array
    {
        if ($expertise) {
            return [
                new ColumnActionEdit(Tools::makeLink('app', 'dossier', 'expertise/fact/edit/' . $dossierFact->getId() . '/' . $expertise->getId())),
                new ColumnActionDelete('dossier_expertise_fact', $dossierFact->getId(), __('Êtes-vous sûr de vouloir supprimer ce fait ?'), '', Tools::makeLink('app', 'dossier', 'expertise/fact/delete/' . $dossierFact->getId() . '/' . $expertise->getId())),
            ];
        }
        return [
            new ColumnActionEdit(Tools::makeLink('app', 'dossier', 'fact/edit/' . $dossierFact->getId() . '/' . $dossierFact->getDossier()->getId())),
            new ColumnActionDelete('dossier_fact', $dossierFact->getId(), __('Êtes-vous sûr de vouloir supprimer ce fait ?'), '', Tools::makeLink('app', 'dossier', 'fact/delete/' . $dossierFact->getId())),
        ];
    }
}
