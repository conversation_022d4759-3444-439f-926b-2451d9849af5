<?php

namespace <PERSON><PERSON><PERSON>ver\Helpers\View\Table\Dossier;

use Mat<PERSON><PERSON><PERSON>\Entity\Dossier\Dossier;
use Mat<PERSON><PERSON><PERSON>\Entity\Shop\Invoice\ShopInvoice;
use MatGyver\Helpers\Number;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Table\Column;
use MatGyver\Helpers\View\Table\ColumnActionEdit;
use MatGyver\Helpers\View\Table\ColumnActionExport;
use MatGyver\Helpers\View\Table\ColumnActionView;
use MatGyver\Helpers\View\Table\Table;
use MatGyver\Helpers\View\Table\ViewTableHelper;
use MatGyver\Helpers\View\Table\Row;
use MatGyver\Services\RightsService;
use MatGyver\Services\Users\UsersService;

/**
 * Class DossierInvoicesViewHelper.
 */
class DossierInvoicesViewHelper extends ViewTableHelper
{

    public function __construct()
    {
        $this->table = new Table('table_dossier_invoices');
        $this->table->setDataTable(false);
        $this->table->addTableColumn(new Column('user', __('Nom/Prénom')));
        $this->table->addTableColumn(new Column('date', __('Date')));
        $this->table->addTableColumn(new Column('payment_method', __('Mode de paiement')));
        $this->table->addTableColumn(new Column('amount', __('TTC')));
        $this->table->addTableColumn(new Column('actions', __('Actions')));
    }

    /**
     * @param ShopInvoice[] $invoices
     * @param Dossier|null $dossier
     * @return string
     */
    public function getContent(array $invoices, ?Dossier $dossier = null): string
    {
        $this->setInvoicesRows($invoices, $dossier);
        return $this->table->getContent();
    }

    /**
     * @param ShopInvoice[] $invoices
     * @param Dossier|null $dossier
     * @return void
     */
    public function setInvoicesRows(array $invoices, ?Dossier $dossier = null): void
    {
        foreach ($invoices as $invoice) {
            $payments = $invoice->getDossierPayments();
            $totalAmountPaid = 0;
            if (count($payments)) {
                foreach ($payments as $payment) {
                    $totalAmountPaid += $payment->getDossierPayment()->getAmount();
                }
            }

            $amount = '<div class="font-weight-bolder text-primary mb-0">' . Number::formatAmount($invoice->getAmountTaxIncl(), $invoice->getCurrency()) . '</div>';
            if (!$invoice->getLocked() or $totalAmountPaid) {
                if ($totalAmountPaid == $invoice->getAmountTaxIncl()) {
                    $amount .= '<div class="text-success font-weight-bold">' . __('Payée') . '</div>';
                } else {
                    $amount .= '<div class="text-muted font-weight-bold">' . Number::formatAmount($totalAmountPaid, $invoice->getCurrency()) . ' / ' . Number::formatAmount($invoice->getAmountTaxIncl(), $invoice->getCurrency()) . '</div>';
                }
            }

            $row = new Row();
            $row->addColumn(new Column('user', $this->getInvoiceUser($invoice)));
            $row->addColumn(new Column('date', dateTimeFr($invoice->getDateCreation()->format('Y-m-d H:i:s'))));
            $row->addColumn(new Column('payment_method', ucfirst($invoice->getPaymentMethod())));
            $row->addColumn(new Column('amount', $amount));
            $row->addColumn((new Column('actions'))->setActions($this->getActions($invoice, $dossier)));
            $this->table->addRow($row);
        }
    }

    /**
     * @param ShopInvoice $invoice
     * @return string
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    private function getInvoiceUser(ShopInvoice $invoice)
    {
        $customer = json_decode($invoice->getCustomer(), true);
        $avatar = $this->container->get(UsersService::class)->getGuestAvatar($customer['email'], 50, $customer['first_name'], $customer['last_name']);
        return '
            <div class="d-flex align-items-center">
                <div class="symbol symbol-40 symbol-sm flex-shrink-0">
                    <img class="" src="' . $avatar . '" alt="' . $customer['first_name'] . ' ' . $customer['last_name'] . '">
                </div>
                <div class="ml-4 d-flex flex-column">
                    <div class="text-dark-75 font-weight-bolder font-size-lg mb-0 d-flex">
                        ' . $customer['first_name'] . ' ' . $customer['last_name'] . '
                    </div>
                    ' . ($customer['email'] ? '<span class="text-muted font-weight-bold">' . $customer['email'] . '</span>' : '') . '
                    <span><strong>' . $invoice->getPrefix() . $invoice->getNumber() . '</strong></span>
                </span>
            </div>';
    }

    /**
     * @param ShopInvoice  $invoice
     * @param Dossier|null $dossier
     * @return array
     */
    private function getActions(ShopInvoice $invoice, ?Dossier $dossier = null)
    {
        $actions = [];
        $actions[] = new ColumnActionView(Tools::makeLink('app', 'shop', 'invoice/' . $invoice->getId()));
        if ($dossier) {
            $actions = [];
            if (!$invoice->getLocked() and RightsService::isGranted(ATTRIBUTE_EDIT, UNIVERSE_APP_SHOP)) {
                $actions[] = new ColumnActionEdit(Tools::makeLink('app', 'dossier', 'invoice/edit/' . $invoice->getId() . '/' . $dossier->getId()));
            }
            $actions[] = new ColumnActionView(Tools::makeLink('app', 'dossier', 'invoice/' . $invoice->getId() . '/' . $dossier->getId()));
        }
        if ($invoice->getLocked() and RightsService::isGranted(ATTRIBUTE_EXPORT, UNIVERSE_APP_SHOP)) {
            $actions[] = new ColumnActionExport(Tools::makeLink('app', 'shop', 'invoice/export/' . $invoice->getId()));
        }
        return $actions;
    }
}
