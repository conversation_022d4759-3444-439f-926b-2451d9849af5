<?php
namespace Mat<PERSON><PERSON>ver\Helpers\View\Table\Dossier;

use Mat<PERSON><PERSON><PERSON>\Entity\Mail\Queue\MailQueue;
use Mat<PERSON>yver\Helpers\Tools;
use Mat<PERSON>yver\Helpers\View\Table\Column;
use MatGyver\Helpers\View\Table\ColumnAction;
use MatGyver\Helpers\View\Table\ColumnActionDelete;
use MatGyver\Helpers\View\Table\Row;
use MatGyver\Helpers\View\Table\Table;
use MatGyver\Helpers\View\Table\ViewTableHelper;
use MatGyver\Services\Users\UsersService;

class DossierMailQueueViewHelper extends ViewTableHelper
{
    public function __construct()
    {
        $this->table = new Table('table_dossier_mail_queue2');
        $this->table->addTableColumn(new Column('date', __('Date')));
        $this->table->addTableColumn(new Column('subject', __('Sujet')));
        $this->table->addTableColumn(new Column('users', __('Destinataires')));
        $this->table->addTableColumn(new Column('status', __('État')));
        $this->table->addTableColumn(new Column('actions', __('Action')));
        $this->table->setTableSort(0, 'desc');
    }

    /**
     * @param MailQueue[] $dossierMailsQueues
     */
    public function getContent(array $dossierMailsQueues)
    {
        $this->setContentRows($dossierMailsQueues);
        return $this->table->getContent();
    }

    /**
     * @param MailQueue[] $dossierMailsQueues
     */
    public function setContentRows(array $dossierMailsQueues)
    {
        foreach ($dossierMailsQueues as $dossierMailQueue) {
            $row = new Row();
            $row->addColumn(new Column('date', dateTimeFr($dossierMailQueue->getDateAdd()->format('Y-m-d H:i:s')), '', $dossierMailQueue->getDateAdd()->getTimestamp()));
            $row->addColumn(new Column('subject', $dossierMailQueue->getSubject()));
            $row->addColumn(new Column('users', $this->getUsers($dossierMailQueue)));
            if ($dossierMailQueue->getError()) {
                $row->addColumn(new Column('status', '<span class="label label-inline label-danger" data-toggle="tooltip" data-title="' . $dossierMailQueue->getErrorMessage() . '">' . __('Erreur') . ' <i class="fas fa-info-circle"></i></span>'));
            } else {
                $row->addColumn(new Column('status', '<span class="label label-inline label-info">' . __('A envoyer') . '</span>'));
            }
            $row->addColumn((new Column('actions'))->setActions($this->getActions($dossierMailQueue)));
            $this->table->addRow($row);
        }
    }

    /**
     * @param MailQueue $dossierMailQueue
     * @return string
     */
    public function getUsers(MailQueue $dossierMailQueue): string
    {
        $users = $dossierMailQueue->getUsers();
        if (!count($users)) {
            return '<em>' . __('Aucun destinataire') . '</em>';
        }

        $content = '<div class="symbol-group symbol-hover">';
        foreach ($users as $user) {
            if ($user->getUser()) {
                $avatar = $this->container->get(UsersService::class)->getAvatar($user->getUser()->getEmail(), 50);
            } else {
                $avatar = $this->container->get(UsersService::class)->getGuestAvatar($user->getEmail(), 50, $user->getFirstName(), $user->getLastName());
            }
            $content .= '
                <div class="symbol symbol-30 symbol-circle" data-toggle="tooltip" data-html="true" data-title="' . ($user->getUser() ? $user->getUser() . '<br>' . $user->getUser()->getEmail() : $user->getFirstName() . ' ' . $user->getLastName() . '<br>' . $user->getEmail()) . '">
                    <img alt="' . ($user->getUser() ?: $user->getFirstName() . ' ' . $user->getLastName()) . '" src="' . $avatar . '">
                </div>';
        }
        $content .= '</div>';

        return $content;
    }

    /**
     * @param MailQueue $dossierMailQueue
     * @return ColumnAction[]
     */
    public function getActions(MailQueue $dossierMailQueue): array
    {
        return [
            new ColumnActionDelete('dossier_mail_delete', $dossierMailQueue->getId(), __('Êtes-vous sûr de vouloir supprimer cet email ?'), __('Supprimer'), Tools::makeLink('app', 'dossier', 'mail/delete/' . $dossierMailQueue->getId() . '/' . $dossierMailQueue->getDossier()->getId())),
        ];
    }
}
