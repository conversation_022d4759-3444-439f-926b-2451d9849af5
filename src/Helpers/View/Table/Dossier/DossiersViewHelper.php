<?php
namespace Mat<PERSON>yver\Helpers\View\Table\Dossier;

use Mat<PERSON><PERSON><PERSON>\Entity\Dossier\Dossier;
use Mat<PERSON><PERSON>ver\Helpers\Number;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Table\Column;
use MatGyver\Helpers\View\Table\ColumnAction;
use MatGyver\Helpers\View\Table\ColumnActionView;
use MatGyver\Helpers\View\Table\Row;
use MatGyver\Helpers\View\Table\Table;
use MatGyver\Helpers\View\Table\ViewTableHelper;
use MatGyver\Services\Dossier\DossierPaymentService;
use MatGyver\Services\Users\UsersService;

class DossiersViewHelper extends ViewTableHelper
{
    public function __construct()
    {
        $this->table = new Table('table_dossiers');
        $this->table->addTableColumn(new Column('id', __('Id')));
        $this->table->addTableColumn(new Column('name', __('Client')));
        $this->table->addTableColumn(new Column('reference', __('N° de sinistre')));
        $this->table->addTableColumn(new Column('vehicle', __('Véhicule')));
        $this->table->addTableColumn(new Column('expert', __('Expert')));
        $this->table->addTableColumn(new Column('amount', __('Montant'), 'w-120px'));
        $this->table->addTableColumn(new Column('expertise', __('Prochaine expertise')));
        $this->table->addTableColumn(new Column('delay', __('Délai OM')));
        $this->table->addTableColumn(new Column('status', __('État')));
        $this->table->addTableColumn(new Column('actions', __('Actions')));
    }

    /**
     * @param Dossier[]|null $dossiers
     */
    public function getContent(?array $dossiers = null)
    {
        if ($dossiers) {
            $this->setContentRows($dossiers);
        }
        return $this->table->getContent();
    }

    /**
     * @param Dossier[] $dossiers
     */
    public function setContentRows(array $dossiers)
    {
        foreach ($dossiers as $dossier) {
            $row = new Row();
            $row->addColumn(new Column('id', $dossier->getId()));

            $name = '<a class="font-weight-bolder d-flex align-items-center text-' . ($dossier->getAlert() ? 'danger' : 'primary') . '" href="' . Tools::makeLink('app', 'dossier', $dossier->getId()) . '">';
            if ($dossier->getAlert()) {
                $name .= '<i class="fas fa-exclamation-triangle text-danger mr-2" data-toggle="tooltip" data-title="' . __('En alerte depuis le %s', dateFr($dossier->getDateAlert()->format('Y-m-d'))) . '"></i>';
            }
            if ($dossier->getContact()) {
                $dossierName = $dossier->getContact()->getCompanyOrName();
                if ($dossier->isJudiciaire()) {
                    if ($dossier->getCaseName()) {
                        $dossierName = $dossier->getCaseName();
                    }
                    $dossierName .= ' <span class="label label-inline label-outline-primary label-sm ml-2">' . __('Judiciaire') . '</span>';
                }
                $name .= $dossierName;
            }
            $name .= '</a>' . $dossier->getExpertReference();
            $row->addColumn(new Column('name', $name));

            if ($dossier->getContact() and $dossier->getContact()->getLegalProtectionNumber()) {
                $row->addColumn(new Column('reference', $dossier->getContact()->getLegalProtectionNumber()));
            }

            $row->addColumn(new Column('expert', $this->addExpert($dossier)));

            if ($dossier->getVehicle()) {
                $vehicleInfos = $dossier->getVehicle()->getBrand() . ' ' . $dossier->getVehicle()->getModel() . '<br>' . $dossier->getVehicle()->getRegistration();
                $vehicleInfos .= '<span class="d-none">' . str_replace(['-', ' '], '', $dossier->getVehicle()->getRegistration());
                $row->addColumn(new Column('vehicle', $vehicleInfos));
            }

            $status = '<a href="' . Tools::makeLink('app', 'dossier', 'status/' . $dossier->getId(), 'return=dossiers') . '">';
            if ($dossier->getStatusMessage()) {
                $status .= '<span class="label label-inline label-outline-' . $dossier->getStatusLabel() . '" data-toggle="tooltip" data-title="' . $dossier->getStatusMessage() . '">' . $dossier->getStatusName() . '<i class="fas fa-info-circle ml-1"></i></span>';
            } else {
                $status .= '<span class="label label-inline label-outline-' . $dossier->getStatusLabel() . '">' . $dossier->getStatusName() . '</span>';
            }
            $status .= '</a>';
            $row->addColumn(new Column('status', $status));

            $amountPaid = $dossier->getAmountPaid();
            $color = 'danger';
            $suffix = '';
            if ($amountPaid > 0) {
                $color = 'warning';
                if ($amountPaid == $dossier->getTotalAmount()) {
                    $color = 'success';
                }
            } else {
                $suffix = '<i class="far fa-times-circle text-danger ml-2" data-toggle="tooltip" data-title="' . __('Aucun lien de paiement envoyé') . '"></i>';
                if (count($dossier->getPaymentsLinks())) {
                    $color = 'info';
                    $suffix = '<i class="far fa-clock text-info ml-2" data-toggle="tooltip" data-title="' . __('Lien de paiement envoyé - en attente de paiement') . '"></i>';
                }
            }
            $row->addColumn(new Column('amount', '<span class="text-' . $color . '">' . Number::formatAmount($amountPaid, DEFAULT_CURRENCY) . ' / ' . Number::formatAmount($dossier->getTotalAmount(), DEFAULT_CURRENCY) . '</span>' . $suffix));

            if ($dossier->getLastExpertise() and $dossier->getLastExpertise()->getDate()) {
                $row->addColumn(new Column('expertise', dateFr($dossier->getLastExpertise()->getDate()->format('Y-m-d'))));
            }

            if ($dossier->getDateClosed()) {
                $diff = $dossier->getDateClosed()->diff($dossier->getDate());
            } else {
                $diff = $dossier->getDate()->diff(new \DateTime());
            }
            $row->addColumn(new Column('delay', $diff->days . 'j'));

            $row->addColumn((new Column('actions'))->setActions($this->getActions($dossier)));
            $this->table->addRow($row);
        }
    }

    /**
     * @param Dossier $dossier
     * @return string
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function addExpert(Dossier $dossier): string
    {
        if (!$dossier->getUser()) {
            return '';
        }

        try {
            $avatar = $this->container->get(UsersService::class)->getAvatar($dossier->getUser()->getEmail(), 50);
            return '
            <div class="symbol-group symbol-hover">
                <div class="symbol symbol-30 symbol-circle" data-toggle="tooltip" data-html="true" data-title="' . $dossier->getUser() . '">
                    <img alt="' . $dossier->getUser() . '" src="' . $avatar . '">
                </div>
            </div>';
        } catch (\Exception $e) {
            return '';
        }
    }

    /**
     * @param Dossier $dossier
     * @return ColumnAction[]
     */
    public function getActions(Dossier $dossier): array
    {
        return [
            new ColumnActionView(Tools::makeLink('app', 'dossier', $dossier->getId())),
        ];
    }
}
