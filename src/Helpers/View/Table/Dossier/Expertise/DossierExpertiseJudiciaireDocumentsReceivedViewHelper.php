<?php
namespace MatGyver\Helpers\View\Table\Dossier\Expertise;

use MatG<PERSON>ver\Entity\Dossier\Dossier;
use MatGyver\Entity\Dossier\DossierInstitution;
use MatGyver\Entity\Dossier\Expertise\DossierExpertise;
use MatGyver\Entity\Dossier\Expertise\DossierExpertiseJudiciaireChronology;
use MatG<PERSON>ver\Entity\Dossier\Expertise\DossierExpertisePerson;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\ImagickHelper;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Table\Column;
use MatGyver\Helpers\View\Table\ColumnAction;
use MatGyver\Helpers\View\Table\ColumnActionDelete;
use MatGyver\Helpers\View\Table\ColumnActionEdit;
use MatGyver\Helpers\View\Table\Row;
use MatGyver\Helpers\View\Table\Table;
use MatG<PERSON>ver\Helpers\View\Table\ViewTableHelper;

class DossierExpertiseJudiciaireDocumentsReceivedViewHelper extends ViewTableHelper
{
    public function __construct()
    {
        $this->table = new Table('table_dossier_expertise_judiciaire_documents_received_');
        $this->table->setClass('sortable-chronologies-table');
        $this->table->addTableColumn(new Column('sort', ''));
        $this->table->addTableColumn(new Column('date', __('Date')));
        $this->table->addTableColumn(new Column('recipient', __('Personne')));
        $this->table->addTableColumn(new Column('parts', __('Pièces n°')));
        $this->table->addTableColumn(new Column('object', __('Objets')));
        $this->table->addTableColumn(new Column('actions', __('Actions')));
        $this->table->setTableSort(0, 'asc');
        $this->table->setDataTable(false);
    }

    /**
     * @param array $chronologiesByDate
     * @param DossierExpertise $expertise
     * @param Dossier|null $dossier
     */
    public function getContent(array $chronologiesByDate, DossierExpertise $expertise, ?Dossier $dossier = null)
    {
        Assets::addJs('plugins/jquery-ui-sortable.js');
        Assets::addJs('plugins/jquery.ui.touch-punch.min.js');
        Assets::addJs('app/dossier/chronologies.js');

        $this->table->setTableData('expertise', $expertise->getId());

        $this->setContentRows($chronologiesByDate, $dossier);
        return $this->table->getContent();
    }

    /**
     * @param array $chronologiesByDate
     * @param Dossier|null $dossier
     */
    public function setContentRows(array $chronologiesByDate, ?Dossier $dossier = null)
    {
        foreach ($chronologiesByDate as $date => $chronologies) {
            /** @var DossierExpertiseJudiciaireChronology[] $chronologies **/
            foreach ($chronologies as $item) {
                $row = new Row();
                $row->setId($item->getId());
                $row->addColumn(new Column('sort', '<i class="fa fa-sort"></i>', 'reorder'));

                $row->addColumn(new Column('date', ($item->getDate() ? $item->getDate()->format('d/m/Y') : ''), '', ($item->getDate() ? $item->getDate()->getTimestamp() : 0)));

                if ($item->getPersons()) {
                    $names = '';
                    foreach ($item->getPersons() as $person) {
                        $name = $person->getName();
                        if (!str_contains($name, 'Maître') and ($person->getType() == DossierExpertisePerson::TYPE_LAWYER or ($person->getInstitution() and $person->getInstitution()->getType() == DossierInstitution::TYPE_LAWYER))) {
                            $name = __('Maître %s', $person->getName());
                        }
                        $names .= $name . '<br>';
                    }
                    $row->addColumn(new Column('recipient', $names));
                } else {
                    $row->addColumn(new Column('recipient', $item->getRecipient()));
                }

                $row->addColumn(new Column('parts', $item->getObject()));

                $comment = $item->getContent();
                if ($item->getDocument()) {
                    if (!$item->getDocument()->getConfidential()) {
                        $image = $item->getDocument()->getThumbnailUrl();
                        $thumbnail = $item->getDocument()->getThumbnailUrl();
                        if (in_array(getExtension($item->getDocument()->getFile()), ['jpg', 'jpeg', 'png', 'gif'])) {
                            $image = $item->getDocument()->getUrl();
                            if (!$item->getDocument()->getThumbnail()) {
                                $thumbnail = $item->getDocument()->getUrl();
                            }
                        }
                        $comment .= '<br><br><a class="cursor-pointer" onclick="createModalImg(\'' . $image . '\', \'\');"><img class="w-200px" src="' . $thumbnail . '"></a>';
                    }
                } elseif ($item->getFile() and in_array(getExtension($item->getFile()), ['jpg', 'jpeg', 'png', 'gif'])) {
                    $comment .= '<br><br><a class="cursor-pointer" onclick="createModalImg(\'' . $item->getUrl() . '\', \'\');"><img class="w-200px" src="' . $item->getUrl() . '"></a>';
                } elseif ($item->getFile() and getExtension($item->getFile()) == 'pdf') {
                    $fullPath = WEB_PATH . '/medias/' . $item->getExpertise()->getDossier()->getFolder();
                    $images = ImagickHelper::convertPdf($fullPath . $item->getFile(), true);
                    $comment .= '<br><br><a class="cursor-pointer" onclick="createModalImg(\'' . Assets::getMediaUrl($images[0]) . '\', \'\');"><img class="w-200px" src="' . Assets::getMediaUrl($images[0]) . '"></a>';
                }

                //$comment = '<div class="show-more" data-nb-lines="25"><div class="show-more-content">' . $comment . '</div></div>';
                $row->addColumn(new Column('object', $comment));

                $row->addColumn((new Column('actions'))->setActions($this->getActions($item, $dossier)));
                $this->table->addRow($row);
            }
        }
    }

    /**
     * @param DossierExpertiseJudiciaireChronology $item
     * @param Dossier|null $dossier
     * @return ColumnAction[]
     */
    public function getActions(DossierExpertiseJudiciaireChronology $item, ?Dossier $dossier = null): array
    {
        $action = new ColumnAction();
        $action->setIcon('fas fa-share');
        $action->setOnClick('modalMoveChronology(' . $item->getId() . ');');
        $action->setTooltip(__('Déplacer'));

        if ($dossier) {
            return [
                new ColumnActionEdit(Tools::makeLink('app', 'dossier', 'expertise_judiciaire/documents_received/edit/' . $item->getId() . '/' . $dossier->getId())),
                $action,
                new ColumnActionDelete('dossier_expertise_judiciaire_documents_received', $item->getId(), __('Etes-vous sûr de vouloir supprimer cette déclaration ?'), '', Tools::makeLink('app', 'dossier', 'expertise_judiciaire/documents_received/delete/' . $item->getId())),
            ];
        }
        return [
            new ColumnActionEdit(Tools::makeLink('app', 'dossier', 'expertise/judiciaires/documents_received/edit/' . $item->getId() . '/' . $item->getExpertise()->getId())),
            $action,
            new ColumnActionDelete('dossier_expertise_judiciaire_documents_received', $item->getId(), __('Etes-vous sûr de vouloir supprimer cette déclaration ?'), '', Tools::makeLink('app', 'dossier', 'expertise/judiciaires/documents_received/delete/' . $item->getId())),
        ];
    }
}
