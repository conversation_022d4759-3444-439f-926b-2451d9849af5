<?php
namespace MatGyver\Helpers\View\Table\Dossier\Expertise;

use Mat<PERSON>yver\Entity\Dossier\Expertise\DossierExpertiseJudiciaireDire;
use MatGyver\Helpers\Assets;
use Mat<PERSON>yver\Helpers\ImagickHelper;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Table\Column;
use MatGyver\Helpers\View\Table\ColumnAction;
use MatGyver\Helpers\View\Table\ColumnActionDelete;
use MatGyver\Helpers\View\Table\ColumnActionEdit;
use MatGyver\Helpers\View\Table\Row;
use MatGyver\Helpers\View\Table\Table;
use MatGyver\Helpers\View\Table\ViewTableHelper;

class DossierExpertiseJudiciaireDireViewHelper extends ViewTableHelper
{
    public function __construct()
    {
        $this->table = new Table('table_dossier_expertise_judiciaire_dires_');
        $this->table->setClass('sortable-dires-table');
        $this->table->addTableColumn(new Column('sort', ''));
        $this->table->addTableColumn(new Column('date', __('Date')));
        $this->table->addTableColumn(new Column('person', __('Personne')));
        $this->table->addTableColumn(new Column('content', __('Contenu')));
        $this->table->addTableColumn(new Column('actions', __('Actions')));
        $this->table->setTableSort(0, 'asc');
        $this->table->setDataTable(false);
    }

    /**
     * @param DossierExpertiseJudiciaireDire[] $dossierExpertiseJudiciaireDires
     */
    public function getContent(array $dossierExpertiseJudiciaireDires)
    {
        Assets::addJs('plugins/jquery-ui-sortable.js');
        Assets::addJs('plugins/jquery.ui.touch-punch.min.js');
        Assets::addJs('app/dossier/expertise_dires.js');

        $this->table->setTableData('expertise', $dossierExpertiseJudiciaireDires[0]->getExpertise()->getId());

        $this->setContentRows($dossierExpertiseJudiciaireDires);
        return $this->table->getContent();
    }

    /**
     * @param DossierExpertiseJudiciaireDire[] $dossierExpertiseJudiciaireDires
     */
    public function setContentRows(array $dossierExpertiseJudiciaireDires)
    {
        foreach ($dossierExpertiseJudiciaireDires as $dossierExpertiseJudiciaireDire) {
            $row = new Row();

            $row->setId($dossierExpertiseJudiciaireDire->getId());
            $row->addColumn(new Column('sort', '<i class="fa fa-sort"></i>', 'reorder'));

            $row->addColumn(new Column('date', ($dossierExpertiseJudiciaireDire->getDireDate() ? $dossierExpertiseJudiciaireDire->getDireDate()->format('d/m/Y') : '')));
            $row->addColumn(new Column('person', $dossierExpertiseJudiciaireDire->getPerson()->getName()));

            $content = $dossierExpertiseJudiciaireDire->getContent();
            if ($dossierExpertiseJudiciaireDire->getFile()) {
                if (in_array(getExtension($dossierExpertiseJudiciaireDire->getFile()), ['jpg', 'jpeg', 'png', 'gif'])) {
                    $content .= '<br><a class="cursor-pointer" onclick="createModalImg(\'' . $dossierExpertiseJudiciaireDire->getFileUrl() . '\', \'\');"><img class="w-200px" src="' . $dossierExpertiseJudiciaireDire->getFileUrl() . '"></a>';
                } elseif (getExtension($dossierExpertiseJudiciaireDire->getFile()) == 'pdf') {
                    $fullPath = WEB_PATH . '/medias/' . $dossierExpertiseJudiciaireDire->getExpertise()->getDossier()->getFolder();
                    $images = ImagickHelper::convertPdf($fullPath . $dossierExpertiseJudiciaireDire->getFile(), true);
                    $content .= '<br><a class="cursor-pointer" onclick="createModalImg(\'' . Assets::getMediaUrl($images[0]) . '\', \'\');"><img class="w-200px" src="' . Assets::getMediaUrl($images[0]) . '"></a>';
                }
            }
            $row->addColumn(new Column('content', $content));

            $row->addColumn((new Column('actions'))->setActions($this->getActions($dossierExpertiseJudiciaireDire)));
            $this->table->addRow($row);
        }
    }

    /**
     * @param DossierExpertiseJudiciaireDire $dossierExpertiseJudiciaireDire
     * @return ColumnAction[]
     */
    public function getActions(DossierExpertiseJudiciaireDire $dossierExpertiseJudiciaireDire): array
    {
        return [
            new ColumnActionEdit(Tools::makeLink('app', 'dossier', 'expertise/judiciaires/dire/edit/' . $dossierExpertiseJudiciaireDire->getId() . '/' . $dossierExpertiseJudiciaireDire->getExpertise()->getId())),
            new ColumnActionDelete('dossier_expertise_judiciaire_dire', $dossierExpertiseJudiciaireDire->getId(), __('Etes-vous sûr de vouloir supprimer ce dire ?'), '', Tools::makeLink('app', 'dossier', 'expertise/judiciaires/dire/delete/' . $dossierExpertiseJudiciaireDire->getId())),
        ];
    }
}
