<?php
namespace MatGyver\Helpers\View\Table\Dossier\Expertise;

use MatGyver\Entity\Dossier\Expertise\DossierExpertiseVehicle;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Table\Column;
use MatGyver\Helpers\View\Table\ColumnAction;
use MatGyver\Helpers\View\Table\ColumnActionDelete;
use MatGyver\Helpers\View\Table\ColumnActionEdit;
use MatGyver\Helpers\View\Table\ColumnActionView;
use MatGyver\Helpers\View\Table\Row;
use MatGyver\Helpers\View\Table\Table;
use MatGyver\Helpers\View\Table\ViewTableHelper;

class DossierExpertiseVehicleViewHelper extends ViewTableHelper
{
    public function __construct()
    {
        $this->table = new Table('table_dossierexpertisevehicle');
            $this->table->addTableColumn(new Column('id', __('id')));
            $this->table->addTableColumn(new Column('meter', __('meter')));
            $this->table->addTableColumn(new Column('meterType', __('meterType')));
            $this->table->addTableColumn(new Column('levelComment', __('levelComment')));
            $this->table->addTableColumn(new Column('levelIgnore', __('levelIgnore')));
            $this->table->addTableColumn(new Column('wheelsNumber', __('wheelsNumber')));
            $this->table->addTableColumn(new Column('tireWear', __('tireWear')));
            $this->table->addTableColumn(new Column('tireIgnore', __('tireIgnore')));
            $this->table->addTableColumn(new Column('additionalWheelComment', __('additionalWheelComment')));
            $this->table->addTableColumn(new Column('examinationCondition', __('examinationCondition')));
            $this->table->addTableColumn(new Column('presented', __('presented')));
            $this->table->addTableColumn(new Column('rolling', __('rolling')));
            $this->table->addTableColumn(new Column('repaired', __('repaired')));
            $this->table->addTableColumn(new Column('dismantled', __('dismantled')));
            $this->table->addTableColumn(new Column('partExamination', __('partExamination')));

        $this->table->addTableColumn(new Column('actions', __('Actions')));
    }

    /**
     * @param DossierExpertiseVehicle[] $dossierExpertiseVehicles
     */
    public function getContent(array $dossierExpertiseVehicles)
    {
        $this->setContentRows($dossierExpertiseVehicles);
        return $this->table->getContent();
    }

    /**
     * @param DossierExpertiseVehicle[] $dossierExpertiseVehicles
     */
    public function setContentRows(array $dossierExpertiseVehicles)
    {
        foreach ($dossierExpertiseVehicles as $dossierExpertiseVehicle) {
            $row = new Row();

            $row->addColumn(new Column('id', $dossierExpertiseVehicle->getId()));
            $row->addColumn(new Column('meter', $dossierExpertiseVehicle->getMeter()));
            $row->addColumn(new Column('meterType', $dossierExpertiseVehicle->getMeterType()));
            $row->addColumn(new Column('levelComment', $dossierExpertiseVehicle->getLevelComment()));
            if ($dossierExpertiseVehicle->getLevelIgnore()) {
                $content = '<span class="label label-inline label-success">' . __('Oui') . '</span>';
            } else {
                $content = '<span class="label label-inline label-danger">' . __('Non') . '</span>';
            }
            $row->addColumn(new Column('levelIgnore', $content));
            $row->addColumn(new Column('wheelsNumber', $dossierExpertiseVehicle->getWheelsNumber()));
            $row->addColumn(new Column('tireWear', $dossierExpertiseVehicle->getTireWear()));
            if ($dossierExpertiseVehicle->getTireIgnore()) {
                $content = '<span class="label label-inline label-success">' . __('Oui') . '</span>';
            } else {
                $content = '<span class="label label-inline label-danger">' . __('Non') . '</span>';
            }
            $row->addColumn(new Column('tireIgnore', $content));
            $row->addColumn(new Column('additionalWheelComment', $dossierExpertiseVehicle->getAdditionalWheelComment()));
            $row->addColumn(new Column('examinationCondition', $dossierExpertiseVehicle->getExaminationCondition()));
            if ($dossierExpertiseVehicle->getPresented()) {
                $content = '<span class="label label-inline label-success">' . __('Oui') . '</span>';
            } else {
                $content = '<span class="label label-inline label-danger">' . __('Non') . '</span>';
            }
            $row->addColumn(new Column('presented', $content));
            if ($dossierExpertiseVehicle->getRolling()) {
                $content = '<span class="label label-inline label-success">' . __('Oui') . '</span>';
            } else {
                $content = '<span class="label label-inline label-danger">' . __('Non') . '</span>';
            }
            $row->addColumn(new Column('rolling', $content));
            if ($dossierExpertiseVehicle->getRepaired()) {
                $content = '<span class="label label-inline label-success">' . __('Oui') . '</span>';
            } else {
                $content = '<span class="label label-inline label-danger">' . __('Non') . '</span>';
            }
            $row->addColumn(new Column('repaired', $content));
            if ($dossierExpertiseVehicle->getDismantled()) {
                $content = '<span class="label label-inline label-success">' . __('Oui') . '</span>';
            } else {
                $content = '<span class="label label-inline label-danger">' . __('Non') . '</span>';
            }
            $row->addColumn(new Column('dismantled', $content));
            if ($dossierExpertiseVehicle->getPartExamination()) {
                $content = '<span class="label label-inline label-success">' . __('Oui') . '</span>';
            } else {
                $content = '<span class="label label-inline label-danger">' . __('Non') . '</span>';
            }
            $row->addColumn(new Column('partExamination', $content));

            $row->addColumn((new Column('actions'))->setActions($this->getActions($dossierExpertiseVehicle)));
            $this->table->addRow($row);
        }
    }

    /**
     * @param DossierExpertiseVehicle $dossierExpertiseVehicle
     * @return ColumnAction[]
     */
    public function getActions(DossierExpertiseVehicle $dossierExpertiseVehicle): array
    {
        return [
            new ColumnActionView(Tools::makeLink('app', 'dossier/expertise/vehicle', $dossierExpertiseVehicle->getId())),
            new ColumnActionEdit(Tools::makeLink('app', 'dossier/expertise/vehicle', 'edit/' . $dossierExpertiseVehicle->getId())),
            new ColumnActionDelete('dossierexpertisevehicle', $dossierExpertiseVehicle->getId(), __('Etes-vous sûr de vouloir supprimer ce véhicule ?'), '', Tools::makeLink('app', 'dossier/expertise/vehicle', 'delete/' . $dossierExpertiseVehicle->getId())),
        ];
    }
}
