<?php
namespace MatG<PERSON>ver\Helpers\View\Table\Dossier\Expertise;

use Mat<PERSON>yver\Entity\Dossier\Expertise\DossierExpertiseJudiciaireDire;
use MatGyver\Helpers\Assets;
use Mat<PERSON>yver\Helpers\ImagickHelper;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Table\Column;
use MatGyver\Helpers\View\Table\ColumnAction;
use MatGyver\Helpers\View\Table\ColumnActionDelete;
use MatGyver\Helpers\View\Table\ColumnActionEdit;
use MatGyver\Helpers\View\Table\Row;
use MatGyver\Helpers\View\Table\Table;
use MatGyver\Helpers\View\Table\ViewTableHelper;

class DossierExpertiseJudiciaireDireAnswerViewHelper extends ViewTableHelper
{
    public function __construct()
    {
        $this->table = new Table('table_dossier_expertise_judiciaire_dires_');
        $this->table->setClass('sortable-dire-answers-table');
        $this->table->addTableColumn(new Column('sort', '', 'w-20px'));
        $this->table->addTableColumn(new Column('date', __('Date')));
        $this->table->addTableColumn(new Column('content', __('Réponse')));
        $this->table->addTableColumn(new Column('actions', __('Actions')));
        $this->table->setTableSort(0, 'asc');
        $this->table->setDataTable(false);
    }

    /**
     * @param DossierExpertiseJudiciaireDire[] $dossierExpertiseJudiciaireDireAnswers
     */
    public function getContent(array $dossierExpertiseJudiciaireDireAnswers)
    {
        Assets::addJs('plugins/jquery-ui-sortable.js');
        Assets::addJs('plugins/jquery.ui.touch-punch.min.js');
        Assets::addJs('app/dossier/expertise_dires_answers.js');

        $this->table->setTableData('expertise', $dossierExpertiseJudiciaireDireAnswers[0]->getExpertise()->getId());
        $this->table->setTableData('dire', $dossierExpertiseJudiciaireDireAnswers[0]->getParent()->getId());

        $this->setContentRows($dossierExpertiseJudiciaireDireAnswers);
        return $this->table->getContent();
    }

    /**
     * @param DossierExpertiseJudiciaireDire[] $dossierExpertiseJudiciaireDireAnswers
     */
    public function setContentRows(array $dossierExpertiseJudiciaireDireAnswers)
    {
        foreach ($dossierExpertiseJudiciaireDireAnswers as $dossierExpertiseJudiciaireDireAnswer) {
            $row = new Row();

            $row->setId($dossierExpertiseJudiciaireDireAnswer->getId());
            $row->addColumn(new Column('sort', '<i class="fa fa-sort"></i>', 'reorder'));

            $row->addColumn(new Column('date', ($dossierExpertiseJudiciaireDireAnswer->getDireDate() ? $dossierExpertiseJudiciaireDireAnswer->getDireDate()->format('d/m/Y') : '')));

            $content = $dossierExpertiseJudiciaireDireAnswer->getContent();

            if ($dossierExpertiseJudiciaireDireAnswer->getFile()) {
                if (in_array(getExtension($dossierExpertiseJudiciaireDireAnswer->getFile()), ['jpg', 'jpeg', 'png', 'gif'])) {
                    $content .= '<br><a class="cursor-pointer" onclick="createModalImg(\'' . $dossierExpertiseJudiciaireDireAnswer->getFileUrl() . '\', \'\');"><img class="w-200px" src="' . $dossierExpertiseJudiciaireDireAnswer->getFileUrl() . '"></a>';
                } elseif (getExtension($dossierExpertiseJudiciaireDireAnswer->getFile()) == 'pdf') {
                    $fullPath = WEB_PATH . '/medias/' . $dossierExpertiseJudiciaireDireAnswer->getExpertise()->getDossier()->getFolder();
                    $images = ImagickHelper::convertPdf($fullPath . $dossierExpertiseJudiciaireDireAnswer->getFile(), true);
                    $content .= '<br><a class="cursor-pointer" onclick="createModalImg(\'' . Assets::getMediaUrl($images[0]) . '\', \'\');"><img class="w-200px" src="' . Assets::getMediaUrl($images[0]) . '"></a>';
                }
            }
            $row->addColumn(new Column('content', $content));

            $column = new Column('actions');
            $column->setClass('w-100px');
            $column->setActions($this->getActions($dossierExpertiseJudiciaireDireAnswer));
            $row->addColumn($column);
            $this->table->addRow($row);
        }
    }

    /**
     * @param DossierExpertiseJudiciaireDire $dossierExpertiseJudiciaireDireAnswer
     * @return ColumnAction[]
     */
    public function getActions(DossierExpertiseJudiciaireDire $dossierExpertiseJudiciaireDireAnswer): array
    {
        $editLink = Tools::makeLink('app', 'dossier', 'expertise/judiciaires/dire/answer/edit/' . $dossierExpertiseJudiciaireDireAnswer->getId() . '/' . $dossierExpertiseJudiciaireDireAnswer->getExpertise()->getId());
        $deleteLink = Tools::makeLink('app', 'dossier', 'expertise/judiciaires/dire/answer/delete/' . $dossierExpertiseJudiciaireDireAnswer->getId() . '/' . $dossierExpertiseJudiciaireDireAnswer->getExpertise()->getId());

        return [
            new ColumnActionEdit($editLink),
            new ColumnActionDelete('dossier_expertise_judiciaire_dire_answer', $dossierExpertiseJudiciaireDireAnswer->getId(), __('Etes-vous sûr de vouloir supprimer cette réponse ?'), '', $deleteLink),
        ];
    }
}
