<?php

namespace MatGyver\Helpers\View\Table\Shop;

use MatGyver\Entity\PaymentMethod\PaymentMethodSubscription;
use MatGyver\Entity\Stripe\Subscription\StripeSubscription;
use MatGyver\Helpers\Number;
use MatGyver\Helpers\Tools;
use MatG<PERSON>ver\Helpers\View\Table\Column;
use MatGyver\Helpers\View\Table\ColumnAction;
use MatGyver\Helpers\View\Table\ColumnActionDelete;
use MatGyver\Helpers\View\Table\ColumnActionDisable;
use MatGyver\Helpers\View\Table\ColumnActionEdit;
use MatGyver\Helpers\View\Table\ColumnActionEnable;
use MatGyver\Helpers\View\Table\ColumnActionView;
use MatGyver\Helpers\View\Table\Table;
use MatGyver\Helpers\View\Table\ViewTableHelper;
use MatGyver\Helpers\View\Table\Row;
use MatGyver\Services\RightsService;
use MatGyver\Services\Subscription\SubscriptionService;
use MatGyver\Services\Users\UsersService;

/**
 * Class ShopSubscriptionsViewHelper.
 */
class ShopSubscriptionsViewHelper extends ViewTableHelper
{
    public function __construct()
    {
        $this->table = new Table('table_subscriptions');
        $this->table->setDataTable(false);
        $this->table->addTableColumn(new Column('id', 'ID', 'hidden'));
        $this->table->addTableColumn(new Column('client', __('Client')));
        $this->table->addTableColumn(new Column('type', __('Type')));
        $this->table->addTableColumn(new Column('product', __('Produit')));
        $this->table->addTableColumn(new Column('amount', __('Montant')));
        $this->table->addTableColumn(new Column('payments_left', __('Paiements restants'), 'hidden-phone'));
        $this->table->addTableColumn(new Column('next_payment', __('Prochain paiement')));
        $this->table->addTableColumn(new Column('status', __('Etat')));
        $this->table->addTableColumn(new Column('actions', __('Actions')));

        $this->addPostContent();
    }

    /**
     * @param PaymentMethodSubscription[]|null $subscriptions
     * @return string
     */
    public function getContent(?array $subscriptions = null): string
    {
        if ($subscriptions and RightsService::isGranted(ATTRIBUTE_VIEW, UNIVERSE_ADMIN_STATS)) {
            $this->displayTotalAmount($subscriptions);
        }
        return $this->table->getContent();
    }

    /**
     * @param PaymentMethodSubscription[] $subscriptions
     * @return void
     */
    public function setSubscriptionsRows(array $subscriptions): void
    {
        $canEdit = RightsService::isGranted(ATTRIBUTE_EDIT, UNIVERSE_ADMIN_SHOP);
        $canDelete = RightsService::isGranted(ATTRIBUTE_DELETE, UNIVERSE_ADMIN_SHOP);
        foreach ($subscriptions as $subscription) {
            $type = $subscription->getType();

            $avatarUrl = $this->container->get(UsersService::class)->getGuestAvatar($subscription->getEmail(), 50, $subscription->getFirstName(), $subscription->getLastName());
            $user = '
                <div class="d-flex align-items-center">
                    <div class="symbol symbol-50 symbol-sm flex-shrink-0">
                        <img class="" src="' . $avatarUrl . '" alt="photo">
                    </div>
                    <div class="ml-4">
                        <div class="text-dark-75 font-weight-bolder font-size-lg mb-0">' . $subscription->getFirstName() . ' ' . $subscription->getLastName() . '</div>
                        <a href="mailto:' . $subscription->getEmail() . '" class="text-muted font-weight-bold text-hover-primary">' . $subscription->getEmail() . '</a>
                    </div>
                </div>';

            //status
            if (!$subscription->getValid()) {
                if ('desactive' == $subscription->getError()) {
                    $status = '<span class="label label-inline label-danger">' . __('Désactivé') . '</span>';
                } else {
                    $status = '<span class="label label-inline label-danger">' . __('Erreur') . '</span>';
                }
            } else {
                if ('0' == $subscription->getNbPaymentsLeft()) {
                    $status = '<span class="label label-inline label-success">' . __('Terminé') . '</span>';
                } else {
                    $status = '<span class="label label-inline label-info">' . __('En cours') . '</span>';
                }
            }

            //montant
            $amount = Number::formatAmount($subscription->getAmount(), $subscription->getCurrency());
            if ($subscription instanceof StripeSubscription) {
                $amount = Number::formatAmount(round($subscription->getAmount() / 100, 2), $subscription->getCurrency());
            }

            if ('x' == $subscription->getNbPayments()) {
                $nbLeftPayments = '&#8734;';
            } else {
                $nbLeftPayments = $subscription->getNbPaymentsLeft() . '/' . ($subscription->getNbPayments() + 1);
            }

            //période
            $delay = ltrim($subscription->getDecalage(), '+');
            $delay = explode(' ', $delay);
            list($nb, $delayType) = $delay;
            $nbLeftPayments .= ' ' . ($delayType == 'days' ? n__('tous les jour', 'tous les %d jours', $nb, $nb) : n__('tous les mois', 'tous les %d mois', $nb, $nb));

            //next payment date
            if ($subscription->getValid() and '0' != $subscription->getNbPayments()) {
                $dateNextPayment = dateFr($subscription->getDate()->format('Y-m-d'));
            } else {
                $dateNextPayment = '<span class="gray">' . dateFr($subscription->getDate()->format('Y-m-d')) . '</span>';
            }

            //actions
            $actions = $this->getActions($subscription, $type, $canEdit, $canDelete);

            $row = new Row();
            $row->addColumn(new Column('id', $subscription->getId()));
            $row->addColumn(new Column('client', $user));
            $row->addColumn(new Column('type', $type));
            $row->addColumn(new Column('product', $subscription->getProduct()));
            $row->addColumn(new Column('amount', $amount));
            $row->addColumn(new Column('payments_left', $nbLeftPayments));
            $row->addColumn(new Column('next_payment', $dateNextPayment));
            $row->addColumn(new Column('status', $status));
            $row->addColumn((new Column('actions'))->setActions($actions));
            $this->table->addRow($row);
        }
    }

    /**
     * @param PaymentMethodSubscription[] $subscriptions
     * @return void
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function displayTotalAmount(array $subscriptions): void
    {
        $amountLeft = $this->container->get(SubscriptionService::class)->getAmountLeft($subscriptions);

        $this->table->setPreContent('
        <div class="alert alert-custom alert-white alert-shadow fade show gutter-b" role="alert">
            <div class="alert-icon">
                <span class="svg-icon svg-icon-primary svg-icon-2x">
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <rect x="0" y="0" width="24" height="24"/>
                            <circle fill="#000000" opacity="0.3" cx="12" cy="12" r="10"/>
                            <rect fill="#000000" x="11" y="10" width="2" height="7" rx="1"/>
                            <rect fill="#000000" x="11" y="7" width="2" height="2" rx="1"/>
                        </g>
                    </svg>
                </span>
            </div>
            <div class="alert-text">
                ' . __('Montant total restant') . ' : ' . Number::formatAmount($amountLeft, DEFAULT_CURRENCY) . ' <i class="fa fa-info-circle" data-rel="tooltip" data-title="' . __('Ce montant ne tient pas compte des abonnements à durée indéterminée') . '" data-placement="bottom"></i>
            </div>
        </div>
        
        <div class="card card-custom gutter-b">
            <div class="card-body">');
    }

    private function addPostContent()
    {
        $this->table->setPostContent('
            </div>
        </div>');
    }

    /**
     * @param $subscription
     * @param string $type
     * @param bool $canEdit
     * @param bool $canDelete
     * @return ColumnAction[]
     */
    public function getActions($subscription, string $type, bool $canEdit, bool $canDelete): array
    {
        $actions = [new ColumnActionView(Tools::makeLink('admin', 'shop', 'subscription/' . $subscription->getId(), 't=' . $type))];

        if ($canEdit) {
            $actions[] = new ColumnActionEdit(Tools::makeLink('admin', 'shop', 'subscription/edit/' . $subscription->getIp(), 't=' . $type));

            if ('0' != $subscription->getNbPayments()) {
                if ($subscription->getValid()) {
                    $actions[] = new ColumnActionDisable('CreateConfirmationDeleteAlert(\'termine_subscription\', ' . $subscription->getId() . ', \'' . __('Etes-vous sûr de vouloir désactiver cet abonnement ?') . '\', \'' . __('Désactiver') . '\', \'\', \'' . Tools::makeLink('admin', 'shop', 'subscription/desactive/' . $subscription->getId(), 't=' . $type) . '\');', '', __('Désactiver'));
                } else {
                    $actions[] = new ColumnActionEnable('', Tools::makeLink('admin', 'shop', 'subscription/active/' . $subscription->getId(), 't=' . $type), __('Réactiver cet abonnement'));
                }
            }
        }
        if ($canDelete) {
            //suppression
            $actions[] = new ColumnActionDelete('subscription', $subscription->getId(), __('Etes-vous sûr de vouloir supprimer cet abonnement ?'), '', Tools::makeLink('admin', 'shop', 'subscription/delete/' . $subscription->getId(), 't=' . $type));
        }

        return $actions;
    }
}
