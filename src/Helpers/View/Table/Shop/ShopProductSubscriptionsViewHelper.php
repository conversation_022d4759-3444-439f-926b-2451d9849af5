<?php

namespace MatG<PERSON>ver\Helpers\View\Table\Shop;

use Mat<PERSON><PERSON><PERSON>\Entity\Client\Freemium\ClientFreemium;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Table\Column;
use MatGyver\Helpers\View\Table\ColumnActionView;
use MatGyver\Helpers\View\Table\Table;
use MatGyver\Helpers\View\Table\ViewTableHelper;
use MatGyver\Helpers\View\Table\Row;

/**
 * Class ShopProductPaymentsViewHelper.
 */
class ShopProductSubscriptionsViewHelper extends ViewTableHelper
{
    public function __construct()
    {
        $this->table = new Table();
        $this->table->addTableColumn(new Column('id', __('ID')));
        $this->table->addTableColumn(new Column('date', __('Date')));
        $this->table->addTableColumn(new Column('name', __('Nom')));
        $this->table->addTableColumn(new Column('actions', __('Actions')));
    }

    /**
     * @param ClientFreemium[] $subscriptions
     * @return string
     */
    public function getContent(array $subscriptions): string
    {
        $this->setSubscriptionsRows($subscriptions);
        return $this->table->getContent();
    }

    /**
     * @param ClientFreemium[] $subscriptions
     * @return void
     */
    public function setSubscriptionsRows(array $subscriptions): void
    {
        foreach ($subscriptions as $subscription) {
            $row = new Row();
            $row->addColumn(new Column('id', $subscription->getId()));
            $row->addColumn(new Column('date', dateTimeFr($subscription->getDate()->format('Y-m-d H:i:s')), '', $subscription->getDate()->getTimestamp()));
            $row->addColumn(new Column('name', $subscription->getName()));

            $actions = [];
            if ($subscription->getClient()) {
                $actions[] = new ColumnActionView(Tools::makeLink('admin', 'client', $subscription->getClient()->getId()));
            }
            $row->addColumn((new Column('actions'))->setActions($actions));
            $this->table->addRow($row);
        }
    }
}
