<?php
namespace Mat<PERSON><PERSON>ver\Helpers\View\Table\Surcharge;

use <PERSON><PERSON><PERSON><PERSON>\Entity\Surcharge\SurchargeComment;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Table\Column;
use MatGyver\Helpers\View\Table\ColumnAction;
use MatGyver\Helpers\View\Table\ColumnActionDelete;
use MatGyver\Helpers\View\Table\ColumnActionEdit;
use MatGyver\Helpers\View\Table\Row;
use MatGyver\Helpers\View\Table\Table;
use MatGyver\Helpers\View\Table\ViewTableHelper;

class SurchargeCommentViewHelper extends ViewTableHelper
{
    public function __construct()
    {
        $this->table = new Table('table_surcharges_comments');
        $this->table->addTableColumn(new Column('comment', __('Commentaire')));
        $this->table->addTableColumn(new Column('actions', __('Actions')));
        $this->table->setTableSort(0, 'asc');
    }

    /**
     * @param SurchargeComment[] $surchargesComments
     */
    public function getContent(array $surchargesComments)
    {
        $this->setContentRows($surchargesComments);
        return $this->table->getContent();
    }

    /**
     * @param SurchargeComment[] $surchargesComments
     */
    public function setContentRows(array $surchargesComments)
    {
        foreach ($surchargesComments as $surchargeComment) {
            $row = new Row();
            $row->addColumn(new Column('comment', nl2br($surchargeComment->getComment())));
            $row->addColumn((new Column('actions'))->setActions($this->getActions($surchargeComment)));
            $this->table->addRow($row);
        }
    }

    /**
     * @param SurchargeComment $surchargeComment
     * @return ColumnAction[]
     */
    public function getActions(SurchargeComment $surchargeComment): array
    {
        return [
            new ColumnActionEdit(Tools::makeLink('app', 'surcharge_comment', 'edit/' . $surchargeComment->getId())),
            new ColumnActionDelete('surcharge_comment', $surchargeComment->getId(), __('Êtes-vous sûr de vouloir supprimer ce commentaire ?'), '', Tools::makeLink('app', 'surcharge_comment', 'delete/' . $surchargeComment->getId())),
        ];
    }
}
