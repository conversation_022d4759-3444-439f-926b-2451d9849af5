<?php
namespace Mat<PERSON><PERSON>ver\Helpers\View\Table\Breakdown;

use Mat<PERSON><PERSON><PERSON>\Entity\Breakdown\Breakdown;
use Mat<PERSON><PERSON>ver\Helpers\Tools;
use MatGyver\Helpers\View\Table\Column;
use MatGyver\Helpers\View\Table\ColumnAction;
use MatG<PERSON>ver\Helpers\View\Table\ColumnActionDelete;
use MatGyver\Helpers\View\Table\ColumnActionEdit;
use MatGyver\Helpers\View\Table\Row;
use MatGyver\Helpers\View\Table\Table;
use MatGyver\Helpers\View\Table\ViewTableHelper;

class BreakdownViewHelper extends ViewTableHelper
{
    public function __construct()
    {
        $this->table = new Table('table_breakdown');
        $this->table->addTableColumn(new Column('name', __('Nom')));
        $this->table->addTableColumn(new Column('actions', __('Actions')));
        $this->table->setTableSort(0, 'asc');
    }

    /**
     * @param Breakdown[] $breakdowns
     */
    public function getContent(array $breakdowns)
    {
        $this->setContentRows($breakdowns);
        return $this->table->getContent();
    }

    /**
     * @param Breakdown[] $breakdowns
     */
    public function setContentRows(array $breakdowns)
    {
        foreach ($breakdowns as $breakdown) {
            $row = new Row();

            $row->addColumn(new Column('name', $breakdown->getName()));
            $row->addColumn((new Column('actions'))->setActions($this->getActions($breakdown)));
            $this->table->addRow($row);
        }
    }

    /**
     * @param Breakdown $breakdown
     * @return ColumnAction[]
     */
    public function getActions(Breakdown $breakdown): array
    {
        return [
            new ColumnActionEdit(Tools::makeLink('app', 'breakdown', 'edit/' . $breakdown->getId())),
            new ColumnActionDelete('breakdown', $breakdown->getId(), __('Etes-vous sûr de vouloir supprimer cette panne ?'), '', Tools::makeLink('app', 'breakdown', 'delete/' . $breakdown->getId())),
        ];
    }
}
