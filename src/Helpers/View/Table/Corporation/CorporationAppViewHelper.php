<?php
namespace MatGyver\Helpers\View\Table\Corporation;

use MatGyver\Entity\Corporation\Corporation;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Table\Column;
use MatGyver\Helpers\View\Table\ColumnAction;
use MatGyver\Helpers\View\Table\Row;
use MatGyver\Helpers\View\Table\Table;
use MatGyver\Helpers\View\Table\ViewTableHelper;

class CorporationAppViewHelper extends ViewTableHelper
{
    public function __construct()
    {
        $this->table = new Table('table_corporations');
        $this->table->addTableColumn(new Column('name', __('Nom')));
        $this->table->addTableColumn(new Column('address', __('Adresse')));
        $this->table->addTableColumn(new Column('phone', __('Téléphone')));
        $this->table->addTableColumn(new Column('website', __('Site internet')));
        $this->table->addTableColumn(new Column('actions', __('Actions')));
    }

    /**
     * @param Corporation[] $corporations
     */
    public function getContent(array $corporations = [])
    {
        if ($corporations) {
            $this->setContentRows($corporations);
        } else {
            $this->table->setDataTable(false);
        }
        return $this->table->getContent();
    }

    /**
     * @param Corporation[] $corporations
     */
    public function setContentRows(array $corporations)
    {
        foreach ($corporations as $corporation) {
            $row = new Row();

            $hiddenContent = '<div id="corporation_' . $corporation->getId() . '" ';
            $data = [
                'name' => $corporation->getName(),
                'address' => $corporation->getAddress(),
                'address2' => $corporation->getAddress2(),
                'zip' => $corporation->getZip(),
                'city' => $corporation->getCity(),
            ];
            foreach ($data as $key => $value) {
                $hiddenContent .= 'data-' . $key . '="' . $value . '" ';
            }
            $hiddenContent .= '></div>';

            $row->addColumn(new Column('name', '<span class="text-primary font-weight-bolder">' . $corporation->getName() . '</span>' . $hiddenContent));
            $row->addColumn(new Column('address', Tools::renderCorporationAddress($corporation)));
            $row->addColumn(new Column('phone', $corporation->getPhone()));
            $row->addColumn(new Column('website', $corporation->getWebsite() ? '<a href="' . $corporation->getWebsite() . '" target="_blank">' . $corporation->getWebsite() . '</a>' : ''));
            $row->addColumn((new Column('actions'))->setActions($this->getActions($corporation)));
            $this->table->addRow($row);
        }
    }

    /**
     * @param Corporation $corporation
     * @return ColumnAction[]
     */
    public function getActions(Corporation $corporation): array
    {
        $action = new ColumnAction();
        $action->setOnClick('loadCorporation(' . $corporation->getId() . ')');
        $action->setIcon('fas fa-check');
        $action->setClass('btn-light');
        return [$action];
    }
}
