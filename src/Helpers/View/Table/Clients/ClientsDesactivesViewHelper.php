<?php

namespace MatGyver\Helpers\View\Table\Clients;

use Mat<PERSON><PERSON>ver\Entity\Client\Client;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Table\Column;
use MatGyver\Helpers\View\Table\ColumnActionView;
use MatGyver\Helpers\View\Table\Table;
use MatGyver\Helpers\View\Table\ViewTableHelper;
use MatGyver\Helpers\View\Table\Row;
use MatGyver\Services\Affiliation\AffiliationClicsService;
use MatGyver\Services\Affiliation\AffiliationCommissionService;
use MatGyver\Services\Affiliation\AffiliationSubPartnersService;
use MatGyver\Services\Users\UsersLogsService;

class ClientsDesactivesViewHelper extends ViewTableHelper
{

    public function __construct()
    {
        $this->table = new Table('table_clients_verif');
        $this->table->addTableColumn(new Column('checkbox', ''));
        $this->table->addTableColumn(new Column('id', 'ID'));
        $this->table->addTableColumn(new Column('name', __('Nom')));
        $this->table->addTableColumn(new Column('date', __('Fin d\'abonnement')));
        $this->table->addTableColumn(new Column('affiliation', __('Affiliation')));
        $this->table->addTableColumn(new Column('last_login', __('Dernière connexion')));
        $this->table->addTableColumn(new Column('actions', __('Actions')));

        $this->table->setDataTable(false);
        $this->table->setPreContent('<form method="post" action="/' . ADMIN_URL . '/clients/delete/" id="FormUsers">');
        $this->table->setPostContent('
	        <input type="hidden" name="redirect" value="' . Tools::makeLink('admin', 'clients', 'desactives') . '">
	        <button type="submit" name="submit" class="btn btn-danger">' . __('Supprimer les clients sélectionnés') . '</button>
            [[CSRF]]
        </form>');
    }

    /**
     * @param Client[]|null $clients
     * @return string
     */
    public function getContent(?array $clients = null): string
    {
        $this->setClientsRows($clients);
        return $this->table->getContent();
    }

    /**
     * @param Client[] $clients
     */
    public function setClientsRows(array $clients): void
    {
        $dateNow = new \DateTime();

        foreach ($clients as $client) {
            if ($client->getId() == CLIENT_MASTER) {
                continue;
            }
            if ($client->getSubscription() == 'affiliation') {
                continue;
            }

            $row = new Row();
            $row->addColumn(new Column('checkbox', '<input type="checkbox" name="clients[]" value="' . $client->getId() . '">'));
            $row->addColumn(new Column('id', $client->getId()));
            $row->addColumn(new Column('name', $client->getName()));

            if ($dateNow > $client->getDateEndSubscription()) {
                $row->addColumn(new Column('date', '<span class="label label-danger label-inline">' . dateFr($client->getDateEndSubscription()->format('Y-m-d H:i:s')) . '</span>'));
            } else {
                $row->addColumn(new Column('date', dateFr($client->getDateEndSubscription()->format('Y-m-d H:i:s'))));
            }

            //affiliation
            $affiliation = false;
            $balance = $client->getAffiliationPartner() ? $this->container->get(AffiliationCommissionService::class)->getAvailableCommissionsAmountByPartner($client->getAffiliationPartner()->getId()) : 0;
            if ($balance and $balance > 0) {
                $affiliation = true;
            }

            if (!$affiliation) {
                $nbSubPartners = $this->container->get(AffiliationSubPartnersService::class)->getCountSubPartners($client->getId());
                if ($nbSubPartners) {
                    $affiliation = true;
                }
            }

            if (!$affiliation) {
                $nbClicks = $client->getAffiliationPartner() ? $this->container->get(AffiliationClicsService::class)->getRepository()->getCountClicks(null, $client->getAffiliationPartner()->getId()) : 0;
                if ($nbClicks) {
                    $affiliation = true;
                }
            }

            if ($affiliation) {
                $row->addColumn(new Column('affiliation', '<span class="label label-warning label-inline">' . __('Oui') . '</span>'));
            } else {
                $row->addColumn(new Column('affiliation', '<span class="label label-danger label-inline">' . __('Non') . '</span>'));
            }


            //last login
            $column = new Column('last_login');
            $admin = $client->getMainAdmin();
            if ($admin) {
                $lastLogin = $this->container->get(UsersLogsService::class)->getLastLoginByEmail($admin->getEmail(), $client->getId());
                if ($lastLogin) {
                    if ($lastLogin->getDate() > $client->getDateEndSubscription()) {
                        $column->setValue('<span class="label label-danger label-inline">' . dateTimeFr($lastLogin->getDate()->format('Y-m-d H:i:s')) . '</span>');
                    } else {
                        $column->setValue(dateTimeFr($lastLogin->getDate()->format('Y-m-d H:i:s')));
                    }
                }
            }
            $row->addColumn($column);


            //actions
            $actions = [new ColumnActionView(Tools::makeLink('admin', 'client/' . $client->getId()))];
            $row->addColumn((new Column('actions'))->setActions($actions));

            $this->table->addRow($row);
        }
    }
}
