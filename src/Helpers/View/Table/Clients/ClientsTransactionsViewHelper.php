<?php

namespace Mat<PERSON><PERSON>ver\Helpers\View\Table\Clients;

use Mat<PERSON><PERSON><PERSON>\Entity\Client\Client;
use Mat<PERSON><PERSON>ver\Helpers\Number;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Table\Column;
use MatGyver\Helpers\View\Table\ColumnActionView;
use MatGyver\Helpers\View\Table\Table;
use MatGyver\Helpers\View\Table\ViewTableHelper;
use MatGyver\Helpers\View\Table\Row;
use MatGyver\Services\Clients\ClientsTransactionsService;

class ClientsTransactionsViewHelper extends ViewTableHelper
{
    public function __construct()
    {
        $this->table = new Table('table_commandes');
        $this->table->addTableColumn(new Column('id', 'ID'));
        $this->table->addTableColumn(new Column('client', __('Client')));
        $this->table->addTableColumn(new Column('name', __('Propriétaire')));
        $this->table->addTableColumn(new Column('subscription', __('Abonnement')));
        $this->table->addTableColumn(new Column('status', __('Etat')));
        $this->table->addTableColumn(new Column('total_amount', __('Total payé')));
        $this->table->addTableColumn(new Column('action', __('Action')));
    }

    /**
     * @param Client[] $clients
     * @return string
     */
    public function getContent(array $clients): string
    {
        $this->setClientsRows($clients);
        return $this->table->getContent();
    }

    /**
     * @param Client[] $clients
     * @return void
     */
    private function setClientsRows(array $clients): void
    {
        $clientsTransactions = $this->container->get(ClientsTransactionsService::class)->getRepository()->getAllClientTransactionValid();

        $clientsAmounts = array();
        foreach ($clientsTransactions as $clientTransaction) {
            if (!$clientTransaction->getClient() or !$clientTransaction->getTransaction()) {
                continue;
            }

            if (!isset($clientsAmounts[$clientTransaction->getClient()->getId()])) {
                $clientsAmounts[$clientTransaction->getClient()->getId()] = 0;
            }
            $clientsAmounts[$clientTransaction->getClient()->getId()] += $clientTransaction->getTransaction()->getAmountTaxExcl();
        }

        foreach ($clients as $client) {
            if (in_array($client->getId(), FREE_CLIENTS)) {
                continue;
            }
            if ('affiliation' == $client->getSubscription()) {
                continue;
            }

            $idClient = $client->getId();
            $total = ($clientsAmounts[$idClient] ?? 0);

            $row = new Row();
            $row->addColumn(new Column('id', $client->getId()));
            $row->addColumn(new Column('client', '<a href="' . Tools::makeLink('admin', 'client', $client->getId()) . '" target="_blank">' . $client->getName() . '</a>'));

            $column = new Column('name');
            $admin = $client->getMainAdmin();
            if ($admin) {
                $column->setValue('<strong>' . $admin->getFirstName() . ' ' . $admin->getLastName() . '</strong><br>' . $admin->getEmail());
            }
            $row->addColumn($column);

            $row->addColumn(new Column('subscription', $client->getSubscription()));
            $row->addColumn(new Column('status', ($client->getActive() ? '<span class="label label-lg font-weight-bold label-light-success label-inline">' . __('Actif') . '</span>' : '<span class="label label-lg font-weight-bold label-light-danger label-inline">' . __('Désactivé') . '</span>')));
            $row->addColumn(new Column('total_amount', Number::formatAmount(round($total), DEFAULT_CURRENCY)));

            $actions = [new ColumnActionView(Tools::makeLink('admin', 'client', $client->getId()))];
            $row->addColumn((new Column('action'))->setActions($actions));

            $this->table->addRow($row);
        }
    }
}
