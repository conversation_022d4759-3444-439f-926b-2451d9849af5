<?php

namespace MatGyver\Helpers\View\Table;

use MatGyver\Services\TwigService;

class Table
{
    /**
     * @var string
     */
    public $id;

    /**
     * @var Column[]
     */
    public $tableColumns = [];

    /**
     * @var Row[]
     */
    public $rows = [];

    /**
     * @var bool
     */
    public $dataTable = true;

    /**
     * @var string
     */
    public $class = '';

    /**
     * @var string
     */
    public $preContent = '';

    /**
     * @var string
     */
    public $postContent = '';

    /**
     * @var array
     */
    public $tableData = [];

    /**
     * @param string $id
     */
    public function __construct(string $id = '')
    {
        $this->id = $id;
    }

    /**
     * @return string
     */
    public function getId(): string
    {
        return $this->id;
    }

    /**
     * @param string $id
     */
    public function setId(string $id): void
    {
        $this->id = $id;
    }

    /**
     * @return Column[]
     */
    public function getTableColumns(): array
    {
        return $this->tableColumns;
    }

    /**
     * @param Column[] $tableColumns
     */
    public function setTableColumns(array $tableColumns): void
    {
        $this->tableColumns = $tableColumns;
    }

    /**
     * @param Column $tableColumn
     */
    public function addTableColumn(Column $tableColumn): void
    {
        $this->tableColumns[$tableColumn->getId()] = $tableColumn;
    }

    /**
     * @param string $tableColumnId)
     */
    public function removeTableColumn(string $tableColumnId): void
    {
        if (isset($this->tableColumns[$tableColumnId])) {
            unset($this->tableColumns[$tableColumnId]);
        }
    }

    /**
     * @return Row[]
     */
    public function getRows(): array
    {
        return $this->rows;
    }

    /**
     * @param Row[] $rows
     */
    public function setRows(array $rows): void
    {
        $this->rows = $rows;
    }

    /**
     * @param Row $row
     */
    public function addRow(Row $row): void
    {
        $this->rows[] = $row;
    }

    /**
     * @return bool
     */
    public function isDataTable(): bool
    {
        return $this->dataTable;
    }

    /**
     * @param bool $dataTable
     */
    public function setDataTable(bool $dataTable): void
    {
        $this->dataTable = $dataTable;
    }

    /**
     * @return string
     */
    public function getClass(): string
    {
        return $this->class;
    }

    /**
     * @param string $class
     */
    public function setClass(string $class): void
    {
        $this->class = $class;
    }

    /**
     * @return string
     */
    public function getPreContent(): string
    {
        return $this->preContent;
    }

    /**
     * @param string $preContent
     */
    public function setPreContent(string $preContent): void
    {
        $this->preContent = $preContent;
    }

    /**
     * @return string
     */
    public function getPostContent(): string
    {
        return $this->postContent;
    }

    /**
     * @param string $postContent
     */
    public function setPostContent(string $postContent): void
    {
        $this->postContent = $postContent;
    }

    /**
     * @return array
     */
    public function getTableData(): array
    {
        return $this->tableData;
    }

    /**
     * @param string $dataName
     * @param string $dataValue
     */
    public function setTableData(string $dataName, string $dataValue): void
    {
        $this->tableData[$dataName] = $dataValue;
    }

    /**
     * @param int $columnId
     * @param string $order
     */
    public function setTableSort(int $columnId, string $order = 'desc'): void
    {
        $this->tableData['sort-column'] = $columnId;
        $this->tableData['sort-order'] = $order;
    }

    /**
     * @return string
     */
    public function getContent(): string
    {
        return TwigService::getInstance()->set('rows', $this->getRows())
            ->set('tableColumns', $this->getTableColumns())
            ->set('dataTable', $this->isDataTable())
            ->set('tableId', $this->getId())
            ->set('class', $this->getClass())
            ->set('preContent', $this->getPreContent())
            ->set('postContent', $this->getPostContent())
            ->set('tableData', $this->renderTableData())
            ->render('common/display/table.php');
    }

    /**
     * @return string
     */
    public function renderTableData(): string
    {
        $content = '';
        foreach ($this->getTableData() as $tableDataName => $tableDataValue) {
            $content .= 'data-' . $tableDataName . '="' . $tableDataValue . '" ';
        }
        return $content;
    }

    /**
     * @param bool $withColumnId
     * @return array
     */
    public function getRowsAsArray(bool $withColumnId = true): array
    {
        $tableColumns = $this->getTableColumns();
        $data = [];
        foreach ($this->getRows() as $row) {
            $dataRow = [];
            foreach ($tableColumns as $tableColumn) {
                $rowColumn = $row->getColumn($tableColumn->getId());
                if (!$rowColumn) {
                    if ($withColumnId) {
                        $dataRow[$tableColumn->getId()] = '';
                    } else {
                        $dataRow[] = '';
                    }
                    continue;
                }

                $value = $rowColumn->getValue();
                if ($rowColumn->getActions()) {
                    $value = '';
                    foreach ($rowColumn->getActions() as $action) {
                        $value .= $action->render();
                    }
                }

                if ($withColumnId) {
                    $dataRow[$tableColumn->getId()] = $value;
                } else {
                    $dataRow[] = $value;
                }
            }
            $data[] = $dataRow;
        }

        return $data;
    }
}
