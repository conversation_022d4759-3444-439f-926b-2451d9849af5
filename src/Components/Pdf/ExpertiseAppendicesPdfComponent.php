<?php
namespace Mat<PERSON><PERSON>ver\Components\Pdf;

use Mat<PERSON><PERSON><PERSON>\Entity\Dossier\Dossier;
use <PERSON><PERSON><PERSON><PERSON>\Entity\Dossier\DossierDocument;
use MatGyver\Entity\Dossier\Expertise\DossierExpertise;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Dossier\DossierDocumentService;
use MatGyver\Services\TwigService;

class ExpertiseAppendicesPdfComponent extends AbstractPdfComponent
{
    /**
     * @param DossierExpertise $expertise
     * @throws \Exception
     */
    public function __construct(DossierExpertise $expertise)
    {
        $this->setDossier($expertise->getDossier());
        $this->setExpertise($expertise);
        $this->setClientInfos();
        $this->setType(DossierDocument::TYPE_EXPERTISE_APPENDICES);
        parent::__construct();
    }

    /**
     * @return string
     * @throws \Exception
     */
    public function render(): string
    {
        $dossier = $this->getDossier();
        if (!$dossier) {
            throw new \Exception(__('Dossier not specified.'));
        }

        $expertise = $this->getExpertise();
        if (!$expertise) {
            throw new \Exception(__('Expertise not specified.'));
        }

        $report = $this->getExpertise()->getExpertiseReport();

        $contact = $dossier->getContact();
        $this->setTitle(__('Annexes'));
        if ($expertise->getDossier()->isJudiciaire()) {
            $this->setTitle(__('Les Annexes du Rapport d\'Expertise Judiciaire'));
        }

        $container = ContainerBuilderService::getInstance();
        $document = $container->get(DossierDocumentService::class)->getRepository()->findOneBy(['expertise' => $expertise, 'type' => DossierDocument::TYPE_EXPERTISE_APPENDICES]);
        if ($document and $document->getFirstPart()) {
            $data = json_decode($document->getFirstPart(), true);
            if (isset($data['title']) and $data['title']) {
                $this->setTitle($data['title']);
            }
        }

        $subTitle = $contact->getFirstName() . ' ' . $contact->getLastName();
        if ($contact->getCompany()) {
            $subTitle = $contact->getCompany();
        }
        $description = $this->getHeaderDescription($dossier);

        if ($expertise->getDossier()->isJudiciaire()) {
            $this->addFirstPageJudiciaire();
        } else {
            $this->addFirstPage($subTitle, $description, '', true);
        }

        $documents = $report->getAppendices();
        if (!$documents) {
            return $this->getContent();
        }

        $this->addTableOfContents($documents);
        $this->addDocuments($documents, null, false, '', false, true);

        return $this->getContent();
    }

    /**
     * @param Dossier $dossier
     * @return string
     */
    private function getHeaderDescription(Dossier $dossier): string
    {
        $dossierMandate = $dossier->getMandate();
        if (!$dossierMandate) {
            return '';
        }

        $description = '<div class="mt-20">';
        $description .= '<p class="text-center"><strong>Mandant : </strong>';
        if ($dossierMandate->getCompany()) {
            $description .= $dossierMandate->getCompany() . '<br>';
        }
        $description .= $dossierMandate->getFirstName() . ' ' . $dossierMandate->getLastName() . '<br>';
        $description .= 'Adresse : ' . $dossierMandate->getAddress() . '<br>';
        $description .= ($dossierMandate->getAddress2() ? '<br>' . $dossierMandate->getAddress2() . '<br>' : '');
        $description .= $dossierMandate->getZip() . ' ' . $dossierMandate->getCity() . '<br>';
        $description .= ($dossierMandate->getEmail() ? 'Email : ' . $dossierMandate->getEmail() . '<br>' : '');
        $description .= ($dossierMandate->getTelephone() ? 'Téléphone : ' . $dossierMandate->getTelephone() . '<br>' : '');
        $description .= '</p>';
        $description .= '</div>';

        return $description;
    }

    private function addTableOfContents(array $selectedDocuments): void
    {
        $this->addSectionTitle(__('Liste des annexes'));

        $documentsNames = [];
        $expertise = $this->getExpertise();
        $dossier = $expertise->getDossier();

        $documents = $expertise->getDossier()->getDocuments();
        foreach ($documents as $document) {
            if ($selectedDocuments !== null and !in_array($document->getId(), $selectedDocuments)) {
                continue;
            }
            if ($document->getVirusScan() == DossierDocument::VIRUS_SCAN_ALERT) {
                continue;
            }

            $fullPath = WEB_PATH . '/medias/' . $dossier->getFolder();
            $documentName = ($document->getName() ?: DossierDocumentService::getType($document->getType()));
            if (!file_exists($fullPath . $document->getFile())) {
                continue;
            }
            $documentsNames[] = $documentName;
        }

        $content = TwigService::getInstance()->set('dossier', $this->getDossier())
            ->set('expertise', $this->getExpertise())
            ->set('documentsNames', $documentsNames)
            ->render('app/dossier/pdf/appendices_table_of_contents.php');
        $this->addContent($content);
    }
}
