<?php
namespace <PERSON><PERSON><PERSON>ver\Components\Pdf;

use Mat<PERSON><PERSON><PERSON>\Entity\Dossier\DossierDocument;
use MatGyver\Entity\Dossier\DossierVehicle;
use MatGyver\Entity\Dossier\Expertise\DossierExpertise;
use MatG<PERSON>ver\Entity\Dossier\Expertise\DossierExpertiseSample;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\ImagickHelper;
use MatGyver\Services\Dossier\DossierVehicleService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseSampleService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseVehicleService;

class SamplePdfComponent extends AbstractPdfComponent
{
    private DossierExpertiseSample $sample;

    /**
     * @param DossierExpertise $expertise
     * @throws \Exception
     */
    public function __construct(DossierExpertise $expertise, DossierExpertiseSample $sample)
    {
        $this->setDossier($expertise->getDossier());
        $this->setExpertise($expertise);
        $this->setSample($sample);

        $this->setClientInfos();

        parent::__construct();
    }

    public function getSample(): DossierExpertiseSample
    {
        return $this->sample;
    }

    public function setSample(DossierExpertiseSample $sample): void
    {
        $this->sample = $sample;
    }

    /**
     * @return string
     * @throws \Exception
     */
    public function render(): string
    {
        $this->addSampleInfos();

        return $this->getContent();
    }

    private function addSampleInfos(): void
    {
        $clientInfos = $this->getClientInfos();
        $sample = $this->getSample();

        $title = __('Prélèvement du %s', $sample->getDate()->format('d/m/Y'));
        $subject = '<h4 class="font-weight-bolder mb-8" style="color: ' . $clientInfos['colors']['h4_color'] . '"><u>' . $this->formatText($title) . '</u></h4>';

        $columns = ['', ''];
        $sampleData = [];
        if ($sample->getAnalysisType()) {
            if ($sample->getAnalysisType() === DossierExpertiseSample::ANALYSIS_OTHER) {
                $sampleData[] = ['<strong>' . __('Type d\'analyse') . '</strong>', $sample->getAnalysisTypeOther()];
            } else {
                $sampleData[] = ['<strong>' . __('Type d\'analyse') . '</strong>', DossierExpertiseSampleService::getAnalysisType($sample->getAnalysisType())];
            }
        }
        if ($sample->getAnalysisType() == DossierExpertiseSample::ANALYSIS_BURNED_VEHICLE and $sample->getFire()) {
            $sampleData[] = ['<strong>' . __('Incendie') . '</strong>', DossierExpertiseSampleService::getFire($sample->getFire())];
        }
        if ($sample->getType()) {
            $sampleData[] = ['<strong>' . __('Type') . '</strong>', $sample->getType()];
        }
        if ($sample->getReference()) {
            $sampleData[] = ['<strong>' . __('Référence') . '</strong>', $sample->getReference()];
        }
        if ($sample->getFluid()) {
            $sampleData[] = ['<strong>' . __('Fluide') . '</strong>', DossierExpertiseSampleService::getFluid($sample->getFluid())];
        }
        if ($sample->getFluid() == DossierExpertiseSample::FLUID_FUEL and $sample->getFuel()) {
            $sampleData[] = ['<strong>' . __('Combustible') . '</strong>', DossierExpertiseSampleService::getFuel($sample->getFuel())];
        }
        if ($sample->getFluidBrand()) {
            $sampleData[] = ['<strong>' . __('Marque du fluide') . '</strong>', $sample->getFluidBrand()];
        }
        if ($sample->getCommercialType()) {
            $sampleData[] = ['<strong>' . __('Type commercial') . '</strong>', $sample->getCommercialType()];
        }
        if ($sample->getSaeGrade()) {
            $sampleData[] = ['<strong>' . __('Grade SAE') . '</strong>', $sample->getSaeGrade()];
        }
        if ($sample->getMileage()) {
            if ($sample->getMileageType() == DossierDocument::MILEAGE_TYPE_METERS) {
                $sampleData[] = ['<strong>' . __('Kms depuis la vidange') . '</strong>', $sample->getMileage() . ' km'];
            } elseif ($sample->getMileageType() == DossierDocument::MILEAGE_TYPE_MILES) {
                $sampleData[] = ['<strong>' . __('Miles depuis la vidange') . '</strong>', $sample->getMileage() . ' miles'];
            } else {
                $sampleData[] = ['<strong>' . __('Heures depuis la vidange') . '</strong>', $sample->getMileage() . ' heures'];
            }
        }
        if ($sample->getSamplingPoint()) {
            $sampleData[] = ['<strong>' . __('Organe de prélèvement') . '</strong>', DossierExpertiseSampleService::getSamplingPoint($sample->getSamplingPoint())];
        }
        if ($sample->getLocation()) {
            $sampleData[] = ['<strong>' . __('Lieu de prélèvement') . '</strong>', DossierExpertiseSampleService::getSamplingLocation($sample->getLocation())];
        }
        if ($sample->getTemperature()) {
            $sampleData[] = ['<strong>' . __('Température de prélèvement') . '</strong>', DossierExpertiseSampleService::getSamplingTemperature($sample->getTemperature())];
        }
        if ($sample->getObservations()) {
            $sampleData[] = ['<strong>' . __('Observations de l\'expert pour le laboratoire') . '</strong>', $sample->getObservations()];
        }
        if ($sample->getResult()) {
            $sampleData[] = ['<strong>' . __('Résultats de l\'analyse du prélèvement') . '</strong>', $sample->getResult()];
        }

        $vehicleData = [];
        $vehicle = $this->getDossier()->getVehicle();
        if ($vehicle->getBrand()) {
            $vehicleData[] = ['<strong>Marque</strong>', $vehicle->getBrand()];
        }
        if ($vehicle->getModel()) {
            $vehicleData[] = ['<strong>Modèle</strong>', $vehicle->getModel()];
        }
        if ($vehicle->getColor()) {
            $vehicleData[] = ['<strong>Couleur</strong>', DossierVehicleService::getColor($vehicle->getColor())];
        }
        if ($vehicle->getRegistration()) {
            $vehicleData[] = ['<strong>Immatriculation</strong>', $vehicle->getRegistration()];
        }
        if ($vehicle->getSerialNumber()) {
            $vehicleData[] = ['<strong>N° Série</strong>', $vehicle->getSerialNumber()];
        }
        if ($vehicle->getDateFirstCirculation()) {
            $vehicleData[] = ['<strong>Mise en circulation</strong>', $vehicle->getDateFirstCirculation()->format('d/m/Y')];
        }
        if ($this->getExpertise()->getExpertiseVehicle() and $this->getExpertise()->getExpertiseVehicle()->getMeter()) {
            $vehicleData[] = ['<strong>Kilométrage</strong>', $this->getExpertise()->getExpertiseVehicle()->getMeter() . ' ' . DossierExpertiseVehicleService::getMeterType($this->getExpertise()->getExpertiseVehicle()->getMeterType()) . ' ' . DossierExpertiseVehicleService::getMeterRead($this->getExpertise()->getExpertiseVehicle()->getMeterRead())];
        }
        if ($vehicle->getEngine()) {
            $vehicleData[] = ['<strong>Énergie</strong>', DossierVehicleService::getEngine($vehicle->getEngine())];
        }
        if ($vehicle->getEngineCode()) {
            $vehicleData[] = ['<strong>Code Moteur</strong>', $vehicle->getEngineCode()];
        }
        if ($vehicle->getPower()) {
            $vehicleData[] = ['<strong>Puissance</strong>', $vehicle->getPower()];
        }
        if ($vehicle->getGearbox()) {
            $vehicleData[] = ['<strong>Boîte de vitesse</strong>', DossierVehicleService::getGearbox($vehicle->getGearbox())];
        }
        if ($vehicle->getUseType()) {
            $usage = '';
            $usesTypes = explode(',', $vehicle->getUseType());
            foreach ($usesTypes as $useType) {
                $usage .= DossierVehicleService::getUseType($useType) . ', ';
            }
            $vehicleData[] = ['<strong>Utilisation</strong>', rtrim($usage, ', ')];
        }
        if ($vehicle->getUsePurpose() != DossierVehicle::USE_PURPOSE_NOT_SET and ($vehicle->getUsePurpose() != DossierVehicle::USE_PURPOSE_OTHER or $vehicle->getUsePurposeOther())) {
            $usePurpose = DossierVehicleService::getUsePurpose($vehicle->getUsePurpose());
            if ($vehicle->getUsePurpose() == DossierVehicle::USE_PURPOSE_OTHER and $vehicle->getUsePurposeOther()) {
                $usePurpose .= ' (' . $vehicle->getUsePurposeOther() . ')';
            }
            $vehicleData[] = ['<strong>Type d\'utilisation</strong>', $usePurpose];
        }

        $expertiseVehicle = $this->expertise->getExpertiseVehicle();
        if ($expertiseVehicle) {
            if ($expertiseVehicle->getCrankcaseCapacity()) {
                $vehicleData[] = ['<strong>Capacité théorique du carter</strong>', $expertiseVehicle->getCrankcaseCapacity() . ' L'];
            }
            $engineOil = $expertiseVehicle->getEngineOil();
            if (isset($engineOil['crankcase_qty']) and $engineOil['crankcase_qty']) {
                $vehicleData[] = ['<strong>Quantité d\'huile présente dans le carter</strong>', $engineOil['crankcase_qty'] . ' L'];
            }
        }

        $this->addSectionTitle($this->formatText($title));
        $this->addSectionTitleLight($this->formatText(__('Informations du véhicule')));
        $this->addTable($columns, $vehicleData);
        $this->addSectionTitleLight($this->formatText(__('Prélèvement')));
        $this->addTable($columns, $sampleData);

        if ($sample->getPicture()) {
            $size = $this->getImageSize(WEB_PATH . '/medias/' . $sample->getDossier()->getFolder() . $sample->getPicture(), null, 800);

            $content = '<div class="mt-4 mb-4 text-center">';
            $content .= '<img class="img-fluid" src="' . $sample->getPictureUrl() . '" ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : '') . '>';
            $content .= '</div>';
            $this->addNewPage();
            $this->addSectionContent($content, 20);
        }

        if ($sample->getFile()) {
            $fullPath = WEB_PATH . '/medias/' . $sample->getDossier()->getFolder();
            $extension = getExtension($sample->getFile());
            if (file_exists($fullPath . $sample->getFile())) {
                $content = '<div class="mt-4 mb-4 text-center">';
                $content .= '<p class="text-center mb-6"><em>' . __('Photographie') . '</em></p>';

                if ($extension == 'pdf') {
                    $images = ImagickHelper::convertPdf($fullPath . $sample->getFile(), false);
                    if ($images) {
                        foreach ($images as $image) {
                            $size = $this->getImageSize(WEB_PATH . '/medias/' . $image, null, 800);
                            $content .= '<img class="img-fluid" src="' . Assets::getMediaUrl($image) . '" ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : '') . '>';
                        }
                    }
                } else {
                    $size = $this->getImageSize(WEB_PATH . '/medias/' . $sample->getDossier()->getFolder() . $sample->getFile(), null, 800);
                    $content .= '<img class="img-fluid" src="' . $sample->getFileUrl() . '" ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : '') . '>';
                }

                $content .= '</div>';
                $this->addSectionContent($content, 20);
            }
        }

        //set content
        $content = $this->getContent();
        $this->setContent('');

        $city = $this->getExpertise()->getCity();
        $date = $sample->getDate()->format('d/m/Y');

        $this->renderOnePageLayout($content, '', false, $city, $date);
    }
}
