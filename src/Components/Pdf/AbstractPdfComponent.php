<?php
namespace MatGyver\Components\Pdf;

use MatGyver\Entity\Dossier\Dossier;
use MatGyver\Entity\Dossier\DossierDocument;
use MatGyver\Entity\Dossier\DossierFact;
use MatGyver\Entity\Dossier\DossierInstitution;
use MatGyver\Entity\Dossier\DossierSignature;
use MatGyver\Entity\Dossier\DossierVehicle;
use MatGyver\Entity\Dossier\Expertise\DossierExpertise;
use MatGyver\Entity\Dossier\Expertise\DossierExpertiseObservations;
use MatGyver\Entity\Dossier\Expertise\DossierExpertisePerson;
use MatGyver\Entity\Dossier\Expertise\DossierExpertisePicture;
use MatGyver\Entity\Dossier\Expertise\DossierExpertiseReport;
use MatGyver\Entity\User\User;
use MatGyver\Enums\ConfigEnum;
use MatGyver\Enums\ExpertiseEnum;
use MatGyver\Enums\VradeEnum;
use MatGyver\Factories\TableOfContents\DossierExpertiseJudiciaireTableOfContentsFactory;
use MatGyver\Formatter\DossierExpertisePersonCommentsFormatter;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\ImagickHelper;
use MatGyver\Helpers\Number;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Dossier\DossierDocumentService;
use MatGyver\Services\Dossier\DossierExpertiseService;
use MatGyver\Services\Dossier\DossierFactService;
use MatGyver\Services\Dossier\DossierVehicleService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseObservationsService;
use MatGyver\Services\Dossier\Expertise\DossierExpertisePictureService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseVehicleService;
use MatGyver\Services\Dossier\Pdf\DossierPdfService;
use MatGyver\Services\I18nService;
use MatGyver\Services\TwigService;

class AbstractPdfComponent
{
    public string $content = '';
    public ?Dossier $dossier = null;
    public ?DossierExpertise $expertise = null;
    public string $type = '';
    public ?DossierExpertisePerson $person = null;
    public string $title = '';
    public array $clientInfos = [];
    public bool $withPictures = true;
    public bool $displayLogo = true;
    public bool $displayFooter = true;

    public const MAX_WIDTH = 1000;
    public const MAX_HEIGHT = 1300;

    public function __construct()
    {
        Assets::addCss('site/pdf.css');
        Assets::removeCss('site/style.min.css');

        if (isset($_GET['remove_pictures'])) {
            $this->setWithPictures(false);
        }
    }

    /**
     * @return string
     */
    public function getContent(): string
    {
        return $this->content;
    }

    /**
     * @param string $content
     */
    public function setContent(string $content): void
    {
        $this->content = $content;
    }

    /**
     * @param string $content
     * @param int $space
     */
    public function addContent(string $content, int $space = 0): void
    {
        if ($space) {
            $content = '<div class="mb-' . $space . '">' . $content . '</div>';
        }
        $this->content .= $content;
    }

    /**
     * @return Dossier|null
     */
    public function getDossier(): ?Dossier
    {
        return $this->dossier;
    }

    /**
     * @param Dossier $dossier
     */
    public function setDossier(Dossier $dossier): void
    {
        $this->dossier = $dossier;
    }

    /**
     * @return DossierExpertise|null
     */
    public function getExpertise(): ?DossierExpertise
    {
        return $this->expertise;
    }

    /**
     * @param DossierExpertise|null $expertise
     */
    public function setExpertise(?DossierExpertise $expertise): void
    {
        $this->expertise = $expertise;
    }

    /**
     * @return string
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * @param string $type
     */
    public function setType(string $type): void
    {
        $this->type = $type;
    }

    /**
     * @return DossierExpertisePerson|null
     */
    public function getPerson(): ?DossierExpertisePerson
    {
        return $this->person;
    }

    /**
     * @param DossierExpertisePerson|null $person
     */
    public function setPerson(?DossierExpertisePerson $person): void
    {
        $this->person = $person;
    }

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return $this->title;
    }

    /**
     * @param string $title
     */
    public function setTitle(string $title): void
    {
        $this->title = $title;
    }

    /**
     * @return bool
     */
    public function isWithPictures(): bool
    {
        return $this->withPictures;
    }

    /**
     * @param bool $withPictures
     */
    public function setWithPictures(bool $withPictures): void
    {
        $this->withPictures = $withPictures;
    }

    public function getDisplayLogo(): bool
    {
        return $this->displayLogo;
    }

    public function setDisplayLogo(bool $displayLogo): void
    {
        $this->displayLogo = $displayLogo;
    }

    public function getDisplayFooter(): bool
    {
        return $this->displayFooter;
    }

    public function setDisplayFooter(bool $displayFooter): void
    {
        $this->displayFooter = $displayFooter;
    }

    /**
     * @return array
     */
    public function getClientInfos(): array
    {
        return $this->clientInfos;
    }

    public function setClientInfos(): void
    {
        $client = $this->getDossier()->getClient();
        $clientConfig = $client->getClientConfigsAsArray();
        $infos = [
            'name' => $client->getMainAdmin()->getFirstName() . ' ' . $client->getMainAdmin()->getLastName(),
            'email' => $client->getMainAdmin()->getEmail(),
            ConfigEnum::LOGO => $clientConfig[ConfigEnum::LOGO] ?? '',
            ConfigEnum::COMPANY => $clientConfig[ConfigEnum::COMPANY] ?? '',
            ConfigEnum::ADDRESS => $clientConfig[ConfigEnum::ADDRESS] ?? '',
            ConfigEnum::ADDRESS2 => $clientConfig[ConfigEnum::ADDRESS2] ?? '',
            ConfigEnum::ZIP => $clientConfig[ConfigEnum::ZIP] ?? '',
            ConfigEnum::CITY => $clientConfig[ConfigEnum::CITY] ?? '',
            ConfigEnum::TELEPHONE => $clientConfig[ConfigEnum::TELEPHONE] ?? '',
            ConfigEnum::TVA_INTRACOM => $clientConfig[ConfigEnum::TVA_INTRACOM] ?? '',
            ConfigEnum::SIGNATURE => $clientConfig[ConfigEnum::SIGNATURE] ?? '',
            'companies' => $client->getCompanies()->toArray(),
            'hasCompleteSubscription' => $client->isComplete(),
            'colors' => DossierPdfService::getColors($client->getId()),
            'freemium' => $client->getFreemium(),
        ];
        if ($this->getDossier() and $this->getDossier()->getCompany()) {
            $company = $this->getDossier()->getCompany();

            /*if (in_array($this->getType(), [DossierDocument::TYPE_CONVOCATION, DossierDocument::TYPE_SETTLEMENT_AGREEMENT, DossierDocument::TYPE_SETTLEMENT_AGREEMENT_TEMPORARY, DossierDocument::TYPE_CLAIM, DossierDocument::TYPE_CLAIM_TEMPORARY, DossierDocument::TYPE_DIRE, DossierDocument::TYPE_DIRE_TEMPORARY])) {
                $infos['companies'] = [$company];
            }*/

            $infos[ConfigEnum::COMPANY] = $company->getName();
            $infos[ConfigEnum::ADDRESS] = $company->getAddress();
            $infos[ConfigEnum::ADDRESS2] = $company->getAddress2();
            $infos[ConfigEnum::ZIP] = $company->getZip();
            if ($company->getClient()->getId() != 2 or $this->getDossier()->isJudiciaire()) {
                //always display "A Clairac" for RCPJ
                $infos[ConfigEnum::CITY] = $company->getCity();
            }
            $infos[ConfigEnum::TELEPHONE] = $company->getTelephone();
            $infos[ConfigEnum::TVA_INTRACOM] = $company->getTvaIntracom();
        }
        if ($this->getDossier() and $this->getDossier()->getUser()) {
            $user = $this->getDossier()->getUser();
            if ($user->getUserConfig(ConfigEnum::SIGNATURE)) {
                $infos[ConfigEnum::SIGNATURE] = $user->getUserConfig(ConfigEnum::SIGNATURE);
            }
        }
        $this->clientInfos = $infos;
    }

    /**
     * @return DossierInstitution|null
     */
    public function getExpert(): ?DossierInstitution
    {
        return $this->getDossier()->getExpert();
    }

    /**
     * @return string
     */
    public function getHeaderLogo(): string
    {
        if (ENV !== ENV_PROD) {
            return 'https://app.rcpj.fr/assets/images/logo.png';
        }

        $clientInfos = $this->getClientInfos();
        if ($clientInfos[ConfigEnum::LOGO]) {
            return $clientInfos[ConfigEnum::LOGO];
        }

        return '';
    }

    /**
     * @return string
     */
    public function getAppHeaderLogo(): string
    {
        //return Assets::getImageUrl('logo-white.png');

        $clientInfos = $this->getClientInfos();
        if (!$clientInfos['hasCompleteSubscription']) {
            return Assets::getImageUrl('logo-white.png');
        }
        return '';
    }

    /**
     * @param string $subTitle
     * @param string $description
     * @param string $blocInfos
     * @param bool $addDate
     * @param DossierExpertisePicture|null $firstPagePicture
     * @return void
     */
    protected function addFirstPage(string $subTitle = '', string $description = '', string $blocInfos = '', bool $addDate = false, ?DossierExpertisePicture $firstPagePicture = null, string $footer = ''): void
    {
        $headerLogo = $this->getHeaderLogo();
        $vehicle = $this->getExpertise()->getDossier()->getVehicle();
        $clientInfos = $this->getClientInfos();
        $footerCompanies = $this->getFooterCompanies();

        $content = TwigService::getInstance()->set('headerLogo', $headerLogo)
            ->set('title', $this->getTitle())
            ->set('subTitle', $subTitle)
            ->set('description', $description)
            ->set('blocInfos', $blocInfos)
            ->set('dossier', $this->getDossier())
            ->set('expertise', $this->getExpertise())
            ->set('vehicle', $vehicle)
            ->set('clientInfos', $clientInfos)
            ->set('footerCompanies', $footerCompanies)
            ->set('addDate', $addDate)
            ->set('firstPagePicture', $firstPagePicture)
            //->set('appHeaderLogo', $this->getAppHeaderLogo())
            ->set('footer', $footer)
            ->render('app/dossier/pdf/first_page.php');
        $this->addContent($content);
    }

    /**
     * @param string $subTitle
     * @param string $recipients
     * @param bool $displayDate
     * @return void
     */
    protected function addFirstPageJudiciaire(string $subTitle = '', string $recipients = '', bool $displayDate = true): void
    {
        $headerLogo = $this->getHeaderLogo();
        $vehicle = $this->getExpertise()->getDossier()->getVehicle();
        $clientInfos = $this->getClientInfos();
        $footerCompanies = $this->getFooterCompanies();
        $expert = $this->getDossier()->getExpert();

        $persons = $this->renderFirstPageFooterJudiciaire();

        $content = TwigService::getInstance()->set('headerLogo', $headerLogo)
            ->set('title', $this->getTitle())
            ->set('dossier', $this->getDossier())
            ->set('expertise', $this->getExpertise())
            ->set('expertiseJudiciaire', $this->getDossier()->getExpertiseJudiciaire())
            ->set('vehicle', $vehicle)
            ->set('clientInfos', $clientInfos)
            ->set('footerCompanies', $footerCompanies)
            ->set('expert', $expert)
            ->set('persons', $persons)
            ->set('type', $this->getType())
            ->set('subTitle', $subTitle)
            ->set('recipients', $recipients)
            ->set('displayDate', $displayDate)
            ->render('app/dossier/pdf/expertise_judiciaire/first_page.php');
        $this->addContent($content);
    }

    protected function renderFirstPageFooterJudiciaire(): string
    {
        $dossier = $this->getDossier();

        return TwigService::getInstance()->set('dossier', $dossier)
            ->render('app/dossier/pdf/expertise_judiciaire/first_page_persons.php');
    }

    /**
     * @return string
     */
    protected function getFooterCompanies(): string
    {
        $clientInfos = $this->getClientInfos();
        $isJudiciaire = ($this->getDossier() and $this->getDossier()->isJudiciaire());
        return TwigService::getInstance()->set('clientInfos', $clientInfos)
            ->set('appHeaderLogo', $this->getAppHeaderLogo())
            ->set('isJudiciaire', $isJudiciaire)
            ->set('displayFooter', $this->displayFooter)
            ->render('app/dossier/pdf/footer_companies.php');
    }

    /**
     * @param string $title
     * @param string $content
     * @param int $space
     * @return void
     */
    public function addSection(string $title, string $content = '', int $space = 0)
    {
        $this->addSectionTitle($title);
        if ($content) {
            $this->addSectionContent($content, $space);
        }
    }

    /**
     * @param string $title
     * @return void
     */
    public function addSectionTitle(string $title)
    {
        $clientInfos = $this->getClientInfos();
        $content = '<div style="background: ' . $clientInfos['colors']['h3_bg_color'] . ';" class="p-1 mb-8"><h3 class="font-weight-bolder mb-0 mt-1 pl-2 text-uppercase" style="color: ' . $clientInfos['colors']['h3_color'] . '">' . $this->formatText($title) . '</h3></div>';
        $this->addContent($content);
    }

    /**
     * @param string $title
     * @return void
     */
    public function addSectionTitleLight(string $title)
    {
        $clientInfos = $this->getClientInfos();
        $content = '<div style="background: ' . $clientInfos['colors']['light_title_bg_color'] . ';" class="p-1 mb-8"><h4 class="font-weight-bolder mb-0 mt-1 pl-2 text-uppercase" style="color: ' . $clientInfos['colors']['light_title_color'] . '">' . $this->formatText($title) . '</h4></div>';
        $this->addContent($content);
    }

    /**
     * @param string $title
     * @return void
     */
    public function addSectionSubTitle(string $title)
    {
        $clientInfos = $this->getClientInfos();
        $content = '<div class="mb-4 pl-0"><h4 class="font-weight-bolder" style="color: ' . $clientInfos['colors']['h4_color'] . '; text-decoration: underline">' . $this->formatText($title) . '</h4></div>';
        $this->addContent($content);
    }

    /**
     * @param string $content
     * @param int    $space
     * @return void
     */
    public function addSectionContent(string $content, int $space = 0)
    {
        if ($space) {
            $content = '<div class="mb-' . $space . '">' . $content . '</div>';
        }
        $this->addContent($content);
    }

    /**
     * @param array $columns
     * @param array $data
     * @param array $w
     * @return void
     */
    public function addTable(array $columns, array $data, array $w = [50, 50])
    {
        $content = TwigService::getInstance()->set('columns', $columns)
            ->set('data', $data)
            ->set('w', $w)
            ->render('app/dossier/pdf/table.php');
        $this->addContent($content);
    }

    /**
     * @param array $data
     * @return void
     */
    public function addTablePictures(array $data)
    {
        foreach ($data as $row) {
            $content = '<p><span class="font-weight-bolder">' . $row[0] . '</span></p>';
            $content .= '<p><img class="img-fluid" style="max-height: 1300px" src="' . $row[1] . '"></p>';
            $this->addContent($content);
            $this->addNewPage();
        }

        /*$content = TwigService::getInstance()->set('data', $data)
            ->render('app/dossier/pdf/table_pictures.php');
        $this->addContent($content);*/
    }

    /**
     * @param string|null $text
     * @return string
     */
    public function formatText(?string $text = null): string
    {
        if ($text === null) {
            return '';
        }

        $text = str_replace('&#039;', '\'', $text);
        $text = str_replace('&#39;', '\'', $text);
        //$text = str_replace("\n", '<br>', $text);
        if (!str_contains($text, '<p>')) {
            //prevent </p>\n<p> to be replaced by </p><br><p>
            $text = nl2br($text);
        }
        return $text;
    }

    protected function addSectionPersons()
    {
        $expertise = $this->getExpertise();

        $this->addSectionTitle(__('Identification des parties'));
        $this->addSectionSubTitle(__('Lieu d\'examen'));

        if ($expertise->getStatus() == DossierExpertise::STATUS_CANCELED) {
            $content = '<p>' . __('Expertise annulée.') . '</p>';
            $this->addSectionContent($content, 20);
            return;
        }

        $place = '<p>' . $expertise->displayPlace() . '</p>';
        if ($expertise->getDate()) {
            $place .= '<p>' . __('Expertise réalisée le %s', dateTimeFr($expertise->getDate()->format('Y-m-d H:i:s'))) . '</p>';
        }
        $this->addContent($place, 12);

        $persons = $expertise->getPersons()->toArray();
        $this->addTablePersons($persons, DossierExpertisePerson::STATUS_PRESENT, DossierSignature::TYPE_START);
        $this->addTablePersons($persons, DossierExpertisePerson::STATUS_ABSENT);

        $this->addPersonsComments($persons);
    }

    /**
     * @param DossierExpertisePerson[] $persons
     * @param string $status
     * @param string $signatureType
     * @return void
     */
    protected function addTablePersons(array $persons, string $status, string $signatureType = DossierSignature::TYPE_END)
    {
        if ($status == DossierExpertisePerson::STATUS_PRESENT) {
            $this->addSectionSubTitle("Personnes présentes");
        } else {
            $this->addSectionSubTitle("Personnes absentes");
        }

        $missionType = $this->getExpertise()?->getDossier()->getMissionType();
        $reportType = $missionType?->getReport() ?: '';

        $w = [20, 20, 20, 20, 20];
        $columns = ['Noms', 'Qualité', 'Coordonnées / Mail / Téléphone', 'Partie représentée'];
        if ($status == DossierExpertisePerson::STATUS_PRESENT) {
            $columns = ['Noms', 'Qualité', 'Coordonnées / Mail / Téléphone', 'Parties représentées et références', 'Émargement'];
        }
        $data = [];
        foreach($persons as $person) {
            if ($person->getStatus() != $status) {
                continue;
            }
            if ($person->isHidden()) {
                continue;
            }

            $name = '<strong>' . $person->getRepresentativeName() . '</strong>';
            if ($status == DossierExpertisePerson::STATUS_ABSENT) {
                if ($person->getExcused()) {
                    $name .= '<br><em>' . __('Absence excusée') . '</em>';
                    if ($person->getExcuseReason()) {
                        $name .= ' (' . $person->getExcuseReason() . ')';
                    }
                } else {
                    $name .= '<br><em>' . __('Absence non excusée') . '</em>';
                }
            }

            $type = $person->displayType();
            if ($reportType == ExpertiseEnum::REPORT_EXPERTISE_JUDICIAIRE and $person->getInstitution() and $person->getInstitution()->isDefendant()) {
                $type = __('Défenseur %s', ($person->getInstitution()->getDefendantNumber() ?: ''));
                $type .= '<br>' . $person->displayType();
            }
            if ($reportType == ExpertiseEnum::REPORT_EXPERTISE_JUDICIAIRE and $person->getInstitution() and $person->getInstitution()->isDemandeur()) {
                $type = __('Demandeur %s', ($person->getInstitution()->getDefendantNumber() ?: ''));
                $type .= '<br>' . $person->displayType();
            }
            $address = ($person->getAddress() ? $person->getAddress() . '<br>' : '');
            $address .= (($person->getZip() or $person->getCity()) ? $person->getZip() . ' ' . $person->getCity() . '<br>' : '');
            if ($person->getTelephone()) {
                $address .= 'Tél : ' . $person->getTelephone() . '<br>';
            }
            if ($person->getEmail()) {
                $address .= $person->getEmail() . '<br>';
            }

            $references = '';
            if ($person->getCompany() and $person->getCompany() != $person->getRepresentativeName()) {
                $references .= $person->getCompany() . '<br>';
            }
            if ($person->getInstitution()) {
                if ($person->getInstitution()->getRepresent()) {
                    $references .= $person->getInstitution()->getRepresent() . '<br>';
                }
                if ($person->getInstitution()->getReference()) {
                    $references .= __('Réf. Dossier : %s', $person->getInstitution()->getReference()) . '<br>';
                }
                if ($person->getInstitution()->getReferenceClient()) {
                    $references .= __('Réf. Mandant : %s', $person->getInstitution()->getReferenceClient()) . '<br>';
                }
                if ($person->getInstitution()->getReferenceCompany()) {
                    $references .= __('Réf. Compagnie : %s', $person->getInstitution()->getReferenceCompany()) . '<br>';
                }
            }
            $personData = [
                $name,
                $type,
                $address,
                $references,
            ];
            if ($status == DossierExpertisePerson::STATUS_PRESENT) {
                $signature = '';
                if ($signatureType == DossierSignature::TYPE_END and $person->getHasLeft()) {
                    $signature = '<em>' . __('A quitté l\'expertise') . '</em>';
                } elseif ($person->getSignatureRefused()) {
                    $signature = '<em>' . __('A refusé de signer') . '</em>';
                    if ($person->getSignatureRefusedReason()) {
                        $signature .= '<br><em>' . $person->getSignatureRefusedReason() . '</em>';
                    }
                } elseif ($person->getSignature($signatureType)) {
                    $signature = '<img class="img-fluid" src="' . $person->getSignature($signatureType)->getUrl() . '">';
                    $signature .= '<small><em>' . $person->getSignature($signatureType)->getDate()->format('d/m/Y H:i') . '</em></small>';
                }
                $personData[] = $signature;
            }
            $data[] = $personData;
        }

        if ($data) {
            $this->addTable($columns, $data, $w);
        } else {
            if ($status == DossierExpertisePerson::STATUS_PRESENT) {
                $content = '<p>' . __('Aucune partie n\'était représentée.') . '</p>';
            } else {
                $content = '<p>' . __('Toutes les parties étaient représentées.') . '</p>';
            }
            $this->addSectionContent($content, 20);
        }
    }

    /**
     * @param DossierExpertisePerson[] $persons
     * @return void
     */
    protected function addPersonsComments(array $persons)
    {
        $hasComment = false;
        foreach ($persons as $person) {
            if ($person->getComment()) {
                $hasComment = true;
                break;
            }
        }
        if (!$hasComment) {
            return;
        }

        $missionType = $this->getDossier()->getMissionType();
        $reportType = $missionType?->getReport() ?: ExpertiseEnum::REPORT_STANDARD;
        if ($reportType != ExpertiseEnum::REPORT_EXPERTISE_JUDICIAIRE) {
            $this->addSectionSubTitle('Auditions des parties');
        }

        $w = [20, 20, 60];
        $columns = ['Noms', 'Qualité', 'Commentaire'];
        $data = [];

        $persons = DossierExpertisePersonCommentsFormatter::sortPersons($this->getExpertise(), $persons);
        foreach($persons as $person) {
            if (!$person->getComment()) {
                continue;
            }

            $name = '<strong>' . $person->getRepresentativeName() . '</strong>';
            $type = $person->displayType();
            if ($this->getDossier()->isJudiciaire()) {
                if ($person->getType() == DossierExpertisePerson::TYPE_INSTITUTION and $person->getInstitution() and $person->getInstitution()->getType() == DossierInstitution::TYPE_CONTACT) {
                    $type = __('Demandeur');
                }
                if (!$person->getParent() and $person->getInstitution() and $person->getInstitution()->isDemandeur()) {
                    $type = __('Demandeur %s', ($person->getInstitution()->getDefendantNumber() ?: ''));
                    $type .= '<br>' . $person->displayType();
                }
                if (!$person->getParent() and $person->getInstitution() and $person->getInstitution()->isDefendant()) {
                    $type = __('Défenseur %s', ($person->getInstitution()->getDefendantNumber() ?: ''));
                    $type .= '<br>' . $person->displayType();
                }
            }

            $personData = [
                $name,
                $type,
                $person->getComment()
            ];
            $data[] = $personData;
        }

        $this->addTable($columns, $data, $w);
    }

    protected function addHistory(bool $displayDocuments = true, bool $displayOnlyHistovec = false, array $tableOfContents = [])
    {
        if ($this->getDossier()->isJudiciaire()) {
            $index = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($tableOfContents, 'Historique du véhicule');
            $this->addSectionSubTitle(__('%s. Historique du véhicule', $index));
        } else {
            $this->addSectionTitle(__('Chronologie des faits'));
        }

        $container = ContainerBuilderService::getInstance();
        $facts = $container->get(DossierFactService::class)->getRepository()->findByDossierAndExpertise($this->getExpertise());
        if (!$facts) {
            $content = '<p><em>' . __('Aucun fait.') . '</em></p>';
            $this->addSectionContent($content, 20);
        } else {
            $factsByDate = DossierFactService::sortFacts($facts);

            $columns = ['Date', 'Kilométrage', 'Évènement'];
            $data = [];
            foreach ($factsByDate as $date => $facts) {
                foreach ($facts as $fact) {
                    /** @var DossierFact $fact */
                    if ($displayOnlyHistovec and $fact->getType() != DossierFact::TYPE_HISTOVEC) {
                        continue;
                    }
                    if (in_array($this->getType(), [DossierDocument::TYPE_CONTRADICTORY, DossierDocument::TYPE_CONTRADICTORY_WO_PICTURES, DossierDocument::TYPE_CONTRADICTORY_WO_DOCUMENTS]) and $fact->getType() == DossierFact::TYPE_SAMPLE) {
                        continue;
                    }

                    $comment = nl2br($fact->getComment());

                    if ($displayDocuments) {
                        if ($fact->getDocument() and $fact->getDocument()->getConfidential()) {
                            continue;
                        }

                        $file = $fact->getFile();
                        if (!$file and $fact->getDocument()) {
                            $file = $fact->getDocument()->getFile();
                        }
                        if ($fact->getDocument() and in_array(getExtension($fact->getDocument()->getFile()), ['jpg', 'jpeg', 'png', 'gif'])) {
                            $size = $this->getImageSize(WEB_PATH . '/medias/' . $fact->getDossier()->getFolder() . $fact->getDocument()->getFile(), null, 800);
                            $comment .= '<br><br><img ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : 'class="img-fluid" style="max-height: 1200px"') . ' src="' . $fact->getDocument()->getUrl(true, true) . '">';
                        } elseif (in_array(getExtension($file), ['jpg', 'jpeg', 'png'])) {
                            $size = $this->getImageSize(WEB_PATH . '/medias/' . $fact->getDossier()->getFolder() . $file, null, 800);
                            $comment .= '<br><br><img src="' . Assets::getMediaUrl($fact->getDossier()->getFolder() . $file . '?dossier_ref=' . $fact->getDossier()->getReference()) . '" ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : 'class="img-fluid" style="max-height: 1200px"') . '>';
                        } elseif (getExtension($file) == 'pdf') {
                            $fullPath = WEB_PATH . '/medias/' . $fact->getDossier()->getFolder();
                            $images = ImagickHelper::convertPdf($fullPath . $file, true);
                            $comment .= '<br><br><img class="img-fluid" style="max-height: 1200px" src="' . Assets::getMediaUrl($images[0] . '?dossier_ref=' . $fact->getDossier()->getReference()) . '">';
                        }
                    }
                    $data[] = [
                        ($fact->getDate() ? $fact->getDate()->format('d/m/Y') : ''),
                        ($fact->getMileage() ? $fact->getMileage() . ' ' . DossierFactService::getMileageType($fact->getMileageType()) : ''),
                        $comment
                    ];
                }
            }

            $this->addTable($columns, $data, [15, 15, 70]);
        }
    }

    protected function addVehicleLoan()
    {
        $expertise = $this->getExpertise();
        $vehicle = $expertise->getDossier()->getVehicle();

        if ($vehicle->getLoan() == 'unsure') {
            return;
        }

        $this->addSectionSubTitle(__('Conditions d\'achat'));

        $content = '<table>';
        $content .= '<tr><td><span class="font-weight-bolder mr-4">Achat à crédit :</span></td><td><input type="checkbox" ' . ($vehicle->getLoan() == 'yes' ? 'checked' : '') . '> Oui<input type="checkbox" class="ml-4" ' . ($vehicle->getLoan() == 'no' ? 'checked' : '') . '> Non</td></tr>';
        if ($vehicle->getLoan() == 'yes' and $vehicle->getLoanAmount()) {
            $loanAmount = Number::formatAmount($vehicle->getLoanAmount(), DEFAULT_CURRENCY) . ' / mois';
            if ($vehicle->getLoanEndDate()) {
                $loanAmount .= ' jusqu\'en ' . $vehicle->getLoanEndDate()->format('m/Y');
            }
            $content .= '<tr><td><span class="font-weight-bolder mr-4">Échéances en cours :</span></td><td>' . $loanAmount . '</td></tr>';
            //$content .= '<tr><td><span class="font-weight-bolder mr-4">Assurance en cas de décès sur le prêt :</span></td><td><input type="checkbox" ' . ($vehicle->getLoanInsurance() ? 'checked' : '') . '> Oui<input type="checkbox" class="ml-4" ' . (!$vehicle->getLoanInsurance() ? 'checked' : '') . '> Non</td></tr>';
        }
        $content .= '</table>';
        $this->addContent($content, 12);
    }

    protected function addVehicleInfos(array $tableOfContents = [])
    {
        $dossier = $this->getDossier();
        $expertise = $this->getExpertise();
        $vehicle = $dossier->getVehicle();

        $columns = ['', ''];
        $data = [];
        if ($vehicle->getBrand()) {
            $data[] = ['<strong>Marque</strong>', $vehicle->getBrand()];
        }
        if ($vehicle->getModel()) {
            $data[] = ['<strong>Modèle</strong>', $vehicle->getModel()];
        }
        if ($vehicle->getColor()) {
            $data[] = ['<strong>Couleur</strong>', DossierVehicleService::getColor($vehicle->getColor())];
        }
        if ($vehicle->getRegistration()) {
            $data[] = ['<strong>Immatriculation</strong>', $vehicle->getRegistration()];
        }
        if ($vehicle->getSerialNumber()) {
            $data[] = ['<strong>N° Série</strong>', $vehicle->getSerialNumber()];
        }
        if ($vehicle->getFormuleNumber()) {
            $data[] = ['<strong>N° Formule</strong>', $vehicle->getFormuleNumber()];
        }
        if ($vehicle->getDateFirstCirculation()) {
            $data[] = ['<strong>Mise en circulation</strong>', $vehicle->getDateFirstCirculation() ? $vehicle->getDateFirstCirculation()->format('d/m/Y') : ''];
        }
        if ($vehicle->getRegistrationDate()) {
            $data[] = ['<strong>Date de création de la carte grise</strong>', $vehicle->getRegistrationDate() ? $vehicle->getRegistrationDate()->format('d/m/Y') : ''];
        }
        if ($expertise->getExpertiseVehicle() and $expertise->getExpertiseVehicle()->getMeter()) {
            $data[] = ['<strong>Kilométrage</strong>', $expertise->getExpertiseVehicle()->getMeter() . ' ' . DossierExpertiseVehicleService::getMeterType($expertise->getExpertiseVehicle()->getMeterType()) . ' ' . DossierExpertiseVehicleService::getMeterRead($expertise->getExpertiseVehicle()->getMeterRead())];
        }
        if ($vehicle->getBodywork()) {
            $data[] = ['<strong>Carrosserie</strong>', $vehicle->getBodywork()];
        }
        if ($vehicle->getVehicleType()) {
            $data[] = ['<strong>Genre</strong>', $vehicle->getVehicleType()];
        }
        if ($vehicle->getEngine()) {
            $data[] = ['<strong>Énergie</strong>', DossierVehicleService::getEngine($vehicle->getEngine())];
        }
        if ($vehicle->getPower()) {
            $data[] = ['<strong>Puissance</strong>', $vehicle->getPower()];
        }
        if ($vehicle->getGearbox()) {
            $data[] = ['<strong>Boîte de vitesse</strong>', DossierVehicleService::getGearbox($vehicle->getGearbox())];
        }
        if ($vehicle->getEngineCode()) {
            $data[] = ['<strong>Code moteur</strong>', $vehicle->getEngineCode()];
        }
        if ($vehicle->getSeating()) {
            $data[] = ['<strong>Places assises</strong>', $vehicle->getSeating()];
        }
        if ($expertise->getExpertiseVehicle() and $expertise->getExpertiseVehicle()->getTireWear() and !$expertise->getExpertiseVehicle()->getTireIgnore()) {
            $tireWears = explode(';', $expertise->getExpertiseVehicle()->getTireWear());
            $tireWears = array_filter($tireWears);
            if ($tireWears) {
                $tires = [
                    'AVG',
                    'AVD',
                    'ARG',
                    'ARD',
                    'INTARG',
                    'INTARD',
                    'DIVERS',
                    'DIVERS',
                ];
                $content = '';
                foreach ($tires as $id => $tire) {
                    if (!isset($tireWears[$id]) or !$tireWears[$id]) {
                        continue;
                    }
                    $content .= $tire . ' : ' . $tireWears[$id] . '% - ';
                }
                $content = rtrim($content, ' -');
                $data[] = ['<strong>Usure des pneus</strong>', $content];
            }
        }

        /*if ($vehicle->getObservations() and $this->getType() == DossierDocument::TYPE_EXPERTISE) {
            $data[] = ["<strong>Observations</strong>", $vehicle->getObservations()];
        }*/

        $serialNumberValid = '<input type="checkbox" ' . ($vehicle->getSerialNumberValid() == 'yes' ? 'checked' : '') . '> Oui';
        $serialNumberValid .= '<input type="checkbox" class="ml-4" ' . ($vehicle->getSerialNumberValid() == 'no' ? 'checked' : '') . '> Non';
        $serialNumberValid .= '<input type="checkbox" class="ml-4" ' . ($vehicle->getSerialNumberValid() == 'unsure' ? 'checked' : '') . '> Sans réponse';
        $data[] = ['<strong>N° de série conforme</strong>', $serialNumberValid];


        $ignoreLevels = ($expertise->getExpertiseVehicle() ? $expertise->getExpertiseVehicle()->getLevelIgnore() : false);
        if (!$ignoreLevels) {
            $levelEngineOilValid = null;
            if ($expertise->getExpertiseVehicle() and $expertise->getExpertiseVehicle()->getLevelEngineOil()) {
                $levelEngineOilValid = ($expertise->getExpertiseVehicle()->getLevelEngineOil() >= 4 and $expertise->getExpertiseVehicle()->getLevelEngineOil() <= 8);
            }
            $levelEngineOil = '<input type="checkbox" ' . ($levelEngineOilValid ? 'checked' : '') . '> Oui';
            $levelEngineOil .= '<input type="checkbox" class="ml-4" ' . ($levelEngineOilValid === false ? 'checked' : '') . '> Non';
            $data[] = ['<strong>Niveau d’huile conforme</strong>', $levelEngineOil];

            $levelCoolantValid = null;
            if ($expertise->getExpertiseVehicle() and $expertise->getExpertiseVehicle()->getLevelCoolant()) {
                $levelCoolantValid = ($expertise->getExpertiseVehicle()->getLevelCoolant() >= 4 and $expertise->getExpertiseVehicle()->getLevelCoolant() <= 8);
            }
            $levelCoolant = '<input type="checkbox" ' . ($levelCoolantValid ? 'checked' : '') . '> Oui';
            $levelCoolant .= '<input type="checkbox" class="ml-4" ' . ($levelCoolantValid === false ? 'checked' : '') . '> Non';
            $data[] = ['<strong>Niveau du liquide de refroidissement conforme</strong>', $levelCoolant];

            if ($expertise->getExpertiseVehicle() and $expertise->getExpertiseVehicle()->getLevelBrake()) {
                $levelBrakeValid = ($expertise->getExpertiseVehicle()->getLevelBrake() >= 4 and $expertise->getExpertiseVehicle()->getLevelBrake() <= 8);
                $levelBrake = '<input type="checkbox" ' . ($levelBrakeValid ? 'checked' : '') . '> Oui';
                $levelBrake .= '<input type="checkbox" class="ml-4" ' . ($levelBrakeValid === false ? 'checked' : '') . '> Non';
                $data[] = ['<strong>Niveau du liquide de frein conforme</strong>', $levelBrake];
            }
        }

        if ($vehicle->getDateTechnicalInspectionExpiry()) {
            $date = $expertise->getDate();
            $technicalInspectionExpiry = '<input type="checkbox" ' . ($vehicle->getDateTechnicalInspectionExpiry() > $date ? 'checked' : '') . '> Oui';
            $technicalInspectionExpiry .= '<input type="checkbox" class="ml-4" ' . ($vehicle->getDateTechnicalInspectionExpiry() < $date ? 'checked' : '') . '> Non';
            $technicalInspectionExpiry .= '<span class="ml-4">(Valide jusqu\'au ' . $vehicle->getDateTechnicalInspectionExpiry()->format('d/m/Y') . ')</span>';
            $data[] = ['<strong>Contrôle technique conforme</strong>', $technicalInspectionExpiry];
        }

        $missionType = $dossier->getMissionType();
        $reportType = $missionType?->getReport() ?: ExpertiseEnum::REPORT_STANDARD;
        if ($reportType == ExpertiseEnum::REPORT_MECHANICAL_BREAKDOWN_GUARANTEE) {
            $useTypes = [];
            if ($vehicle->getUseType()) {
                $vehicleUseTypes = explode(',', $vehicle->getUseType());
                foreach ($vehicleUseTypes as $vehicleUseType) {
                    $useTypes[] = DossierVehicleService::getUseType($vehicleUseType);
                }
            }
            $data[] = ['<strong>Utilisation</strong>', implode(', ' , $useTypes)];

            if ($vehicle->getUsePurpose() != DossierVehicle::USE_PURPOSE_NOT_SET and ($vehicle->getUsePurpose() != DossierVehicle::USE_PURPOSE_OTHER or $vehicle->getUsePurposeOther())) {
                $usePurpose = DossierVehicleService::getUsePurpose($vehicle->getUsePurpose());
                if ($vehicle->getUsePurpose() == DossierVehicle::USE_PURPOSE_OTHER and $vehicle->getUsePurposeOther()) {
                    $usePurpose .= ' (' . $vehicle->getUsePurposeOther() . ')';
                }
                $data[] = ['<strong>Type d\'utilisation</strong>', $usePurpose];
            }

            if ($vehicle->getVrade()) {
                $vrade = Number::formatAmount($vehicle->getVrade(), DEFAULT_CURRENCY);
                if ($vehicle->getVradeTax() == VradeEnum::VRADE_TAX_EXCL) {
                    $vrade .= ' HT';
                } elseif ($vehicle->getVradeTax() == VradeEnum::VRADE_TAX_INCL) {
                    $vrade .= ' TTC';
                }
                $data[] = ['<strong>VRADE approximative</strong>', $vrade];
            }
        }

        if ($this->type == DossierDocument::TYPE_CONTRADICTORY and $dossier->isJudiciaire()) {
            $index = DossierExpertiseJudiciaireTableOfContentsFactory::findIndex($tableOfContents, 'Identification du véhicule');
            $this->addSectionTitle(__('%s. Identification du véhicule', $index));

            $subIndex = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($tableOfContents, 'Description du véhicule litigieux');
            $this->addSectionSubTitle(__('%s. Description du véhicule litigieux', $subIndex));
        } elseif ($reportType == ExpertiseEnum::REPORT_EXPERTISE_JUDICIAIRE) {
            $index = DossierExpertiseJudiciaireTableOfContentsFactory::findIndex($tableOfContents, 'Constatations et investigations');
            $this->addSectionTitle(__('%s. Constatations et investigations', $index));

            $subIndex = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($tableOfContents, 'Description du véhicule litigieux');
            $this->addSectionSubTitle(__('%s. Description du véhicule litigieux', $subIndex));
        } else {
            $this->addSectionTitle(__('Identification du véhicule'));
        }
        $this->addTable($columns, $data);

        if ($reportType == ExpertiseEnum::REPORT_MECHANICAL_BREAKDOWN_GUARANTEE) {
            $content = '<div class="rounded p-4" style="border: 1px solid #3F4254;"><p class="mb-0"><strong><u>' . __('Sont exclus :') . '</u> Les véhicules destinés à la location courte durée, les véhicules à usage de taxis, ambulances, auto-écoles, de transport de marchandises et de voyageurs à titre onéreux, spectacles, cascades, épreuves sportives ou de vitesse, rallye, course.</strong></p></div>';
            $this->addSectionContent($content, 20);
        }

        if ($this->getType() == DossierDocument::TYPE_APPRAISED_VALUE) {
            return;
        }

        if ($expertise->getExpertiseVehicle() and $expertise->getExpertiseVehicle()->getRecommendations()) {
            $content = '';
            if ($reportType == ExpertiseEnum::REPORT_EXPERTISE_JUDICIAIRE) {
                $subIndex = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($tableOfContents, 'Préconisations du constructeur');
                $this->addSectionSubTitle(__('%s. Préconisations du constructeur', $subIndex));
            } else {
                $content = '<p><strong><u>' . __('Préconisations du constructeur') . '</u></strong></p>';
            }

            $engineOil = ($expertise->getExpertiseVehicle() ? $expertise->getExpertiseVehicle()->getEngineOil() : []);
            $engineOilContent = '';

            if (isset($engineOil['change_time']) and $engineOil['change_time'] and isset($engineOil['change_kms']) and $engineOil['change_kms']) {
                $changeTimeType = ((isset($engineOil['change_time_type']) and $engineOil['change_time_type']) ? $engineOil['change_time_type'] : 'year');
                $changeKmType = 'kms';
                if (isset($engineOil['change_km_type']) and $engineOil['change_km_type']) {
                    $changeKmType = DossierDocumentService::getMileageType($engineOil['change_km_type']);
                }
                $engineOilContent .= 'Vidange moteur et filtre à huile tous les ' . $engineOil['change_kms'] . ' ' . $changeKmType . ' ou tous les ' . $engineOil['change_time'] . ' ' . ($changeTimeType == 'year' ? 'ans' : 'mois');
                if (isset($engineOil['first_due_date']) and $engineOil['first_due_date']) {
                    $engineOilContent .= ', au premier terme échu';
                }
                $engineOilContent .= '<br>';
            } elseif (isset($engineOil['change_time']) and $engineOil['change_time']) {
                $changeTimeType = ((isset($engineOil['change_time_type']) and $engineOil['change_time_type']) ? $engineOil['change_time_type'] : 'year');
                $engineOilContent .= __('Vidange moteur et filtre à huile tous les %s %s', $engineOil['change_time'], ($changeTimeType == 'year' ? 'ans' : 'mois')) . '<br>';
            } elseif (isset($engineOil['change_kms']) and $engineOil['change_kms']) {
                $engineOilContent .= __('Vidange moteur et filtre à huile tous les %s kms', $engineOil['change_kms']) . '<br>';
            }

            if (isset($engineOil['viscosity']) and $engineOil['viscosity']) {
                $engineOilContent .= 'Viscosité Huile : ' . $engineOil['viscosity'] . '<br>';
            }
            if (isset($engineOil['standard']) and $engineOil['standard']) {
                $engineOilContent .= 'Norme Huile : ' . $engineOil['standard'] . '<br>';
            }
            if (isset($engineOil['crankcase_qty']) and $engineOil['crankcase_qty']) {
                $engineOilContent .= 'Quantité d\'huile présente dans le carter : ' . $engineOil['crankcase_qty'] . ' L<br>';
            }
            if (isset($engineOil['qty_with_filter']) and $engineOil['qty_with_filter']) {
                $engineOilContent .= 'Quantité d\'huile avec filtre : ' . $engineOil['qty_with_filter'] . ' L<br>';
            }
            if ($expertise->getExpertiseVehicle() and $expertise->getExpertiseVehicle()->getCrankcaseCapacity()) {
                $engineOilContent .= 'Capacité théorique du carter : ' . $expertise->getExpertiseVehicle()->getCrankcaseCapacity() . ' L<br>';
            }
            if ($engineOilContent) {
                $content .= '<p>' . $engineOilContent . '</p>';
            }

            if ($expertise->getExpertiseVehicle() and $expertise->getExpertiseVehicle()->getRecommendations()) {
                $content .= '<p>' . $this->formatText($expertise->getExpertiseVehicle()->getRecommendations()) . '</p>';
            }

            $this->addSectionContent($content, 20);
        }

        $firstExpertise = $dossier->getFirstExpertise();
        if ($this->withPictures) {
            $picture = $firstExpertise->getPicture(DossierExpertisePicture::TYPE_VEHICLE_FRONT);
            if (!$picture and $expertise->getPicture(DossierExpertisePicture::TYPE_VEHICLE_FRONT)) {
                $picture = $expertise->getPicture(DossierExpertisePicture::TYPE_VEHICLE_FRONT);
            }
            if ($picture) {
                $size = $this->getImageSize($picture->getFullPath(), null, 800);

                $content = '<div class="mt-4 mb-4 text-center">';
                $content .= '<img class="img-fluid" src="' . $picture->getUrl() . '" ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : '') . '>';
                $content .= '<p class="text-center mt-6"><em>' . __('Identification du véhicule') . '</em></p>';
                $content .= '</div>';
                $this->addSectionContent($content, 20);
            }

            $size = 50;
            $pictures = [
                DossierExpertisePicture::TYPE_VEHICLE_REAR => __('Identification 3/4 arrière du véhicule'),
                DossierExpertisePicture::TYPE_SERIAL_NUMBER => __('Numéro de série'),
            ];
            if ($firstExpertise->getPicture(DossierExpertisePicture::TYPE_TECHNICAL_CONTROL_LABEL) or $expertise->getPicture(DossierExpertisePicture::TYPE_TECHNICAL_CONTROL_LABEL)) {
                $size = 33;
                $pictures[DossierExpertisePicture::TYPE_TECHNICAL_CONTROL_LABEL] = __('Etiquette contrôle technique');
            }
            if ($this->getType() == DossierDocument::TYPE_APPRAISED_VALUE) {
                $size = 50;
                $pictures = [
                    DossierExpertisePicture::TYPE_VEHICLE_REAR => __('Identification 3/4 arrière du véhicule'),
                    DossierExpertisePicture::TYPE_METER => __('Compteur'),
                    DossierExpertisePicture::TYPE_FRONT_TIRE => __('Pneumatique avant'),
                ];
            }

            $content = '<table class="table">';
            $content .= '<tr>';
            foreach ($pictures as $type => $title) {
                $content .= '<td style="width: ' . $size . '%; text-align: center; height: 30px"><strong>' . $title . '</strong></td>';
            }
            $content .= '</tr>';
            $content .= '<tr>';
            foreach ($pictures as $type => $title) {
                $picture = $firstExpertise->getPicture($type);
                if (!$picture) {
                    $picture = $expertise->getPicture($type);
                }
                $content .= '<td style="vertical-align: top"><div class="text-center">' . ($picture ? '<img class="img-fluid" src="' . $picture->getUrl() . '">' : 'Photo non éditée') . '</div></td>';
            }
            $content .= '</tr>';
            $content .= '</table>';

            $this->addContent($content, 20);
        }
    }

    protected function addExpertiseInfos(bool $addPreviousExpertises = false, array $tableOfContents = [])
    {
        $expertise = $this->getExpertise();

        if ($tableOfContents) {
            $index = DossierExpertiseJudiciaireTableOfContentsFactory::findIndex($this->getTableOfContents(), 'Conditions d\'expertises et constatations');
            $this->addSectionTitle(__('%s. Conditions d\'expertises et constatations', $index));
        } else {
            $this->addSectionTitle(__('Conditions d\'expertises et constatations'));
        }

        $nbExpertise = 0;
        if ($addPreviousExpertises) {
            $previousExpertises = $expertise->getPreviousExpertises();
            if ($previousExpertises) {
                foreach ($previousExpertises as $previousExpertise) {
                    $nbExpertise++;
                    $title = __('Expertise %s du %s', $nbExpertise, dateFrench('d F Y', $previousExpertise->getDate()->getTimestamp()));
                    $this->addSectionTitleLight($title);

                    $this->addSectionSubTitle(__('Lieu d\'examen'));
                    if ($previousExpertise->getStatus() == DossierExpertise::STATUS_CANCELED) {
                        $content = '<p>' . __('Expertise annulée.') . '</p>';
                        $this->addContent($content, 12);
                    } else {
                        $place = '<p>' . $previousExpertise->displayPlace() . '</p>';
                        $this->addContent($place, 12);

                        $this->addExaminationConditions($previousExpertise);
                        $this->addLevelMeters($previousExpertise);

                        $persons = $previousExpertise->getPersons()->toArray();
                        $this->addTablePersons($persons, DossierExpertisePerson::STATUS_PRESENT, DossierSignature::TYPE_START);
                        $this->addTablePersons($persons, DossierExpertisePerson::STATUS_ABSENT);

                        $this->addPersonsComments($persons);

                        $observations = $previousExpertise->getObservations()->toArray();
                        if ($observations) {
                            $this->addSectionSubTitle('Constatations');
                            $this->addObservations($observations);
                        }
                    }

                    if ($previousExpertise->getConservatoryMeasures()) {
                        $this->addSectionSubTitle(__('Mesures conservatoires à prendre'));

                        $content = $this->formatText($previousExpertise->getConservatoryMeasures());
                        if ($previousExpertise->getConservatoryMeasuresFile() and $this->withPictures) {
                            $size = $this->getImageSize(WEB_PATH . '/medias/' . $previousExpertise->getDossier()->getFolder() . $previousExpertise->getConservatoryMeasuresFile(), null, 600);

                            $content .= '<div class="mt-0 mb-12 text-center">';
                            $content .= '<img class="img-fluid" src="' . Assets::getMediaUrl($previousExpertise->getDossier()->getFolder() . $previousExpertise->getConservatoryMeasuresFile()) . '" ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : '') . '>';
                            $content .= '</div>';
                        }

                        $this->addContent($content, 12);
                    }
                }
            }
        }

        if ($nbExpertise) {
            $nbExpertise++;
            $title = __('Expertise %s du %s', $nbExpertise, dateFrench('d F Y', $expertise->getDate()->getTimestamp()));
            $this->addSectionTitleLight($title);
        }

        if ($this->getType() == DossierDocument::TYPE_EXPERTISE) {
            $this->addSectionSubTitle(__('Lieu d\'examen'));
            if ($expertise->getStatus() == DossierExpertise::STATUS_CANCELED) {
                $content = '<p>' . __('Expertise annulée.') . '</p>';
                $this->addContent($content, 12);
            } else {
                $place = '<p>' . $expertise->displayPlace() . '</p>';
                if (!$nbExpertise and $expertise->getDate()) {
                    $place .= '<p>' . __('Expertise réalisée le %s', dateTimeFr($expertise->getDate()->format('Y-m-d H:i:s'))) . '</p>';
                }
                $this->addContent($place, 12);
            }
        }

        $this->addExaminationConditions();
        $this->addLevelMeters();

        if ($this->getType() == DossierDocument::TYPE_EXPERTISE and $expertise->getStatus() != DossierExpertise::STATUS_CANCELED and !$expertise->getDossier()->isJudiciaire()) {
            $persons = $expertise->getPersons()->toArray();
            $this->addTablePersons($persons, DossierExpertisePerson::STATUS_PRESENT, DossierSignature::TYPE_START);
            $this->addTablePersons($persons, DossierExpertisePerson::STATUS_ABSENT);

            $this->addPersonsComments($persons);
        }

        $observations = $expertise->getObservations()->toArray();
        if ($observations) {
            $this->addSectionSubTitle('Constatations');
            $this->addObservations($observations);
        }

        if ($expertise->getConservatoryMeasures() and !$expertise->getDossier()->isJudiciaire()) {
            $this->addSectionSubTitle(__('Mesures conservatoires à prendre'));

            $content = $this->formatText($expertise->getConservatoryMeasures());
            if ($expertise->getConservatoryMeasuresFile() and $this->withPictures) {
                $size = $this->getImageSize(WEB_PATH . '/medias/' . $expertise->getDossier()->getFolder() . $expertise->getConservatoryMeasuresFile(), null, 600);

                $content .= '<div class="mt-0 mb-12 text-center">';
                $content .= '<img class="img-fluid" src="' . Assets::getMediaUrl($expertise->getDossier()->getFolder() . $expertise->getConservatoryMeasuresFile()) . '" ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : '') . '>';
                $content .= '</div>';
            }

            $this->addContent($content, 12);
        }
    }

    protected function addSamples()
    {
        $samples = $this->getExpertise()->getSamples();
        if (!count($samples)) {
            return;
        }

        $columns = ['Date', 'Type', 'Infos'];
        $data = [];
        foreach ($samples as $sample) {
            $type = $sample->getType();
            if ($sample->getReference()) {
                $type .= '<br>' . __('Réf') . ' : ' . $sample->getReference();
            }

            $infos = $sample->getInfos();
            if ($sample->getPicture() and $this->withPictures) {
                $infos .= '<div class="mt-4 mb-4 text-center">';
                $infos .= '<img class="img-fluid" src="' . Assets::getMediaUrl($sample->getDossier()->getFolder() . $sample->getPicture()) . '" style="max-height: 400px">';
                $infos .= '</div>';
            }

            $data[] = [
                $sample->getDate()->format('d/m/Y'),
                $type,
                $infos
            ];
        }

        $missionType = $this->dossier->getMissionType();
        $reportType = $missionType?->getReport() ?: ExpertiseEnum::REPORT_STANDARD;
        if ($reportType != ExpertiseEnum::REPORT_EXPERTISE_JUDICIAIRE) {
            $this->addSectionSubTitle(__('Prélèvements'));
        }

        $this->addTable($columns, $data, [15, 25, 60]);
    }

    protected function addEvaluations(array $tableOfContents = [])
    {
        $dossier = $this->getDossier();
        $expertise = $this->getExpertise();

        if ($expertise->getStatus() == DossierExpertise::STATUS_CANCELED and !$expertise->getEvaluation() and !$expertise->getNotes()) {
            return;
        }

        $missionType = $this->dossier->getMissionType();
        $reportType = $missionType?->getReport() ?: ExpertiseEnum::REPORT_STANDARD;
        $isJudiciaire = ($reportType == ExpertiseEnum::REPORT_EXPERTISE_JUDICIAIRE or ($this->getType() == DossierDocument::TYPE_CONTRADICTORY and $dossier->isJudiciaire()));

        if ($isJudiciaire) {
            $index = DossierExpertiseJudiciaireTableOfContentsFactory::findIndex($tableOfContents, 'Méthodologie et évaluation de remise en état');
            $this->addSectionTitle(__('%s. Méthodologie et évaluation de remise en état', $index));
        } else {
            $this->addSectionTitle(__('Évaluation et préjudice annexe'));
        }

        if ($isJudiciaire) {
            $index = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($tableOfContents, 'Méthodologie - Chiffrage');
            $this->addSectionSubTitle(__('%s. Méthodologie - Chiffrage', $index));
        } else {
            $this->addSectionSubTitle(__('Évaluation'));
        }
        if (!$expertise->getEvaluation()) {
            $content = '<p><em>' . __('Aucune évaluation') . '</em></p>';
        } else {
            $content = $this->formatText($expertise->getEvaluation());
        }
        $this->addContent($content, 12);

        if ($isJudiciaire) {
            $index = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($tableOfContents, 'Observations de l\'expert de justice');
            $this->addSectionSubTitle(__('%s. Observations de l\'expert de justice', $index));
        } else {
            $this->addSectionSubTitle(__('Observations'));
        }
        if (!$expertise->getNotes()) {
            $content = '<p><em>' . __('Aucune observation.') . '</em></p>';
        } else {
            $content = $this->formatText($expertise->getNotes());
        }
        $breakdown = $dossier->getBreakdown();
        $origins = ($breakdown ? $breakdown->getOrigins() : []);
        $hasFuel = false;
        foreach ($origins as $origin) {
            if ($origin->getName() == 'Carburant') {
                $hasFuel = true;
                break;
            }
        }
        if ($hasFuel) {
            if ($breakdown and $breakdown->getCauseOfMalfunction()) {
                $content .= '<p><strong>' . __('Origine du désordre ou cause de la panne') . '</strong><br>' . nl2br($breakdown->getCauseOfMalfunction()) . '</p>';
            }
            if ($breakdown and ($breakdown->getFuelReversal() == 'yes' or $breakdown->getFuelReversal() == 'no')) {
                $content .= '<p><strong>' . __('Inversion de carburant (faute imputable au propriétaire du véhicule)') . '</strong> : ' . ($breakdown->getFuelReversal() == 'yes' ? __('Oui') : __('Non')) . '</p>';
            }
            if ($breakdown and ($breakdown->getFuelPollution() == 'yes' or $breakdown->getFuelPollution() == 'no')) {
                $content .= '<p><strong>' . __('Pollution du carburant (faute imputable à la station-service)') . '</strong> : ' . ($breakdown->getFuelPollution() == 'yes' ? __('Oui') : __('Non')) . '</p>';
            }
        }
        $this->addContent($content, 12);


        $report = $expertise->getExpertiseReport();
        $content = '';
        $displayPrice = $report->getDisplayPrice();
        $displayFees = $report->getDisplayFees();
        if (isset($_GET['display_price'])) {
            $displayPrice = $_GET['display_price'];
        }
        if (isset($_GET['display_fees'])) {
            $displayFees = $_GET['display_fees'];
        }
        if ($displayPrice or $displayFees) {
            $content = '<p>';
            if ($displayPrice) {
                $content .= __('Frais de la 1ère expertise du cabinet %s : %s', $dossier->getCompany()->getName(), ($dossier->getPrice() ? $dossier->displayPrice() : __('Aucun'))) . '<br>';
            }
            if ($displayFees) {
                $content .= __('Frais annexes à l\'expertise du cabinet %s :', $dossier->getCompany()->getName()) . '<br>';
                if ($dossier->getSurcharges()) {
                    foreach ($dossier->getSurcharges() as $surcharge) {
                        $content .= '&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;' . $surcharge->getName() . ' : ' . $surcharge->displayAmount() . '<br>';
                    }
                } else {
                    $content .= ' ' . __('Aucun');
                }
            }
            $content .= '</p>';
        }

        $content .= '<p>';
        if ($dossier->getBreakdown() and $dossier->getBreakdown()->getTowingCost()) {
            $content .= __('Frais de remorquage : %s', Number::formatAmount($dossier->getBreakdown()->getTowingCost(), DEFAULT_CURRENCY)) . '<br>';
        }
        if ($dossier->getVehicle()->getCarStorage() == 'yes') {
            $content .= __('Frais de gardiennage : %s', Number::formatAmount($dossier->getVehicle()->getCarStorageFees(), DEFAULT_CURRENCY) . ' TTC / ' . ($dossier->getVehicle()->getCarStorageFeesType() == DossierVehicle::FEES_PER_DAY ? __('jour') : __('mois'))) . '<br>';

            if ($dossier->getVehicle()->getCarStorageDate()) {
                $now = new \DateTime();
                if ($dossier->getVehicle()->getCarStorageDate() > $now) {
                    $content .= ' à partir du ' . $dossier->getVehicle()->getCarStorageDate()->format('d/m/Y');
                } else {
                    $content .= ' depuis le ' . $dossier->getVehicle()->getCarStorageDate()->format('d/m/Y');
                }
            }
            if ($dossier->getVehicle()->getCarStorageDateEnd()) {
                $content .= ' jusqu\'au ' . $dossier->getVehicle()->getCarStorageDateEnd()->format('d/m/Y');
            }
            $content .= '<br>';
        } else {
            $content .= __('Frais de gardiennage : Non') . '<br>';
        }
        if ($dossier->getBreakdown() and $dossier->getBreakdown()->getDismantlingCost()) {
            $content .= __('Frais de démontage : %s', Number::formatAmount($dossier->getBreakdown()->getDismantlingCost(), DEFAULT_CURRENCY)) . '<br>';
            if ($dossier->getBreakdown()->getDismantlingDate()) {
                $content .= __('Démontage effectué le %s', $dossier->getBreakdown()->getDismantlingDate()->format('d/m/Y'));
            }
        }
        $content .= '</p>';
        if ($expertise->getPrejudice()) {
            $content .= $expertise->getPrejudice();
        }
        if ($content) {
            if ($isJudiciaire) {
                $index = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($tableOfContents, 'Coût du remorquage et immobilisation');
                $this->addSectionSubTitle(__('%s. Coût du remorquage et immobilisation', $index));
            } else {
                $this->addSectionSubTitle(__('Préjudice annexe'));
            }
            $content = $this->formatText($content);
            $this->addContent($content, 12);
        }
    }

    /**
     * @param DossierExpertise|null $expertise
     * @return void
     */
    protected function addExaminationConditions(?DossierExpertise $expertise = null)
    {
        if ($expertise === null) {
            $expertise = $this->getExpertise();
        }
        if (!$expertise->getExpertiseVehicle()) {
            return;
        }

        $missionType = $expertise->getDossier()->getMissionType();
        $reportType = $missionType?->getReport() ?: ExpertiseEnum::REPORT_STANDARD;

        $this->addSectionSubTitle(__('Conditions d\'examen'));

        $content = '
        <table>
            <tr>
                <td style="width: 50%;">
                    <table>';
        if ($expertise->getExpertiseVehicle()->getPresented() != 'unsure') {
            $content .= '<tr><td><span class="font-weight-bolder mr-4">Véhicule présenté :</span></td><td><input type="checkbox" ' . ($expertise->getExpertiseVehicle()->getPresented() == 'yes' ? 'checked' : '') . '> Oui<input type="checkbox" class="ml-4" ' . ($expertise->getExpertiseVehicle()->getPresented() == 'no' ? 'checked' : '') . '> Non</td></tr>';
        }
        if ($expertise->getExpertiseVehicle()->getRolling() != 'unsure') {
            $content .= '<tr><td><span class="font-weight-bolder mr-4">Véhicule roulant :</span></td><td><input type="checkbox" ' . ($expertise->getExpertiseVehicle()->getRolling() == 'yes' ? 'checked' : '') . '> Oui<input type="checkbox" class="ml-4" ' . ($expertise->getExpertiseVehicle()->getRolling() == 'no' ? 'checked' : '') . '> Non</td></tr>';
        }
        if ($reportType != ExpertiseEnum::REPORT_APPRAISED_VALUE) {
            if ($expertise->getExpertiseVehicle()->getImmobilisedNotDismantled() != 'unsure') {
                $content .= '<tr><td><span class="font-weight-bolder mr-4">Véhicule immobilisé :</span></td><td><input type="checkbox" ' . ($expertise->getExpertiseVehicle()->getImmobilisedNotDismantled() == 'yes' ? 'checked' : '') . '> Oui<input type="checkbox" class="ml-4" ' . ($expertise->getExpertiseVehicle()->getImmobilisedNotDismantled() == 'no' ? 'checked' : '') . '> Non</td></tr>';
            }
            if ($expertise->getExpertiseVehicle()->getOnLift() != 'unsure') {
                $content .= '<tr><td><span class="font-weight-bolder mr-4">Véhicule sur pont élévateur :</span></td><td><input type="checkbox" ' . ($expertise->getExpertiseVehicle()->getOnLift() == 'yes' ? 'checked' : '') . '> Oui<input type="checkbox" class="ml-4" ' . ($expertise->getExpertiseVehicle()->getOnLift() == 'no' ? 'checked' : '') . '> Non</td></tr>';
            }
            if ($expertise->getExpertiseVehicle()->getRepaired() != 'unsure') {
                $content .= '<tr><td><span class="font-weight-bolder mr-4">Véhicule réparé :</span></td><td><input type="checkbox" ' . ($expertise->getExpertiseVehicle()->getRepaired() == 'yes' ? 'checked' : '') . '> Oui<input type="checkbox" class="ml-4" ' . ($expertise->getExpertiseVehicle()->getRepaired() == 'no' ? 'checked' : '') . '> Non</td></tr>';
            }
            if ($expertise->getExpertiseVehicle()->getDismantled() != 'unsure') {
                $content .= '<tr><td><span class="font-weight-bolder mr-4">Véhicule démonté :</span></td><td><input type="checkbox" ' . ($expertise->getExpertiseVehicle()->getDismantled() == 'yes' ? 'checked' : '') . '> Oui<input type="checkbox" class="ml-4" ' . ($expertise->getExpertiseVehicle()->getDismantled() == 'no' ? 'checked' : '') . '> Non</td></tr>';
            }
            if ($expertise->getExpertiseVehicle()->getPartiallyDismantled() != 'unsure') {
                $content .= '<tr><td><span class="font-weight-bolder mr-4">Véhicule partiellement démonté :</span></td><td><input type="checkbox" ' . ($expertise->getExpertiseVehicle()->getPartiallyDismantled() == 'yes' ? 'checked' : '') . '> Oui<input type="checkbox" class="ml-4" ' . ($expertise->getExpertiseVehicle()->getPartiallyDismantled() == 'no' ? 'checked' : '') . '> Non</td></tr>';
            }
            if ($expertise->getDossier()->getVehicle()->getReassembled() != 'unsure') {
                $content .= '<tr><td><span class="font-weight-bolder mr-4">Véhicule remonté :</span></td><td><input type="checkbox" ' . ($expertise->getDossier()->getVehicle()->getReassembled() == 'yes' ? 'checked' : '') . '> Oui<input type="checkbox" class="ml-4" ' . ($expertise->getDossier()->getVehicle()->getReassembled() == 'no' ? 'checked' : '') . '> Non</td></tr>';
            }
            if ($expertise->getExpertiseVehicle()->getEngineWorking() and $expertise->getExpertiseVehicle()->getEngineWorking() != 'unsure') {
                $content .= '<tr><td><span class="font-weight-bolder mr-4">Moteur démarre :</span></td><td><input type="checkbox" ' . ($expertise->getExpertiseVehicle()->getEngineWorking() == 'yes' ? 'checked' : '') . '> Oui<input type="checkbox" class="ml-4" ' . ($expertise->getExpertiseVehicle()->getEngineWorking() == 'no' ? 'checked' : '') . '> Non</td></tr>';
            }
            if ($expertise->getDossier()->getVehicle()->getDraining() != 'unsure') {
                $content .= '<tr><td><span class="font-weight-bolder mr-4">Vidange des fluides et filtres réalisée :</span></td><td><input type="checkbox" ' . ($expertise->getDossier()->getVehicle()->getDraining() == 'yes' ? 'checked' : '') . '> Oui<input type="checkbox" class="ml-4" ' . ($expertise->getDossier()->getVehicle()->getDraining() == 'no' ? 'checked' : '') . '> Non</td></tr>';
            }
            if ($expertise->getDossier()->getVehicle()->getDrainingComment()) {
                $content .= '<tr><td><span class="font-weight-bolder mr-4">Précisions :</span></td><td>' . $expertise->getDossier()->getVehicle()->getDrainingComment() . '</td></tr>';
            }
            if ($expertise->getExpertiseVehicle()->getPartExamination() != 'unsure') {
                $content .= '<tr><td><span class="font-weight-bolder mr-4">Examen sur pièce :</span></td><td><input type="checkbox" ' . ($expertise->getExpertiseVehicle()->getPartExamination() == 'yes' ? 'checked' : '') . '> Oui<input type="checkbox" class="ml-4" ' . ($expertise->getExpertiseVehicle()->getPartExamination() == 'no' ? 'checked' : '') . '> Non</td></tr>';
            }
        }
        $content .= '
                    </table>
                </td>
                <td style="width: 10%;"></td>
                <td style="width: 40%;">';

        if ($expertise->getExpertiseVehicle()->getMeter()) {
            $content .= '
                <table class="table">
                    <tr>
                        <td style="width: 100%; text-align: center; height: 30px"><strong>' . __('Compteur') . '</strong></td>
                    </tr>
                    <tr>
                        <td style="width: 100%; text-align: center; height: 50px; vertical-align: middle;"><div class="py-2 px-4 border w-250px m-auto text-center">' . $expertise->getExpertiseVehicle()->getMeter() . ' ' . DossierExpertiseVehicleService::getMeterType($expertise->getExpertiseVehicle()->getMeterType()) . ' ' . DossierExpertiseVehicleService::getMeterRead($expertise->getExpertiseVehicle()->getMeterRead()) . '</div></td>
                    </tr>';

            if ($this->withPictures) {
                $content .= '
                    <tr>
                        <td style="width: 100%;"><div class="text-center">' . ($expertise->getPicture(DossierExpertisePicture::TYPE_METER) ? '<img class="img-fluid" src="' . $expertise->getPicture(DossierExpertisePicture::TYPE_METER)->getUrl() . '">' : '') . '</div></td>
                    </tr>';
            }

            $content .= '</table>';
        }

        $content .= '
                </td>
            </tr>
        </table>';
        $this->addContent($content, 12);

        if ($expertise->getExpertiseVehicle()->getExaminationCondition()) {
            $this->addSectionSubTitle(__('Précisions sur les conditions d\'examen'));
            $content = '<p>' . $this->formatText($expertise->getExpertiseVehicle()->getExaminationCondition()) . '</p>';
            $this->addContent($content, 12);
        }

        if ($expertise->getExpertiseVehicle()->getIdentificationComment()) {
            $this->addSectionSubTitle(__('Commentaire sur l\'identification'));
            $content = '<p>' . $this->formatText($expertise->getExpertiseVehicle()->getIdentificationComment()) . '</p>';
            $this->addContent($content, 12);
        }
    }

    /**
     * @param DossierExpertiseObservations[] $observations
     * @return void
     */
    protected function addObservations(array $observations)
    {
        $observationsByDate = DossierExpertiseObservationsService::sortObservations($observations);

        $dossier = $this->getDossier();
        $content = '';
        foreach ($observationsByDate as $date => $observations) {
            $content .= '<div class="mb-12">';
            $content .= '<p><strong><u>' . __('Constatation du %s', dateFr($date)) . '</u></strong></p>';

            foreach ($observations as $observation) {
                if (str_contains($observation->getComment(), '<p')) {
                    $content .= $observation->getComment();
                } else {
                    $content .= '<p>' . $observation->getComment() . '</p>';
                }
                if ($observation->getFile() and $this->withPictures) {
                    $size = $this->getImageSize(WEB_PATH . '/medias/' . $dossier->getFolder() . $observation->getFile(), null, 500);

                    $content .= '<div class="mt-0 mb-12 text-center">';
                    $content .= '<img class="img-fluid" src="' . Assets::getMediaUrl($dossier->getFolder() . $observation->getFile()) . '" ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : '') . '>';
                    if ($observation->getLegend()) {
                        $content .= '<p class="text-center mt-6"><em>' . $observation->getLegend() . '</em></p>';
                    }
                    $content .= '</div>';
                }
                if ($observation->getFile2() and $this->withPictures) {
                    $size = $this->getImageSize(WEB_PATH . '/medias/' . $dossier->getFolder() . $observation->getFile2(), null, 500);

                    $content .= '<div class="mt-0 mb-12 text-center">';
                    $content .= '<img class="img-fluid" src="' . Assets::getMediaUrl($dossier->getFolder() . $observation->getFile2()) . '" ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : '') . '>';
                    if ($observation->getLegend2()) {
                        $content .= '<p class="text-center mt-6"><em>' . $observation->getLegend2() . '</em></p>';
                    }
                    $content .= '</div>';
                }
            }

            $content .= '</div>';
        }

        $this->addContent($content, 20);
    }

    protected function addFollowups(array $tableOfContents = [])
    {
        $expertise = $this->getExpertise();
        if (!$expertise->getFollowup()) {
            return;
        }

        if ($tableOfContents) {
            $index = DossierExpertiseJudiciaireTableOfContentsFactory::findIndex($tableOfContents, 'Pièces demandées aux parties');
            $this->addSectionTitle(__('%s. Pièces demandées aux parties', $index));
        } else {
            $this->addSection(__('Suites à donner'));
        }

        $this->addContent($this->formatText($expertise->getFollowup()), 20);
    }

    protected function addPositions()
    {
        $expertise = $this->getExpertise();
        if (!$expertise->getPositions()) {
            return;
        }
        $this->addSection(__('Position des parties'), $this->formatText($expertise->getPositions()), 20);
    }

    protected function addConclusions(array $tableOfContents = [])
    {
        $expertise = $this->getExpertise();
        $report = $expertise->getExpertiseReport();
        if ($report and $report->getConclusions()) {
            $title = __('Conclusions');
            if ($report->getType() == DossierExpertiseReport::TYPE_PRELIMINARY) {
                $title = __('Conclusions provisoires');
            }

            if ($tableOfContents) {
                $index = DossierExpertiseJudiciaireTableOfContentsFactory::findIndex($tableOfContents, $title);
                $title = __('%s. %s', $index, $title);
            }

            $this->addSection($title, $this->formatText($report->getConclusions()), 20);
        }
    }

    protected function addTechnicalAnalyses()
    {
        $dossier = $this->getDossier();
        $expertise = $this->getExpertise();
        $report = $expertise->getExpertiseReport();
        if (!$report) {
            return;
        }

        if ($expertise->getStatus() == DossierExpertise::STATUS_CANCELED and !$report->getTechnicalAnalyses() and !$report->getCauses() and !$report->getConsequences() and !$report->getResponsibilities()) {
            return;
        }

        $container = ContainerBuilderService::getInstance();

        $this->addSectionTitle('Analyses et responsabilités');

        $this->addSectionSubTitle(__('Analyses techniques'));
        $content = ($report->getTechnicalAnalyses() ?: '<p><em>' . __('Aucune analyse technique indiquée') . '</em></p>');
        if ($report->getTechnicalAnalyses()) {
            $documents = $container->get(DossierDocumentService::class)->getRepository()->findBy(['expertise' => $expertise, 'type' => DossierDocument::TYPE_TECHNICAL_ANALYSIS]);
            if ($documents) {
                foreach ($documents as $document) {
                    $size = $this->getImageSize($document->getFullPath(), null, 700);
                    $content .= '<div class="mt-4 mb-4 text-center">';
                    $content .= '<img class="img-fluid" src="' . $document->getUrl() . '" ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : '') . '>';
                    $content .= '</div>';
                }
            }
        }
        $this->addContent($content, 20);

        if ($report->getCauses() or $report->getCausesPictures()) {
            $this->addSectionSubTitle('Causes');
            $content = $report->getCauses();
            if ($report->getCausesPictures() and $this->withPictures) {
                foreach ($report->getCausesPictures() as $pictureId) {
                    $picture = $container->get(DossierExpertisePictureService::class)->getRepository()->findOneBy(['id' => $pictureId, 'expertise' => $expertise, 'client' => $expertise->getClient()]);
                    if ($picture) {
                        $size = $this->getImageSize($picture->getFullPath(), null, 700);

                        $content .= '<div class="mt-4 mb-4 text-center">';
                        $content .= '<img class="img-fluid" src="' . $picture->getUrl() . '" ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : '') . '>';
                        $content .= '<p class="text-center mt-6"><em>' . ($picture->getName() ?: DossierExpertisePictureService::getType($picture->getType())) . '</em></p>';
                        $content .= '</div>';
                    }
                }
            }

            $this->addContent($content, 20);
        }

        if ($report->getConsequences() or $report->getConsequencesPictures()) {
            $this->addSectionSubTitle('Conséquences');
            $content = $report->getConsequences();
            if ($report->getConsequencesPictures() and $this->withPictures) {
                foreach ($report->getConsequencesPictures() as $pictureId) {
                    $picture = $container->get(DossierExpertisePictureService::class)->getRepository()->findOneBy(['id' => $pictureId, 'expertise' => $expertise, 'client' => $expertise->getClient()]);
                    if ($picture) {
                        $size = $this->getImageSize($picture->getFullPath(), null, 700);

                        $content .= '<div class="mt-4 mb-4 text-center">';
                        $content .= '<img class="img-fluid" src="' . $picture->getUrl() . '" ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : '') . '>';
                        $content .= '<p class="text-center mt-6"><em>' . ($picture->getName() ?: DossierExpertisePictureService::getType($picture->getType())) . '</em></p>';
                        $content .= '</div>';
                    }
                }
            }

            $this->addContent($content, 20);
        }

        if ($report->getResponsibilities()) {
            $this->addSectionSubTitle(__('Responsabilités'));
            $content = $report->getResponsibilities();
            $this->addContent($content, 20);

            $contentCompliance = '';
            if ($expertise->getMaintenanceCompliance() != 'not_set' or $expertise->getBreakdownCause() != 'not_set') {
                $contentCompliance .= '<table class="table">';
                if ($expertise->getMaintenanceCompliance() != 'not_set') {
                    $contentCompliance .= '<tr style="vertical-align: top"><td><span class="font-weight-bolder mr-4">Respect des entretiens et échéancier constructeur :</span></td><td style="width:125px"><input type="checkbox" ' . ($expertise->getMaintenanceCompliance() == 'yes' ? 'checked' : '') . '> Oui<input type="checkbox" class="ml-4" ' . ($expertise->getMaintenanceCompliance() == 'no' ? 'checked' : '') . '> Non</td>';
                    $contentCompliance .= '<td><span>' . $expertise->getMaintenanceComplianceComment() . '</span></td></tr>';
                }
                if ($expertise->getBreakdownCause() != 'not_set') {
                    $contentCompliance .= '<tr style="vertical-align: top"><td><span class="font-weight-bolder mr-4">Origine de la panne constatée :</span></td><td style="width:125px"><input type="checkbox" ' . ($expertise->getBreakdownCause() == 'yes' ? 'checked' : '') . '> Oui<input type="checkbox" class="ml-4" ' . ($expertise->getBreakdownCause() == 'no' ? 'checked' : '') . '> Non</td>';
                    $contentCompliance .= '<td><span>' . $expertise->getBreakdownCauseComment() . '</span></td></tr>';
                }
                $contentCompliance .= '</table>';
            }

            $displayDamageAttribution = false;
            $damageAttribution = $expertise->getDamageAttribution();
            $damageAttributionTypes = DossierExpertiseService::getDamageAttributions();
            if ($damageAttribution) {
                foreach ($damageAttributionTypes as $key => $value) {
                    if (isset($damageAttribution[$key]) and $damageAttribution[$key] == 'yes') {
                        $displayDamageAttribution = true;
                        break;
                    }
                }
            }

            $contentDamageAttribution = '';
            if ($displayDamageAttribution) {
                $contentDamageAttribution .= '<p><strong>Imputabilité des dommages</strong></p>';
                $contentDamageAttribution .= '<table class="table">';
                foreach ($damageAttributionTypes as $key => $value) {
                    if (!isset($damageAttribution[$key]) or $damageAttribution[$key] != 'yes') {
                        continue;
                    }
                    $contentDamageAttribution .= '<tr><td style="width: 200px;"><span class="font-weight-bolder mr-4">' . $value . '</span></td><td>' . $damageAttribution[$key . '_comment'] . '</td></tr>';
                }
                $contentDamageAttribution .= '</table>';
            }

            if ($contentCompliance) {
                $this->addContent($contentCompliance, 10);
            }
            if ($contentDamageAttribution) {
                $this->addContent($contentDamageAttribution, 10);
            }
        }
    }

    /**
     * @param DossierExpertise|null $expertise
     * @return void
     */
    protected function addLevelMeters(?DossierExpertise $expertise = null): void
    {
        if ($expertise === null) {
            $expertise = $this->getExpertise();
        }
        if (!$expertise->getExpertiseVehicle()) {
            return;
        }

        if ($expertise->getExpertiseVehicle()->getLevelIgnore() and !$expertise->getExpertiseVehicle()->getLevelComment()) {
            return;
        }

        $this->addSectionSubTitle(__('Niveaux à l\'expertise'));

        $content = '';
        if (!$expertise->getExpertiseVehicle()->getLevelIgnore()) {
            $nbMeters = 2;
            $hasLevelBrake = false;
            $hasLevelFuel = false;
            if ($expertise->getExpertiseVehicle()->getLevelBrake() or $expertise->getExpertiseVehicle()->getLevelBrakeUnreadable()) {
                $nbMeters++;
                $hasLevelBrake = true;
            }
            if ($expertise->getExpertiseVehicle()->getLevelBrake() or $expertise->getExpertiseVehicle()->getLevelFuel()) {
                $nbMeters++;
                $hasLevelFuel = true;
            }

            $size = round(100 / $nbMeters);

            $content = '
            <table class="table">
                <tr>
                    <td style="width: ' . $size . '%; text-align: center; height: 30px"><strong>' . __('Niveau d\'huile moteur') . '</strong></td>
                    <td style="width: ' . $size . '%; text-align: center; height: 30px"><strong>' . __('Niveau du liquide de refroidissement') . '</strong></td>
                    ' . ($hasLevelBrake ? '<td style="width: ' . $size . '%; text-align: center; height: 30px"><strong>' . __('Niveau du liquide de frein') . '</strong></td>' : '') . '
                    ' . ($hasLevelFuel ? '<td style="width: ' . $size . '%; text-align: center; height: 30px"><strong>' . __('Niveau de carburant') . '</strong></td>' : '') . '
                </tr>
                <tr>';

            //level engine oil
            $content .= '<td style="width: ' . $size . '%; text-align: center; height: 50px; vertical-align: middle;">';
            if ($expertise->getExpertiseVehicle()->getLevelEngineOilUnreadable()) {
                $content .= '<p class="mb-0 text-center">' . __('Le niveau d\'huile n\'est pas lisible à la jauge') . '</p>';
            } else {
                $content .= $this->getLevelMeter($expertise->getExpertiseVehicle()->getLevelEngineOil());
            }
            $content .= '</td>';

            //level coolant
            $content .= '<td style="width: ' . $size . '%; text-align: center; height: 50px; vertical-align: middle;">';
            if ($expertise->getExpertiseVehicle()->getLevelCoolantUnreadable()) {
                $content .= '<p class="mb-0 text-center">' . __('Le niveau du liquide de refroidissement ne peut être observé dans le vase d\'expansion') . '</p>';
            } else {
                $content .= $this->getLevelMeter($expertise->getExpertiseVehicle()->getLevelCoolant());
            }
            $content .= '</td>';

            //level brake
            if ($hasLevelBrake) {
                $content .= '<td style="width: ' . $size . '%; text-align: center; height: 50px; vertical-align: middle;">';
                if ($expertise->getExpertiseVehicle()->getLevelBrakeUnreadable()) {
                    $content .= '<p class="mb-0 text-center">' . __('Le niveau du liquide de frein n\'est pas visible dans le bocal') . '</p>';
                } else {
                    $content .= $this->getLevelMeter($expertise->getExpertiseVehicle()->getLevelBrake());
                }
                $content .= '</td>';
            }

            //level fuel
            if ($hasLevelFuel) {
                $content .= '<td style="width: ' . $size . '%; text-align: center; height: 50px; vertical-align: middle;">';
                $content .= $this->getLevelMeter($expertise->getExpertiseVehicle()->getLevelFuel(), 'fuel');
                $content .= '</td>';
            }

            $content .= '</tr>';

            if ($this->withPictures) {
                $content .= '
                <tr>
                    <td style="width: ' . $size . '%;"><div class="text-center">' . ($expertise->getPicture(DossierExpertisePicture::TYPE_ENGINE_OIL_LEVEL) ? '<img class="img-fluid" src="' . $expertise->getPicture(DossierExpertisePicture::TYPE_ENGINE_OIL_LEVEL)->getUrl() . '">' : '') . '</div></td>
                    <td style="width: ' . $size . '%;"><div class="text-center">' . ($expertise->getPicture(DossierExpertisePicture::TYPE_COOLANT_LEVEL) ? '<img class="img-fluid" src="' . $expertise->getPicture(DossierExpertisePicture::TYPE_COOLANT_LEVEL)->getUrl() . '">' : '') . '</div></td>
                    ' . ($hasLevelBrake ? '<td style="width: ' . $size . '%;"><div class="text-center">' . ($expertise->getPicture(DossierExpertisePicture::TYPE_BRAKE_LEVEL) ? '<img class="img-fluid" src="' . $expertise->getPicture(DossierExpertisePicture::TYPE_BRAKE_LEVEL)->getUrl() . '">' : '') . '</div></td>' : '') . '
                    ' . ($hasLevelFuel ? '<td style="width: ' . $size . '%;"><div class="text-center">' . ($expertise->getPicture(DossierExpertisePicture::TYPE_FUEL_LEVEL) ? '<img class="img-fluid" src="' . $expertise->getPicture(DossierExpertisePicture::TYPE_FUEL_LEVEL)->getUrl() . '">' : '') . '</div></td>' : '') . '
                </tr>';
            }

            $content .= '</table>';
        }

        if ($expertise->getExpertiseVehicle()->getLevelComment()) {
            $content .= '<p><strong>' . __('Commentaire sur les niveaux') . '</strong></p>';
            $content .= '<p>' . nl2br($expertise->getExpertiseVehicle()->getLevelComment()) . '</p>';
        }

        $this->addContent($content, 12);
    }

    /**
     * @param int|null $value
     * @param string|null $type
     * @return string
     */
    protected function getLevelMeter(?int $value = null, ?string $type = null): string
    {
        $content = '<table style="margin: 0 auto;"><tr>';

        $width = 20;
        $content .= '<td class="text-center border-0 p-0" style="height: ' . $width . 'px; width: ' . $width . 'px"><div style="height: ' . $width . 'px; width: ' . $width . 'px; padding-top: 3px">-</div></td>';

        for ($i = 1; $i <= 11; $i++) {
            $color = 'red';
            if ($i >= 4 and $i <= 8) {
                $color = '#00FF00';
            }
            if ($type === 'fuel' and $i >= 4) {
                $color = '#00FF00';
            }

            $columnContent = '';
            if ($value == $i) {
                $columnContent = '<i class="fas fa-circle" style="font-size: 12px"></i>';
            }
            $content .= '
            <td class="border border-dark text-center p-0" style="height: ' . $width . 'px; width: ' . $width . 'px; background: ' . $color . '">
                <div style="height: ' . $width . 'px; width: ' . $width . 'px; background: ' . $color . '; padding-top: 0">' . $columnContent . '</div>
            </td>';
        }
        $content .= '<td class="text-center border-0 p-0" style="height: ' . $width . 'px; width: ' . $width . 'px"><div style="height: ' . $width . 'px; width: ' . $width . 'px; padding-top: 3px">+</div></td>';
        $content .= '</tr></table>';

        return $content;
    }

    /**
     * @param array $persons
     * @return void
     */
    protected function addTableEnd(array $persons)
    {
        $this->addSectionTitle(__('Signatures'));

        $columns = ['Nom', 'Signature'];
        $data = [];
        foreach ($persons as $person) {
            if ($person->getStatus() != DossierExpertisePerson::STATUS_PRESENT) {
                continue;
            }

            $signature = '';
            if ($person->getHasLeft()) {
                $signature = '<em>' . __('A quitté l\'expertise') . '</em>';
            } elseif ($person->getSignatureRefused()) {
                $signature = '<em>' . __('A refusé de signer') . '</em>';
            } elseif ($person->getSignature()) {
                $signature = '<img class="img-fluid" src="' . $person->getSignature()->getUrl() . '">';
                $signature .= '<small><em>' . $person->getSignature()->getDate()->format('d/m/Y H:i') . '</em></small>';
            }

            $data[] = [
                $person->getRepresentativeName(),
                $signature
            ];
        }

        if ($data) {
            $this->addTable($columns, $data, [75, 25]);
        }
    }

    /**
     * @param User $user
     * @param int $margin
     * @return void
     */
    protected function addExpertSignatureEnd(User $user, int $margin = 20)
    {
        $content = '<div class="mt-' . $margin . ' text-right w-100">';
        $content .= '<p class="mb-8">' . __('L\'expert : %s', $user->getFirstName() . ' ' . $user->getLastName());
        if ($user->getExpertNumber()) {
            $content .= '<br>' . __('N° %s', $user->getExpertNumber());
        }
        $content .= '</p>';

        if ($user->getUserConfig(ConfigEnum::SIGNATURE)) {
            $content .= '<img class="max-h-120px" src="' . $user->getUserConfig(ConfigEnum::SIGNATURE) . '">';
        }
        $content .= '</div>';
        $this->addContent($content);
    }

    /**
     * @param int $margin
     * @return void
     */
    protected function addSignatureEnd(int $margin = 20)
    {
        $clientInfos = $this->getClientInfos();
        $expert = $this->getExpert();

        $content = '<div class="mt-' . $margin . ' text-right w-100">';
        $content .= '<p class="mb-8">' . __('L\'expert : %s', ($expert ? $expert->getFirstName() . ' ' . $expert->getLastName() : $clientInfos['name']));
        if ($expert and $expert->getExpertRegistrationNumber()) {
            $content .= '<br>' . __('N° %s', $expert->getExpertRegistrationNumber());
        }
        $content .= '</p>';

        if ($clientInfos[ConfigEnum::SIGNATURE]) {
            $content .= '<img class="max-h-120px" src="' . $clientInfos[ConfigEnum::SIGNATURE] . '">';
        }
        $content .= '</div>';
        $this->addContent($content);
    }

    protected function addExpertisePictures()
    {
        $content = '';
        $expertise = $this->getExpertise();
        $report = $expertise->getExpertiseReport();
        if (!$report) {
            return;
        }
        if (!$this->withPictures) {
            return;
        }

        $pictures = $report->getPictures();
        if ($pictures) {
            $container = ContainerBuilderService::getInstance();
            foreach ($pictures as $pictureId) {
                $picture = $container->get(DossierExpertisePictureService::class)->getRepository()->findOneBy(['id' => $pictureId, 'expertise' => $expertise, 'client' => $expertise->getClient()]);
                if ($picture) {
                    $size = $this->getImageSize($picture->getFullPath());

                    $content .= '<div class="mb-10 text-center">';
                    $content .= '<img class="img-fluid" src="' . $picture->getUrl() . '" ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : '') . '>';
                    $content .= '<p class="text-center mt-6"><em>' . ($picture->getName() ?: DossierExpertisePictureService::getType($picture->getType())) . '</em></p>';
                    $content .= '</div>';
                }
            }
        }

        if ($content) {
            $this->addNewPage();
            $this->addSectionTitle(__('Photographies supplémentaires'));
            $this->addContent($content);
        }
    }

    /**
     * @param string $image
     * @param int|null $maxWidth
     * @param int|null $maxHeight
     * @return array|null
     */
    protected function getImageSize(string $image, ?int $maxWidth = null, ?int $maxHeight = null): ?array
    {
        if (filter_var($image, FILTER_VALIDATE_URL)) {
            $data = parse_url($image);
            $image = WEB_PATH . $data['path'];
        }
        if (!str_contains($image, WEB_PATH)) {
            $image = WEB_PATH . '/medias/' . $image;
        }

        if (!file_exists($image)) {
            return null;
        }

        $pathInfo = pathinfo($image);
        if ($pathInfo['extension'] === 'jpg' or $pathInfo['extension'] === 'jpeg') {
            $exif = @exif_read_data($image);
            if ($exif and isset($exif['Orientation'])) {
                $orientation = $exif['Orientation'];
                if ($orientation != 1) {
                    $deg = 0;
                    switch ($orientation) {
                        case 3:
                            $deg = 180;
                            break;
                        case 6:
                            $deg = 270;
                            break;
                        case 8:
                            $deg = 90;
                            break;
                    }
                    if ($deg) {
                        $img = imagecreatefromstring(file_get_contents($image));
                        $img = imagerotate($img, $deg, 0);
                        imagejpeg($img, $image);
                    }
                }
            }
        }

        $size = @getimagesize($image);
        if (!$size) {
            return null;
        }

        $imageWidth = $size[0];
        $imageHeight = $size[1];

        $maxWidth = ($maxWidth === null ? self::MAX_WIDTH : $maxWidth);
        $maxHeight = ($maxHeight === null ? self::MAX_HEIGHT : $maxHeight);

        if ($imageWidth > $maxWidth) {
            $ratio = $imageWidth / $maxWidth;
            $imageWidth = $maxWidth;
            $imageHeight = round($imageHeight / $ratio);
        }

        if ($imageHeight > $maxHeight) {
            $ratio = $imageHeight / $maxHeight;
            $imageHeight = $maxHeight;
            $imageWidth = round($imageWidth / $ratio);
        }

        return ['width' => $imageWidth, 'height' => $imageHeight];
    }

    protected function addNewPage()
    {
        $this->addContent('<div class="page"></div>');
    }

    /**
     * @param array|null $selectedDocuments
     * @param array|null $documentsToRemove
     * @param bool $addNewPage
     * @return void
     */
    protected function addDocuments(?array $selectedDocuments = null, ?array $documentsToRemove = null, bool $addNewPage = true, string $title = '', bool $displayTitle = true, bool $appendices = false)
    {
        $expertise = $this->getExpertise();
        $dossier = $expertise->getDossier();
        $person = $this->getPerson();

        $documents = $expertise->getDossier()->getDocuments();
        $data = array();
        $i = 0;
        foreach ($documents as $document) {
            if ($documentsToRemove and in_array($document->getType(), $documentsToRemove)) {
                continue;
            }
            if ($selectedDocuments !== null and !in_array($document->getId(), $selectedDocuments)) {
                continue;
            }
            if ($document->getVirusScan() == DossierDocument::VIRUS_SCAN_ALERT) {
                continue;
            }

            if ($person and $document->getPerson() !== $person) {
                continue;
            }
            /*if ($document->getExpertise() and $document->getExpertise() !== $expertise) {
                continue;
            }*/

            $fullPath = WEB_PATH . '/medias/' . $dossier->getFolder();
            $extension = getExtension($document->getFile());
            $documentName = ($document->getName() ?: DossierDocumentService::getType($document->getType()));
            if (!file_exists($fullPath . $document->getFile())) {
                continue;
            }

            if ($appendices) {
                $i++;
                $documentName = __('Annexe %d', $i) . '. ' . $documentName;
            }

            if ($extension == 'pdf') {
                $images = ImagickHelper::convertPdf($fullPath . $document->getFile(), false);
                foreach ($images as $image) {
                    $data[] = [$documentName, Assets::getMediaUrl($image)];
                }
            } else {
                $data[] = [$documentName, $document->getUrl()];
            }
        }

        if ($data) {
            if ($addNewPage) {
                $this->addNewPage();
            }
            if ($displayTitle) {
                $this->addSection($title ?: 'Documents');
            }
            $this->addTablePictures($data);
        }
    }

    /**
     * @param string $documentContent
     * @param string $subject
     * @param bool $addExpertSignature
     * @param string $city
     * @param string $date
     * @param string $rightContent
     * @param bool $displayDate
     * @return void
     */
    protected function renderOnePageLayout(string $documentContent, string $subject = '', bool $addExpertSignature = true, string $city = '', string $date = '', string $rightContent = '', bool $displayDate = true)
    {
        $clientInfos = $this->getClientInfos();
        $dossier = $this->getDossier();
        if (!$city) {
            $city = $clientInfos[ConfigEnum::CITY];
        }
        if (!$date) {
            $date = date('d/m/Y');
        }

        $content = '';
        if ($dossier->getClient()->getClientConfig(ConfigEnum::REPORT_FONT_FAMILY)) {
            $content .= '<style>body { font-family: ' . $dossier->getClient()->getClientConfig(ConfigEnum::REPORT_FONT_FAMILY) . '; }</style>';
        }

        $fontSize = $dossier->getClient()->getClientConfig(ConfigEnum::REPORT_FONT_SIZE);
        if ($fontSize) {
            $content .= '<style>body { font-size: ' . $fontSize . 'px !important; }</style>';
            $content .= '<style>p, .table thead tr td, .table thead tr th, .table tr td { font-size: ' . $fontSize . 'px; }</style>';
        }

        $content .= '<div class="d-flex flex-column" style="height: 100vh">';

        if ($this->getDisplayLogo() or $dossier->getClient()->getClientConfig(ConfigEnum::REPORT_RIGHT_CONTENT)) {
            $content .= '<div class="d-flex flex-row justify-content-between align-items-center mb-8">';
            $content .= '
                <div>
                    ' . ($this->getDisplayLogo() ? '<img class="img-fluid max-h-100px" src="' . $this->getHeaderLogo() . '">' : '') . '
                </div>';

            if ($dossier->getClient()->getClientConfig(ConfigEnum::REPORT_RIGHT_CONTENT)) {
                $content .= '
                    <div class="ml-auto">
                        <div class="without-margin">' . $dossier->getClient()->getClientConfig(ConfigEnum::REPORT_RIGHT_CONTENT) . '</div>
                    </div>';
            }

            $content .= '</div>';
            $content .= '<div class="w-100 mb-12" style="border-bottom: 1px solid #EBEDF3;"></div>';
        }

        $content .= '<div class="d-flex flex-row w-100">';

        //vehicle infos
        $vehicle = $this->getDossier()->getVehicle();
        $content .= '<div><p>';
        if (!$dossier->isJudiciaire()) {
            if ($dossier->getExpertReference()) {
                $content .= $this->formatText("N/REF : " . $this->getDossier()->getExpertReference()) . '<br>';
            }
        } else {
            if ($dossier->getCaseName()) {
                $content .= $this->formatText("Affaire : " . $this->getDossier()->getCaseName()) . '<br>';
            }
            $expertiseJudiciaire = $dossier->getExpertiseJudiciaire();
            if ($expertiseJudiciaire) {
                if ($expertiseJudiciaire->getExpertiseNumber()) {
                    $content .= $this->formatText("N° Expertise : " . $expertiseJudiciaire->getExpertiseNumber()) . '<br>';
                }
                if ($expertiseJudiciaire->getRgNumber()) {
                    $content .= $this->formatText(I18nService::getRGNumber() . ' : ' . $expertiseJudiciaire->getRgNumber()) . '<br>';
                }
                if ($expertiseJudiciaire->getMinuteNumber()) {
                    $content .= $this->formatText(I18nService::getMinuteNumber() . ' : ' . $expertiseJudiciaire->getMinuteNumber()) . '<br>';
                }
                if ($expertiseJudiciaire->getOrderDate()) {
                    $content .= $this->formatText(I18nService::getOrdonnanceDate() . " : " . $expertiseJudiciaire->getOrderDate()->format('d/m/Y')) . '<br>';
                }
                if ($expertiseJudiciaire->getCourtChamber()) {
                    $content .= $this->formatText("Chambre : " . $expertiseJudiciaire->getCourtChamber()) . '<br>';
                }
                if ($expertiseJudiciaire->getPortalisNumber()) {
                    $content .= $this->formatText("N° Portalis : " . $expertiseJudiciaire->getPortalisNumber()) . '<br>';
                }
            }
        }
        $content .= $this->formatText("Véhicule : " . $vehicle->getBrand() . " " . $vehicle->getModel()) . '<br>';
        $content .= $this->formatText("Immatriculation : " . $vehicle->getRegistration()) . '<br>';
        if (!$dossier->isJudiciaire()) {
            if ($vehicle->getSerialNumber()) {
                $content .= $this->formatText("N° série : " . $vehicle->getSerialNumber()) . '<br>';
            }
            if ($dossier->getContact() and $dossier->getContact()->getLegalProtectionNumber()) {
                $content .= $this->formatText("Sinistre : " . $dossier->getContact()->getLegalProtectionNumber()) . '<br>';
            }
            if ($dossier->getBreakdown() and $dossier->getBreakdown()->getDate()) {
                $content .= $this->formatText("Date sinistre : " . $dossier->getBreakdown()->getDate()->format('d/m/Y')) . '<br>';
            }
        }
        if ($this->getPerson() and $this->getPerson()->getInstitution() and $this->getPerson()->getInstitution()->getReference()) {
            $content .= $this->formatText("Numéro de référence : " . $this->getPerson()->getInstitution()->getReference()) . '<br>';
        }
        $content .= '</p></div>';


        $content .= '<div class="d-flex flex-column ml-auto pl-20">';
        if ($rightContent) {
            $content .= $rightContent;
        } else {
            if ($this->getPerson()) {
                $person = $this->getPerson();
                $name = $person->getLastName() . ' ' . $person->getFirstName();
                $isLawyer = false;
                if ($person->getType() == DossierExpertisePerson::TYPE_LAWYER or ($person->getInstitution() and $person->getInstitution()->getType() == DossierInstitution::TYPE_LAWYER)) {
                    $isLawyer = true;
                }
                $company = $person->getCompany();
                $address = $person->getAddress();
                $address2 = $person->getAddress2();
                $zip = $person->getZip();
                $personCity = $person->getCity();

                $content .= '<p>';

                if ($isLawyer) {
                    if ($company and $company != $name) {
                        $content .= $this->formatText($company) . '<br>';
                    }
                    if (trim($name) != '') {
                        if (!str_contains($name, 'Maître')) {
                            $name = __('Maître %s', $name);
                        }
                        $content .= $this->formatText($name) . '<br>';
                    }
                } else {
                    if ($company and $company != $name) {
                        $content .= $this->formatText($company) . '<br>';
                    }
                    if (trim($name) != '' and ($this->type != DossierDocument::TYPE_CONVOCATION or !$company or $company == $name)) {
                        $content .= $this->formatText($name) . '<br>';
                    }
                }

                if ($address) {
                    $content .= $this->formatText($address) . '<br>';
                }
                if ($address2) {
                    $content .= $this->formatText($address2) . '<br>';
                }
                if ($zip or $personCity) {
                    $content .= $this->formatText($zip . ' ' . $personCity) . '<br>';
                }
                $content .= '</p>';
            }
        }

        if ($displayDate) {
            $content .= '<p class="mt-4 mb-8">' . __('À %s, le %s', $city, $date) . '</p>';
        }
        $content .= '
                </div>
            </div>';


        //document content
        $content .= '<div class="mt-14">';
        if ($subject) {
            $content .= $subject;
        }
        $content .= $documentContent;
        $this->addContent($content);

        if ($addExpertSignature) {
            $this->addSignatureEnd(6);
        }

        $content = '</div>'; // close div class mt-14
        $this->addContent($content);

        $content = '<div class="d-flex flex-column justify-content-end h-100">';
        $content .= $this->getFooterCompanies();
        $content .= '</div>';
        $this->addContent($content);
    }

    public function getFiles(Dossier $dossier, string $file): array
    {
        $files = [];
        if (in_array(getExtension($file), ['jpg', 'jpeg', 'png', 'gif'])) {
            $files[] = Assets::getMediaUrl($dossier->getFolder() . $file . '?dossier_ref=' . $dossier->getReference());
        } elseif (getExtension($file) == 'pdf') {
            $fullPath = WEB_PATH . '/medias/' . $dossier->getFolder();
            $pathInfo = pathinfo($file);
            $fileName = $pathInfo['filename'] . '-thumbnail';

            $images = ImagickHelper::convertPdf($fullPath . $file, true);
            if ($images) {
                foreach ($images as $image) {
                    $files[] = Assets::getMediaUrl($image . '?dossier_ref=' . $dossier->getReference());
                }
            }
        }

        return $files;
    }
}
