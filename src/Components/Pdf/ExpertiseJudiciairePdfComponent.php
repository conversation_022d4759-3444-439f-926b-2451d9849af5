<?php
namespace MatGyver\Components\Pdf;

use MatGyver\Entity\Dossier\DossierDocument;
use MatGyver\Entity\Dossier\DossierInstitution;
use MatGyver\Entity\Dossier\DossierSignature;
use MatGyver\Entity\Dossier\DossierVehicle;
use MatGyver\Entity\Dossier\Expertise\DossierExpertise;
use MatGyver\Entity\Dossier\Expertise\DossierExpertiseJudiciaire;
use MatGyver\Entity\Dossier\Expertise\DossierExpertiseJudiciaireChronology;
use MatGyver\Entity\Dossier\Expertise\DossierExpertisePerson;
use MatGyver\Entity\Dossier\Expertise\DossierExpertiseReport;
use MatGyver\Enums\ExpertiseEnum;
use MatGyver\Factories\TableOfContents\DossierExpertiseJudiciaireTableOfContentsFactory;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\ImagickHelper;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Dossier\DossierVehicleService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseJudiciaireChronologyService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseJudiciaireDireService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseJudiciaireService;
use MatGyver\Services\Dossier\Expertise\DossierExpertisePersonService;
use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\TwigService;

class ExpertiseJudiciairePdfComponent extends AbstractPdfComponent
{
    private array $tableOfContents = [];

    /**
     * @param DossierExpertise $expertise
     * @throws \Exception
     */
    public function __construct(DossierExpertise $expertise)
    {
        $this->setDossier($expertise->getDossier());
        $this->setExpertise($expertise);
        $this->setClientInfos();
        $this->setType(DossierDocument::TYPE_EXPERTISE);
        parent::__construct();
    }

    public function getTableOfContents(): array
    {
        return $this->tableOfContents;
    }

    public function setTableOfContents(array $tableOfContents): void
    {
        $this->tableOfContents = $tableOfContents;
    }

    /**
     * @return string
     * @throws \Exception
     */
    public function render(): string
    {
        $dossier = $this->getDossier();
        if (!$dossier) {
            throw new \Exception(__('Dossier not specified.'));
        }

        $expertise = $this->getExpertise();
        if (!$expertise) {
            throw new \Exception(__('Expertise not specified.'));
        }

        $expert = $this->getDossier()->getExpert();
        if (!$expert) {
            throw new \Exception(__('Expert not specified.'));
        }

        $container = ContainerBuilderService::getInstance();
        $expertiseJudiciaire = $container->get(DossierExpertiseJudiciaireService::class)->getRepository()->findOneBy(['expertise' => $expertise]);
        if (!$expertiseJudiciaire) {
            throw new \Exception(__('Expertise judiciaire not specified.'));
        }

        $missionType = $dossier->getMissionType();
        $report = $this->getExpertise()->getExpertiseReport();
        $reportType = $missionType?->getReport() ?: ExpertiseEnum::REPORT_EXPERTISE_JUDICIAIRE;

        $displayDocuments = true;
        $displayPictures = true;
        if ($report) {
            $this->setWithPictures($report->getDisplayPictures());
            $displayDocuments = $report->getDisplayDocuments();
            $displayPictures = $report->getDisplayPictures();
        }

        $documents = ($report ? $report->getDocuments() : []);
        $this->setTableOfContents(DossierExpertiseJudiciaireTableOfContentsFactory::create($expertiseJudiciaire, $documents));

        $title = __('RAPPORT D\'EXPERTISE JUDICIAIRE');
        if ($report and $report->getType() == DossierExpertiseReport::TYPE_PRELIMINARY) {
            $title = __('PRÉ-RAPPORT D\'EXPERTISE JUDICIAIRE');
        }
        if ($report and $report->getType() == DossierExpertiseReport::TYPE_INFORMATION) {
            $title = __('NOTE TECHNIQUE');
        }

        $subTitle = __('Rapport d\'expertise judiciaire du %s', date('d/m/Y'));
        if ($report and $report->getType() == DossierExpertiseReport::TYPE_PRELIMINARY) {
            $subTitle = __('Pré-rapport d\'expertise judiciaire du %s', date('d/m/Y'));
        }
        if ($report and $report->getType() == DossierExpertiseReport::TYPE_INFORMATION) {
            $subTitle = __('Note technique du %s', date('d/m/Y'));
        }

        if ($reportType != ExpertiseEnum::REPORT_EXPERTISE_JUDICIAIRE) {
            LoggerService::logError('ExpertiseJudiciairePdfComponent::render : invalid report type for expertise judiciaire');
            return '';
        }

        $this->setTitle($title);

        $recipients = $this->getRecipients($report);
        $displayDate = true;
        if ($report and $report->getType() == DossierExpertiseReport::TYPE_PRELIMINARY and $expertise->getPreviousExpertises()) {
            $displayDate = false;
        }
        $this->addFirstPageJudiciaire($subTitle, $recipients, $displayDate);

        $this->addSectionTitle(__('1. Table des matières'));
        $this->addTableOfContents();

        $this->addIntroduction($expertiseJudiciaire);
        $this->addSection3($expertiseJudiciaire);

        $index = DossierExpertiseJudiciaireTableOfContentsFactory::findIndex($this->getTableOfContents(), 'Identification des parties');
        $this->addSectionTitle(__('%s. Identification des parties', $index));

        $persons = $expertise->getPersons()->toArray();
        $this->addTablePersons($persons, DossierExpertisePerson::STATUS_PRESENT, DossierSignature::TYPE_START);
        $this->addTablePersons($persons, DossierExpertisePerson::STATUS_ABSENT);

        $this->addAuditions();

        $this->addVehicleInfos($this->getTableOfContents());

        $this->addVehicleUsage();

        $this->addHistory($displayDocuments, false, $this->getTableOfContents());

        $this->addBreakdown($expertiseJudiciaire);
        $this->addExpertDiligences($expertiseJudiciaire);

        $this->addExpertiseInfos(true, $this->getTableOfContents());

        $this->addConservatoryMeasures($expertiseJudiciaire);
        $this->addExpertAnalysis($expertiseJudiciaire);
        $this->addEvaluations($this->getTableOfContents());
        $this->addDiresAnswers($expertiseJudiciaire);
        $this->addCourtQuestions($expertiseJudiciaire, $displayPictures);

        if ($report and $report->getConclusions()) {
            $this->addConclusions($this->getTableOfContents());
        }
        if ($report and $report->getType() == DossierExpertiseReport::TYPE_PRELIMINARY and $report->getObservationsPreReport()) {
            $this->addContent($report->getObservationsPreReport(), 20);
        } elseif ($report and $report->getType() == DossierExpertiseReport::TYPE_FINAL and $report->getObservationsFinalReport()) {
            $this->addContent($report->getObservationsFinalReport(), 20);
        }

        if ($report and $report->getExpert()) {
            $this->addExpertSignatureEnd($report->getExpert());
        } else {
            $this->addSignatureEnd();
        }

        if ($displayPictures) {
            $this->addExpertisePictures();
        }

        if ($displayDocuments) {
            $documents = $report->getDocuments();
            if ($documents === null) {
                $documents = []; //documents must not be null otherwise they will be all added
            }
            $this->addDocuments($documents, null, true, __('13. Pièces annexes'));
        }

        return $this->getContent();
    }

    /**
     * @return void
     */
    private function addTableOfContents(): void
    {
        $content = TwigService::getInstance()->set('dossier', $this->getDossier())
            ->set('expertise', $this->getExpertise())
            ->set('tableOfContents', $this->tableOfContents)
            ->render('app/dossier/pdf/expertise_judiciaire/table_of_contents.php');
        $this->addContent($content);
    }

    private function addIntroduction(DossierExpertiseJudiciaire $expertiseJudiciaire): void
    {
        if (!$expertiseJudiciaire->getCircumstances() and !count($expertiseJudiciaire->getCourtQuestions())) {
            return;
        }

        $this->addSectionTitle(__('2. Introduction'));
        $this->addContent($expertiseJudiciaire->getIntroduction(), 20);

        if ($expertiseJudiciaire->getCircumstances()) {
            $index = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($this->getTableOfContents(), 'Circonstances de l\'affaire');
            $this->addSectionSubTitle(__('%s. Circonstances de l\'affaire', $index));
            $this->addContent($expertiseJudiciaire->getCircumstances(), 20);
        }

        /*if ($expertiseJudiciaire->getExpertise()->getDossier()->getInterventionComment()) {
            $index = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($this->getTableOfContents(), 'Mission du tribunal');
            $this->addSectionSubTitle(__('%s. Mission du tribunal', $index));
            $this->addContent($expertiseJudiciaire->getExpertise()->getDossier()->getInterventionComment(), 20);
        }*/

        if (count($expertiseJudiciaire->getCourtQuestions())) {
            $index = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($this->getTableOfContents(), 'Libellé de la mission');
            $this->addSectionSubTitle(__('%s. Libellé de la mission', $index));

            $content = '<ul>';
            foreach ($expertiseJudiciaire->getCourtQuestions() as $question) {
                $content .= '<li>' . $question->getQuestion() . '</li>';
            }
            $content .= '</ul>';
            $this->addContent($content, 20);
        }

        if ($expertiseJudiciaire->getDeclarationIndependence()) {
            $index = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($this->getTableOfContents(), 'Déclaration d\'indépendance');
            $this->addSectionSubTitle(__('%s. Déclaration d\'indépendance', $index));
            $this->addContent($expertiseJudiciaire->getDeclarationIndependence(), 20);
        }
    }

    private function addSection3(DossierExpertiseJudiciaire $expertiseJudiciaire): void
    {
        $container = ContainerBuilderService::getInstance();
        $nbFacts = $container->get(DossierExpertiseJudiciaireChronologyService::class)->getRepository()->count(['expertise' => $expertiseJudiciaire->getExpertise(), 'type' => DossierExpertiseJudiciaireChronology::TYPE_CHRONOLOGY]);
        $nbDocumentsReceived = $container->get(DossierExpertiseJudiciaireChronologyService::class)->getRepository()->count(['expertise' => $expertiseJudiciaire->getExpertise(), 'type' => DossierExpertiseJudiciaireChronology::TYPE_DOCUMENT_RECEIVED]);
        $nbDocumentsSent = $container->get(DossierExpertiseJudiciaireChronologyService::class)->getRepository()->count(['expertise' => $expertiseJudiciaire->getExpertise(), 'type' => DossierExpertiseJudiciaireChronology::TYPE_DOCUMENT_SENT]);
        if (!$nbFacts and !$nbDocumentsReceived and !$nbDocumentsSent) {
            return;
        }

        $this->addSectionTitle(__('3. Déroulement administratif de l\'expertise'));

        if ($expertiseJudiciaire->getChronologyIntroduction()) {
            $this->addContent($expertiseJudiciaire->getChronologyIntroduction(), 20);
        }

        if ($nbFacts) {
            $index = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($this->getTableOfContents(), 'Chronologie de l\'expertise');
            $this->addChronology($expertiseJudiciaire, DossierExpertiseJudiciaireChronology::TYPE_CHRONOLOGY, __('%s. Chronologie de l\'expertise', $index));
        }

        if ($nbDocumentsReceived) {
            $index = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($this->getTableOfContents(), 'Déclarations et pièces reçues');
            $this->addChronology($expertiseJudiciaire, DossierExpertiseJudiciaireChronology::TYPE_DOCUMENT_RECEIVED, __('%s. Déclarations et pièces reçues', $index));
        }

        if ($nbDocumentsSent) {
            $index = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($this->getTableOfContents(), 'Documents communiqués aux parties');
            $this->addChronology($expertiseJudiciaire, DossierExpertiseJudiciaireChronology::TYPE_DOCUMENT_SENT, __('%s. Documents communiqués aux parties', $index));
        }
    }

    private function addAuditions()
    {
        $hasComment = false;
        $persons = $this->getExpertise()->getPersons()->toArray();
        foreach ($persons as $person) {
            if ($person->getComment()) {
                $hasComment = true;
                break;
            }
        }
        if (!$hasComment) {
            return;
        }

        $index = DossierExpertiseJudiciaireTableOfContentsFactory::findIndex($this->getTableOfContents(), 'Auditions des parties');
        $this->addSectionTitle(__('%s. Auditions des parties', $index));
        $this->addPersonsComments($persons);
    }

    private function addChronology(DossierExpertiseJudiciaire $expertiseJudiciaire, string $type, string $title): void
    {
        $this->addSectionSubTitle($title);

        $width = [15, 15, 15, 55];
        $columns = ['Date', 'Origine', 'Pièces n°', 'Objets'];
        if ($type == DossierExpertiseJudiciaireChronology::TYPE_CHRONOLOGY) {
            $columns = ['Date', 'Destinataire', 'Type', 'Objet'];
        }
        $data = [];
        $container = ContainerBuilderService::getInstance();
        $facts = $container->get(DossierExpertiseJudiciaireChronologyService::class)->getRepository()->findBy(['expertise' => $expertiseJudiciaire->getExpertise(), 'type' => $type], ['position' => 'ASC']);
        if ($facts) {
            $factsByDate = DossierExpertiseJudiciaireChronologyService::sort($facts);
            foreach ($factsByDate as $date => $facts) {
                /** @var DossierExpertiseJudiciaireChronology[] $facts */
                foreach ($facts as $fact) {
                    $factData = [
                        ($fact->getDate() ? $fact->getDate()->format('d/m/Y') : '')
                    ];

                    if ($fact->getPersons()) {
                        $names = '';
                        foreach ($fact->getPersons() as $person) {
                            $name = $person->getName();
                            if (!str_contains($name, 'Maître') && ($person->getType() == DossierExpertisePerson::TYPE_LAWYER or ($person->getInstitution() and $person->getInstitution()->getType() == DossierInstitution::TYPE_LAWYER))) {
                                $name = __('Maître %s', $person->getName());
                            }
                            $names .= $name . '<br>';
                        }
                        $factData[] = $names;
                    } else {
                        $factData[] = $fact->getRecipient();
                    }

                    if ($type == DossierExpertiseJudiciaireChronology::TYPE_CHRONOLOGY) {
                        $factData[] = DossierExpertiseJudiciaireChronologyService::getMethod($fact->getObject());
                    } else {
                        $factData[] = $fact->getObject();
                    }

                    $comment = $fact->getContent();

                    /*if ($fact->getDocument() and in_array(getExtension($fact->getDocument()->getFile()), ['jpg', 'jpeg', 'png', 'gif'])) {
                        if (!$fact->getDocument()->getConfidential()) {
                            $comment .= '<br><br><img class="img-fluid" style="max-height: 1200px" src="' . $fact->getDocument()->getUrl() . '">';
                        }
                    } elseif ($fact->getFile() and in_array(getExtension($fact->getFile()), ['jpg', 'jpeg', 'png'])) {
                        $comment .= '<br><br><img class="img-fluid" style="max-height: 1200px" src="' . Assets::getMediaUrl($fact->getExpertise()->getDossier()->getFolder() . $fact->getFile()) . '">';
                    } elseif ($fact->getFile() and getExtension($fact->getFile()) == 'pdf') {
                        $fullPath = WEB_PATH . '/medias/' . $fact->getExpertise()->getDossier()->getFolder();
                        $pathInfo = pathinfo($fact->getFile());
                        $fileName = $pathInfo['filename'] . '-thumbnail';

                        $images = ImagickHelper::convertPdf($fullPath . $fact->getFile(), true);
                        $comment .= '<br><br><img class="img-fluid" style="max-height: 1200px" src="' . Assets::getMediaUrl($images[0]) . '">';
                    }*/
                    $factData[] = $comment;

                    $data[] = $factData;
                }
            }
        }

        $this->addTable($columns, $data, $width);
    }

    private function addVehicleUsage(): void
    {
        $columns = ['', ''];
        $data = [];

        $vehicle = $this->getDossier()->getVehicle();
        $useTypes = [];
        if ($vehicle->getUseType()) {
            $vehicleUseTypes = explode(',', $vehicle->getUseType());
            foreach ($vehicleUseTypes as $vehicleUseType) {
                $useTypes[] = DossierVehicleService::getUseType($vehicleUseType);
            }
        }
        $data[] = ['<strong>Utilisation</strong>', implode(', ' , $useTypes)];

        if ($vehicle->getUsePurpose() != DossierVehicle::USE_PURPOSE_NOT_SET and ($vehicle->getUsePurpose() != DossierVehicle::USE_PURPOSE_OTHER or $vehicle->getUsePurposeOther())) {
            $usePurpose = DossierVehicleService::getUsePurpose($vehicle->getUsePurpose());
            if ($vehicle->getUsePurpose() == DossierVehicle::USE_PURPOSE_OTHER and $vehicle->getUsePurposeOther()) {
                $usePurpose .= ' (' . $vehicle->getUsePurposeOther() . ')';
            }
            $data[] = ['<strong>Type d\'utilisation</strong>', $usePurpose];
        }

        $index = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($this->getTableOfContents(), 'Utilisation du véhicule');
        $this->addSectionSubTitle(__('%s. Utilisation du véhicule', $index));

        $this->addTable($columns, $data);
    }

    public function addBreakdown(DossierExpertiseJudiciaire $expertiseJudiciaire): void
    {
        if (!$expertiseJudiciaire->getDamageCircumstances()) {
            return;
        }

        $subIndex = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($this->getTableOfContents(), 'Rappel des circonstances de l\'avarie');
        $this->addSectionSubTitle(__('%s. Rappel des circonstances de l\'avarie', $subIndex));

        $content = $expertiseJudiciaire->getDamageCircumstances();
        $this->addContent($content, 20);
    }

    public function addExpertDiligences(DossierExpertiseJudiciaire $expertiseJudiciaire): void
    {
        if (!$expertiseJudiciaire->getDueDiligences()) {
            return;
        }

        $index = DossierExpertiseJudiciaireTableOfContentsFactory::findIndex($this->getTableOfContents(), 'Diligences de l\'expert');
        $this->addSectionTitle(__('%s. Diligences de l\'expert', $index));

        $this->addContent($expertiseJudiciaire->getDueDiligences(), 20);
    }

    public function addConservatoryMeasures(DossierExpertiseJudiciaire $expertiseJudiciaire): void
    {
        $expertise = $this->getExpertise();
        $samples = $expertise->getSamples();
        $conservatoryMeasures = $expertise->getConservatoryMeasures();
        $externalCompany = $expertiseJudiciaire->getExternalCompany();
        if (!count($samples) and !$conservatoryMeasures and !$externalCompany) {
            return;
        }

        $index = DossierExpertiseJudiciaireTableOfContentsFactory::findIndex($this->getTableOfContents(), 'Mesures conservatoires ou autres interventions');
        $this->addSectionTitle(__('%s. Mesures conservatoires ou autres interventions', $index));

        if ($conservatoryMeasures) {
            $subIndex = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($this->getTableOfContents(), 'Mesures conservatoires à prendre');
            $this->addSectionSubTitle(__('%s. Mesures conservatoires à prendre', $subIndex));

            $content = $this->formatText($expertise->getConservatoryMeasures());
            if ($expertise->getConservatoryMeasuresFile() and $this->withPictures) {
                $size = $this->getImageSize(WEB_PATH . '/medias/' . $expertise->getDossier()->getFolder() . $expertise->getConservatoryMeasuresFile(), null, 600);

                $content .= '<div class="mt-0 mb-12 text-center">';
                $content .= '<img class="img-fluid" src="' . Assets::getMediaUrl($expertise->getDossier()->getFolder() . $expertise->getConservatoryMeasuresFile()) . '" ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : '') . '>';
                $content .= '</div>';
            }

            $this->addContent($content, 12);
        }

        if (count($samples)) {
            $subIndex = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($this->getTableOfContents(), 'Prélèvements');
            $this->addSectionSubTitle(__('%s. Prélèvements', $subIndex));
            $this->addSamples();
        }

        if ($externalCompany) {
            $subIndex = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($this->getTableOfContents(), 'Appel à sapiteur ou société externe');
            $this->addSectionSubTitle(__('%s. Appel à sapiteur ou société externe', $subIndex));

            if ($expertiseJudiciaire->getExternalCompanyFile()) {
                $files = $this->getFiles($expertiseJudiciaire->getExpertise()->getDossier(), $expertiseJudiciaire->getExternalCompanyFile());
                if ($files) {
                    foreach ($files as $file) {
                        $externalCompany .= '<br><br><img class="img-fluid" style="max-height: 1200px" src="' . $file . '">';
                    }
                }
            }

            $this->addContent($externalCompany, 20);
        }
    }

    public function addExpertAnalysis(DossierExpertiseJudiciaire $expertiseJudiciaire): void
    {
        $facts = $expertiseJudiciaire->getFacts();
        $doubts = $expertiseJudiciaire->getDoubts();
        $opinions = $expertiseJudiciaire->getOpinions();
        if (!count($facts) and !count($doubts) and !count($opinions)) {
            return;
        }

        //$this->addNewPage();
        $index = DossierExpertiseJudiciaireTableOfContentsFactory::findIndex($this->getTableOfContents(), 'Analyse de l\'expert de justice');
        $this->addSectionTitle(__('%s. Analyse de l\'expert de justice', $index));

        $dossier = $this->getDossier();
        if (count($facts)) {
            $subIndex = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($this->getTableOfContents(), 'Les faits, leur origine et causes établies');
            $this->addSectionSubTitle(__('%s. Les faits, leur origine et causes établies', $subIndex));
            if (count($facts)) {
                foreach ($facts as $fact) {
                    $content = $fact->getContent();

                    if ($fact->getFile()) {
                        $size = $this->getImageSize(WEB_PATH . '/medias/' . $dossier->getFolder() . $fact->getFile(), null, 400);

                        $content .= '<div class="mt-0 mb-12 text-center">';
                        $content .= '<img class="img-fluid" src="' . Assets::getMediaUrl($dossier->getFolder() . $fact->getFile()) . '" ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : '') . '>';
                        if ($fact->getLegend()) {
                            $content .= '<p class="text-center mt-6"><em>' . $fact->getLegend() . '</em></p>';
                        }
                        $content .= '</div>';
                    }
                    if ($fact->getFile2()) {
                        $size = $this->getImageSize(WEB_PATH . '/medias/' . $dossier->getFolder() . $fact->getFile2(), null, 600);

                        $content .= '<div class="mt-0 mb-12 text-center">';
                        $content .= '<img class="img-fluid" src="' . Assets::getMediaUrl($dossier->getFolder() . $fact->getFile2()) . '" ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : '') . '>';
                        if ($fact->getLegend2()) {
                            $content .= '<p class="text-center mt-6"><em>' . $fact->getLegend2() . '</em></p>';
                        }
                        $content .= '</div>';
                    }

                    $this->addContent($content, 12);
                }
            }
        }

        if (count($doubts)) {
            $subIndex = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($this->getTableOfContents(), 'Les éventuels doutes et incertitudes non levées');
            $this->addSectionSubTitle(__('%s. Les éventuels doutes et incertitudes non levées', $subIndex));

            foreach ($doubts as $doubt) {
                $content = $doubt->getContent();

                if ($doubt->getFile()) {
                    $size = $this->getImageSize(WEB_PATH . '/medias/' . $dossier->getFolder() . $doubt->getFile(), null, 400);

                    $content .= '<div class="mt-0 mb-12 text-center">';
                    $content .= '<img class="img-fluid" src="' . Assets::getMediaUrl($dossier->getFolder() . $doubt->getFile()) . '" ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : '') . '>';
                    if ($doubt->getLegend()) {
                        $content .= '<p class="text-center mt-6"><em>' . $doubt->getLegend() . '</em></p>';
                    }
                    $content .= '</div>';
                }
                if ($doubt->getFile2()) {
                    $size = $this->getImageSize(WEB_PATH . '/medias/' . $dossier->getFolder() . $doubt->getFile2(), null, 600);

                    $content .= '<div class="mt-0 mb-12 text-center">';
                    $content .= '<img class="img-fluid" src="' . Assets::getMediaUrl($dossier->getFolder() . $doubt->getFile2()) . '" ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : '') . '>';
                    if ($doubt->getLegend2()) {
                        $content .= '<p class="text-center mt-6"><em>' . $doubt->getLegend2() . '</em></p>';
                    }
                    $content .= '</div>';
                }

                $this->addContent($content, 12);
            }
        }

        if (count($opinions)) {
            $subIndex = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($this->getTableOfContents(), 'Avis technique développé, motivé et argumenté');
            $this->addSectionSubTitle(__('%s. Avis technique développé, motivé et argumenté', $subIndex));

            foreach ($opinions as $opinion) {
                $content = $opinion->getContent();

                if ($opinion->getFile()) {
                    $size = $this->getImageSize(WEB_PATH . '/medias/' . $dossier->getFolder() . $opinion->getFile(), null, 400);

                    $content .= '<div class="mt-0 mb-12 text-center">';
                    $content .= '<img class="img-fluid" src="' . Assets::getMediaUrl($dossier->getFolder() . $opinion->getFile()) . '" ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : '') . '>';
                    if ($opinion->getLegend()) {
                        $content .= '<p class="text-center mt-6"><em>' . $opinion->getLegend() . '</em></p>';
                    }
                    $content .= '</div>';
                }
                if ($opinion->getFile2()) {
                    $size = $this->getImageSize(WEB_PATH . '/medias/' . $dossier->getFolder() . $opinion->getFile2(), null, 600);

                    $content .= '<div class="mt-0 mb-12 text-center">';
                    $content .= '<img class="img-fluid" src="' . Assets::getMediaUrl($dossier->getFolder() . $opinion->getFile2()) . '" ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : '') . '>';
                    if ($opinion->getLegend2()) {
                        $content .= '<p class="text-center mt-6"><em>' . $opinion->getLegend2() . '</em></p>';
                    }
                    $content .= '</div>';
                }

                $this->addContent($content, 12);
            }
        }
    }

    public function addDires(DossierExpertiseJudiciaire $expertiseJudiciaire): void
    {
        $index = DossierExpertiseJudiciaireTableOfContentsFactory::findIndex($this->getTableOfContents(), 'Dires des parties');
        $this->addSectionTitle(__('%s. Dires des parties', $index));

        $columns = ['Partie', 'Date', 'Dire'];
        $data = [];

        $container = ContainerBuilderService::getInstance();
        $dires = $container->get(DossierExpertiseJudiciaireDireService::class)->getRepository()->findBy(['expertise' => $expertiseJudiciaire->getExpertise(), 'parent' => null]);
        if ($dires) {
            foreach ($dires as $dire) {
                $content = $dire->getContent();
                if ($dire->getFile()) {
                    $files = $this->getFiles($expertiseJudiciaire->getExpertise()->getDossier(), $dire->getFile());
                    if ($files) {
                        foreach ($files as $file) {
                            $content .= '<br><br><img class="img-fluid" style="max-height: 1200px" src="' . $file . '">';
                        }
                    }
                }

                $data[] = [
                    $dire->getPerson()->getName(),
                    $dire->getDireDate() ? $dire->getDireDate()->format('d/m/Y') : '',
                    $content
                ];
            }
        }

        $this->addTable($columns, $data, [20, 20, 60]);
    }

    public function addDiresAnswers(DossierExpertiseJudiciaire $expertiseJudiciaire): void
    {
        $container = ContainerBuilderService::getInstance();
        $dires = $container->get(DossierExpertiseJudiciaireDireService::class)->getRepository()->findBy(['expertise' => $expertiseJudiciaire->getExpertise(), 'parent' => null]);
        if (!$dires) {
            return;
        }

        $index = DossierExpertiseJudiciaireTableOfContentsFactory::findIndex($this->getTableOfContents(), 'Réponses aux dires des parties');
        $this->addSectionTitle(__('%s. Réponses aux dires des parties', $index));

        foreach ($dires as $dire) {
            $direTitle = __('Dire de %s %s', $dire->getPerson()->getName(), $dire->getDireDate() ? 'du ' . $dire->getDireDate()->format('d/m/Y') : '');
            $this->addSectionSubTitle($direTitle);

            $content = $dire->getContent();
            if ($dire->getFile()) {
                /*$files = $this->getFiles($expertiseJudiciaire->getExpertise()->getDossier(), $dire->getFile());
                if ($files) {
                    foreach ($files as $file) {
                        $content .= '<br><br><img class="img-fluid" style="max-height: 1200px" src="' . $file . '">';
                    }
                }*/
            }
            $direContent = '<em class="p-4">' . $content . '</em>';
            $this->addContent($direContent, 6);

            $answers = $dire->getAnswers();
            if (!$answers) {
                $answersContent = '<p><em>' . __('Aucune réponse enregistrée') . '</em></p>';
            } else {
                $answersContent = '<p><strong>' . n__('Réponse', 'Réponses', count($answers)) . '</strong></p>';
                foreach ($answers as $answer) {
                    $answersContent .= $answer->getContent();
                    if ($answer->getFile()) {
                        $files = $this->getFiles($expertiseJudiciaire->getExpertise()->getDossier(), $answer->getFile());
                        if ($files) {
                            foreach ($files as $file) {
                                $answersContent .= '<br><br><img class="img-fluid" style="max-height: 1200px" src="' . $file . '">';
                            }
                        }
                    }
                }
            }
            $this->addContent($answersContent, 12);
        }
    }

    public function addCourtQuestions(DossierExpertiseJudiciaire $expertiseJudiciaire, bool $displayPictures = true): void
    {
        $index = DossierExpertiseJudiciaireTableOfContentsFactory::findIndex($this->getTableOfContents(), 'Réponses aux questions du tribunal');
        $this->addSectionTitle(__('%s. Réponses aux questions du tribunal', $index));

        $courtQuestions = $expertiseJudiciaire->getCourtQuestions();
        $id = 0;
        $dossier = $this->getDossier();
        foreach ($courtQuestions as $courtQuestion) {
            $id++;
            $this->addSectionSubTitle(__('Question %d', $id));
            $this->addContent('<strong>' . $courtQuestion->getQuestion() . '</strong>', 4);
            if (count($courtQuestion->getAnswers())) {
                $this->addContent('<p><strong><u>' . __('Réponse') . '</u></strong></p>', 4);
                foreach ($courtQuestion->getAnswers() as $answer) {
                    $content = $answer->getAnswer();
                    if ($displayPictures and $answer->getFile()) {
                        $size = $this->getImageSize(WEB_PATH . '/medias/' . $dossier->getFolder() . $answer->getFile(), null, 600);

                        $content .= '<div class="mt-0 mb-12 text-center">';
                        $content .= '<img class="img-fluid" src="' . Assets::getMediaUrl($dossier->getFolder() . $answer->getFile()) . '" ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : '') . '>';
                        $content .= '</div>';
                    }
                    $this->addContent($content, 8);
                }
            } else {
                $this->addContent('<em>' . __('Aucune réponse enregistrée') . '</em>', 8);
            }
        }
    }

    public function getRecipients(DossierExpertiseReport $report): string
    {
        if (!$report->getRecipients()) {
            if ($report->getType() == DossierExpertiseReport::TYPE_PRELIMINARY) {
                return '&nbsp;'; //do not display default recipients
            }

            return '';
        }

        $content = '<p>';
        $container = ContainerBuilderService::getInstance();
        foreach ($report->getRecipients() as $personId) {
            $person = $container->get(DossierExpertisePersonService::class)->getRepository()->findOneBy(['id' => $personId, 'expertise' => $report->getExpertise(), 'client' => $report->getExpertise()->getClient()]);
            if (!$person) {
                continue;
            }

            $name = $person->getRepresentativeName();
            $company = $person->getCompany();
            if ($company and $company != $name) {
                $content .= '<strong>' . $this->formatText($company) . '</strong><br>';
            }
            if ($name) {
                $content .= $this->formatText($name) . '<br>';
            }
            $content .= '<br>';
        }
        $content .= '</p>';

        return $content;
    }
}
