<?php
namespace MatGyver\Components\Pdf;

use MatGyver\Entity\Dossier\DossierDocument;
use MatGyver\Entity\Dossier\DossierFact;
use MatGyver\Entity\Dossier\Expertise\DossierExpertise;
use MatGyver\Entity\Dossier\Expertise\DossierExpertisePicture;
use MatGyver\Entity\Dossier\Expertise\DossierExpertiseVehicleEvaluations;
use MatGyver\Entity\Dossier\Expertise\DossierExpertiseVehicleSales;
use MatGyver\Enums\VradeEnum;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\ImagickHelper;
use MatGyver\Helpers\Number;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Dossier\DossierDocumentService;
use MatGyver\Services\Dossier\DossierFactService;
use MatGyver\Services\Dossier\Expertise\DossierExpertisePictureService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseVehicleEvaluationsService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseVehicleObservationsService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseVehicleSalesService;
use MatGyver\Services\TwigService;

class AppraisedValuePdfComponent extends AbstractPdfComponent
{
    /**
     * @param DossierExpertise $expertise
     * @throws \Exception
     */
    public function __construct(DossierExpertise $expertise)
    {
        $this->setDossier($expertise->getDossier());
        $this->setExpertise($expertise);
        $this->setClientInfos();
        $this->setType(DossierDocument::TYPE_APPRAISED_VALUE);
        parent::__construct();
    }

    /**
     * @return string
     * @throws \Exception
     */
    public function render(): string
    {
        $dossier = $this->getDossier();
        if (!$dossier) {
            throw new \Exception(__('Dossier not specified.'));
        }

        $expertise = $this->getExpertise();
        if (!$expertise) {
            throw new \Exception(__('Expertise not specified.'));
        }

        $report = $expertise->getExpertiseReport();

        $title = __('Évaluation de votre véhicule');
        $contact = $dossier->getContact();
        $this->setTitle($title);
        $subTitle = $contact->getFirstName() . ' ' . $contact->getLastName();
        if ($contact->getCompany()) {
            $subTitle = $contact->getCompany();
        }

        /*$description = '
        <div class="p-8" style="background: #F4F4F4; text-align: center">
            <h3>' . __('Nous estimons la valeur de votre véhicule à') . '</h3>
            <br>
            <h1>' . Number::formatAmount($report->getAppraisedValueAmount(), DEFAULT_CURRENCY) . '</h1>
        </div>';*/
        $description = '';
        $this->addFirstPage($subTitle, $description, '', true, $report->getFirstPagePicture());

        $this->addVehicleInfos();
        $this->addVehiclePictures();
        if ($report->getDisplayHistory()) {
            $this->addFacts();
        }
        $this->addAppraisedValueInformations();
        $this->addAppraisedValueEvaluations();

        $content = $this->getAppraisedValueFirstPictures();
        if ($content) {
            $this->addContent($content);
            $this->addNewPage();
        }

        //$this->addAppraisedValuePictures();
        $this->addVehicleSales();

        $this->getAppraisedValueConclusions();

        $documents = $report->getSuppliedDocuments();
        if ($documents) {
            $this->addDocuments($documents);
        }

        return $this->getContent();
    }

    /**
     * @return string
     * @throws \Exception
     */
    public function renderInExpertise(): string
    {
        $this->addAppraisedValueInformations(__('Calcul de la VRADE (Valeur du véhicule à dire d\'expert)'));
        $this->addAppraisedValueEvaluations();
        $this->addVehicleSales();
        return $this->getContent();
    }

    /**
     * @return string
     */
    public function getAppraisedValueFirstPictures(): string
    {
        $expertise = $this->getExpertise();
        $report = $expertise->getExpertiseReport();
        $pictures = $report->getAppraisedValuePictures();
        $content = '';
        if ($pictures !== null and count($pictures)) {
            $container = ContainerBuilderService::getInstance();

            $nbPictures = count($pictures);
            $displaySideBySide = false;
            if ($nbPictures > 1) {
                //display pictures 2 by 2
                $displaySideBySide = true;
            }

            $content .= ($displaySideBySide ? '<div class="row">' : '');
            foreach ($pictures as $pictureId) {
                $picture = $container->get(DossierExpertisePictureService::class)->getRepository()->findOneBy(['id' => $pictureId, 'expertise' => $expertise, 'client' => $expertise->getClient()]);
                if ($picture) {
                    $size = $this->getImageSize($picture->getFullPath(), null, 400);

                    $content .= ($displaySideBySide ? '<div class="col-sm-6 col-md-6">' : '');
                    $content .= '<div class="mb-10 text-center">';
                    $content .= '<img class="img-fluid" src="' . $picture->getUrl() . '" ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : '') . '>';
                    $content .= '<p class="text-center mt-6"><em>' . ($picture->getName() ?: DossierExpertisePictureService::getType($picture->getType())) . '</em></p>';
                    $content .= '</div>';
                    $content .= ($displaySideBySide ? '</div>' : '');
                }
            }
            $content .= ($displaySideBySide ? '</div>' : '');
        }

        return $content;
    }

    /**
     * @param string $title
     * @return void
     */
    private function addAppraisedValueInformations(string $title = '')
    {
        $this->addSectionTitle(($title ?: __('Informations générales')));

        $this->addSectionSubTitle(__('Documents fournis pour l\'étude du dossier'));
        $expertise = $this->getExpertise();
        $dossier = $expertise->getDossier();
        $vehicle = $dossier->getVehicle();
        $report = $expertise->getExpertiseReport();
        $suppliedDocuments = $report->getSuppliedDocuments();
        if ($suppliedDocuments) {
            $content = '<table class="table">';
            $documents = $expertise->getDossier()->getDocuments();
            foreach ($documents as $document) {
                if (!in_array($document->getId(), $suppliedDocuments)) {
                    continue;
                }
                if ($document->getVirusScan() == DossierDocument::VIRUS_SCAN_ALERT) {
                    continue;
                }
                $content .= '<tr><td class="w-30px"><input type="checkbox" checked></td><td>' . ($document->getName() ?: DossierDocumentService::getType($document->getType())) . '</td></tr>';
            }
            $content .= '</table>';
        } else {
            $content = '<p>' . __('Aucun') . '</p>';
        }
        $this->addContent($content, 12);


        if ($vehicle->getStandardEquipments()) {
            $this->addSectionSubTitle(__('Équipements de série'));
            $this->addContent($vehicle->getStandardEquipments(), 12);
        }

        if ($vehicle->getAccessoryEquipments()) {
            $this->addSectionSubTitle(__('Équipements accessoires'));
            $this->addContent($vehicle->getAccessoryEquipments(), 12);
        }

        if ($vehicle->getSecurityEquipments()) {
            $this->addSectionSubTitle(__('Équipements Antivol – Sécurité'));
            $content = $vehicle->getSecurityEquipments();
            $content = str_replace('<table>', '<table class="table">', $content);
            $this->addContent($content, 12);
        }

        $observations = $expertise->getVehicleObservations();
        if (count($observations)) {
            $observations = $observations->toArray();
            $observationsByType = DossierExpertiseVehicleObservationsService::sortObservations($observations);
            foreach ($observationsByType as $type => $observations) {
                $this->addSectionSubTitle(DossierExpertiseVehicleObservationsService::getType($type));

                foreach ($observations as $observation) {
                    $content = $observation->getComment();
                    if ($observation->getFile()) {
                        $size = $this->getImageSize(WEB_PATH . '/medias/' . $dossier->getFolder() . $observation->getFile(), null, 500);

                        $content .= '<div class="mt-0 mb-12 text-center">';
                        $content .= '<img class="img-fluid" src="' . Assets::getMediaUrl($dossier->getFolder() . $observation->getFile()) . '" ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : '') . '>';
                        if ($observation->getLegend()) {
                            $content .= '<p class="text-center mt-6"><em>' . $observation->getLegend() . '</em></p>';
                        }
                        $content .= '</div>';
                    }
                    $this->addContent($content, 12);
                }
            }
        }
    }

    private function addFacts()
    {
        $expertise = $this->getExpertise();

        $container = ContainerBuilderService::getInstance();
        $facts = $container->get(DossierFactService::class)->getRepository()->findByDossierAndExpertise($expertise);
        if (!$facts) {
            return;
        }

        $this->addSectionTitle(__('Historique connu'));
        $factsByDate = DossierFactService::sortFacts($facts);

        $columns = ['Date', 'Kilométrage', 'Évènement'];
        $data = [];
        foreach ($factsByDate as $date => $facts) {
            foreach ($facts as $fact) {
                if (in_array($fact->getType(), [DossierFact::TYPE_CONVOCATION, DossierFact::TYPE_EXPERTISE])) {
                    continue;
                }

                $comment = nl2br($fact->getComment());
                $data[] = [
                    ($fact->getDate() ? $fact->getDate()->format('d/m/Y') : ''),
                    ($fact->getMileage() ? $fact->getMileage() . ' ' . (($fact->getMileageType() == DossierFact::MILEAGE_TYPE_METERS ? 'km' : 'heures')) : ''),
                    $comment
                ];
            }
        }

        $this->addTable($columns, $data, [15, 15, 70]);
    }

    private function addAppraisedValueEvaluations()
    {
        $expertise = $this->getExpertise();
        $evaluations = $expertise->getVehicleEvaluations();
        if (!count($evaluations)) {
            return;
        }

        $this->addSectionTitle(__('Évaluation'));
        $this->addSectionSubTitle(__('Récapitulatif général - cotation technique'));

        $selectedEvaluations = [];
        $workToBeDone = '';
        foreach ($evaluations as $evaluation) {
            $selectedEvaluations[$evaluation->getType()] = $evaluation->getStatus();
            if ($evaluation->getType() == DossierExpertiseVehicleEvaluations::TYPE_WORK_TO_BE_DONE) {
                $workToBeDone = $evaluation->getComment();
            }
        }

        $types = DossierExpertiseVehicleEvaluationsService::getTypes();
        $statuses = DossierExpertiseVehicleEvaluationsService::getStatuses();

        foreach ($types as $type => $name) {
            if (!isset($selectedEvaluations[$type])) {
                unset($types[$type]);
            }
        }

        if ($types) {
            $content = TwigService::getInstance()->set('types', $types)
                ->set('statuses', $statuses)
                ->set('selectedEvaluations', $selectedEvaluations)
                ->render('app/dossier/vehicle_evaluations.php');
            $this->addContent($content, ($workToBeDone ? 12 : 20));
        }

        if ($workToBeDone) {
            $this->addSectionSubTitle(__('Travaux à effectuer'));
            $this->addContent($workToBeDone, 20);
        }
    }

    private function addAppraisedValuePictures()
    {
        $expertise = $this->getExpertise();
        $report = $expertise->getExpertiseReport();
        if ($report->getAppraisedValuePictures()) {
            $container = ContainerBuilderService::getInstance();
            $content = '<div class="row">';
            foreach ($report->getAppraisedValuePictures() as $pictureId) {
                $picture = $container->get(DossierExpertisePictureService::class)->getRepository()->findOneBy(['id' => $pictureId, 'expertise' => $expertise, 'client' => $expertise->getClient()]);
                if ($picture) {
                    $content .= '<div class="col-sm-6 col-md-6"><div class="mt-4 mb-4 text-center">';
                    $content .= '<img class="img-fluid" src="' . $picture->getUrl() . '">';
                    $content .= '<p class="text-center mt-6"><em>' . ($picture->getName() ?: DossierExpertisePictureService::getType($picture->getType())) . '</em></p>';
                    $content .= '</div></div>';
                }
            }
            $content .= '</div>';

            $this->addContent($content, 20);
        }
    }

    private function addVehicleSales()
    {
        $expertise = $this->getExpertise();
        $dossier = $expertise->getDossier();
        $vehicleSales = $expertise->getVehicleSales();
        if (!count($vehicleSales)) {
            return;
        }

        $vehicleSales = $vehicleSales->toArray();
        usort($vehicleSales, function (DossierExpertiseVehicleSales $vehicleSale1, DossierExpertiseVehicleSales $vehicleSale2) {
            return ($vehicleSale1->getFinalPrice() > $vehicleSale2->getFinalPrice() ? 1 : -1);
        });

        $this->addSectionTitle(__('Recherche marché de l\'occasion'));

        $totalPrice = 0;
        $totalDate = 0;
        $totalMileage = 0;
        $totalProfessional = 0;
        $totalPrivate = 0;
        $totalFinalPrice = 0;

        $content = '
        <table class="table">
            <thead>
                <tr>
                    <th>Informations</th>
                    <th>Prix</th>
                    <th>Annonce</th>
                </tr>
            </thead>
            <tbody>';
        foreach ($vehicleSales as $vehicleSale) {
            $totalPrice += $vehicleSale->getPrice();
            $totalDate += $vehicleSale->getVehicleDate() ? $vehicleSale->getVehicleDate()->getTimestamp() : 0;
            $totalMileage += $vehicleSale->getMileage();
            if ($vehicleSale->getType() == DossierExpertiseVehicleSales::TYPE_PROFESSIONAL) {
                $totalProfessional++;
            } else {
                $totalPrivate++;
            }
            $totalFinalPrice += $vehicleSale->getFinalPrice();

            $content .= '<td><p class="mb-0">';
            if ($vehicleSale->getVehicleDate()) {
                $content .= __('Année : %s', $vehicleSale->getVehicleDate()->format('m/Y')) . '<br>';
            }
            if ($vehicleSale->getMileage()) {
                $content .= __('Kilométrage : %s km', $vehicleSale->getMileage()) . '<br>';
            }
            if ($vehicleSale->getFinish()) {
                $content .= __('Finition : %s', $vehicleSale->getFinish()) . '<br>';
            }
            $content .= DossierExpertiseVehicleSalesService::getType($vehicleSale->getType()) . '<br>';
            if ($vehicleSale->getCity()) {
                $content .= __('Lieu de vente : %s', $vehicleSale->getCity() . ($vehicleSale->getZip() ? ' (' . $vehicleSale->getZip() . ')' : '')) . '<br>';
            }
            $content .= '</p></td>';

            $content .= '<td><p class="mb-0">';
            if ($vehicleSale->getGains()) {
                $content .= __('Prix : %s', Number::formatAmount($vehicleSale->getPrice(), DEFAULT_CURRENCY)) . '<br>';
                $content .= __('+/- values : %s', Number::formatAmount($vehicleSale->getGains(), DEFAULT_CURRENCY)) . '<br>';
            }
            $content .= '<span class="font-weight-bolder text-primary">' . __('Prix : %s', Number::formatAmount($vehicleSale->getFinalPrice(), DEFAULT_CURRENCY)) . '</span>';
            $content .= '</p></td>';

            $content .= '<td>';
            if ($vehicleSale->getDescription()) {
                $content .= $vehicleSale->getDescription();
            }

            if ($vehicleSale->getPicture()) {
                $size = $this->getImageSize(WEB_PATH . '/medias/' . $dossier->getFolder() . $vehicleSale->getPicture(), 400);
                $content .= '<img class="img-fluid" src="' . $vehicleSale->getPictureUrl(true) . '" ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : '') . '>';
            } elseif ($vehicleSale->getFile()) {
                $file = $vehicleSale->getFile();
                if (in_array(getExtension($file), ['jpg', 'jpeg', 'png', 'gif'])) {
                    $size = $this->getImageSize(WEB_PATH . '/medias/' . $dossier->getFolder() . $file, null, 400);
                    $content .= '<br><br><img ' . ($size ? 'style="width: ' . $size['width'] . 'px; height: ' . $size['height'] . 'px;"' : 'class="img-fluid" style="max-height: 400px"') . ' src="' . $vehicleSale->getFileUrl(true) . '">';
                } elseif (getExtension($file) == 'pdf') {
                    $fullPath = WEB_PATH . '/medias/' . $dossier->getFolder();
                    $pathInfo = pathinfo($file);
                    $fileName = $pathInfo['filename'] . '-thumbnail';

                    $images = ImagickHelper::convertPdf($fullPath . $file, true);
                    $content .= '<br><br><img class="img-fluid" style="max-height: 400px" src="' . Assets::getMediaUrl($images[0] . '?dossier_ref=' . $dossier->getReference()) . '">';
                }
            }

            if ($vehicleSale->getLink()) {
                $content .= '<p class="mt-6"><a href="' . $vehicleSale->getLink() . '" target="_blank">' . __('Lien vers l\'annonce') . '</a></p>';
            }
            $content .= '</td></tr>';
        }
        $content .= '</tbody></table>';
        $this->addContent($content, 12);


        $this->addSectionSubTitle(__('Résumé des annonces'));
        $content = '
        <table class="table">
            <thead>
                <tr>
                    <th>Prix annoncé</th>
                    <th>Mois / Année</th>
                    <th>Kilométrage</th>
                    <th>Finition</th>
                    <th>Pro ou Particulier</th>
                    <th>Localisation</th>
                    <th>+ ou - values</th>
                    <th>Prix retenu</th>
                </tr>
            </thead>
            <tbody>';
        foreach ($vehicleSales as $vehicleSale) {
            $content .= '<tr>';
            $content .= '<td>' . Number::formatAmount($vehicleSale->getPrice(), DEFAULT_CURRENCY) . '</td>';
            $content .= '<td>' . ($vehicleSale->getVehicleDate() ? $vehicleSale->getVehicleDate()->format('m/Y') : '') . '</td>';
            $content .= '<td>' . ($vehicleSale->getMileage() ? $vehicleSale->getMileage() . ' km' : '') . '</td>';
            $content .= '<td>' . ($vehicleSale->getFinish() ?: '') . '</td>';
            $content .= '<td>' . DossierExpertiseVehicleSalesService::getType($vehicleSale->getType(), true) . '</td>';
            $content .= '<td>' . ($vehicleSale->getCity() ?: '') . '</td>';
            $content .= '<td>' . ($vehicleSale->getGains() ? Number::formatAmount($vehicleSale->getGains(), DEFAULT_CURRENCY) : '') . '</td>';
            $content .= '<td>' . Number::formatAmount($vehicleSale->getFinalPrice(), DEFAULT_CURRENCY) . '</td>';
            $content .= '</tr>';
        }
        $content .= '</tbody></table>';
        $this->addContent($content, 12);


        $this->addSectionSubTitle(__('Moyenne des annonces'));
        $content = '
        <table class="table">
            <thead>
                <tr>
                    <th>Prix €TTC annoncés</th>
                    <th>Mois / Année</th>
                    <th>Kilométrage</th>
                    <th>Vendeur Pro ou Particulier</th>
                    <th>Prix moyen marché retenu € TTC</th>
                </tr>
            </thead>
            <tbody>';

        $avgPrice = round($totalPrice / count($vehicleSales));
        $avgMileage = round($totalMileage / count($vehicleSales));
        $avgFinalPrice = round($totalFinalPrice / count($vehicleSales));

        $avgDateTimestamp = round($totalDate / count($vehicleSales));
        $avgDate = new \DateTime();
        $avgDate->setTimestamp($avgDateTimestamp);

        $content .= '<tr>';
        $content .= '<td>' . Number::formatAmount($avgPrice, DEFAULT_CURRENCY) . '</td>';
        $content .= '<td>' . $avgDate->format('m/Y') . '</td>';
        $content .= '<td>' . $avgMileage . ' km</td>';
        $content .= '<td>' . n__('%d professionnel', '%d professionnels', $totalProfessional, $totalProfessional) . '<br>' . n__('%d particulier', '%d particuliers', $totalPrivate, $totalPrivate) . '</td>';
        $content .= '<td>' . Number::formatAmount($avgFinalPrice, DEFAULT_CURRENCY) . '</td>';
        $content .= '</tr>';
        $content .= '</tbody></table>';

        $this->addContent($content, 12);


        $report = $expertise->getExpertiseReport();
        if ($report->getSalesConclusion()) {
            $this->addContent($report->getSalesConclusion(), 12);
        }
    }

    /**
     * @return string
     */
    public function getAppraisedValueConclusions(): string
    {
        $expertise = $this->getExpertise();
        $report = $expertise->getExpertiseReport();

        $this->addSectionTitle(__('Valeur à dire d\'expert'));
        $this->addContent($report->getAppraisedValue(), 12);

        $vrade = Number::formatAmount($report->getAppraisedValueAmount(), DEFAULT_CURRENCY);
        if ($report->getAppraisedValueTax() == VradeEnum::VRADE_TAX_EXCL) {
            $vrade .= ' ' . __('HT');
        } elseif ($report->getAppraisedValueTax() == VradeEnum::VRADE_TAX_INCL) {
            $vrade .= ' ' . __('TTC');
        }
        $description = '
        <div class="p-8" style="background: #F4F4F4; text-align: center">
            <h3>' . __('Valeur finale de votre véhicule') . '<br>(' . __('Valeur de Remplacement À Dire d’Expert') . ')</h3>
            <br>
            <h1>' . $vrade . '</h1>
        </div>';
        $this->addContent($description, 12);

        $this->addSignatureEnd();

        $this->addContent($report->getLegalReserves(), 12);
        return $this->getContent();
    }

    public function addVehiclePictures()
    {
        $expertise = $this->getExpertise();
        /*$report = $expertise->getExpertiseReport();
        $pictures = $report->getPictures();
        if ($pictures) {
            $content = '<table class="table">';
            $container = ContainerBuilderService::getInstance();
            $i = 0;
            foreach ($pictures as $pictureId) {
                $picture = $container->get(DossierExpertisePictureService::class)->getRepository()->findOneBy(['id' => $pictureId, 'expertise' => $expertise, 'client' => $expertise->getClient()]);
                if ($picture) {
                    if ($i % 2 == 0) {
                        $content .= '<tr>';
                    }

                    $content .= '
                        <td style="width: 50%; text-align: center;">
                            <strong>' . ($picture->getName() ?: DossierExpertisePictureService::getType($picture->getType())) . '</strong>
                            <br><br>
                            <img class="img-fluid" src="' . $picture->getUrl() . '">
                        </td>';

                    if ($i % 2 == 1) {
                        $content .= '</tr>';
                    }
                    $i++;
                }
            }
            if ($i % 2 == 1) {
                $content .= '</tr>';
            }
            $content .= '</table>';

            $this->addContent($content, 20);
            return;
        }*/

        $content = '
        <table class="table">
            <tr>
                <td style="width: 50%; text-align: center;">
                    <strong>' . __('Identification 3/4 avant du véhicule') . '</strong>
                    <br>
                    <div class="text-center">
                        ' . ($expertise->getPicture(DossierExpertisePicture::TYPE_VEHICLE_FRONT) ? '<br><img class="img-fluid" src="' . $expertise->getPicture(DossierExpertisePicture::TYPE_VEHICLE_FRONT)->getUrl() . '">' : 'Photo non éditée') . '
                    </div>
                </td>
                <td style="width: 50%; text-align: center;">
                    <strong>' . __('Identification 3/4 arrière du véhicule') . '</strong>
                    <br>
                    <div class="text-center">
                        ' . ($expertise->getPicture(DossierExpertisePicture::TYPE_VEHICLE_REAR) ? '<br><img class="img-fluid" src="' . $expertise->getPicture(DossierExpertisePicture::TYPE_VEHICLE_REAR)->getUrl() . '">' : 'Photo non éditée') . '
                    </div>
                </td>
            </tr>
            <tr>
                <td style="width: 50%; text-align: center;">
                    <strong>' . __('Compteur') . '</strong>
                    <br>
                    <div class="text-center">
                        ' . ($expertise->getPicture(DossierExpertisePicture::TYPE_METER) ? '<br><img class="img-fluid" src="' . $expertise->getPicture(DossierExpertisePicture::TYPE_METER)->getUrl() . '">' : 'Photo non éditée') . '
                    </div>
                </td>
                <td style="width: 50%; text-align: center;">
                    <strong>' . __('Numéro de série') . '</strong>
                    <br>
                    <div class="text-center">
                        ' . ($expertise->getPicture(DossierExpertisePicture::TYPE_SERIAL_NUMBER) ? '<br><img class="img-fluid" src="' . $expertise->getPicture(DossierExpertisePicture::TYPE_SERIAL_NUMBER)->getUrl() . '">' : 'Photo non éditée') . '
                    </div>
                </td>
            </tr>
        </table>';

        $this->addContent($content, 20);
    }
}
