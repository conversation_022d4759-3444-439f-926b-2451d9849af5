<?php
namespace MatGyver\Components\Mailer\Mailers;

use <PERSON><PERSON><PERSON><PERSON>\Enums\MailersEnum;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Mailer\Mailer;
use Symfony\Component\Mailer\Transport;
use Symfony\Component\Mime\Address;
use Symfony\Component\Mime\Email;
use Symfony\Component\Mime\Part\DataPart;

class SesMailer extends AbstractMailer
{
    /**
     * @var Mailer
     */
    private $mailer;

    public function __construct()
    {
        $this->setMailerName(MailersEnum::MAILER_SES);
    }

    /**
     * @param int|null $clientId
     * @return array
     */
    public function startConnection(?int $clientId = null): array
    {
        $dsn = 'ses+api://' . SQS_KEY . ':' . SQS_SECRET . '@default?region=' . SQS_REGION;
        $this->mailer = Transport::fromDsn($dsn);
        return ['valid' => true];
    }

    /**
     * @return array
     */
    public function sendTransactional(): array
    {
        $message = (new Email())
            ->from(new Address($this->getFromEmail(), $this->getFromName()))
            ->to(new Address($this->getRecipientEmail(), $this->getRecipientFirstName() . ($this->getRecipientLastName() ? ' ' . $this->getRecipientLastName() : '')))
            ->subject(html_entity_decode($this->getSubject(), ENT_QUOTES, 'UTF-8'))
            ->text($this->getMessageText(), 'utf-8')
            ->html($this->getMessageHtml(), 'utf-8');

        if ($this->getReplyToEmail()) {
            $message->replyTo(new Address($this->getReplyToEmail(), $this->getReplyToName()));
        }

        $attachments = $this->getAttachments();
        if ($attachments) {
            foreach ($attachments as $attachment) {
                $message->addPart(new DataPart(fopen($attachment, 'r')));
            }
        }

        try {
            $this->mailer->send($message);
        } catch (TransportExceptionInterface $e) {
            if (str_contains($e->getMessage(), 'Timed Out') or str_contains($e->getMessage(), '421 too many commands')) {
                return ['valid' => false, 'retry' => true];
            }
            return ['valid' => false, 'message' => __('Erreur lors de l\'envoi à %s', $this->getRecipientEmail()) . ' : ' . $e->getMessage()];
        } catch (\Exception $e) {
            if (str_contains($e->getMessage(), 'Timed Out') or str_contains($e->getMessage(), '421 too many commands')) {
                return ['valid' => false, 'retry' => true];
            }
            return ['valid' => false, 'message' => __('Erreur lors de l\'envoi à %s', $this->getRecipientEmail()) . ' : ' . $e->getMessage()];
        }

        return ['valid' => true];
    }

    /**
     * @param string $idList
     * @return array
     * @throws \Exception
     */
    public function sendNewsletter(string $idList): array
    {
        throw new \Exception('Unable to send newsletter via ses');
    }

    /**
     * @param string $name
     * @param string $mailerType
     * @return array
     * @throws \Exception
     */
    public function createList(string $name, string $mailerType): array
    {
        throw new \Exception('Unable to create list');
    }

    /**
     * @param string $idList
     * @param string $file
     * @param array $fields
     * @return array
     * @throws \Exception
     */
    public function importSubscribers(string $idList, string $file, array $fields = array()): array
    {
        throw new \Exception('Unable to import subscribers');
    }

    /**
     * @param string $idImport
     * @return array
     * @throws \Exception
     */
    public function getImportStatus(string $idImport): array
    {
        throw new \Exception('Unable to get import status');
    }

    /**
     * @param string $idNewsletter
     * @return array
     * @throws \Exception
     */
    public function cancelNewsletter(string $idNewsletter): array
    {
        throw new \Exception('Unable to cancel newsletter');
    }
}
