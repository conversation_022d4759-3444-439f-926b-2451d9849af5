<?php
namespace MatGyver\Components\Chat;

use <PERSON><PERSON><PERSON><PERSON>\Entity\Chat\ChatRoom;
use MatGyver\Factories\BlankStates\ChatRoomsBlankState;
use MatGyver\Helpers\Assets;
use MatGyver\Services\Chat\ChatUserService;
use MatGyver\Services\TwigService;
use MatGyver\Services\Users\UsersService;

class ChatRoomComponent
{
    private ChatUserService $chatUserService;
    private UsersService $usersService;

    /**
     * @param ChatUserService $chatUserService
     * @param UsersService $usersService
     */
    public function __construct(
        ChatUserService $chatUserService,
        UsersService $usersService
    ) {
        $this->chatUserService = $chatUserService;
        $this->usersService = $usersService;
    }

    /**
     * @param int|null $chatRoomId
     * @return string
     */
    public function renderChatRooms(?int $chatRoomId = null): string
    {
        $currentUser = $this->usersService->getUser();
        $chatRooms = $this->chatUserService->findUserRooms($currentUser);
        if (!$chatRooms) {
            $factory = new ChatRoomsBlankState();
            return $factory->render();
        }

        Assets::addCss('common/blank_state.css');
        Assets::addJs('common/chat-room.js');

        $rooms = '';
        $selectedRoom = null;
        foreach ($chatRooms as $id => $chatRoom) {
            if ($chatRoomId and $chatRoomId == $chatRoom->getId()) {
                $selectedRoom = $chatRoom;
            } elseif (!$chatRoomId and !$selectedRoom) {
                $selectedRoom = $chatRoom;
            }
            $rooms .= $this->renderChatRoom($chatRoom, ($selectedRoom === $chatRoom));
        }

        Assets::addInlineJs($this->getModalRoomUsers());

        return TwigService::getInstance()->set('rooms', $rooms)
            ->set('actions', ($selectedRoom ? $selectedRoom->getActions() : ''))
            ->render('common/chat/layout.php');
    }

    /**
     * @param ChatRoom $chatRoom
     * @param bool $selected
     * @return string
     */
    public function renderChatRoom(ChatRoom $chatRoom, bool $selected = false): string
    {
        $nbUnseenMessages = $chatRoom->getNbUserUnseenMessages();
        $roomUsersInfos = $chatRoom->getRoomUsersInfos();
        $chatRoomUser = $chatRoom->findUser();

        $message = '';
        if (!$chatRoom->getActive()) {
            $message = __('Conversation fermée');
        }

        $lastMessage = $chatRoom->getLastMessage();

        return TwigService::getInstance()->set('room', $chatRoom)
            ->set('roomUsersInfos', $roomUsersInfos)
            ->set('chatRoomUser', $chatRoomUser)
            ->set('selected', $selected)
            ->set('nbUnseenMessages', $nbUnseenMessages)
            ->set('message', $message)
            ->set('lastMessage', $lastMessage)
            ->render('common/chat/room.php');
    }

    /**
     * @return string
     */
    public function getModalRoomUsers(): string
    {
        return TwigService::getInstance()->render('common/chat/modal-room-users.php');
    }
}
