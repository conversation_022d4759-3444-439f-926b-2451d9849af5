<?php
namespace MatG<PERSON>ver\Components\Word;

use Mat<PERSON><PERSON>ver\Entity\Dossier\DossierDocument;
use Mat<PERSON><PERSON><PERSON>\Entity\Dossier\DossierFact;
use MatGyver\Entity\Dossier\Expertise\DossierExpertiseObservations;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\ImagickHelper;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Dossier\DossierFactService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseObservationsService;
use PhpOffice\PhpWord\Element\Header;
use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\Shared\Html;

class DireWordComponent extends AbstractWordComponent
{
    private DossierDocument $document;

    /**
     * @return PhpWord
     * @throws \Exception
     */
    public function make(): PhpWord
    {
        $dossier = $this->getDossier();
        if (!$dossier) {
            throw new \Exception(__('Dossier not specified.'));
        }

        $document = $this->getDocument();
        if (!$document) {
            throw new \Exception(__('Document not specified.'));
        }

        $expertise = $this->getExpertise();
        if (!$expertise) {
            throw new \Exception(__('Expertise not specified.'));
        }

        $this->init();
        $this->initVars();

        $title = __('Dire technique');
        if ($document->getType() == DossierDocument::TYPE_DIRE_TEMPORARY) {
            $title = __('Dire technique provisoire');
        }

        $this->addFirstPage($title, '', '', '', $document->getPerson(), false);

        $element = $this->section->addText($document->getSubject());
        $element->setFontStyle($this->h4FontStyle, ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::START, 'spaceBefore' => 500, 'spaceAfter' => 350]);

        if ($document->getFirstPart()) {
            $data = json_decode($document->getFirstPart(), true);
            if (isset($data['introduction'])) {
                $content = $data['introduction'];
                $this->addContent($content);

                //add facts and constatations
                $this->addDireFacts();
                $this->addDireObservations();

                if (isset($data['content']) and $data['content']) {
                    $content = '<p>&nbsp;</p>';
                    $content .= $data['content'];
                    $this->addContent($content);
                }
            } else {
                //old dire : no introduction and facts and constatations are already in $data['content']
                if (isset($data['content']) and $data['content']) {
                    $content = $data['content'];
                    $this->addContent($content);
                }
            }
        }

        $this->addSignatureEnd();

        $footerCompanies = $this->getFooterCompanies();
        $footer = $this->section->addFooter(Header::FIRST);
        $this->addHtml($footer, $footerCompanies);

        return $this->phpWord;
    }

    public function getDocument(): DossierDocument
    {
        return $this->document;
    }

    public function setDocument(DossierDocument $document): void
    {
        $this->document = $document;
    }

    public function addDireFacts(): void
    {
        $container = ContainerBuilderService::getInstance();
        $facts = $container->get(DossierFactService::class)->getRepository()->findBy(['dossier' => $this->getDossier(), 'dires' => true], ['position' => 'ASC']);
        if ($facts) {
            $content = '<p><strong>' . __('Il est rappelé que :') . '</strong></p>';
            $this->addContent($content);

            $factsByDate = DossierFactService::sortFacts($facts);
            foreach ($factsByDate as $date => $facts) {
                foreach ($facts as $fact) {
                    $content = $fact->getInlineComment('- ');
                    $this->addContent($content);
                    $this->addFactDocument($fact);
                }
            }
        }
    }

    public function addDireObservations(): void
    {
        $container = ContainerBuilderService::getInstance();
        $observations = $container->get(DossierExpertiseObservationsService::class)->getRepository()->findBy(['expertise' => $this->getExpertise(), 'dires' => true]);
        if ($observations) {
            $observationsByDate = DossierExpertiseObservationsService::sortObservations($observations);
            foreach ($observationsByDate as $date => $observations) {
                $date = new \DateTime($date);
                $content = '<p>&nbsp;</p><p><strong>Lors des constatations du ' . $date->format('d/m/Y') . ', nous avons relevé que :</strong></p>';
                $this->addContent($content);

                foreach ($observations as $observation) {
                    $content = $observation->getInlineComment('- ');
                    $this->addContent($content);

                    $this->addObservationDocuments($observation);
                }
            }
        }
    }

    public function addFactDocument(DossierFact $fact): void
    {
        if ($fact->getDocument() and $fact->getDocument()->getConfidential()) {
            return;
        }

        $file = $fact->getFile();
        if (!$file and $fact->getDocument()) {
            $file = $fact->getDocument()->getFile();
        }
        if ($fact->getDocument() and in_array(getExtension($file), ['jpg', 'jpeg', 'png', 'gif'])) {
            $this->addImage($fact->getDocument()->getUrl());
        } elseif (in_array(getExtension($file), ['jpg', 'jpeg', 'png'])) {
            $this->addImage(Assets::getMediaUrl($fact->getDossier()->getFolder() . $file));
        } elseif (getExtension($file) == 'pdf') {
            $fullPath = WEB_PATH . '/medias/' . $fact->getDossier()->getFolder();
            $images = ImagickHelper::convertPdf($fullPath . $file, true);
            $this->addImage(Assets::getMediaUrl($images[0]));
        }
    }

    public function addObservationDocuments(DossierExpertiseObservations $observation): void
    {
        if ($observation->getFile()) {
            $this->addImage(Assets::getMediaUrl($observation->getExpertise()->getDossier()->getFolder() . $observation->getFile()), $observation->getLegend());
        }
        if ($observation->getFile2()) {
            $this->addImage(Assets::getMediaUrl($observation->getExpertise()->getDossier()->getFolder() . $observation->getFile2()), $observation->getLegend2());
        }
    }
}
