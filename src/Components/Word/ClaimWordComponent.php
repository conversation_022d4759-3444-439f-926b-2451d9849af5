<?php
namespace MatGyver\Components\Word;

use MatGyver\Entity\Dossier\DossierDocument;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseReportService;
use PhpOffice\PhpWord\Element\Header;
use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\Shared\Html;

class ClaimWordComponent extends AbstractWordComponent
{
    private DossierDocument $document;

    /**
     * @return PhpWord
     * @throws \Exception
     */
    public function make(): PhpWord
    {
        $dossier = $this->getDossier();
        if (!$dossier) {
            throw new \Exception(__('Dossier not specified.'));
        }

        $document = $this->getDocument();
        if (!$document) {
            throw new \Exception(__('Document not specified.'));
        }

        $expertise = $this->getExpertise();
        if (!$expertise) {
            throw new \Exception(__('Expertise not specified.'));
        }

        $this->init();
        $this->initVars();

        $this->addFirstPage('', '', '', '', $document->getPerson(), false);

        $element = $this->section->addText($document->getSubject());
        $element->setFontStyle($this->h4FontStyle, ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::START, 'spaceBefore' => 500, 'spaceAfter' => 350]);

        $content = '';
        $data = json_decode($document->getFirstPart(), true);
        if (isset($data['introduction'])) {
            $content = $data['introduction'];

            //add facts and constatations
            $content .= DossierExpertiseReportService::displayFactsAndObservations($document->getExpertise(), 'claims', '- ', false);

            if (isset($data['content']) and $data['content']) {
                $content .= '<p>&nbsp;</p>';
                $content .= $data['content'];
            }
        } else {
            //old claim : no introduction and facts and constatations are already in $data['content']
            if (isset($data['content']) and $data['content']) {
                $content = $data['content'];
            }
        }

        $content = str_replace('<div class="facts">', '', $content);
        $content = str_replace('<div class="observations">', '', $content);
        $content = str_replace('</div>', '', $content);
        $this->addContent($content);

        $this->addSignatureEnd();

        $footerCompanies = $this->getFooterCompanies();
        $footer = $this->section->addFooter(Header::FIRST);
        $this->addHtml($footer, $footerCompanies);

        return $this->phpWord;
    }

    public function getDocument(): DossierDocument
    {
        return $this->document;
    }

    public function setDocument(DossierDocument $document): void
    {
        $this->document = $document;
    }
}
