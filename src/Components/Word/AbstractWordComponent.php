<?php
namespace MatGyver\Components\Word;

use MatGyver\Entity\Dossier\Dossier;
use MatGyver\Entity\Dossier\DossierContact;
use MatGyver\Entity\Dossier\DossierDocument;
use MatGyver\Entity\Dossier\DossierFact;
use MatGyver\Entity\Dossier\DossierInstitution;
use MatGyver\Entity\Dossier\DossierSignature;
use MatGyver\Entity\Dossier\Expertise\DossierExpertise;
use MatGyver\Entity\Dossier\Expertise\DossierExpertiseObservations;
use MatGyver\Entity\Dossier\Expertise\DossierExpertisePerson;
use MatGyver\Entity\Dossier\Expertise\DossierExpertisePicture;
use MatGyver\Entity\User\User;
use MatGyver\Enums\ConfigEnum;
use MatGyver\Enums\ExpertiseEnum;
use MatGyver\Factories\TableOfContents\DossierExpertiseJudiciaireTableOfContentsFactory;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\ImagickHelper;
use MatGyver\Helpers\Number;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Dossier\DossierDocumentService;
use MatGyver\Services\Dossier\DossierFactService;
use MatGyver\Services\Dossier\DossierVehicleService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseObservationsService;
use MatGyver\Services\Dossier\Expertise\DossierExpertisePictureService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseVehicleService;
use MatGyver\Services\Dossier\Pdf\DossierPdfService;
use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\TwigService;
use PhpOffice\PhpWord\Element\Header;
use PhpOffice\PhpWord\Element\Section;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\Shared\Html;
use PhpOffice\PhpWord\Style\Font;

class AbstractWordComponent
{
    const LINE_BREAK = '<w:br/>';

    public ?Dossier $dossier = null;
    public ?DossierExpertise $expertise = null;
    public string $type = '';
    public ?DossierExpertisePerson $person = null;
    public bool $withPictures = true;
    protected PhpWord $phpWord;
    protected Section $section;
    protected Font $h1FontStyle;
    protected Font $h2FontStyle;
    protected Font $h3FontStyle;
    protected Font $h4FontStyle;
    protected string $fancyTableStyleName;
    protected array $fancyTableCellStyle;
    protected array $fancyTableFontStyle;

    /**
     * @return Dossier|null
     */
    public function getDossier(): ?Dossier
    {
        return $this->dossier;
    }

    /**
     * @param Dossier $dossier
     */
    public function setDossier(Dossier $dossier): void
    {
        $this->dossier = $dossier;
    }

    /**
     * @return DossierExpertise|null
     */
    public function getExpertise(): ?DossierExpertise
    {
        return $this->expertise;
    }

    /**
     * @param DossierExpertise|null $expertise
     */
    public function setExpertise(?DossierExpertise $expertise): void
    {
        $this->expertise = $expertise;
    }

    /**
     * @return string
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * @param string $type
     */
    public function setType(string $type): void
    {
        $this->type = $type;
    }

    /**
     * @return DossierExpertisePerson|null
     */
    public function getPerson(): ?DossierExpertisePerson
    {
        return $this->person;
    }

    /**
     * @param DossierExpertisePerson $person
     */
    public function setPerson(DossierExpertisePerson $person): void
    {
        $this->person = $person;
    }

    /**
     * @return bool
     */
    public function isWithPictures(): bool
    {
        return $this->withPictures;
    }

    /**
     * @param bool $withPictures
     */
    public function setWithPictures(bool $withPictures): void
    {
        $this->withPictures = $withPictures;
    }

    public function getClientInfos(): array
    {
        $client = $this->getDossier()->getClient();
        $clientConfig = $client->getClientConfigsAsArray();
        $infos = [
            'name' => $client->getMainAdmin()->getFirstName() . ' ' . $client->getMainAdmin()->getLastName(),
            'email' => $client->getMainAdmin()->getEmail(),
            ConfigEnum::LOGO => $clientConfig[ConfigEnum::LOGO] ?? '',
            ConfigEnum::COMPANY => $clientConfig[ConfigEnum::COMPANY] ?? '',
            ConfigEnum::ADDRESS => $clientConfig[ConfigEnum::ADDRESS] ?? '',
            ConfigEnum::ADDRESS2 => $clientConfig[ConfigEnum::ADDRESS2] ?? '',
            ConfigEnum::ZIP => $clientConfig[ConfigEnum::ZIP] ?? '',
            ConfigEnum::CITY => $clientConfig[ConfigEnum::CITY] ?? '',
            ConfigEnum::TELEPHONE => $clientConfig[ConfigEnum::TELEPHONE] ?? '',
            ConfigEnum::TVA_INTRACOM => $clientConfig[ConfigEnum::TVA_INTRACOM] ?? '',
            ConfigEnum::SIGNATURE => $clientConfig[ConfigEnum::SIGNATURE] ?? '',
            'companies' => $client->getCompanies()->toArray(),
            'colors' => DossierPdfService::getColors($client->getId()),
        ];
        if ($this->getDossier() and $this->getDossier()->getCompany()) {
            $company = $this->getDossier()->getCompany();
            $infos[ConfigEnum::COMPANY] = $company->getName();
            $infos[ConfigEnum::ADDRESS] = $company->getAddress();
            $infos[ConfigEnum::ADDRESS2] = $company->getAddress2();
            $infos[ConfigEnum::ZIP] = $company->getZip();
            if ($company->getClient()->getId() != 2) {
                //always display "A Clairac" for RCPJ
                $infos[ConfigEnum::CITY] = $company->getCity();
            }
            $infos[ConfigEnum::TELEPHONE] = $company->getTelephone();
            $infos[ConfigEnum::TVA_INTRACOM] = $company->getTvaIntracom();
        }
        if ($this->getDossier() and $this->getDossier()->getUser()) {
            $user = $this->getDossier()->getUser();
            if ($user->getUserConfig(ConfigEnum::SIGNATURE)) {
                $infos[ConfigEnum::SIGNATURE] = $user->getUserConfig(ConfigEnum::SIGNATURE);
            }
        }
        return $infos;
    }

    /**
     * @return string
     */
    public function getHeaderLogo(): string
    {
        if (ENV !== ENV_PROD) {
            return 'https://app.rcpj.fr/assets/images/logo.png';
        }

        $clientInfos = $this->getClientInfos();
        if ($clientInfos[ConfigEnum::LOGO]) {
            $url = parse_url($clientInfos[ConfigEnum::LOGO]);
            if (!isset($url['query'])) {
                $clientInfos[ConfigEnum::LOGO] .= '?client=' . $this->getDossier()->getClient()->getId();
            }
            return $clientInfos[ConfigEnum::LOGO];
        }

        return Assets::getImageUrl('logo.png');
    }

    protected function init()
    {
        $languageFr = new \PhpOffice\PhpWord\Style\Language(\PhpOffice\PhpWord\Style\Language::FR_FR);
        $this->phpWord = new PhpWord();
        $this->phpWord->setDefaultFontSize(8);

        $fontSize = $this->dossier->getClient()->getClientConfig(ConfigEnum::REPORT_FONT_SIZE);
        if ($fontSize) {
            $fontSize = match ($fontSize) {
                '10' => 6,
                '11' => 7,
                '12' => 8,
                '13' => 9,
                '14' => 10,
                '15' => 11,
                '16' => 12,
                '18' => 14,
                '20' => 16,
                '24' => 18,
                default => 8
            };
            $this->phpWord->setDefaultFontSize($fontSize);
        }

        $this->phpWord->getSettings()->setThemeFontLang($languageFr);

        if (isset($_GET['remove_pictures']) or ENV !== ENV_PROD) {
            $this->setWithPictures(false);
        }
    }

    protected function initVars()
    {
        $clientInfos = $this->getClientInfos();

        $this->h1FontStyle = new Font();
        $this->h1FontStyle->setBold(true);
        $this->h1FontStyle->setName('Helvetica');
        $this->h1FontStyle->setSize(24);
        $this->h1FontStyle->setColor($clientInfos['colors']['h1_color']);

        $this->h2FontStyle = new Font();
        $this->h2FontStyle->setBold(true);
        $this->h2FontStyle->setName('Helvetica');
        $this->h2FontStyle->setSize(18);
        $this->h2FontStyle->setColor($clientInfos['colors']['h2_color']);

        $this->h3FontStyle = new Font();
        $this->h3FontStyle->setBold(true);
        $this->h3FontStyle->setName('Helvetica');
        $this->h3FontStyle->setSize(13);
        $this->h3FontStyle->setColor($clientInfos['colors']['h3_color']);
        $this->h3FontStyle->setBgColor($clientInfos['colors']['h3_bg_color']);

        $this->h4FontStyle = new Font();
        $this->h4FontStyle->setBold(true);
        $this->h4FontStyle->setName('Helvetica');
        $this->h4FontStyle->setSize(12);
        $this->h4FontStyle->setColor($clientInfos['colors']['h4_color']);
        $this->h4FontStyle->setUnderline(Font::UNDERLINE_SINGLE);

        $this->fancyTableStyleName = 'Fancy Table';
        $this->fancyTableCellStyle = ['valign' => 'top', 'borderBottomSize' => 8, 'borderBottomColor' => 'B5B5C3'];
        $this->fancyTableFontStyle = ['bold' => true, 'color' => 'B5B5C3'];

        $fancyTableStyle = ['cellMargin' => 50, 'alignment' => \PhpOffice\PhpWord\SimpleType\JcTable::START, 'cellSpacing' => 0];
        $fancyTableFirstRowStyle = ['borderBottomSize' => 8, 'borderBottomColor' => 'B5B5C3'];
        $this->phpWord->addTableStyle($this->fancyTableStyleName, $fancyTableStyle, $fancyTableFirstRowStyle);
    }

    /**
     * @param string $title
     * @param string $subTitle
     * @param string $description
     * @param string $blocInfos
     * @param DossierExpertisePerson|null $expertisePerson
     * @param bool $addFooter
     * @return void
     */
    protected function addFirstPage(string $title, string $subTitle, string $description, string $blocInfos = '', ?DossierExpertisePerson $expertisePerson = null, bool $addFooter = true)
    {
        $headerLogo = $this->getHeaderLogo();
        $dossier = $this->getDossier();
        $vehicle = $dossier->getVehicle();

        $this->addSection();
        if (ENV === ENV_PROD) {
            $this->section->addImage($headerLogo, ['width' => 150, 'alignment' => \PhpOffice\PhpWord\SimpleType\Jc::START]);
        }

        $table = $this->section->addTable();
        $table->addRow();
        $firstCell = $table->addCell(4500);

        $textRun = $firstCell->addTextRun();
        $textRun->addText(self::LINE_BREAK);

        $textRun = $firstCell->addTextRun();
        $textRun->addText(__('Lésé'), ['bold' => true]);
        $textRun->addText(' : ' . $dossier->getContact()->getCompanyOrName());

        if ($dossier->getExpertReference()) {
            $textRun = $firstCell->addTextRun();
            $textRun->addText(__('N/REF'), ['bold' => true]);
            $textRun->addText(' : ' . $dossier->getExpertReference());
        }
        if ($dossier->getCaseName()) {
            $textRun = $firstCell->addTextRun();
            $textRun->addText(__('Nom de l\'affaire'), ['bold' => true]);
            $textRun->addText(' : ' . $dossier->getCaseName());
        }
        if ($dossier->getContact() and $dossier->getContact()->getLegalProtectionNumber()) {
            $textRun = $firstCell->addTextRun();
            $textRun->addText(__('Réf. compagnie'), ['bold' => true]);
            $textRun->addText(' : ' . $dossier->getContact()->getLegalProtectionNumber());
        }
        if ($dossier->getContact() and $dossier->getContact()->getLegalProtectionIntervention()) {
            $textRun = $firstCell->addTextRun();
            $textRun->addText(__('Intervention de la compagnie'), ['bold' => true]);
            if ($dossier->getContact()->getLegalProtectionIntervention() == DossierContact::INSURANCE_PJ) {
                $textRun->addText(' : ' . __('Protection Juridique'));
            } else {
                $textRun->addText(' : ' . __('Responsabilité Civile Professionnelle'));
            }
        }

        $textRun = $firstCell->addTextRun();
        $textRun->addText(__('Véhicule'), ['bold' => true]);
        $textRun->addText(' : ' . ($vehicle ? $vehicle->getBrand() : ''));

        $textRun = $firstCell->addTextRun();
        $textRun->addText(__('Modèle'), ['bold' => true]);
        $textRun->addText(' : ' . ($vehicle ? $vehicle->getModel() : ''));

        $textRun = $firstCell->addTextRun();
        $textRun->addText(__('Immatriculation'), ['bold' => true]);
        $textRun->addText(' : ' . ($vehicle ? $vehicle->getRegistration() : ''));

        //right cell
        $rightCell = $table->addCell(4500);
        $rightCell->addText(self::LINE_BREAK);

        if ($expertisePerson) {
            $name = $expertisePerson->getRepresentativeName();
            $company = $expertisePerson->getCompany();
            $address = $expertisePerson->getAddress();
            $zip = $expertisePerson->getZip();
            $city = $expertisePerson->getCity();

            if ($company and $company != $name) {
                $rightCell->addText($company, '', ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::END]);
            }
            if ($name) {
                $rightCell->addText($name, '', ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::END]);
            }
            if ($address) {
                $rightCell->addText($address, '', ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::END]);
            }
            if ($zip or $city) {
                $rightCell->addText($zip . ' ' . $city, '', ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::END]);
            }
        }

        $clientInfos = $this->getClientInfos();
        $place = __('À %s, le %s', $clientInfos[ConfigEnum::CITY], date('d/m/Y'));
        $rightCell->addText(self::LINE_BREAK . $place, '', ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::END]);

        if ($title) {
            $element = $this->section->addText($title);
            $element->setFontStyle($this->h1FontStyle, ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER, 'spaceBefore' => 1500, 'spaceAfter' => 500]);
        }
        if ($subTitle) {
            $element = $this->section->addText($subTitle);
            $element->setFontStyle($this->h2FontStyle, ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER, 'spaceBefore' => 150, 'spaceAfter' => 350]);
        }

        if ($description) {
            $this->addHtml($this->section, $description);
            //$element = $section->addText($description);
            //$element->setFontStyle('', ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER, 'spaceBefore' => 250, 'spaceAfter' => 1500]);
        }

        if ($blocInfos) {
            $table = $this->section->addTable(['cellMargin' => 150, 'alignment' => \PhpOffice\PhpWord\SimpleType\JcTable::START, 'cellSpacing' => 0]);
            $table->addRow();
            $table->addCell(9000, ['valign' => 'center', 'bgColor' => 'F4F4F4'])->addText($blocInfos);
        }

        if ($addFooter) {
            $footerCompanies = $this->getFooterCompanies();
            $footer = $this->section->addFooter(Header::FIRST);
            $this->addHtml($footer, $footerCompanies);
        }
    }

    /**
     * @return string
     */
    protected function getFooterCompanies(): string
    {
        $clientInfos = $this->getClientInfos();
        return TwigService::getInstance()->set('clientInfos', $clientInfos)
            ->render('app/dossier/word/footer_companies.php');
    }

    protected function addSection(): void
    {
        $this->section = $this->phpWord->addSection();
    }

    /**
     * @param string  $title
     * @return void
     */
    protected function addSectionTitle(string $title)
    {
        $clientInfos = $this->getClientInfos();
        $table = $this->section->addTable(['cellMargin' => 50, 'alignment' => \PhpOffice\PhpWord\SimpleType\JcTable::START, 'cellSpacing' => 0]);
        $table->addRow(300);
        $table->addCell(9000, ['valign' => 'center', 'bgColor' => $clientInfos['colors']['h3_bg_color'], 'color' => $clientInfos['colors']['h3_color']])->addText(' ' . strtoupper($title), $this->h3FontStyle);
        $this->addSpace();
    }

    /**
     * @param string $title
     * @return void
     */
    protected function addSectionTitleLight(string $title)
    {
        $clientInfos = $this->getClientInfos();
        $table = $this->section->addTable(['cellMargin' => 50, 'alignment' => \PhpOffice\PhpWord\SimpleType\JcTable::START, 'cellSpacing' => 0]);
        $table->addRow(300);
        $table->addCell(9000, ['valign' => 'center', 'bgColor' => $clientInfos['colors']['light_title_bg_color'], 'color' => $clientInfos['colors']['light_title_color']])->addText(' ' . strtoupper($title), $this->h4FontStyle);
        $this->addSpace();
    }

    /**
     * @param string  $subTitle
     * @return void
     */
    protected function addSectionSubTitle(string $subTitle)
    {
        $element = $this->section->addText($subTitle);
        $element->setFontStyle($this->h4FontStyle, ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::LEFT, 'spaceBefore' => 150, 'spaceAfter' => 50]);
        $this->addSpace();
    }

    /**
     * @param string $content
     * @param string $title
     * @param bool $addDoubleSpace
     * @return void
     */
    protected function addContent(string $content = '', string $title = '', bool $addDoubleSpace = false)
    {
        if ($title) {
            $this->addSectionTitle($title);
        }
        if ($content) {
            $content = str_replace('<br>', '<br/>', $content);
            $this->addHtml($this->section, $content);
        }
        if ($addDoubleSpace) {
            $this->addDoubleSpace();
        }
    }

    public function addSpace()
    {
        $this->section->addText('');
    }

    public function addDoubleSpace()
    {
        $this->section->addText(self::LINE_BREAK);
    }

    protected function addTable(array $columns, array $data, array $w = [50, 50], bool $addDoubleSpace = true)
    {
        $table = $this->section->addTable($this->fancyTableStyleName);
        if ($columns) {
            $table->addRow(300);
            foreach ($columns as $id => $column) {
                //100% = 9000
                $table->addCell(round(9000 * $w[$id] / 100), $this->fancyTableCellStyle)->addText($column, $this->fancyTableFontStyle);
            }
        }

        foreach ($data as $row) {
            $table->addRow();
            foreach ($row as $id => $column) {
                $cell = $table->addCell(round(9000 * $w[$id] / 100), $this->fancyTableCellStyle);
                if (filter_var($column, FILTER_VALIDATE_URL)) {
                    if (ENV === ENV_PROD) {
                        $width = 150;
                        $height = $this->getImageHeight($column, $width);
                        if ($height) {
                            $cell->addImage($column, ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER, 'width' => $width, 'height' => $height]);
                        }
                    }
                } else {
                    if (is_null($column)) {
                        $column = '';
                    }

                    $this->addHtml($cell, $column);
                }
            }
        }

        if ($addDoubleSpace) {
            $this->addDoubleSpace();
        }
    }

    /**
     * @param string $img
     * @param string|null $legend
     * @param int $width
     * @param string $alignment
     * @return void
     */
    protected function addImage(string $img, ?string $legend = null, int $width = 400, string $alignment = \PhpOffice\PhpWord\SimpleType\Jc::CENTER)
    {
        if (ENV !== ENV_PROD) {
            return;
        }

        $extension = getExtension($img);
        if (!in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
            return;
        }

        $height = $this->getImageHeight($img, $width);
        if (!$height) {
            return;
        }

        $this->section->addImage($img, ['alignment' => $alignment, 'width' => $width, 'height' => $height]);
        if ($legend) {
            $this->section->addText(
                $legend,
                ['italic' => true],
                ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER, 'spaceBefore' => 100]
            );
        }
        $this->addDoubleSpace();
    }

    /**
     * @param string $image
     * @param int $width
     * @return int
     */
    public function getImageHeight(string $image, int $width): int
    {
        $file = $image;
        if (filter_var($image, FILTER_VALIDATE_URL)) {
            $data = parse_url($image);
            $file = WEB_PATH . $data['path'];
        }

        if (!file_exists($file)) {
            LoggerService::logError('unable to find file ' . $file);
            return 0;
        }

        $height = round($width * 0.75);
        $size = @getimagesize($file);
        if ($size) {
            $imageWidth = $size[0];
            $ratio = $imageWidth / $width;
            $height = round($size[1] / $ratio);
        }

        return $height;
    }

    /**
     * @param string|null $text
     * @return string
     */
    public function formatText(?string $text = null): string
    {
        if ($text === null) {
            return '';
        }

        $text = str_replace('&#039;', '\'', $text);
        $text = str_replace('&#39;', '\'', $text);
        $text = str_replace("\n", '<br/>', $text);
        $text = str_replace('&nbsp;', ' ', $text);
        $text = str_replace('<br>', '<br/>', $text);
        //$text = str_replace('&', '&amp;', $text);
        //$text = str_replace('&', '&amp;', $text);
        return $text;
    }

    protected function addSectionPersons()
    {
        $expertise = $this->getExpertise();

        $this->addSectionTitle(__('Identification des parties'));
        $this->addSectionSubTitle(__('Lieu d\'examen'));

        $place = $expertise->displayPlace(true, '<br/>');
        $this->addContent($place);
        $this->addDoubleSpace();

        $persons = $expertise->getPersons()->toArray();
        $this->addTablePersons($persons, DossierExpertisePerson::STATUS_PRESENT, DossierSignature::TYPE_START);
        $this->addTablePersons($persons, DossierExpertisePerson::STATUS_ABSENT);
    }

    /**
     * @param DossierExpertisePerson[] $persons
     * @param string $status
     * @param string $signatureType
     * @return void
     */
    protected function addTablePersons(array $persons, string $status, string $signatureType = DossierSignature::TYPE_END)
    {
        if ($status == DossierExpertisePerson::STATUS_PRESENT) {
            $this->addSectionSubTitle("Personnes présentes");
        } else {
            $this->addSectionSubTitle("Personnes absentes");
        }

        $w = [20, 20, 20, 20, 20];
        $columns = ['Noms', 'Qualité', 'Coordonnées / Mail / Téléphone', 'Partie représentée'];
        if ($status == DossierExpertisePerson::STATUS_PRESENT) {
            $columns = ['Noms', 'Qualité', 'Coordonnées / Mail / Téléphone', 'Parties représentées et références', 'Émargement'];
        }
        $data = [];
        foreach($persons as $person) {
            if ($person->getStatus() != $status) {
                continue;
            }

            $name = '<strong>' . $person->getRepresentativeName() . '</strong>';
            $type = $person->displayType();

            $address = [
                $person->getAddress(),
                trim($person->getZip() . ' ' . $person->getCity()),
                $person->getTelephone(),
                $person->getEmail()
            ];

            $references = [
                $person->getCompany(),
            ];
            if ($person->getInstitution()) {
                if ($person->getInstitution()->getRepresent()) {
                    $references[] = $person->getInstitution()->getRepresent() . '<br/>';
                }
                if ($person->getInstitution()->getReference()) {
                    $references[] = __('Réf. Dossier : %s', $person->getInstitution()->getReference()) . '<br/>';
                }
                if ($person->getInstitution()->getReferenceClient()) {
                    $references[] = __('Réf. Mandant : %s', $person->getInstitution()->getReferenceClient()) . '<br/>';
                }
                if ($person->getInstitution()->getReferenceCompany()) {
                    $references[] = __('Réf. Compagnie : %s', $person->getInstitution()->getReferenceCompany()) . '<br/>';
                }
            }

            $address = array_filter($address);
            $references = array_filter($references);

            $personData = [
                $name,
                $type,
                implode('<br/>', $address),
                implode('<br/>', $references),
            ];
            if ($status == DossierExpertisePerson::STATUS_PRESENT) {
                $personData[] = ($person->getSignature($signatureType) ? $person->getSignature($signatureType)->getUrl() . '?dossier_ref=' . $this->getDossier()->getReference() : '');
            }
            $data[] = $personData;
        }

        if ($data) {
            $this->addTable($columns, $data, $w);
        } else {
            if ($status == DossierExpertisePerson::STATUS_PRESENT) {
                $content = '<p>' . __('Aucune partie n\'était représentée.') . '</p>';
            } else {
                $content = '<p>' . __('Toutes les parties étaient représentées.') . '</p>';
            }
            $this->addContent($content);
            $this->addDoubleSpace();
        }
    }

    protected function addVehicleInfos(array $tableOfContents = [])
    {
        $dossier = $this->getDossier();
        $expertise = $this->getExpertise();
        $vehicle = $dossier->getVehicle();

        $columns = ['', ''];
        $data = [];
        $data[] = ['<strong>Marque</strong>', $vehicle->getBrand()];
        $data[] = ['<strong>Modèle</strong>', $vehicle->getModel()];
        $data[] = ['<strong>Couleur</strong>', DossierVehicleService::getColor($vehicle->getColor())];
        $data[] = ['<strong>Immatriculation</strong>', $vehicle->getRegistration()];
        $data[] = ['<strong>N° Série</strong>', $vehicle->getSerialNumber()];
        if ($vehicle->getFormuleNumber()) {
            $data[] = ['<strong>N° Formule</strong>', $vehicle->getFormuleNumber()];
        }
        $data[] = ['<strong>Mise en circulation</strong>', $vehicle->getDateFirstCirculation() ? $vehicle->getDateFirstCirculation()->format('d/m/Y') : ''];
        $data[] = ['<strong>Date de création de la carte grise</strong>', $vehicle->getRegistrationDate() ? $vehicle->getRegistrationDate()->format('d/m/Y') : ''];
        $data[] = ['<strong>Kilométrage</strong>', ($expertise->getExpertiseVehicle() ? $expertise->getExpertiseVehicle()->getMeter() . ' ' . DossierExpertiseVehicleService::getMeterType($expertise->getExpertiseVehicle()->getMeterType()) . ' ' . DossierExpertiseVehicleService::getMeterRead($expertise->getExpertiseVehicle()->getMeterRead()) : '')];
        $data[] = ['<strong>Carrosserie</strong>', $vehicle->getBodywork()];
        $data[] = ['<strong>Genre</strong>', $vehicle->getVehicleType()];
        $data[] = ['<strong>Énergie</strong>', DossierVehicleService::getEngine($vehicle->getEngine())];
        $data[] = ['<strong>Puissance</strong>', $vehicle->getPower()];
        $data[] = ['<strong>Boîte de vitesse</strong>', DossierVehicleService::getGearbox($vehicle->getGearbox())];
        $data[] = ['<strong>Code moteur</strong>', $vehicle->getEngineCode()];
        if ($vehicle->getSeating()) {
            $data[] = ['<strong>Places assises</strong>', $vehicle->getSeating()];
        }
        if ($expertise->getExpertiseVehicle() and $expertise->getExpertiseVehicle()->getTireWear()) {
            $tireWears = explode(';', $expertise->getExpertiseVehicle()->getTireWear());
            $tires = [
                'AVG',
                'AVD',
                'ARG',
                'ARD',
                'INTARG',
                'INTARD',
                'DIVERS',
                'DIVERS',
            ];
            $content = '';
            foreach ($tires as $id => $tire) {
                if (!$tireWears[$id]) {
                    break;
                }
                $content .= $tire . ' : ' . $tireWears[$id] . '% - ';
            }
            $content = rtrim($content, ' -');
            $data[] = ['<strong>Usure des pneus</strong>', $content];
        }
        //$data[] = ["<strong>Observations</strong>", $vehicle->getObservations() ?: __('Sans réponse')];

        $data[] = ['<strong>N° de série conforme</strong>', ($vehicle->getSerialNumberValid() == 'yes' ? 'Oui' : ($vehicle->getSerialNumberValid() == 'no' ? 'Non' : 'Sans réponse'))];


        $ignoreLevels = ($expertise->getExpertiseVehicle() ? $expertise->getExpertiseVehicle()->getLevelIgnore() : false);
        if (!$ignoreLevels) {
            $levelEngineOilValid = null;
            if ($expertise->getExpertiseVehicle() and $expertise->getExpertiseVehicle()->getLevelEngineOil()) {
                $levelEngineOilValid = ($expertise->getExpertiseVehicle()->getLevelEngineOil() >= 4 and $expertise->getExpertiseVehicle()->getLevelEngineOil() <= 8);
            }
            $data[] = ['<strong>Niveau d’huile conforme</strong>', ($levelEngineOilValid ? 'Oui' : ($levelEngineOilValid === false ? 'Non' : ''))];

            $levelCoolantValid = null;
            if ($expertise->getExpertiseVehicle() and $expertise->getExpertiseVehicle()->getLevelCoolant()) {
                $levelCoolantValid = ($expertise->getExpertiseVehicle()->getLevelCoolant() >= 4 and $expertise->getExpertiseVehicle()->getLevelCoolant() <= 8);
            }
            $data[] = ['<strong>Niveau du liquide de refroidissement conforme</strong>', ($levelCoolantValid ? 'Oui' : ($levelCoolantValid === false ? 'Non' : ''))];

            $levelBrakeValid = null;
            if ($expertise->getExpertiseVehicle() and $expertise->getExpertiseVehicle()->getLevelBrake()) {
                $levelBrakeValid = ($expertise->getExpertiseVehicle()->getLevelBrake() >= 4 and $expertise->getExpertiseVehicle()->getLevelBrake() <= 8);
            }
            $data[] = ['<strong>Niveau du liquide de frein</strong>', ($levelBrakeValid ? 'Oui' : ($levelBrakeValid === false ? 'Non' : ''))];
        }

        if ($vehicle->getDateTechnicalInspectionExpiry()) {
            $date = $expertise->getDate();
            $technicalInspectionExpiry = ($vehicle->getDateTechnicalInspectionExpiry() > $date ? 'Oui' : 'Non');
            $technicalInspectionExpiry .= '   (Valide jusqu\'au ' . $vehicle->getDateTechnicalInspectionExpiry()->format('d/m/Y') . ')';
        } else {
            $technicalInspectionExpiry = 'Sans réponse';
        }
        $data[] = ['<strong>Contrôle technique conforme</strong>', $technicalInspectionExpiry];


        if ($this->type == DossierDocument::TYPE_CONTRADICTORY and $dossier->isJudiciaire()) {
            $index = DossierExpertiseJudiciaireTableOfContentsFactory::findIndex($tableOfContents, 'Identification du véhicule');
            $this->addSectionTitle(__('%s. Identification du véhicule', $index));

            $subIndex = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($tableOfContents, 'Description du véhicule litigieux');
            $this->addSectionSubTitle(__('%s. Description du véhicule litigieux', $subIndex));
        } elseif ($this->type == DossierDocument::TYPE_EXPERTISE and $dossier->isJudiciaire()) {
            $index = DossierExpertiseJudiciaireTableOfContentsFactory::findIndex($tableOfContents, 'Constatations et investigations');
            $this->addSectionTitle(__('%s. Constatations et investigations', $index));

            $subIndex = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($tableOfContents, 'Description du véhicule litigieux');
            $this->addSectionSubTitle(__('%s. Description du véhicule litigieux', $subIndex));
        } else {
            $this->addSectionTitle(__('IDENTIFICATION DU VÉHICULE'));
        }
        $this->addTable($columns, $data);

        if ($expertise->getExpertiseVehicle() and $expertise->getExpertiseVehicle()->getRecommendations()) {
            if ($this->type == DossierDocument::TYPE_CONTRADICTORY and $dossier->isJudiciaire() or $this->type == DossierDocument::TYPE_EXPERTISE and $dossier->isJudiciaire()) {
                $subIndex = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($tableOfContents, 'Préconisations du constructeur');
                $this->addSectionSubTitle(__('%s. Préconisations du constructeur', $subIndex));
            }

            $content = '<p><strong><u>' . __('Préconisations du constructeur au premier terme échu') . '</u></strong></p>';

            $engineOil = ($expertise->getExpertiseVehicle() ? $expertise->getExpertiseVehicle()->getEngineOil() : []);
            $engineOilContent = '';

            if (isset($engineOil['change_time']) and $engineOil['change_time'] and isset($engineOil['change_kms']) and $engineOil['change_kms']) {
                $changeTimeType = ((isset($engineOil['change_time_type']) and $engineOil['change_time_type']) ? $engineOil['change_time_type'] : 'year');
                $changeKmType = 'kms';
                if (isset($engineOil['change_km_type']) and $engineOil['change_km_type']) {
                    $changeKmType = DossierDocumentService::getMileageType($engineOil['change_km_type']);
                }
                $engineOilContent .= 'Vidange moteur et filtre à huile tous les ' . $engineOil['change_kms'] . ' ' . $changeKmType . ' ou tous les ' . $engineOil['change_time'] . ' ' . ($changeTimeType == 'year' ? 'ans' : 'mois');
                if (isset($engineOil['first_due_date']) and $engineOil['first_due_date']) {
                    $engineOilContent .= ', au premier terme échu';
                }
                $engineOilContent .= '<br/>';
            } elseif (isset($engineOil['change_time']) and $engineOil['change_time']) {
                $changeTimeType = ((isset($engineOil['change_time_type']) and $engineOil['change_time_type']) ? $engineOil['change_time_type'] : 'year');
                $engineOilContent .= __('Vidange moteur et filtre à huile tous les %s %s', $engineOil['change_time'], ($changeTimeType == 'year' ? 'ans' : 'mois')) . '<br/>';
            } elseif (isset($engineOil['change_kms']) and $engineOil['change_kms']) {
                $engineOilContent .= __('Vidange moteur et filtre à huile tous les %s kms', $engineOil['change_kms']) . '<br/>';
            }

            if (isset($engineOil['viscosity']) and $engineOil['viscosity']) {
                $engineOilContent .= 'Viscosité Huile : ' . $engineOil['viscosity'] . '<br/>';
            }
            if (isset($engineOil['standard'])) {
                $engineOilContent .= 'Norme Huile : ' . $engineOil['standard'] . '<br/>';
            }
            if (isset($engineOil['crankcase_qty'])) {
                $engineOilContent .= 'Quantité d\'huile présente dans le carter : ' . $engineOil['crankcase_qty'] . ' L<br/>';
            }
            if (isset($engineOil['qty_with_filter'])) {
                $engineOilContent .= 'Quantité d\'huile avec filtre : ' . $engineOil['qty_with_filter'] . ' L<br/>';
            }
            if ($expertise->getExpertiseVehicle() and $expertise->getExpertiseVehicle()->getCrankcaseCapacity()) {
                $engineOilContent .= 'Capacité du carter : ' . $expertise->getExpertiseVehicle()->getCrankcaseCapacity() . ' L<br/>';
            }
            if ($engineOilContent) {
                $content .= '<p>' . $engineOilContent . '</p>';
            }

            if ($expertise->getExpertiseVehicle() and $expertise->getExpertiseVehicle()->getRecommendations()) {
                $content .= '<p>' . $this->formatText($expertise->getExpertiseVehicle()->getRecommendations()) . '</p>';
            }

            $this->addContent($content, '', true);
        }

        if ($this->withPictures) {
            if ($expertise->getPicture(DossierExpertisePicture::TYPE_VEHICLE_FRONT)) {
                $this->addImage($expertise->getPicture(DossierExpertisePicture::TYPE_VEHICLE_FRONT)->getUrl(false, true), 'Identification du véhicule');
            }

            $columns = ['Identification 3/4 arrière du véhicule', 'Numéro de série', 'Étiquette contrôle technique'];
            $data = [];
            $data[] = [
                ($expertise->getPicture(DossierExpertisePicture::TYPE_VEHICLE_REAR) ? $expertise->getPicture(DossierExpertisePicture::TYPE_VEHICLE_REAR)->getUrl(false, true) : 'Photo non éditée'),
                ($expertise->getPicture(DossierExpertisePicture::TYPE_SERIAL_NUMBER) ? $expertise->getPicture(DossierExpertisePicture::TYPE_SERIAL_NUMBER)->getUrl(false, true) : 'Photo non éditée'),
                ($expertise->getPicture(DossierExpertisePicture::TYPE_TECHNICAL_CONTROL_LABEL) ? $expertise->getPicture(DossierExpertisePicture::TYPE_TECHNICAL_CONTROL_LABEL)->getUrl(false, true) : 'Photo non éditée'),
            ];
            $this->addTable($columns, $data, [33, 33, 33]);
        }

        $this->addDoubleSpace();
    }

    protected function addHistory(array $tableOfContents = [])
    {
        if ($this->getDossier()->isJudiciaire()) {
            $index = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($tableOfContents, 'Historique du véhicule');
            $this->addSectionSubTitle(__('%s. Historique du véhicule', $index));
        } else {
            $this->addSectionTitle(__('Chronologie des faits'));
        }

        $container = ContainerBuilderService::getInstance();
        $facts = $container->get(DossierFactService::class)->getRepository()->findByDossierAndExpertise($this->getExpertise());
        if (!$facts) {
            $content = '<p><em>' . __("Aucun fait.") . '</em></p>';
            $this->addContent($content, '', true);
        } else {
            $factsByDate = DossierFactService::sortFacts($facts);

            $columns = ['Date', 'Kilométrage', 'Évènement', 'Photo'];
            $data = [];
            foreach ($factsByDate as $date => $facts) {
                foreach ($facts as $fact) {
                    $file = '';
                    if ($fact->getDocument() and in_array(getExtension($fact->getDocument()->getFile()), ['jpg', 'jpeg', 'png', 'gif']) and $this->withPictures) {
                        $file .= $fact->getDocument()->getUrl(false, true);
                    } elseif ($fact->getFile() and in_array(getExtension($fact->getFile()), ['jpg', 'jpeg', 'png']) and $this->withPictures) {
                        $file = Assets::getMediaUrl($fact->getDossier()->getFolder() . $fact->getFile()) . '?dossier_ref=' . $this->getDossier()->getReference();
                    } elseif ($fact->getFile() and getExtension($fact->getFile()) == 'pdf') {
                        $fullPath = WEB_PATH . '/medias/' . $fact->getDossier()->getFolder();
                        $pathInfo = pathinfo($fact->getFile());
                        $fileName = $pathInfo['filename'] . '-thumbnail';

                        $images = ImagickHelper::convertPdf($fullPath . $fact->getFile(), true);
                        $file = Assets::getMediaUrl($images[0]) . '?dossier_ref=' . $this->getDossier()->getReference();
                    }

                    $data[] = [
                        ($fact->getDate() ? $fact->getDate()->format('d/m/Y') : ''),
                        ($fact->getMileage() ? $fact->getMileage() . ' ' . DossierFactService::getMileageType($fact->getMileageType()) : ''),
                        $this->formatText($fact->getComment()),
                        $file
                    ];
                }
            }

            $this->addTable($columns, $data, [15, 15, 50, 20]);
        }
    }

    protected function addVehicleLoan()
    {
        $expertise = $this->getExpertise();
        $vehicle = $expertise->getDossier()->getVehicle();

        if ($vehicle->getLoan() == 'unsure') {
            return;
        }

        $this->addSectionSubTitle(__('Conditions d\'achat'));

        $table = $this->section->addTable();
        $table->addRow();
        $table->addCell(4500)->addText('Achat à crédit : ', ['bold' => true]);
        $table->addCell(4500)->addText(($vehicle->getLoan() == 'yes' ? 'Oui' : 'Non'));
        if ($vehicle->getLoan() == 'yes' and $vehicle->getLoanAmount()) {
            $table->addRow();
            $table->addCell(4500)->addText('Échéances en cours : ', ['bold' => true]);
            $table->addCell(4500)->addText(Number::formatAmount($vehicle->getLoanAmount(), DEFAULT_CURRENCY) . ' / mois jusqu\'en ' . ($vehicle->getLoanEndDate() ? $vehicle->getLoanEndDate()->format('m/Y') : ''));

            /*$table->addRow();
            $table->addCell(4500)->addText('Assurance en cas de décès sur le prêt : ', ['bold' => true]);
            $table->addCell(4500)->addText(($vehicle->getLoanInsurance() ? 'Oui' : 'Non'));*/
        }

        $this->addDoubleSpace();
    }

    /**
     * @param bool $addPreviousExpertises
     * @param array $tableOfContents
     * @return void
     */
    protected function addExpertiseInfos(bool $addPreviousExpertises = false, array $tableOfContents = [])
    {
        $dossier = $this->getDossier();
        $expertise = $this->getExpertise();

        $this->addSectionTitle(__('Conditions d\'expertises et constatations'));

        $nbExpertise = 0;
        if ($addPreviousExpertises) {
            $previousExpertises = $expertise->getPreviousExpertises();
            if ($previousExpertises) {
                foreach ($previousExpertises as $previousExpertise) {
                    $nbExpertise++;
                    $title = __('Expertise %s du %s', $nbExpertise, dateFrench('d F Y', $previousExpertise->getDate()->getTimestamp()));
                    $this->addSectionTitleLight($title);

                    $this->addSectionSubTitle(__('Lieu d\'examen'));
                    $place = '<p>' . $previousExpertise->displayPlace() . '</p>';
                    $this->addContent($place, '', true);

                    $this->addExaminationConditions($previousExpertise);
                    $this->addLevelMeters($previousExpertise);

                    $persons = $previousExpertise->getPersons()->toArray();
                    $this->addTablePersons($persons, DossierExpertisePerson::STATUS_PRESENT, DossierSignature::TYPE_START);
                    $this->addTablePersons($persons, DossierExpertisePerson::STATUS_ABSENT);

                    $observations = $previousExpertise->getObservations()->toArray();
                    if ($observations) {
                        $this->addSectionSubTitle('Constatations');
                        $this->addObservations($observations);
                    }
                }
            }
        }

        if ($nbExpertise) {
            $nbExpertise++;
            $title = __('Expertise %s du %s', $nbExpertise, dateFrench('d F Y', $expertise->getDate()->getTimestamp()));
            $this->addSectionTitleLight($title);

            $this->addSectionSubTitle(__('Lieu d\'examen'));
            $place = '<p>' . $expertise->displayPlace() . '</p>';
            $this->addContent($place, '', true);
        }

        $this->addExaminationConditions();
        $this->addLevelMeters();

        $persons = $expertise->getPersons()->toArray();
        $this->addTablePersons($persons, DossierExpertisePerson::STATUS_PRESENT, DossierSignature::TYPE_START);
        $this->addTablePersons($persons, DossierExpertisePerson::STATUS_ABSENT);

        $observations = $expertise->getObservations()->toArray();
        if ($observations) {
            $this->addSectionSubTitle('Constatations');
            $this->addObservations($observations);
        }

        if ($expertise->getConservatoryMeasures()) {
            $this->addSectionSubTitle(__('Mesures conservatoires à prendre'));

            $content = $this->formatText($expertise->getConservatoryMeasures());
            $this->addContent($content);

            if ($expertise->getConservatoryMeasuresFile() and $this->withPictures) {
                $this->addImage(Assets::getMediaUrl($expertise->getDossier()->getFolder() . $expertise->getConservatoryMeasuresFile() . '?dossier_ref=' . $this->getDossier()->getReference()));
            } else {
                $this->addDoubleSpace();
            }
        }
    }

    protected function addExaminationConditions(?DossierExpertise $expertise = null)
    {
        if ($expertise === null) {
            $expertise = $this->getExpertise();
        }
        if (!$expertise->getExpertiseVehicle()) {
            return;
        }

        $missionType = $expertise->getDossier()->getMissionType();
        $reportType = $missionType?->getReport() ?: ExpertiseEnum::REPORT_STANDARD;

        $this->addSectionSubTitle(__('Conditions d\'examen'));

        $vehicleData = [];
        if ($expertise->getExpertiseVehicle()->getPresented() != 'unsure') {
            $vehicleData['Véhicule présenté'] = ($expertise->getExpertiseVehicle()->getPresented() == 'yes');
        }
        if ($expertise->getExpertiseVehicle()->getRolling() != 'unsure') {
            $vehicleData['Véhicule roulant'] = ($expertise->getExpertiseVehicle()->getRolling() == 'yes');
        }
        if ($reportType != ExpertiseEnum::REPORT_APPRAISED_VALUE) {
            if ($expertise->getExpertiseVehicle()->getImmobilisedNotDismantled() != 'unsure') {
                $vehicleData['Véhicule immobilisé'] = ($expertise->getExpertiseVehicle()->getImmobilisedNotDismantled() == 'yes');
            }
            if ($expertise->getExpertiseVehicle()->getOnLift() != 'unsure') {
                $vehicleData['Véhicule sur pont élévateur'] = ($expertise->getExpertiseVehicle()->getOnLift() == 'yes');
            }
            if ($expertise->getExpertiseVehicle()->getRepaired() != 'unsure') {
                $vehicleData['Véhicule réparé'] = ($expertise->getExpertiseVehicle()->getRepaired() == 'yes');
            }
            if ($expertise->getExpertiseVehicle()->getDismantled() != 'unsure') {
                $vehicleData['Véhicule démonté'] = ($expertise->getExpertiseVehicle()->getDismantled() == 'yes');
            }
            if ($expertise->getExpertiseVehicle()->getPartiallyDismantled() != 'unsure') {
                $vehicleData['Véhicule partiellement démonté'] = ($expertise->getExpertiseVehicle()->getPartiallyDismantled() == 'yes');
            }
            if ($expertise->getDossier()->getVehicle()->getReassembled() != 'unsure') {
                $vehicleData['Véhicule remonté'] = ($expertise->getDossier()->getVehicle()->getReassembled() == 'yes');
            }
            if ($expertise->getExpertiseVehicle()->getEngineWorking() and $expertise->getExpertiseVehicle()->getEngineWorking() != 'unsure') {
                $vehicleData['Moteur démarre'] = ($expertise->getExpertiseVehicle()->getEngineWorking() == 'yes');
            }
            if ($expertise->getDossier()->getVehicle()->getDraining() != 'unsure') {
                $vehicleData['Vidange des fluides et filtres réalisée'] = ($expertise->getDossier()->getVehicle()->getDraining() == 'yes');
            }
            if ($expertise->getExpertiseVehicle()->getPartExamination() != 'unsure') {
                $vehicleData['Examen sur pièce'] = ($expertise->getExpertiseVehicle()->getPartExamination() == 'yes');
            }
        }
        $table = $this->section->addTable();
        foreach ($vehicleData as $name => $value) {
            $table->addRow();
            $table->addCell(4500)->addText($name . ' : ', ['bold' => true]);
            $table->addCell(4500)->addText(($value ? 'Oui' : 'Non'));
        }

        if ($expertise->getDossier()->getVehicle()->getDrainingComment()) {
            $table->addRow();
            $table->addCell(4500)->addText('Précisions sur la vidange des fluides et filtres :', ['bold' => true]);
            $table->addCell(4500)->addText($this->formatText($expertise->getDossier()->getVehicle()->getDrainingComment()));
        }

        $this->addDoubleSpace();


        $this->addSectionSubTitle(__('Commentaire sur l\'identification'));
        $content = '<p>' . ($expertise->getExpertiseVehicle()->getIdentificationComment() ?: __('Aucun commentaire')) . '</p>';
        $this->addContent($content);

        if ($expertise->getExpertiseVehicle()->getExaminationCondition()) {
            $this->addSectionSubTitle(__('Conditions d\'examen'));
            $content = '<p>' . $expertise->getExpertiseVehicle()->getExaminationCondition() . '</p>';
            $this->addContent($content);
        }
    }

    public function addEvaluations(array $tableOfContents = [])
    {
        $dossier = $this->getDossier();
        $expertise = $this->getExpertise();

        if ($dossier->isJudiciaire()) {
            $index = DossierExpertiseJudiciaireTableOfContentsFactory::findIndex($tableOfContents, 'Méthodologie et évaluation de remise en état');
            $this->addSectionTitle(__('%s. Méthodologie et évaluation de remise en état', $index));
        } else {
            $this->addSectionTitle(__('Évaluation et préjudice annexe'));
        }

        if ($dossier->isJudiciaire()) {
            $index = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($tableOfContents, 'Méthodologie - Chiffrage');
            $this->addSectionSubTitle(__('%s. Méthodologie - Chiffrage', $index));
        } else {
            $this->addSectionSubTitle(__('Évaluation'));
        }
        if (!$expertise->getEvaluation()) {
            $content = '<p><em>' . __('Aucune évaluation') . '</em></p>';
        } else {
            $content = $this->formatText($expertise->getEvaluation());
        }
        $this->addContent($content, '', true);


        if ($dossier->isJudiciaire()) {
            $index = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($tableOfContents, 'Observations de l\'expert de justice');
            $this->addSectionSubTitle(__('%s. Observations de l\'expert de justice', $index));
        } else {
            $this->addSectionSubTitle(__('Observations'));
        }
        if (!$expertise->getNotes()) {
            $content = '<p><em>' . __('Aucune observation.') . '</em></p>';
        } else {
            $content = $this->formatText($expertise->getNotes());
        }
        $this->addContent($content, '', true);


        $report = $expertise->getExpertiseReport();
        $content = '';
        if ($report->getDisplayPrice() or $report->getDisplayFees()) {
            $content = '<p>';
            if ($report->getDisplayPrice()) {
                $content .= __('Frais de la 1ère expertise du cabinet %s : %s', $dossier->getCompany()->getName(), ($dossier->getPrice() ? $dossier->displayPrice() : __('Aucun'))) . '<br/>';
            }
            if ($report->getDisplayFees()) {
                $content .= __('Frais annexes à l\'expertise du cabinet %s :', $dossier->getCompany()->getName()) . '<br/>';
                if ($dossier->getSurcharges()) {
                    foreach ($dossier->getSurcharges() as $surcharge) {
                        $content .= '&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;' . $surcharge->getName() . ' : ' . $surcharge->displayAmount() . '<br/>';
                    }
                }
            }
            $content .= '</p>';

            /*$content .= '<p>';
            $content .= __('Frais de remorquage : %s', (($dossier->getBreakdown() and $dossier->getBreakdown()->getTowingCost()) ? Number::formatAmount($dossier->getBreakdown()->getTowingCost(), DEFAULT_CURRENCY) : __('Aucun'))) . '<br/>';
            if ($dossier->getVehicle()->getCarStorage() == 'yes') {
                $content .= __('Frais de gardiennage : %s', Number::formatAmount($dossier->getVehicle()->getCarStorageFees(), DEFAULT_CURRENCY) . ' TTC / jour');
                if ($dossier->getVehicle()->getCarStorageDate()) {
                    $content .= ' depuis le ' . $dossier->getVehicle()->getCarStorageDate()->format('d/m/Y');
                }
                $content .= '<br/>';
            } else {
                $content .= __('Frais de gardiennage : Non') . '<br>';
            }
            $content .= '</p>';*/
        }
        if ($expertise->getPrejudice()) {
            $content .= $expertise->getPrejudice();
        }
        if ($content) {
            if ($dossier->isJudiciaire()) {
                $index = DossierExpertiseJudiciaireTableOfContentsFactory::findSubIndex($tableOfContents, 'Coût du remorquage et immobilisation');
                $this->addSectionSubTitle(__('%s. Coût du remorquage et immobilisation', $index));
            } else {
                $this->addSectionSubTitle(__('Préjudice annexe'));
            }
            $content = $this->formatText($content);
            $this->addContent($content, '', true);
        }
    }

    /**
     * @param DossierExpertiseObservations[] $observations
     * @param string $title
     * @return void
     */
    protected function addObservations(array $observations, string $title = '')
    {
        $observationsByDate = DossierExpertiseObservationsService::sortObservations($observations);

        if ($title) {
            $content = '<p><strong><u>' . $title . '</u></strong></p>';
            $this->addContent($content);
            $this->addSpace();
        }

        foreach ($observationsByDate as $date => $observations) {
            $content = '<p><strong><u>' . __('Constatation du %s', dateFr($date)) . '</u></strong></p>';
            $this->addContent($content);
            $this->addSpace();

            foreach ($observations as $observation) {
                if (str_contains($observation->getComment(), '<p')) {
                    $content = $observation->getComment();
                } else {
                    $content = '<p>' . $observation->getComment() . '</p>';
                }
                $this->addContent($content, '', true);

                if ($observation->getFile() and $this->withPictures) {
                    $this->addImage($observation->getUrl(true), $observation->getLegend());
                }
                if ($observation->getFile2() and $this->withPictures) {
                    $this->addImage($observation->getUrl2(true), $observation->getLegend2());
                }
            }
        }
    }

    protected function addTechnicalAnalyses()
    {
        $dossier = $this->getDossier();
        $expertise = $this->getExpertise();
        $report = $expertise->getExpertiseReport();
        if (!$report) {
            return;
        }

        $container = ContainerBuilderService::getInstance();

        $this->addSectionTitle('Analyses et responsabilités');

        $this->addSectionSubTitle(__('Analyses techniques'));
        $content = ($report->getTechnicalAnalyses() ?: '<p><em>' . __('Aucune analyse technique indiquée') . '</em></p>');
        $this->addContent($content, '', true);
        if ($report->getTechnicalAnalyses()) {
            $documents = $container->get(DossierDocumentService::class)->getRepository()->findBy(['expertise' => $expertise, 'type' => DossierDocument::TYPE_TECHNICAL_ANALYSIS]);
            if ($documents) {
                foreach ($documents as $document) {
                    $this->addImage($document->getUrl(false, true));
                }
            }
        }

        if ($report->getCauses() or $report->getCausesPictures()) {
            $this->addSectionSubTitle('Causes');
            $content = $report->getCauses();
            if ($report->getCausesPictures() and $this->withPictures) {
                foreach ($report->getCausesPictures() as $pictureId) {
                    $picture = $container->get(DossierExpertisePictureService::class)->getRepository()->findOneBy(['id' => $pictureId, 'expertise' => $expertise, 'client' => $expertise->getClient()]);
                    if ($picture) {
                        $this->addImage($picture->getUrl(false, true), ($picture->getName() ?: DossierExpertisePictureService::getType($picture->getType())));
                    }
                }
            }

            $this->addContent($content, '', true);
        }

        if ($report->getConsequences() or $report->getConsequencesPictures()) {
            $this->addSectionSubTitle('Conséquences');
            $content = $report->getConsequences();
            if ($report->getConsequencesPictures() and $this->withPictures) {
                foreach ($report->getConsequencesPictures() as $pictureId) {
                    $picture = $container->get(DossierExpertisePictureService::class)->getRepository()->findOneBy(['id' => $pictureId, 'expertise' => $expertise, 'client' => $expertise->getClient()]);
                    if ($picture) {
                        $this->addImage($picture->getUrl(false, true), ($picture->getName() ?: DossierExpertisePictureService::getType($picture->getType())));
                    }
                }
            }

            $this->addContent($content, '', true);
        }

        $this->addSectionSubTitle(__('Responsabilités'));
        $content = ($report->getResponsibilities() ?: '<p><em>' . __('Aucune responsabilité indiquée') . '</em></p>');
        $this->addContent($content, '', true);
    }

    protected function addFollowups(array $tableOfContents = [])
    {
        $expertise = $this->getExpertise();
        $title = __('Suites à donner');
        if ($tableOfContents) {
            $index = DossierExpertiseJudiciaireTableOfContentsFactory::findIndex($tableOfContents, 'Pièces demandées aux parties');
            $title = __('%s. Pièces demandées aux parties', $index);
        }
        $this->addContent($this->formatText($expertise->getFollowup()), $title, true);
    }

    protected function addPositions()
    {
        $expertise = $this->getExpertise();
        $this->addContent($this->formatText($expertise->getPositions()), __('Position des parties'), true);
    }

    protected function addLevelMeters(?DossierExpertise $expertise = null)
    {
        if ($expertise === null) {
            $expertise = $this->getExpertise();
        }
        if (!$expertise->getExpertiseVehicle()) {
            return;
        }
        if ($expertise->getExpertiseVehicle()->getLevelIgnore() and !$expertise->getExpertiseVehicle()->getLevelComment()) {
            return;
        }

        $this->addSectionSubTitle(__('Niveaux à l\'expertise'));

        if (!$expertise->getExpertiseVehicle()->getLevelIgnore()) {
            $size = 3000;
            if ($expertise->getExpertiseVehicle()->getLevelFuel()) {
                $size = 2250;
            }

            $table = $this->section->addTable();
            $table->addRow();
            $table->addCell($size)->addText(__('Niveau d\'huile moteur'), ['bold' => true], ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER]);
            $table->addCell($size)->addText(__('Compteur'), ['bold' => true], ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER]);
            $table->addCell($size)->addText(__('Niveau du liquide de refroidissement'), ['bold' => true], ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER]);
            if ($expertise->getExpertiseVehicle()->getLevelFuel()) {
                $table->addCell($size)->addText(__('Niveau de carburant'), ['bold' => true], ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER]);
            }

            $table->addRow(500);
            $table2 = $table->addCell($size, ['valign' => 'center'])->addTable(['alignment' => \PhpOffice\PhpWord\SimpleType\JcTable::CENTER]);
            if ($expertise->getExpertiseVehicle()->getLevelEngineOilUnreadable()) {
                $table2->addRow();
                $table2->addCell($size)->addText(__('Illisible'), '', ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER]);
            } else {
                $this->getLevelMeter($table2, $expertise->getExpertiseVehicle()->getLevelEngineOil());
            }

            $table->addCell($size, ['valign' => 'center'])->addText($expertise->getExpertiseVehicle()->getMeter() . ' km', '', ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER]);

            $table2 = $table->addCell($size, ['valign' => 'center'])->addTable(['alignment' => \PhpOffice\PhpWord\SimpleType\JcTable::CENTER]);
            if ($expertise->getExpertiseVehicle()->getLevelCoolantUnreadable()) {
                $table2->addRow();
                $table2->addCell($size)->addText(__('Illisible'), '', ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER]);
            } else {
                $this->getLevelMeter($table2, $expertise->getExpertiseVehicle()->getLevelCoolant());
            }

            if ($expertise->getExpertiseVehicle()->getLevelFuel()) {
                $table2 = $table->addCell($size, ['valign' => 'center'])->addTable(['alignment' => \PhpOffice\PhpWord\SimpleType\JcTable::CENTER]);
                $this->getLevelMeter($table2, $expertise->getExpertiseVehicle()->getLevelFuel());
            }

            if ($this->withPictures) {
                $table->addRow();
                $cell = $table->addCell($size);
                if ($expertise->getPicture(DossierExpertisePicture::TYPE_ENGINE_OIL_LEVEL)) {
                    $img = $expertise->getPicture(DossierExpertisePicture::TYPE_ENGINE_OIL_LEVEL)->getUrl(false, true);
                    $width = 150;
                    $height = $this->getImageHeight($img, $width);
                    if ($height) {
                        $cell->addImage($img, ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER, 'width' => $width, 'height' => $height]);
                    }
                }
                $cell = $table->addCell($size);
                if ($expertise->getPicture(DossierExpertisePicture::TYPE_METER)) {
                    $img = $expertise->getPicture(DossierExpertisePicture::TYPE_METER)->getUrl(false, true);
                    $width = 150;
                    $height = $this->getImageHeight($img, $width);
                    if ($height) {
                        $cell->addImage($img, ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER, 'width' => $width, 'height' => $height]);
                    }
                }
                $cell = $table->addCell($size);
                if ($expertise->getPicture(DossierExpertisePicture::TYPE_COOLANT_LEVEL)) {
                    $img = $expertise->getPicture(DossierExpertisePicture::TYPE_COOLANT_LEVEL)->getUrl(false, true);
                    $width = 150;
                    $height = $this->getImageHeight($img, $width);
                    if ($height) {
                        $cell->addImage($img, ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER, 'width' => $width, 'height' => $height]);
                    }
                }

                if ($expertise->getExpertiseVehicle()->getLevelFuel()) {
                    $cell = $table->addCell($size);
                    if ($expertise->getPicture(DossierExpertisePicture::TYPE_FUEL_LEVEL)) {
                        $img = $expertise->getPicture(DossierExpertisePicture::TYPE_FUEL_LEVEL)->getUrl(false, true);
                        $width = 150;
                        $height = $this->getImageHeight($img, $width);
                        if ($height) {
                            $cell->addImage($img, ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER, 'width' => $width, 'height' => $height]);
                        }
                    }
                }
            }
        }

        if ($expertise->getExpertiseVehicle()->getLevelComment()) {
            $this->addContent($this->formatText($expertise->getExpertiseVehicle()->getLevelComment()), __('Commentaire sur les niveaux'), true);
        }
    }

    /**
     * @param int $value
     * @return void
     */
    protected function getLevelMeter(Table $table, int $value): void
    {
        $width = 200;

        $table->addRow();
        $table->addCell($width)->addText('-', '', ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER]);

        for ($i = 1; $i <= 11; $i++) {
            $color = 'FF0000';
            if ($i >= 4 and $i <= 8) {
                $color = '00FF00';
            }

            $columnContent = '';
            if ($value == $i) {
                $columnContent = '●';
            }
            $table->addCell($width, ['bgColor' => $color, 'borderColor' => '000000', 'borderSize' => 1])->addText($columnContent, '', ['color' => 'FFFFFF', 'alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER]);
        }
        $table->addCell($width)->addText('+', '', ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER]);
    }

    /**
     * @param array $persons
     * @return void
     */
    protected function addTableEnd(array $persons)
    {
        $this->addSectionTitle(__('Signatures'));

        $columns = ['Nom', 'Signature'];
        $data = [];
        foreach ($persons as $person) {
            if ($person->getStatus() != DossierExpertisePerson::STATUS_PRESENT) {
                continue;
            }

            $data[] = [
                $person->getRepresentativeName(),
                $person->getSignature() ? $person->getSignature()->getUrl() . '?dossier_ref=' . $this->getDossier()->getReference() : ''
            ];
        }

        if ($data) {
            $this->addTable($columns, $data, [75, 25]);
        }
    }

    protected function addSignatureEnd()
    {
        $clientInfos = $this->getClientInfos();
        $expert = $this->getDossier()->getExpert();

        $content = '<p style="text-align: right">' . __('L\'expert : %s %s', ($expert ? $expert->getFirstName() . ' ' . $expert->getLastName() : $clientInfos['name']), ($expert ? $expert->getExpertRegistrationNumber() : '')) . '</p>';
        $this->addContent($content);
        $this->addSpace();

        if ($clientInfos[ConfigEnum::SIGNATURE]) {
            $this->addImage($clientInfos[ConfigEnum::SIGNATURE] . '?dossier_ref=' . $this->getDossier()->getReference(), '', 150, \PhpOffice\PhpWord\SimpleType\Jc::END);
        }
    }

    protected function addExpertSignatureEnd(User $user): void
    {
        $content = '<p style="text-align: right">' . __('L\'expert : %s', $user->getFirstName() . ' ' . $user->getLastName());
        if ($user->getExpertNumber()) {
            $content .= '<br>' . __('N° %s', $user->getExpertNumber());
        }
        $content .= '</p>';
        $this->addContent($content);

        if ($user->getUserConfig(ConfigEnum::SIGNATURE)) {
            $this->addImage($user->getUserConfig(ConfigEnum::SIGNATURE) . '?dossier_ref=' . $this->getDossier()->getReference(), '', 150, \PhpOffice\PhpWord\SimpleType\Jc::END);
        }
    }

    protected function addConclusions()
    {
        $expertise = $this->getExpertise();
        $report = $expertise->getExpertiseReport();
        if ($report and $report->getConclusions()) {
            $this->addContent($this->formatText($report->getConclusions()), __('Conclusions'), true);
        }
    }

    protected function addExpertisePictures()
    {
        $expertise = $this->getExpertise();
        $report = $expertise->getExpertiseReport();
        if (!$report) {
            return;
        }
        if (!$this->withPictures) {
            return;
        }

        $pictures = $report->getPictures();
        if ($pictures) {
            $this->addSection();
            $this->addSectionTitle(__('Photographies supplémentaires'));

            $container = ContainerBuilderService::getInstance();
            foreach ($pictures as $pictureId) {
                $picture = $container->get(DossierExpertisePictureService::class)->getRepository()->findOneBy(['id' => $pictureId, 'expertise' => $expertise, 'client' => $expertise->getClient()]);
                if ($picture) {
                    $this->addImage($picture->getUrl(false, true), ($picture->getName() ?: DossierExpertisePictureService::getType($picture->getType())));
                }
            }
        }
    }

    /**
     * @param array|null $selectedDocuments
     * @return void
     */
    protected function addDocuments(?array $selectedDocuments = null)
    {
        $expertise = $this->getExpertise();
        $dossier = $expertise->getDossier();
        $person = $this->getPerson();

        $documents = $expertise->getDossier()->getDocuments();
        $data = array();
        foreach ($documents as $document) {
            if (in_array($document->getType(), [DossierDocument::TYPE_CONVOCATION, DossierDocument::TYPE_SUMMARY, DossierDocument::TYPE_EXPERTISE, DossierDocument::TYPE_CONTRADICTORY, DossierDocument::TYPE_CONTRADICTORY_WO_PICTURES, DossierDocument::TYPE_CONTRADICTORY_WO_DOCUMENTS, DossierDocument::TYPE_POSTAL_MAIL_DEPOSIT, DossierDocument::TYPE_POSTAL_MAIL_RECEIPT, DossierDocument::TYPE_POSTAL_MAIL_NON_RECEIPT])) {
                continue;
            }
            if ($selectedDocuments !== null and !in_array($document->getId(), $selectedDocuments)) {
                continue;
            }
            if ($document->getVirusScan() == DossierDocument::VIRUS_SCAN_ALERT) {
                continue;
            }

            if ($person and $document->getPerson() !== $person) {
                continue;
            }
            if ($document->getExpertise() and $document->getExpertise() !== $expertise) {
                continue;
            }

            $fullPath = WEB_PATH . '/medias/' . $dossier->getFolder();
            $extension = getExtension($document->getFile());
            $documentName = DossierDocumentService::getType($document->getType());
            if (!file_exists($fullPath . $document->getFile())) {
                continue;
            }
            if (filter_var($documentName, FILTER_VALIDATE_URL)) {
                $documentName = str_replace(['http://', 'https://'], '', $documentName);
            }

            if ($extension == 'pdf') {
                $images = ImagickHelper::convertPdf($fullPath . $document->getFile(), false);
                if ($images) {
                    foreach ($images as $image) {
                        $data[] = [$documentName, Assets::getMediaUrl($image) . '?dossier_ref=' . $dossier->getReference()];
                    }
                }
            } else {
                $data[] = [$documentName, $document->getUrl(false, true)];
            }
        }
        if ($data) {
            $this->addSection();
            $this->addSectionTitle(__('Documents'));
            $this->addTable(['Type de photographie', 'Photographie'], $data);
        }
    }

    /**
     * @param DossierExpertisePerson[] $persons
     * @return void
     */
    protected function addPersonsComments(array $persons)
    {
        $hasComment = false;
        foreach ($persons as $person) {
            if ($person->getComment()) {
                $hasComment = true;
                break;
            }
        }
        if (!$hasComment) {
            return;
        }

        $missionType = $this->getDossier()->getMissionType();
        $reportType = $missionType?->getReport() ?: ExpertiseEnum::REPORT_STANDARD;
        if ($reportType != ExpertiseEnum::REPORT_EXPERTISE_JUDICIAIRE) {
            $this->addSectionSubTitle('Auditions des parties');
        }

        $w = [20, 20, 60];
        $columns = ['Noms', 'Qualité', 'Commentaire'];
        $data = [];
        foreach($persons as $person) {
            if (!$person->getComment()) {
                continue;
            }

            $name = '<strong>' . $person->getRepresentativeName() . '</strong>';
            $type = $person->displayType();
            if ($this->getDossier()->isJudiciaire()) {
                if ($person->getType() == DossierExpertisePerson::TYPE_INSTITUTION and $person->getInstitution() and $person->getInstitution()->getType() == DossierInstitution::TYPE_CONTACT) {
                    $type = __('Demandeur');
                }
                if (!$person->getParent() and $person->getInstitution() and $person->getInstitution()->isDemandeur()) {
                    $type = __('Demandeur %s', ($person->getInstitution()->getDefendantNumber() ?: ''));
                }
                if (!$person->getParent() and $person->getInstitution() and $person->getInstitution()->isDefendant()) {
                    $type = __('Défendeur %s', ($person->getInstitution()->getDefendantNumber() ?: ''));
                }
            }

            $personData = [
                $name,
                $type,
                $person->getComment()
            ];
            $data[] = $personData;
        }

        $this->addTable($columns, $data, $w);
    }

    public function addHtml($element, $html): void
    {
        $html = str_replace(' & ', ' et ', $html);
        $html = str_replace('&amp;', 'et', $html);
        $html = str_replace('<br>', '<br/>', $html);
        Html::addHtml($element, $html);
    }
}
