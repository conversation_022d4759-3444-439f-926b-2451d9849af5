<?php

namespace MatGyver\Controllers\Ajax\Dossier;

use MatGyver\Controllers\Ajax\AbstractAjaxController;
use MatGyver\Forms\Dossier\DossierForm;
use MatGyver\Services\RightsService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class DossierAjaxController
 * @package MatGyver\Controllers\Ajax\Shop
 */
class DossierAjaxController extends AbstractAjaxController
{

    #[Route('/ajax/dossier/tutorial/', name: 'ajax_dossier_tutorial')]
    public function dossierTutorial()
    {
        if (!RightsService::hasAccess(UNIVERSE_APP_DOSSIER)) {
            $this->sendUnauthorizedAccess();
        }

        $validatePost = $this->get(DossierForm::class)->validatePostInsert($this->submittedData);
        if (!$validatePost['valid']) {
            $this->displayOutput([
                'status' => false,
                'message' => $validatePost['message'],
                'step' => $validatePost['step'] ?? 1,
            ]);
        }

        $this->displayOutput(['status' => true]);
    }
}
