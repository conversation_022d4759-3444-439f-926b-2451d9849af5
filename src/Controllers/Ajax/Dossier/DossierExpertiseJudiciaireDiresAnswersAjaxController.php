<?php

namespace MatGyver\Controllers\Ajax\Dossier;

use MatGyver\Controllers\Ajax\AbstractAjaxController;
use MatGyver\Services\Dossier\DossierExpertiseService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseJudiciaireDireService;
use MatGyver\Services\RightsService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class DossierExpertiseJudiciaireCourtQuestionsAnswersAjaxController
 * @package MatGyver\Controllers\Ajax\Dossier
 */
class DossierExpertiseJudiciaireDiresAnswersAjaxController extends AbstractAjaxController
{
    #[Route('/ajax/dossier/expertise/judiciaire/dires_answers/sort/', name: 'ajax_dossier_expertise_judiciaire_dires_answers_sort')]
    public function ajaxDossierExpertiseJudiciaireDiresAnswersSort()
    {
        if (!RightsService::hasAccess(UNIVERSE_APP_DOSSIER)) {
            $this->sendUnauthorizedAccess();
        }

        $diresAnswersIds = filter_input(INPUT_POST, 'diresAnswers', FILTER_UNSAFE_RAW, FILTER_REQUIRE_ARRAY);
        if (!$diresAnswersIds) {
            $this->displayOutput(['status' => false, 'message' => __('Les réponses n\'ont pas été reçus.')]);
            return;
        }

        $diresAnswersIds = array_filter($diresAnswersIds);

        $expertiseId = filter_input(INPUT_POST, 'expertiseId', FILTER_VALIDATE_INT);
        if (!$expertiseId) {
            $this->displayOutput(['status' => false, 'message' => __('Aucune expertise définie.')]);
            return;
        }
        $expertise = $this->get(DossierExpertiseService::class)->getRepository()->find($expertiseId);
        if (!$expertise) {
            $this->displayOutput(['status' => false, 'message' => __('Cette expertise n\'existe pas.')]);
            return;
        }

        $direId = filter_input(INPUT_POST, 'direId', FILTER_VALIDATE_INT);
        if (!$direId) {
            $this->displayOutput(['status' => false, 'message' => __('Aucun dire défini.')]);
            return;
        }
        $dire = $this->get(DossierExpertiseJudiciaireDireService::class)->getRepository()->find($direId);
        if (!$dire) {
            $this->displayOutput(['status' => false, 'message' => __('Ce dire n\'existe pas.')]);
            return;
        }

        $updatePositions = $this->get(DossierExpertiseJudiciaireDireService::class)->updateAnswersPositions($dire, $diresAnswersIds);
        if (!$updatePositions['valid']) {
            $this->displayOutput(['status' => false, 'message' => $updatePositions['message']]);
            return;
        }

        $this->displayOutput(['status' => true]);
    }
}
