<?php

namespace MatGyver\Controllers\Ajax\Dossier;

use MatGyver\Controllers\Ajax\AbstractAjaxController;
use MatGyver\Services\Dossier\DossierFactService;
use MatGyver\Services\Dossier\DossierService;
use MatGyver\Services\RightsService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class DossierFactsAjaxController
 * @package MatGyver\Controllers\Ajax\Dossier
 */
class DossierFactsAjaxController extends AbstractAjaxController
{
    #[Route('/ajax/dossier/facts/sort/', name: 'ajax_dossier_facts_sort')]
    public function ajaxDossierFactsSort()
    {
        if (!RightsService::hasAccess(UNIVERSE_APP_DOSSIER)) {
            $this->sendUnauthorizedAccess();
        }

        $factsIds = filter_input(INPUT_POST, 'facts', FILTER_UNSAFE_RAW, FILTER_REQUIRE_ARRAY);
        if (!$factsIds) {
            $this->displayOutput(['status' => false, 'message' => __('Les faits n\'ont pas été reçus.')]);
            return;
        }

        $factsIds = array_filter($factsIds);

        $dossierId = filter_input(INPUT_POST, 'dossierId', FILTER_VALIDATE_INT);
        if (!$dossierId) {
            $this->displayOutput(['status' => false, 'message' => __('Aucun dossier défini.')]);
            return;
        }
        $dossier = $this->get(DossierService::class)->getRepository()->find($dossierId);
        if (!$dossier) {
            $this->displayOutput(['status' => false, 'message' => __('Ce dossier n\'existe pas.')]);
            return;
        }

        $updateFacts = $this->get(DossierFactService::class)->updateFactsPositions($dossier, $factsIds);
        if (!$updateFacts['valid']) {
            $this->displayOutput(['status' => false, 'message' => $updateFacts['message']]);
            return;
        }

        $this->displayOutput(['status' => true]);
    }
}
