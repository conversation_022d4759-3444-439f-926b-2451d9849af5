<?php

namespace MatGyver\Controllers\Ajax\Dossier;

use MatGyver\Controllers\Ajax\AbstractAjaxController;
use MatGyver\Forms\Dossier\DossierCourrierSendForm;
use MatGyver\Services\RightsService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class DossierCourrierAjaxController
 * @package MatGyver\Controllers\Ajax\Shop
 */
class DossierCourrierAjaxController extends AbstractAjaxController
{
    #[Route('/ajax/dossier/courrier/send/tutorial/', name: 'ajax_dossier_courrier_send_tutorial')]
    public function dossierCourrierSendTutorial()
    {
        if (!RightsService::hasAccess(UNIVERSE_APP_DOSSIER)) {
            $this->sendUnauthorizedAccess();
        }

        $submittedData = $this->submittedData;

        $validatePost = $this->get(DossierCourrierSendForm::class)->validatePostInsert($submittedData);
        if (!$validatePost['valid']) {
            $this->displayOutput([
                'status' => false,
                'message' => $validatePost['message'],
                'step' => $validatePost['step'] ?? 1,
            ]);
        }

        $this->displayOutput(['status' => true]);
    }
}
