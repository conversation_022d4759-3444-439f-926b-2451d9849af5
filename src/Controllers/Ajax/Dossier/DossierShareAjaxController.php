<?php

namespace MatGyver\Controllers\Ajax\Dossier;

use MatGyver\Controllers\Ajax\AbstractAjaxController;
use MatGyver\Services\Dossier\DossierFactService;
use MatGyver\Services\Dossier\DossierService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseObservationsService;
use MatGyver\Services\RightsService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class DossierShareAjaxController
 * @package MatGyver\Controllers\Ajax\Dossier
 */
class DossierShareAjaxController extends AbstractAjaxController
{

    #[Route('/ajax/dossier/share/', name: 'ajax_dossier_share')]
    public function dossierShare()
    {
        if (!RightsService::hasAccess(UNIVERSE_APP_DOSSIER)) {
            $this->sendUnauthorizedAccess();
        }

        $submittedData = $this->submittedData;

        $dossierId = $submittedData['dossierId'] ?? null;
        if (!$dossierId) {
            $this->displayOutput(['status' => false, 'message' => __('Aucun dossier spécifié')]);
        }
        $dossier = $this->get(DossierService::class)->getRepository()->find($dossierId);
        if (!$dossier) {
            $this->displayOutput(['status' => false, 'message' => __('Ce dossier n\'existe pas')]);
        }

        $objects = $submittedData['objects'] ?? null;
        $type = $submittedData['type'] ?? null;
        $expertiseId = $submittedData['expertiseId'] ?? 0;
        $types = filter_var($submittedData['types'], FILTER_UNSAFE_RAW, FILTER_REQUIRE_ARRAY);

        if (!$objects) {
            if ($type == 'fact') {
                $this->displayOutput(['status' => false, 'message' => __('Aucun fait spécifié')]);
            } else if ($type == 'observation') {
                $this->displayOutput(['status' => false, 'message' => __('Aucune constatation spécifiée')]);
            }
        }
        $objects = explode(',', $objects);

        if (!in_array($type, ['fact', 'observation'])) {
            $this->displayOutput(['status' => false, 'message' => __('Type invalide')]);
        }
        if ($type == 'fact') {
            $share = $this->get(DossierFactService::class)->shareMultiple($objects, $types, $expertiseId);
        } else {
            $share = $this->get(DossierExpertiseObservationsService::class)->shareMultiple($objects, $types, $expertiseId);
        }
        if (!$share['valid']) {
            $this->displayOutput(['status' => false, 'message' => $share['message']]);
        }

        $this->displayOutput(['status' => true]);
    }
}
