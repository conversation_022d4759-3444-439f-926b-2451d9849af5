<?php

namespace MatGyver\Controllers\Ajax\Dossier;

use MatGyver\Controllers\Ajax\AbstractAjaxController;
use MatGyver\Forms\Dossier\DossierMailSendForm;
use MatGyver\Services\RightsService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class DossierAjaxController
 * @package MatGyver\Controllers\Ajax\Shop
 */
class DossierMailAjaxController extends AbstractAjaxController
{

    #[Route('/ajax/dossier/mail/send/tutorial/', name: 'ajax_dossier_mail_send_tutorial')]
    public function dossierMailSendTutorial()
    {
        if (!RightsService::hasAccess(UNIVERSE_APP_DOSSIER)) {
            $this->sendUnauthorizedAccess();
        }

        $submittedData = $this->submittedData;
        $submittedData['content'] = 'temp'; //serializeArray can't get ckeditor instance value

        $validatePost = $this->get(DossierMailSendForm::class)->validatePostInsert($submittedData);
        if (!$validatePost['valid']) {
            $this->displayOutput([
                'status' => false,
                'message' => $validatePost['message'],
                'step' => $validatePost['step'] ?? 1,
            ]);
        }

        $this->displayOutput(['status' => true]);
    }
}
