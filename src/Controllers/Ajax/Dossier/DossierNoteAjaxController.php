<?php

namespace MatGyver\Controllers\Ajax\Dossier;

use MatGyver\Controllers\Ajax\AbstractAjaxController;
use MatGyver\Services\Dossier\DossierNotesService;
use MatGyver\Services\Dossier\DossierService;
use MatGyver\Services\RightsService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class DossierNoteAjaxController
 * @package MatGyver\Controllers\Ajax
 */
class DossierNoteAjaxController extends AbstractAjaxController
{

    #[Route('/ajax/dossier/note/', name: 'ajax_dossier_note')]
    public function noteInsert()
    {
        if (!RightsService::hasAccess(UNIVERSE_APP_DOSSIER)) {
            $this->sendUnauthorizedAccess();
        }

        $dossierId = filter_input(INPUT_POST, 'dossier_id', FILTER_VALIDATE_INT);
        $content = filter_input(INPUT_POST, 'content', FILTER_UNSAFE_RAW);
        $label = filter_input(INPUT_POST, 'label', FILTER_UNSAFE_RAW);

        $dossier = $this->get(DossierService::class)->getRepository()->find($dossierId);
        if (!$dossier) {
            $this->displayOutput(['status' => false, 'message' => __('Ce dossier n\'existe pas')]);
        }

        $result = $this->get(DossierNotesService::class)->insert($dossier, $content, $label);
        if (!$result['valid']) {
            $this->displayOutput([
                'status' => $result['valid'],
                'message' => $result['message']
            ]);
        }

        $displayNote = $this->get(DossierNotesService::class)->displayNote($result['note']);
        $this->displayOutput([
            'status' => true,
            'note' => $displayNote,
            'message' => __('Note enregistrée')
        ]);
    }

    #[Route('/ajax/dossier/note/get/', name: 'ajax_dossier_note_get_form')]
    public function noteGet()
    {
        if (!RightsService::hasAccess(UNIVERSE_APP_DOSSIER)) {
            $this->sendUnauthorizedAccess();
        }

        $idNote = filter_input(INPUT_POST, 'id_note', FILTER_VALIDATE_INT);
        $note = $this->get(DossierNotesService::class)->getRepository()->find($idNote);
        if (!$note) {
            $this->displayOutput(['status' => false, 'message' => __('Cette note n\'existe pas')]);
        }

        $form = $this->get(DossierNotesService::class)->displayEditNoteForm($note);
        $this->displayOutput([
            'status' => true,
            'form' => $form
        ]);
    }

    #[Route('/ajax/dossier/note/update/', name: 'ajax_dossier_note_update')]
    public function noteUpdate()
    {
        if (!RightsService::hasAccess(UNIVERSE_APP_DOSSIER)) {
            $this->sendUnauthorizedAccess();
        }

        $idNote = filter_input(INPUT_POST, 'id_note', FILTER_VALIDATE_INT);
        $content = filter_input(INPUT_POST, 'content', FILTER_UNSAFE_RAW);
        $label = filter_input(INPUT_POST, 'label', FILTER_UNSAFE_RAW);

        $note = $this->get(DossierNotesService::class)->getRepository()->find($idNote);
        if (!$note) {
            $this->displayOutput(['status' => false, 'message' => __('Cette note n\'existe pas')]);
        }

        $result = $this->get(DossierNotesService::class)->update($note, $content, $label);
        if (!$result['valid']) {
            $this->displayOutput(['status' => $result['valid'], 'message' => $result['message']]);
        }

        $displayNote = $this->get(DossierNotesService::class)->displayNote($result['note']);
        $this->displayOutput([
            'status' => true,
            'note' => $displayNote,
            'message' => __('Note enregistrée'),
        ]);
    }

    #[Route('/ajax/dossier/note/delete/', name: 'ajax_dossier_note_delete')]
    public function noteDelete()
    {
        if (!RightsService::hasAccess(UNIVERSE_APP_DOSSIER)) {
            $this->sendUnauthorizedAccess();
        }

        $idNote = filter_input(INPUT_POST, 'id_note', FILTER_VALIDATE_INT);
        $note = $this->get(DossierNotesService::class)->getRepository()->find($idNote);
        if (!$note) {
            $this->displayOutput(['status' => false, 'message' => __('Cette note n\'existe pas')]);
        }

        $delete = $this->get(DossierNotesService::class)->delete($note);
        $delete['status'] = $delete['valid'];
        $this->displayOutput($delete);
    }

    #[Route('/ajax/dossier/notes/positions/', name: 'ajax_dossier_notes_positions')]
    public function notesPositions()
    {
        if (!RightsService::hasAccess(UNIVERSE_APP_DOSSIER)) {
            $this->sendUnauthorizedAccess();
        }

        $notes = filter_input(INPUT_POST, 'notes', FILTER_UNSAFE_RAW, FILTER_REQUIRE_ARRAY);
        if (!$notes) {
            $this->displayOutput(['status' => true]);
        }

        $result = $this->get(DossierNotesService::class)->updatePositions($notes);
        $result['status'] = $result['valid'];
        $this->displayOutput($result);
    }
}
