<?php

namespace MatGyver\Controllers\Ajax\Shop;

use MatGyver\Controllers\Ajax\AbstractAjaxController;
use MatGyver\Helpers\View\Table\Shop\ShopCustomersViewHelper;
use MatGyver\Services\Filters\ShopCustomersFiltersService;
use MatGyver\Services\RightsService;
use MatGyver\Services\Shop\ShopCustomersService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class ShopCustomersAjaxController
 * @package MatGyver\Controllers\Ajax\Shop
 */
class ShopCustomersAjaxController extends AbstractAjaxController
{

    #[Route('/ajax/shop/customers/datatable/', name: 'ajax_shop_customers_datatable')]
    public function shopCustomersDatatable()
    {
        if (!RightsService::hasAccess(UNIVERSE_ADMIN_SHOP)) {
            $this->displayOutput(array('data' => array()));
        }

        //datatable columns (utilisé pour le tri ORDER BY)
        $columns = array(
            0 => 'lastName',
            1 => 'date',
            2 => '',
            3 => '',
        );

        $filters = $this->get(ShopCustomersFiltersService::class)->getFilters();
        $dateStart = $filters['date_start'];
        $dateEnd = $filters['date_end'];
        $idProduct = $filters['id_product'];
        $search = filter_var($filters['search'], FILTER_UNSAFE_RAW);

        // custom filters
        $draw = filter_var($_REQUEST['draw'], FILTER_VALIDATE_INT);

        if (isset($_POST['product']) and $_POST['product']) {
            $idProduct = filter_input(INPUT_POST, 'product', FILTER_VALIDATE_INT);
        }

        //récupération du nombre de clients (sans LIMIT 0,10 et sans recherche)
        $totalRecords = $this->get(ShopCustomersService::class)->getRepository()->countSearchCustomers($idProduct, '', $dateStart, $dateEnd);

        //ordering
        $orderVar = 'date';
        $orderDir = 'DESC';
        if (isset($_REQUEST['order']) and $_REQUEST['order']) {
            foreach ($_REQUEST['order'] as $order) {
                if (!$columns[intval($order['column'])]) {
                    continue;
                }
                $orderVar = $columns[intval($order['column'])];
                $orderDir = ('asc' === $order['dir'] ? 'ASC' : 'DESC');
                break;
            }
        }

        //récupération du nombre de clients (sans LIMIT 0,10)
        $totalDisplayRecords = $totalRecords;
        if ($search) {
            $totalDisplayRecords = $this->get(ShopCustomersService::class)->getRepository()->countSearchCustomers($idProduct, $search, $dateStart, $dateEnd);
        }

        //paging
        $start = 0;
        $length = 10;
        if (isset($_REQUEST['start'])) {
            $start = filter_var($_REQUEST['start'], FILTER_VALIDATE_INT);
        }
        if (isset($_REQUEST['length'])) {
            $length = filter_var($_REQUEST['length'], FILTER_VALIDATE_INT);
        }

        //récupération de toutes les transactions avec $sLimit
        $customers = $this->get(ShopCustomersService::class)->getRepository()->searchCustomers($idProduct, $search, $dateStart, $dateEnd, $orderVar, $orderDir, $start, $length);

        $data = array();
        if ($customers) {
            $this->get(ShopCustomersViewHelper::class)->setCustomersRows($customers);
            $data = $this->get(ShopCustomersViewHelper::class)->getTable()->getRowsAsArray(false);
        }

        $json_data = array(
            'draw' => intval($draw),
            'iTotalRecords' => intval($totalRecords),
            'iTotalDisplayRecords' => intval($totalDisplayRecords),
            'aaData' => $data,
        );
        $this->displayOutput($json_data);
    }
}
