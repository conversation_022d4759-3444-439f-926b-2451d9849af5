<?php

namespace MatGyver\Controllers\Ajax\Shop;

use MatGyver\Controllers\Ajax\AbstractAjaxController;
use MatGyver\Helpers\View\Table\Shop\AppShopCreditNotesViewHelper;
use MatGyver\Helpers\View\Table\Shop\ShopCreditNotesViewHelper;
use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\RightsService;
use MatGyver\Services\Shop\CreditNotes\ShopCreditNotesExportService;
use MatGyver\Services\Shop\CreditNotes\ShopCreditNotesService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class ShopCreditNotesAjaxController
 * @package MatGyver\Controllers\Ajax\Shop
 */
class ShopCreditNotesAjaxController extends AbstractAjaxController
{

    #[Route('/ajax/credit_notes/datatable/', name: 'ajax_credit_notes_datatable')]
    public function creditNotesDatatable()
    {
        if (!RightsService::hasAccess(UNIVERSE_ADMIN_SHOP)) {
            $this->displayOutput(array('data' => array()));
        }

        //datatable columns (utilisé pour le tri ORDER BY)
        $columns = array(
            0 => 'id',
            1 => 'dateCreation',
            2 => '',
            3 => 'paymentMethod',
            4 => 'amountTaxIncl',
            5 => '',
        );

        $draw = filter_var($_REQUEST['draw'], FILTER_VALIDATE_INT);

        //récupération du nombre d'avoirs (sans LIMIT 0,10 et sans recherche)
        $totalRecords = $this->get(ShopCreditNotesService::class)->getCountCreditNotes();

        //search
        $search = '';
        if (isset($_REQUEST['search']) and $_REQUEST['search']) {
            $searchRules = $_REQUEST['search'];
            if (isset($searchRules['value']) and $searchRules['value']) {
                $search = addslashes($searchRules['value']);
            }
        }

        //ordering
        $orderVar = 'number';
        $orderDir = 'DESC';
        if (isset($_REQUEST['order']) and $_REQUEST['order']) {
            foreach ($_REQUEST['order'] as $order) {
                if (!$columns[intval($order['column'])]) {
                    continue;
                }
                $orderVar = $columns[intval($order['column'])];
                $orderDir = ('asc' === $order['dir'] ? 'ASC' : 'DESC');
                break;
            }
        }

        //récupération du nombre d'avoirs (sans LIMIT 0,10)
        $totalDisplayRecords = $totalRecords;
        if ($search) {
            $totalDisplayRecords = $this->get(ShopCreditNotesService::class)->getRepository()->countSearchCreditNotes($search);
        }

        //paging
        $start = 0;
        $length = 10;
        if (isset($_REQUEST['start'])) {
            $start = filter_var($_REQUEST['start'], FILTER_VALIDATE_INT);
        }
        if (isset($_REQUEST['length'])) {
            $length = filter_var($_REQUEST['length'], FILTER_VALIDATE_INT);
        }

        $creditNotes = $this->get(ShopCreditNotesService::class)->getRepository()->searchCreditNotes($search, $orderVar, $orderDir, $start, $length);

        $data = array();
        if ($creditNotes) {
            $this->get(ShopCreditNotesViewHelper::class)->setCreditNotesRows($creditNotes);
            $data = $this->get(ShopCreditNotesViewHelper::class)->getTable()->getRowsAsArray(false);
        }

        $json_data = array(
            'draw' => intval($draw),
            'iTotalRecords' => intval($totalRecords),
            'iTotalDisplayRecords' => intval($totalDisplayRecords),
            'aaData' => $data,
        );

        $this->displayOutput($json_data);
    }

    #[Route('/ajax/credit_notes/app/datatable/', name: 'ajax_credit_notes_app_datatable')]
    public function creditNotesAppDatatable()
    {
        if (!RightsService::hasAccess(UNIVERSE_APP_SHOP)) {
            $this->displayOutput(array('data' => array()));
        }

        //datatable columns (utilisé pour le tri ORDER BY)
        $columns = array(
            0 => 'id',
            1 => 'dateCreation',
            2 => '',
            3 => 'paymentMethod',
            4 => 'amountTaxIncl',
            5 => '',
        );

        $draw = filter_var($_REQUEST['draw'], FILTER_VALIDATE_INT);

        //récupération du nombre d'avoirs (sans LIMIT 0,10 et sans recherche)
        $totalRecords = $this->get(ShopCreditNotesService::class)->getCountCreditNotes();

        //search
        $search = '';
        if (isset($_REQUEST['search']) and $_REQUEST['search']) {
            $searchRules = $_REQUEST['search'];
            if (isset($searchRules['value']) and $searchRules['value']) {
                $search = addslashes($searchRules['value']);
            }
        }

        //ordering
        $orderVar = 'number';
        $orderDir = 'DESC';
        if (isset($_REQUEST['order']) and $_REQUEST['order']) {
            foreach ($_REQUEST['order'] as $order) {
                if (!$columns[intval($order['column'])]) {
                    continue;
                }
                $orderVar = $columns[intval($order['column'])];
                $orderDir = ('asc' === $order['dir'] ? 'ASC' : 'DESC');
                break;
            }
        }
        if (!$orderVar) {
            $orderVar = 'number';
        }

        //récupération du nombre d'avoirs (sans LIMIT 0,10)
        $totalDisplayRecords = $totalRecords;
        if ($search) {
            $totalDisplayRecords = $this->get(ShopCreditNotesService::class)->getRepository()->countSearchCreditNotes($search);
        }

        //paging
        $start = 0;
        $length = 10;
        if (isset($_REQUEST['start'])) {
            $start = filter_var($_REQUEST['start'], FILTER_VALIDATE_INT);
        }
        if (isset($_REQUEST['length'])) {
            $length = filter_var($_REQUEST['length'], FILTER_VALIDATE_INT);
        }

        $creditNotes = $this->get(ShopCreditNotesService::class)->getRepository()->searchCreditNotes($search, $orderVar, $orderDir, $start, $length);
        if ($creditNotes) {
            foreach ($creditNotes as $id => $creditNote) {
                if ($creditNote->getDossier() and RightsService::hasAccess(UNIVERSE_APP_DOSSIER) and !RightsService::isGranted(ATTRIBUTE_VIEW, UNIVERSE_APP_DOSSIER, $creditNote->getDossier()->getId())) {
                    unset($creditNotes[$id]);
                }
            }
        }

        $data = array();
        if ($creditNotes) {
            $this->get(AppShopCreditNotesViewHelper::class)->setCreditNotesRows($creditNotes);
            $data = $this->get(AppShopCreditNotesViewHelper::class)->getTable()->getRowsAsArray(false);
        }

        $json_data = array(
            'draw' => intval($draw),
            'iTotalRecords' => intval($totalRecords),
            'iTotalDisplayRecords' => intval($totalDisplayRecords),
            'aaData' => $data,
        );

        $this->displayOutput($json_data);
    }

    #[Route('/ajax/credit_notes/export/', name: 'ajax_credit_notes_export')]
    public function exportCreditNotes()
    {
        if (!RightsService::hasAccess(UNIVERSE_ADMIN_SHOP) and !RightsService::hasAccess(UNIVERSE_APP_SHOP)) {
            $this->displayOutput(['status' => false, 'message' => __('Accès non autorisé')]);
        }

        set_time_limit(0);
        @ini_set('memory_limit', '1024M');

        $dateStart = filter_input(INPUT_POST, 'date_start', FILTER_UNSAFE_RAW);
        $dateEnd = filter_input(INPUT_POST, 'date_end', FILTER_UNSAFE_RAW);

        $export = $this->get(ShopCreditNotesExportService::class)->exportAllCreditNotesPdf($dateStart, $dateEnd);
        if (!$export['valid']) {
            $this->displayOutput(array('status' => false, 'message' => $export['message']));
        }

        $this->displayOutput(array('status' => true, 'file' => $export['file'], 'message' => __('Export des avoirs terminé')));
    }

    #[Route('/ajax/credit_notes/export/csv/', name: 'ajax_credit_notes_export_csv')]
    public function exportCreditNotesCsv()
    {
        if (!RightsService::hasAccess(UNIVERSE_ADMIN_SHOP) and !RightsService::hasAccess(UNIVERSE_APP_SHOP)) {
            $this->displayOutput(['status' => false, 'message' => __('Accès non autorisé')]);
        }

        set_time_limit(0);
        @ini_set('memory_limit', '1024M');

        $dateStart = filter_input(INPUT_POST, 'date_start', FILTER_UNSAFE_RAW);
        $dateEnd = filter_input(INPUT_POST, 'date_end', FILTER_UNSAFE_RAW);

        $export = $this->get(ShopCreditNotesExportService::class)->exportAllCreditNotesCsv($dateStart, $dateEnd);
        if (!$export['valid']) {
            $this->displayOutput(array('status' => false, 'message' => $export['message']));
        }

        $this->displayOutput(array('status' => true, 'file' => $export['file'], 'message' => __('Export des avoirs terminé')));
    }
}
