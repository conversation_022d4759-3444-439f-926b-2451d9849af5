<?php

namespace MatGyver\Controllers\Ajax\Shop;

use MatGyver\Controllers\Ajax\AbstractAjaxController;
use MatGyver\Helpers\View\Table\Shop\AppShopInvoicesViewHelper;
use MatGyver\Helpers\View\Table\Shop\ShopInvoicesViewHelper;
use MatGyver\Services\RightsService;
use MatGyver\Services\Shop\Invoices\ShopInvoicesExportService;
use MatGyver\Services\Shop\Invoices\ShopInvoicesService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class ShopInvoicesAjaxController
 * @package MatGyver\Controllers\Ajax\Shop
 */
class ShopInvoicesAjaxController extends AbstractAjaxController
{

    #[Route('/ajax/invoices/datatable/', name: 'ajax_invoices_datatable')]
    public function invoicesDatatable()
    {
        if (!RightsService::hasAccess(UNIVERSE_ADMIN_SHOP)) {
            $this->displayOutput(array('data' => array()));
        }

        //datatable columns (utilisé pour le tri ORDER BY)
        $columns = array(
            0 => 'id',
            1 => 'dateCreation',
            2 => '',
            3 => 'paymentMethod',
            4 => 'amountTaxIncl',
            5 => '',
        );

        $draw = filter_var($_REQUEST['draw'], FILTER_VALIDATE_INT);

        //récupération du nombre de factures (sans LIMIT 0,10 et sans recherche)
        $totalRecords = $this->get(ShopInvoicesService::class)->getCountInvoices();

        //search
        $search = '';
        if (isset($_REQUEST['search']) and $_REQUEST['search']) {
            $searchRules = $_REQUEST['search'];
            if (isset($searchRules['value']) and $searchRules['value']) {
                $search = addslashes($searchRules['value']);
            }
        }

        //ordering
        $orderVar = 'number';
        $orderDir = 'DESC';
        if (isset($_REQUEST['order']) and $_REQUEST['order']) {
            foreach ($_REQUEST['order'] as $order) {
                if (!$columns[intval($order['column'])]) {
                    continue;
                }
                $orderVar = $columns[intval($order['column'])];
                $orderDir = ('asc' === $order['dir'] ? 'ASC' : 'DESC');
                break;
            }
        }
        if (!$orderVar) {
            $orderVar = 'number';
        }

        //récupération du nombre de factures (sans LIMIT 0,10)
        $totalDisplayRecords = $totalRecords;
        if ($search) {
            $totalDisplayRecords = $this->get(ShopInvoicesService::class)->getRepository()->countSearchInvoices($search);
        }

        //paging
        $start = 0;
        $length = 10;
        if (isset($_REQUEST['start'])) {
            $start = filter_var($_REQUEST['start'], FILTER_VALIDATE_INT);
        }
        if (isset($_REQUEST['length'])) {
            $length = filter_var($_REQUEST['length'], FILTER_VALIDATE_INT);
        }

        $invoices = $this->get(ShopInvoicesService::class)->getRepository()->searchInvoices($search, $orderVar, $orderDir, $start, $length);

        $data = array();
        if ($invoices) {
            $this->get(ShopInvoicesViewHelper::class)->setInvoicesRows($invoices);
            $data = $this->get(ShopInvoicesViewHelper::class)->getTable()->getRowsAsArray(false);
        }

        $jsonData = array(
            'draw' => intval($draw),
            'iTotalRecords' => intval($totalRecords),
            'iTotalDisplayRecords' => intval($totalDisplayRecords),
            'aaData' => $data,
        );

        $this->displayOutput($jsonData);
    }

    #[Route('/ajax/invoices/app/datatable/', name: 'ajax_app_invoices_datatable')]
    public function appInvoicesDatatable()
    {
        if (!RightsService::hasAccess(UNIVERSE_APP_SHOP)) {
            $this->displayOutput(array('data' => array()));
        }

        //datatable columns (utilisé pour le tri ORDER BY)
        $columns = array(
            0 => 'id',
            1 => 'dateCreation',
            2 => '',
            3 => 'paymentMethod',
            4 => 'amountTaxIncl',
            5 => '',
        );

        $draw = filter_var($_REQUEST['draw'], FILTER_VALIDATE_INT);

        //récupération du nombre de factures (sans LIMIT 0,10 et sans recherche)
        $totalRecords = $this->get(ShopInvoicesService::class)->getCountInvoices();

        //search
        $search = '';
        if (isset($_REQUEST['search']) and $_REQUEST['search']) {
            $searchRules = $_REQUEST['search'];
            if (isset($searchRules['value']) and $searchRules['value']) {
                $search = addslashes($searchRules['value']);
            }
        }

        //ordering
        $orderVar = 'number';
        $orderDir = 'DESC';
        if (isset($_REQUEST['order']) and $_REQUEST['order']) {
            foreach ($_REQUEST['order'] as $order) {
                if (!$columns[intval($order['column'])]) {
                    continue;
                }
                $orderVar = $columns[intval($order['column'])];
                $orderDir = ('asc' === $order['dir'] ? 'ASC' : 'DESC');
                break;
            }
        }
        if (!$orderVar) {
            $orderVar = 'number';
        }

        //récupération du nombre de factures (sans LIMIT 0,10)
        $totalDisplayRecords = $totalRecords;
        if ($search) {
            $totalDisplayRecords = $this->get(ShopInvoicesService::class)->getRepository()->countSearchInvoices($search);
        }

        //paging
        $start = 0;
        $length = 10;
        if (isset($_REQUEST['start'])) {
            $start = filter_var($_REQUEST['start'], FILTER_VALIDATE_INT);
        }
        if (isset($_REQUEST['length'])) {
            $length = filter_var($_REQUEST['length'], FILTER_VALIDATE_INT);
        }

        $invoices = $this->get(ShopInvoicesService::class)->getRepository()->searchInvoices($search, $orderVar, $orderDir, $start, $length);
        if ($invoices) {
            foreach ($invoices as $id => $invoice) {
                if ($invoice->getDossier() and RightsService::hasAccess(UNIVERSE_APP_DOSSIER) and !RightsService::isGranted(ATTRIBUTE_VIEW, UNIVERSE_APP_DOSSIER, $invoice->getDossier()->getId())) {
                    unset($invoices[$id]);
                }
            }
        }

        $data = array();
        if ($invoices) {
            $this->get(AppShopInvoicesViewHelper::class)->setInvoicesRows($invoices);
            $data = $this->get(AppShopInvoicesViewHelper::class)->getTable()->getRowsAsArray(false);
        }

        $jsonData = array(
            'draw' => intval($draw),
            'iTotalRecords' => intval($totalRecords),
            'iTotalDisplayRecords' => intval($totalDisplayRecords),
            'aaData' => $data,
        );

        $this->displayOutput($jsonData);
    }

    #[Route('/ajax/invoices/export/csv/', name: 'ajax_invoices_export_csv')]
    public function exportInvoicesCsv()
    {
        if (!RightsService::hasAccess(UNIVERSE_ADMIN_SHOP) and !RightsService::hasAccess(UNIVERSE_APP_SHOP)) {
            $this->displayOutput(['status' => false, 'message' => __('Accès non autorisé')]);
        }

        set_time_limit(0);
        @ini_set('memory_limit', '1024M');

        $dateStart = filter_input(INPUT_POST, 'date_start', FILTER_UNSAFE_RAW);
        $dateEnd = filter_input(INPUT_POST, 'date_end', FILTER_UNSAFE_RAW);

        $export = $this->get(ShopInvoicesExportService::class)->exportAllInvoicesCsv($dateStart, $dateEnd);
        if (!$export['valid']) {
            $this->displayOutput(array('status' => false, 'message' => $export['message']));
            exit();
        }

        $nbInvoices = n__('%d facture à exporter', '%d factures à exporter', $export['nbInvoices'], $export['nbInvoices']);
        $nbSeconds = round($export['nbInvoices'] * 0.1);
        $timeSpent = __('Temps estimé') . ' : ' . humanizeTime($nbSeconds);

        $this->displayOutput([
            'status' => true,
            'file' => $export['file'],
            'nbInvoices' => $nbInvoices,
            'timeSpent' => $timeSpent,
            'message' => __('Export des factures terminé')
        ]);
    }

    #[Route('/ajax/invoices/export/', name: 'ajax_invoices_export')]
    public function exportInvoices()
    {
        if (!RightsService::hasAccess(UNIVERSE_ADMIN_SHOP) and !RightsService::hasAccess(UNIVERSE_APP_SHOP)) {
            $this->displayOutput(['status' => false, 'message' => __('Accès non autorisé')]);
        }

        $dateStart = filter_input(INPUT_POST, 'date_start', FILTER_UNSAFE_RAW);
        $dateEnd = filter_input(INPUT_POST, 'date_end', FILTER_UNSAFE_RAW);

        if ($dateStart) {
            $dateStart .= ' 00:00:00';
        }
        if ($dateEnd) {
            $dateEnd .= ' 23:59:59';
        }

        $invoices = $this->get(ShopInvoicesService::class)->getRepository()->getAllInvoicesByDate($dateStart, $dateEnd);
        if (!$invoices) {
            $this->displayOutput(array('status' => false, 'message' => __('Aucune facture trouvée')));
        }

        $nbInvoices = count($invoices);
        $nbInvoicesPerLoop = 20;

        $nbLoops = ceil($nbInvoices / $nbInvoicesPerLoop);

        $nbSeconds = round($nbInvoices * 0.3);
        $strNbInvoices = n__('%d facture à exporter', '%d factures à exporter', $nbInvoices, $nbInvoices);
        $timeSpent = __('Temps estimé') . ' : ' . humanizeTime($nbSeconds);

        //create folder
        $date = date('YmdHis');
        $uploadId = 'invoices_' . $date . '_' . $_SESSION['client']['id'];
        $folder = WEB_PATH . '/medias/' . $_SESSION['client']['uniqid'] . '/invoices/' . $uploadId;
        @mkdir($folder, 0777, true);

        //output dir
        //@mkdir(WEB_PATH . '/medias/upload', 0777);

        $this->displayOutput([
            'status' => true,
            'uploadId' => $uploadId,
            'nb_loops' => $nbLoops,
            'strNbInvoices' => $strNbInvoices,
            'timeSpent' => $timeSpent
        ]);
    }

    #[Route('/ajax/invoices/export_loop/', name: 'ajax_invoices_export_loop')]
    public function exportInvoicesLoop()
    {
        if (!RightsService::hasAccess(UNIVERSE_ADMIN_SHOP) and !RightsService::hasAccess(UNIVERSE_APP_SHOP)) {
            $this->displayOutput(['status' => false, 'message' => __('Accès non autorisé')]);
        }

        $dateStart = filter_input(INPUT_POST, 'date_start', FILTER_UNSAFE_RAW);
        $dateEnd = filter_input(INPUT_POST, 'date_end', FILTER_UNSAFE_RAW);
        $nbLoop = filter_input(INPUT_POST, 'nb_loop', FILTER_VALIDATE_INT);
        $nbLoops = filter_input(INPUT_POST, 'nb_loops', FILTER_VALIDATE_INT);
        $uploadId = filter_input(INPUT_POST, 'uploadId', FILTER_UNSAFE_RAW);

        $export = $this->get(ShopInvoicesExportService::class)->exportAllInvoicesPdf($uploadId, $dateStart, $dateEnd, $nbLoop);
        if (!$export['valid']) {
            $this->displayOutput($export);
        }

        if ($nbLoop != $nbLoops) {
            $percentage = round($nbLoop * 100 / $nbLoops);
            $this->displayOutput(['status' => true, 'percentage' => $percentage]);
        }

        $fileUrl = $this->get(ShopInvoicesExportService::class)->getInvoicesZipFile($uploadId);
        if ($fileUrl === null) {
            $this->displayOutput(['status' => false, 'message' => __('Erreur lors de la création de l\'archive.')]);
        }

        $this->displayOutput([
            'status' => true,
            'file_url' => $fileUrl,
            'message' => __('Export des factures terminé'),
        ]);
    }
}
