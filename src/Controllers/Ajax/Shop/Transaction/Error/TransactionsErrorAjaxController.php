<?php

namespace MatGyver\Controllers\Ajax\Shop\Transaction\Error;

use MatGyver\Controllers\Ajax\AbstractAjaxController;
use MatGyver\Helpers\View\Table\Shop\ShopTransactionsErrorsViewHelper;
use MatGyver\Services\Filters\ShopTransactionsErrorsFiltersService;
use MatGyver\Services\RightsService;
use MatGyver\Services\Shop\Transaction\Error\ShopTransactionErrorService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class TransactionsErrorAjaxController
 * @package MatGyver\Controllers\Ajax\Shop\Transaction\Error
 */
class TransactionsErrorAjaxController extends AbstractAjaxController
{
    #[Route('/ajax/transactions/errors/datatable/', name: 'ajax_transactions_errors_datatable')]
    public function ajaxTransactionsErrorsDatatable()
    {
        if (!RightsService::hasAccess(UNIVERSE_ADMIN_SHOP)) {
            $this->displayOutput(array('data' => array()));
        }

        //datatable columns (utilisé pour le tri ORDER BY)
        $columns = array(
            0 => '',
            1 => 't.reference',
            2 => '',
            3 => 'te.date',
            4 => 't.lastName',
            5 => 'te.product',
            6 => 'te.type',
            7 => 't.amountTaxIncl',
            8 => 'ts.name',
            9 => '',
        );

        // custom filters
        $filters = $this->get(ShopTransactionsErrorsFiltersService::class)->getFilters();
        $dateStart = $filters['date_start'];
        $dateEnd = $filters['date_end'];
        $status = $filters['status'];
        $search = filter_var($filters['search'], FILTER_UNSAFE_RAW);

        $draw = filter_var($_REQUEST['draw'], FILTER_VALIDATE_INT);

        //récupération du nombre de transactions (sans LIMIT 0,10 et sans recherche)
        $totalRecords = $this->get(ShopTransactionErrorService::class)->getRepository()->countSearchTransactionsErrors($dateStart, $dateEnd, $status);

        //ordering
        $orderVar = 'te.date';
        $orderDir = 'DESC';
        if (isset($_REQUEST['order']) and $_REQUEST['order']) {
            foreach ($_REQUEST['order'] as $order) {
                if (!$columns[intval($order['column'])]) {
                    continue;
                }
                $orderVar = $columns[intval($order['column'])];
                $orderDir = ('asc' === $order['dir'] ? 'ASC' : 'DESC');
                break;
            }
        }

        //récupération du nombre de transactions (sans LIMIT 0,10)
        if (!$search) {
            $totalDisplayRecords = $totalRecords;
        } else {
            $totalDisplayRecords = $this->get(ShopTransactionErrorService::class)->getRepository()->countSearchTransactionsErrors($dateStart, $dateEnd, $status, '', $search);
        }

        //paging
        $start = 0;
        $length = 10;
        if (isset($_REQUEST['start'])) {
            $start = filter_var($_REQUEST['start'], FILTER_VALIDATE_INT);
        }
        if (isset($_REQUEST['length'])) {
            $length = filter_var($_REQUEST['length'], FILTER_VALIDATE_INT);
        }

        if ($start and $totalDisplayRecords < $start) {
            $json_data = array(
                'draw' => intval($draw),
                'iTotalRecords' => intval($totalRecords),
                'iTotalDisplayRecords' => intval($totalDisplayRecords),
                'aaData' => array(),
                'resetPage' => true,
            );

            $this->displayOutput($json_data);
        }

        //récupération de toutes les transactions avec $sLimit
        $transactionsErrors = $this->get(ShopTransactionErrorService::class)->getRepository()->searchTransactionsErrors($dateStart, $dateEnd, $status, '', $search, $orderVar, $orderDir, $start, $length);

        $data = array();
        if ($transactionsErrors) {
            $this->get(ShopTransactionsErrorsViewHelper::class)->setTransactionsErrorsRows($transactionsErrors);
            $data = $this->get(ShopTransactionsErrorsViewHelper::class)->getTable()->getRowsAsArray(false);
        }

        $json_data = array(
            'draw' => intval($draw),
            'iTotalRecords' => intval($totalRecords),
            'iTotalDisplayRecords' => intval($totalDisplayRecords),
            'aaData' => $data,
            'resetPage' => false,
        );

        $this->displayOutput($json_data);
    }
}
