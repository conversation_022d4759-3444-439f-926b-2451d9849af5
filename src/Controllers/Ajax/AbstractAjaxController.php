<?php

namespace MatGyver\Controllers\Ajax;

use MatGyver\Controllers\AbstractController;
use MatGyver\Services\CsrfService;

/**
 * Class AbstractAppController
 * @package MatGyver\Controllers\Ajax
 */
class AbstractAjaxController extends AbstractController
{
    /**
     * @var string
     */
    protected $param;

    /**
     * @var array
     */
    protected $requestParams;

    /**
     * AbstractAjaxController constructor.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @param string $action
     * @param string $param
     * @param string $query
     * @param string $route
     * @param array $params
     * @return mixed|void
     */
    public function dispatch(string $action = '', string $param = '', string $query = '', string $route = '', array $params = [])
    {
        $controller = 'ajax';
        $_SESSION['controller'] = $controller;

        $this->param = $param;
        $this->requestParams = null;

        if (!$action) {
            $this->displayOutput([
                'status' => false,
                'message' => __('Erreur : l\'action requise n\'a pas été spécifiée.')
            ]);
        }

        //validate csrf token
        $key = 'token';
        if (isset($_POST['key'])) {
            $key = filter_input(INPUT_POST, 'key', FILTER_UNSAFE_RAW);
        }
        $validToken = $this->get(CsrfService::class)->validateToken($key);
        if (!$validToken['valid']) {
            $this->displayOutput([
                'status' => false,
                'message' => __('Votre session a expiré, merci de recharger la page')
            ]);
        }

        // Get params from request method PUT,  DELETE and PATCH
        $requestMethod = $_SERVER['REQUEST_METHOD'];
        if ($requestMethod == 'PUT' || $requestMethod == 'DELETE' || $requestMethod == 'PATCH') {
            $putContent = file_get_contents("php://input");
            parse_str($putContent, $this->requestParams);
        } elseif ($requestMethod == 'GET') {
            $this->requestParams = $_GET;
        }

        if (method_exists($this, $action)) {
            if ($params) {
                $method = new \ReflectionMethod($this, $action);
                if (!$method->getParameters()) {
                    $params = [];
                }
            }
            call_user_func_array(array($this, $action), $params);
            return;
        }

        $this->displayOutput([
            'status' => false,
            'message' => __('Erreur : l\'action requise n\'a pas été trouvée.')
        ]);
    }

    /**
     * @param array $output
     */
    protected function displayOutput($output)
    {
        header('Content-Type: application/json');
        echo json_encode($output);
        exit();
    }

    /**
     * @param string $error
     * @param array $data
     */
    protected function sendError(string $error, array $data = []): void
    {
        $data = array_merge(['status' => false, 'message' => $error], $data);
        $this->displayOutput($data);
    }

    protected function sendUnauthorizedAccess(): void
    {
        header($_SERVER['SERVER_PROTOCOL'] . ' 403 Forbidden', true, 403);
        $data = ['status' => false, 'message' => 'Unauthorized access'];
        $this->displayOutput($data);
    }

    /**
     * @param string $message
     * @param array $data
     */
    protected function sendSuccess(string $message = '', array $data = []): void
    {
        $data = array_merge(['status' => true], $data);
        if ($message) {
            $data['message'] = $message;
        }
        $this->displayOutput($data);
    }

    /**
     * @param array $data
     */
    protected function sendData(array $data): void
    {
        $data['status'] = true;
        $this->displayOutput($data);
    }
}
