<?php

namespace MatGyver\Controllers\Ajax;

use MatGyver\Helpers\Image;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseJudiciaireDoubtService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseJudiciaireFactService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseJudiciaireOpinionService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseObservationsService;
use Symfony\Component\Routing\Attribute\Route;

class ImageAjaxController extends AbstractAjaxController
{
    #[Route('/ajax/image/crop/', name: 'ajax_image_crop')]
    public function cropImage()
    {
        $submittedData = getPostData($_POST);
        $originalImg = filter_var($submittedData['originalImg'], FILTER_VALIDATE_URL);

        $originalImg = urldecode($originalImg);
        if (!$originalImg) {
            $this->displayOutput(['status' => false, 'message' => __('Aucune image reçue')]);
        }

        $rawImg = file_get_contents($_FILES['img']['tmp_name']);
        if (!$rawImg) {
            $this->displayOutput(['status' => false, 'message' => __('Aucune image reçue')]);
        }

        if (str_contains($originalImg, 'upload/')) {
            //image is in upload/ directory
            $filePath = str_replace(CDN_URL, '', $originalImg);

            $newUrl = $originalImg;
        } else {
            $data = parse_url($originalImg);
            $path = pathinfo($data['path']);
            $filePath = UPLOAD_PATH . '/files/' . $path['filename'] . '.jpg';
            $newUrl = CDN_URL . '/lib/upload/files/' . $path['filename'] . '.jpg';
        }

        $fp = fopen($filePath, 'w');
        fwrite($fp, $rawImg);
        fclose($fp);

        $this->displayOutput(['status' => true, 'url' => $newUrl]);
    }

    #[Route('/ajax/image/rotate/{direction}/', name: 'ajax_image_rotate')]
    public function imageRotate(string $direction)
    {
        $fileName = filter_input(INPUT_POST, 'fileName', FILTER_UNSAFE_RAW);
        $file = UPLOAD_PATH . '/files/' . $fileName;

        if (isset($_POST['imageId']) and $_POST['imageId']) {
            $imageId = filter_input(INPUT_POST, 'imageId', FILTER_UNSAFE_RAW);
            if (str_contains($imageId, 'observation')) {
                $observationId = str_replace('observation-img', '', $imageId);
                $observationId = str_replace('-2', '', $observationId);
                $observationId = (int)$observationId;

                $observation = $this->get(DossierExpertiseObservationsService::class)->getRepository()->find($observationId);
                if (!$observation) {
                    $this->displayOutput(['status' => false, 'message' => __('Cette observation n\'existe pas.')]);
                }
                $file = WEB_PATH . '/medias/' . $observation->getExpertise()->getDossier()->getFolder() . $fileName;
            } elseif (str_contains($imageId, 'fact')) {
                $factId = str_replace('fact-img', '', $imageId);
                $factId = str_replace('-2', '', $factId);
                $factId = (int)$factId;

                $fact = $this->get(DossierExpertiseJudiciaireFactService::class)->getRepository()->find($factId);
                if (!$fact) {
                    $this->displayOutput(['status' => false, 'message' => __('Ce fait n\'existe pas.')]);
                }
                $file = WEB_PATH . '/medias/' . $fact->getExpertise()->getDossier()->getFolder() . $fileName;
            } elseif (str_contains($imageId, 'doubt')) {
                $doubtId = str_replace('doubt-img', '', $imageId);
                $doubtId = str_replace('-2', '', $doubtId);
                $doubtId = (int)$doubtId;

                $doubt = $this->get(DossierExpertiseJudiciaireDoubtService::class)->getRepository()->find($doubtId);
                if (!$doubt) {
                    $this->displayOutput(['status' => false, 'message' => __('Ce doute n\'existe pas.')]);
                }
                $file = WEB_PATH . '/medias/' . $doubt->getExpertise()->getDossier()->getFolder() . $fileName;
            } elseif (str_contains($imageId, 'opinion')) {
                $opinionId = str_replace('opinion-img', '', $imageId);
                $opinionId = str_replace('-2', '', $opinionId);
                $opinionId = (int)$opinionId;

                $opinion = $this->get(DossierExpertiseJudiciaireOpinionService::class)->getRepository()->find($opinionId);
                if (!$opinion) {
                    $this->displayOutput(['status' => false, 'message' => __('Cette opinion n\'existe pas.')]);
                }
                $file = WEB_PATH . '/medias/' . $opinion->getExpertise()->getDossier()->getFolder() . $fileName;
            }
        }

        if (!file_exists($file)) {
            $this->displayOutput(['status' => false, 'message' => __('Ce fichier n\'existe pas.')]);
        }

        $rotate = Image::rotate($file, $direction);
        if (!$rotate['valid']) {
            $this->displayOutput(['status' => false, 'message' => $rotate['message']]);
        }

        $type = pathinfo($file, PATHINFO_EXTENSION);
        $data = file_get_contents($file);
        $base64 = 'data:image/' . $type . ';base64,' . base64_encode($data);
        $this->displayOutput(['status' => true, 'img64' => $base64]);
    }

    #[Route('/ajax/webcam/picture/save/', name: 'ajax_webcam_picture_save')]
    public function saveWebcamPicture()
    {
        $rawImg = file_get_contents($_FILES['img']['tmp_name']);
        if (!$rawImg) {
            $this->displayOutput(['status' => false, 'message' => __('Aucune image reçue')]);
        }

        $fileName = uniqid();
        $filePath = UPLOAD_PATH . '/files/' . $fileName . '.jpg';

        $fp = fopen($filePath, 'w');
        fwrite($fp, $rawImg);
        fclose($fp);

        $this->displayOutput(['status' => true, 'file' => $fileName . '.jpg', 'fileSize' => filesize($filePath)]);
    }
}
