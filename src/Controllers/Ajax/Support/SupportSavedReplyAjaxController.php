<?php

namespace MatGyver\Controllers\Ajax\Support;

use MatGyver\Controllers\Ajax\AbstractAjaxController;
use MatGyver\Services\Support\SupportSavedReplyService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class SupportSavedReplyAjaxController
 * @package MatGyver\Controllers\Ajax
 */
class SupportSavedReplyAjaxController extends AbstractAjaxController
{

    #[Route('/ajax/support/saved_reply/', name: 'ajax_support_saved_reply')]
    public function savedReply()
    {
        $idSavedReply = filter_input(INPUT_POST, 'id_saved_reply', FILTER_VALIDATE_INT);
        $savedReply = $this->get(SupportSavedReplyService::class)->getSavedReplyById($idSavedReply);
        if (!$savedReply) {
            $this->displayOutput(['status' => true]);
        }
        $this->displayOutput(['status' => true, 'content' => $savedReply->getReply()]);
    }
}
