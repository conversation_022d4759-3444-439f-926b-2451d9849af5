<?php

namespace MatGyver\Controllers\Ajax;

use MatGyver\Services\ConfigService;
use MatGyver\Services\Document\DocumentTemplateService;
use MatGyver\Services\Dossier\DossierExpertiseService;
use MatGyver\Services\RightsService;
use MatGyver\Services\TwigService;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\Requirement\Requirement;

/**
 * Class AudioRecorderAjaxController
 * @package MatGyver\Controllers\Ajax
 */
class DocumentTemplateAjaxController extends AbstractAjaxController
{
    #[Route('/ajax/document_template/example/get/{type}/{personType}/', name: 'ajax_document_template_example_get', requirements: ['type' => Requirement::CATCH_ALL, 'personType' => Requirement::CATCH_ALL])]
    public function ajaxGetDocumentTemplateExample(string $type, string $personType)
    {
        if (!RightsService::isEditor()) {
            $this->sendError(__('Accès non autorisé'));
        }

        $title = DocumentTemplateService::getType($type);
        if (!$title) {
            $this->sendError(__('Type de document incorrect'));
        }
        $name = $title . ' #Modèle 1';

        $content2 = '';
        if ($type == 'convocation') {
            $file = VIEWS_PATH . '/app/dossier/convocations/' . $personType . '.php';
            if (!file_exists($file)) {
                $file = VIEWS_PATH . '/app/dossier/convocations/default.php';
            }

            $content = TwigService::getInstance()->render($file);
            $convocationContents = explode('-----', $content);
            list ($title, $content, $content2) = $convocationContents;

            match ($personType) {
                'contact' => $name = 'Convocation du lésé',
                'mandate' => $name = 'Convocation du mandant',
                'expertise_place' => $name = 'Convocation du lieu d\'expertise',
                'expert' => $name = 'Convocation de l\'expert automobile',
                'insurer' => $name = 'Convocation de l\'assureur',
                'lawyer' => $name = 'Convocation de l\'avocat',
                'manufacturer' => $name = 'Convocation du constructeur',
                'seller' => $name = 'Convocation du vendeur',
                'repairer' => $name = 'Convocation du réparateur',
                'technical_inspector' => $name = 'Convocation du contrôleur technique',
                'other' => $name = 'Autre convocation',
                default => $name = 'Convocation'
            };
        } else {
            $config = $this->get(ConfigService::class)->getConfig();
            $content = $this->parser->set('config', $config)
                ->render('app/documents/templates/' . $type . '.php');
        }

        $this->sendData([
            'title' => $title,
            'name' => $name,
            'content' => $content,
            'content2' => $content2
        ]);
    }

    #[Route('/ajax/document_template/get/{templateId}/{expertiseId}/', name: 'ajax_document_template_get', requirements: ['templateId' => Requirement::POSITIVE_INT, 'expertiseId' => Requirement::POSITIVE_INT])]
    public function ajaxGetDocumentTemplate(int $templateId, int $expertiseId): void
    {
        if (!RightsService::isEditor()) {
            $this->sendError(__('Accès non autorisé'));
        }

        $template = $this->get(DocumentTemplateService::class)->getRepository()->find($templateId);
        if (!$template) {
            $this->sendError(__('Template introuvable'));
        }

        $expertise = $this->get(DossierExpertiseService::class)->getRepository()->find($expertiseId);
        if (!$expertise) {
            $this->sendError(__('Expertise introuvable'));
        }

        $content = $this->get(DocumentTemplateService::class)->applyExpertise($template->getContent(), $expertise);

        $content2 = '';
        if ($template->getContent2()) {
            $content2 = $this->get(DocumentTemplateService::class)->applyExpertise($template->getContent2(), $expertise);
        }

        $this->sendData([
            'title' => $template->getTitle(),
            'content' => $content,
            'content2' => $content2
        ]);
    }
}
