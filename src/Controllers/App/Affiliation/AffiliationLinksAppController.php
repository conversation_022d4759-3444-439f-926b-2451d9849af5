<?php

namespace MatGyver\Controllers\App\Affiliation;

use <PERSON>G<PERSON><PERSON>\Attributes\View;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Table\Affiliation\AppAffiliationLinksViewHelper;
use MatGyver\Services\Affiliation\AffiliationLinksService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class AffiliationLinksAppController
 * @package MatGyver\Controllers\App\Affiliation
 */
#[View('MatGyver\Services\Views\ViewAppAffiliation')]
class AffiliationLinksAppController extends AbstractAffiliationAppController
{

    #[Route('/app/affiliation/links/', name: 'app_affiliation_links')]
    public function affiliationLinks()
    {
        $partner = $this->checkIfPartnerIsDefined();
        $this->view->setTitle(__('Liens'));
        $this->view->addBreadcrumbItem(__('Affiliation'), Tools::makeLink('app', 'affiliation'));

        Assets::addJs('common/clipboard.min.js');
        Assets::addJs('common/clipboard-copy.js');

        $links = $this->get(AffiliationLinksService::class)->getAllLinks(CLIENT_MASTER);
        $output = $this->get(AppAffiliationLinksViewHelper::class)->getContent($links, $partner);
        $this->displayOutputCard($output);
    }
}
