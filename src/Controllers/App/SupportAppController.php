<?php

namespace MatGyver\Controllers\App;

use Mat<PERSON><PERSON>ver\Attributes\Universe;
use Mat<PERSON><PERSON><PERSON>\FormsFactory\FormFactory;
use Mat<PERSON><PERSON><PERSON>\FormsFactory\Support\SupportTicketFormFactory;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Support\SupportService;
use MatGyver\Services\Support\SupportTicketService;
use Symfony\Component\Routing\Attribute\Route;

#[Universe(UNIVERSE_APP_RELATIONCLIENT)]
class SupportAppController extends AbstractAppController
{

    #[Route('/app/support/', name: 'app_support')]
    public function support()
    {
        $this->view->setTitle(__('Support'));
        $this->view->addBreadcrumbItem(__('Aide'), Tools::makeLink('app', 'help'));

        $tickets = $this->get(SupportTicketService::class)->getRepository()->getAllTickets(null, null, null, false, 0, 20);
        $nbTickets = count($this->get(SupportTicketService::class)->getRepository()->getAllTickets(null, null, null, false, 0, null));

        $ticket = null;
        if (isset($_GET['ticketid'])) {
            $ticketId = filter_input(INPUT_GET, 'ticketid', FILTER_UNSAFE_RAW);
            $ticket = $this->get(SupportTicketService::class)->getTicketById($ticketId);
        }

        Assets::addCss('common/support.css');
        Assets::addCss('common/blank_state.css');
        Assets::addJs('app/support.js');

        $modalNotHappy = $this->get(SupportService::class)->getModalNotHappy();
        Assets::addInlineJs($modalNotHappy);

        $this->displayOutput($this->get(SupportService::class)->renderAppLayout($tickets, $nbTickets, $ticket));
    }

    #[Route('/app/support/ticket/create/', name: 'app_support_ticket_create')]
    public function supportCreateTicket()
    {
        $this->view->setTitle(__('Création d\'un ticket'));
        $this->view->addBreadcrumbItem(__('Support'), Tools::makeLink('app', 'support'));

        Assets::addJs('app/support.js');

        $ticketForm = new SupportTicketFormFactory();
        if ($ticketForm->isSubmitted() and $ticketForm->isValid()) {
            $process = $ticketForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                header('Location: ' . Tools::makeLink('app', 'support', '', 'ticketid=' . $process['ticketid']));
                exit();
            }
        }
        $this->displayOutput($this->builderForm->render($ticketForm));
    }
}
