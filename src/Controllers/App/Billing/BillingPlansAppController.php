<?php

namespace MatGyver\Controllers\App\Billing;

use Mat<PERSON><PERSON><PERSON>\Attributes\Universe;
use Mat<PERSON>yver\Attributes\View;
use MatGyver\Controllers\App\AbstractAppController;
use MatGyver\Enums\ProductsEnum;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Clients\ClientsSubscriptionsService;
use MatGyver\Services\Clients\ClientsTransactionsService;
use MatGyver\Services\ConfigService;
use MatGyver\Services\Limit\LimitClientsService;
use MatGyver\Services\PaymentsMethodsService;
use MatGyver\Services\PlansService;
use MatGyver\Services\Shop\Product\Form\ShopProductFormService;
use MatGyver\Services\Shop\Product\ShopProductsLimitsService;
use MatGyver\Services\Shop\Product\ShopProductsService;
use MatGyver\Services\Shop\ShopPaymentsService;
use MatGyver\Services\Shop\ShopVatRulesService;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\Requirement\Requirement;

#[Universe(UNIVERSE_APP_PRIVATE)]
class BillingPlansAppController extends AbstractAppController
{
    #[Route('/app/billing/plans/', name: 'app_billing_plans')]
    public function plans()
    {
        /*$config = $this->get(ConfigService::class)->getConfig();
        if (!isset($config['address']) or !$config['address']) {
            $this->displayErrorAndRedirect(__('Vous devez d\'abord définir vos informations de facturation'), Tools::makeLink('app', 'billing', 'settings'));
        }

        $paymentMethod = $this->get(PaymentsMethodsService::class)->getDefault();
        if (!$paymentMethod) {
            $this->displayErrorAndRedirect(__('Vous devez d\'abord ajouter un moyen de paiement'), Tools::makeLink('app', 'billing', 'payment_methods'));
        }*/

        $this->view->setTitle(__('Abonnements'));

        $features = $this->get(PlansService::class)->getFeatures();
        $plans = $this->get(PlansService::class)->getPlans();

        $productsIds = [];
        $subscriptions = $this->get(ClientsSubscriptionsService::class)->getAllSubscriptionsActive();
        if ($subscriptions) {
            foreach ($subscriptions as $subscription) {
                $productsIds[] = $subscription->getProduct()->getPermalink();
            }
        }

        //send empty array so button for same subscription will be displayed
        $productsIds = [];

        $content = $this->parser->set('features', $features)
            ->set('plans', $plans)
            ->set('productsIds', $productsIds)
            ->set('displayBasic', true)
            ->render('app/billing/offers.php');
        Assets::addCss('app/offers.css');
        Assets::addJs('app/premium.js');
        $this->displayOutput($content);
        return;



        $products = $this->get(ShopProductsService::class)->getRepository()->getProductsVisible();

        $displayProducts = '';
        if (!$products) {
            Assets::addCss('common/blank_state.css');
        } else {
            foreach ($products as $id => $product) {
                $displayProducts .= $this->parser->set('product', $product)
                    ->set('id', $id)
                    ->render('app/billing/product.php');
            }
        }

        $content = $this->parser->set('products', $products)
            ->set('displayProducts', $displayProducts)
            ->render('app/billing/plans.php');

        Assets::addCss('app/billing.css');

        $this->displayOutput($content);
    }

    #[Route("/app/billing/plan/add/{permalink}/", name: "app_billing_add_plan", requirements: ['permalink' => Requirement::ASCII_SLUG], methods: ["GET"])]
    #[View('MatGyver\Services\Views\ViewAppSubscriptionSettings')]
    public function planAdd(string $permalink)
    {
        $product = $this->get(ShopProductsService::class)->getProductByPermalink($permalink, CLIENT_MASTER);
        if (!$product) {
            $this->displayError(__('Une erreur est survenue.'), Tools::makeLink('app', 'billing', 'plans'));
            return;
        }

        $canUpgrade = false;
        $productLimits = $this->get(ShopProductsLimitsService::class)->getByProduct($product->getId());
        if ($productLimits) {
            foreach ($productLimits as $productLimit) {
                if ($productLimit->getAction() == 'increment') {
                    $canUpgrade = true;
                    break;
                }
            }
        }

        if (!$canUpgrade) {
            $subscription = $this->get(ClientsSubscriptionsService::class)->getSubscriptionActiveByProduct($product->getId());
            if ($subscription) {
                $this->displayError(__('Votre abonnement comporte déjà ce produit.'), Tools::makeLink('app', 'billing', 'plans'));
                return;
            }
            if ($product->getType() != ProductsEnum::TYPE_SUBSCRIPTION) {
                $clientTransaction = $this->get(ClientsTransactionsService::class)->getRepository()->getValidTransactionsByProduct($product->getId());
                if ($clientTransaction) {
                    $this->displayError(__('Vous avez déjà commandé ce produit.'), Tools::makeLink('app', 'billing', 'plans'));
                    return;
                }
            }
        }

        $country = $this->get(ConfigService::class)->findByName('country');
        if (!$country or !$country->getValue()) {
            $this->displayErrorAndRedirect(__('Vous devez d\'abord définir vos informations de facturation'), Tools::makeLink('app', 'billing', 'settings', 'return=app_billing_add_plan&id=' . $permalink));
        }

        $paymentMethod = $this->get(PaymentsMethodsService::class)->getDefault();
        if (!$paymentMethod) {
            $this->displayErrorAndRedirect(__('Vous devez d\'abord ajouter un moyen de paiement'), Tools::makeLink('app', 'billing', 'payment_method/add', 'return=app_billing_add_plan&id=' . $permalink));
        }

        $vatRate = $this->get(ShopVatRulesService::class)->getVatRateByClient($product->getId());

        $product->setPriceTaxIncl($product->getPriceTaxExcl());
        if ($vatRate) {
            $product->setPriceTaxIncl($product->getPriceTaxExcl() * (1 + $vatRate / 100));
        }

        $productLimits = [];
        $getProductLimits = $this->get(ShopProductsLimitsService::class)->getByProduct($product->getId());
        if ($getProductLimits) {
            foreach ($getProductLimits as $getProductLimit) {
                $limit = $getProductLimit->getLimit();
                if (!$limit) {
                    continue;
                }
                $productLimits[$limit->getId()] = [
                    'name' => $limit->getName(),
                    'reference' => $limit->getReference(),
                    'value' => $getProductLimit->getValue(),
                    'action' => $getProductLimit->getAction(),
                ];
            }
        }

        $paymentEase = '';
        $trialAmount = 0;
        $payment = $this->get(ShopPaymentsService::class)->getRepository()->findOneBy(['product' => $product, 'active' => true, 'client' => CLIENT_MASTER]);
        if ($payment) {
            $checkout = json_decode($payment->getCheckout(), true);
            if (isset($checkout['trial_period']) and $checkout['trial_period']) {
                //can client get free trial
                $hadTrial = false;
                $subscriptions = $this->get(ClientsSubscriptionsService::class)->getRepository()->findBy(['client' => $_SESSION['client']['id']], ['id' => 'DESC']);
                foreach ($subscriptions as $subscription) {
                    if ($subscription->hadTrial()) {
                        $hadTrial = true;
                        break;
                    }
                }

                if ($_SESSION['client']['id'] === 56) {
                    $hadTrial = true;
                }

                if (!$hadTrial) {
                    $trialAmount = $checkout['trial_amount'];

                    $getPaymentEase = $this->get(ShopProductFormService::class)->getPaymentEase($checkout, $product->getCurrency());
                    $paymentEase = $getPaymentEase['subtitle'];
                }
            }
        }

        $output = $this->parser->set('permalink', $permalink)
            ->set('product', $product)
            ->set('vatRate', $vatRate)
            ->set('productLimits', $productLimits)
            ->set('paymentEase', $paymentEase)
            ->set('trialAmount', $trialAmount)
            ->render('app/billing/plan_add.php');

        Assets::addCss('app/billing.css');
        $this->displayOutput($output);
    }

    #[Route("/app/billing/plan/add/{permalink}/", name: "app_billing_add_plan_post", requirements: ['permalink' => Requirement::ASCII_SLUG], methods: ["POST"])]
    #[View('MatGyver\Services\Views\ViewAppSubscriptionSettings')]
    public function planAddPost(string $permalink)
    {
        $this->checkRequest();

        $addPlan = $this->get(ClientsSubscriptionsService::class)->addSubscription($this->submittedData);
        if (!$addPlan['valid']) {
            $this->displayErrorAndRedirect($addPlan['message'], Tools::makeLink('app', 'billing', 'plan/add/' . $permalink));
        }

        $this->displaySuccessAndRedirect(__('Votre abonnement a bien été enregistré.'), Tools::makeLink('app', 'billing', 'confirmation', 'id_order=' . $addPlan['id_order']));
    }

    #[Route("/app/billing/plan/change/{permalink}/", name: "app_billing_plan_change", requirements: ['permalink' => Requirement::ASCII_SLUG], methods: ["GET"])]
    #[View('MatGyver\Services\Views\ViewAppSubscriptionSettings')]
    public function planChange(string $permalink)
    {
        $product = $this->get(ShopProductsService::class)->getProductByPermalink($permalink, CLIENT_MASTER);
        if (!$product) {
            $this->displayError(__('Une erreur est survenue.'), Tools::makeLink('app', 'billing', 'plans'));
            return;
        }

        $canUpgrade = false;
        $productLimits = $this->get(ShopProductsLimitsService::class)->getByProduct($product->getId());
        if ($productLimits) {
            foreach ($productLimits as $productLimit) {
                if ($productLimit->getAction() == 'increment') {
                    $canUpgrade = true;
                    break;
                }
            }
        }

        $productSubscription = $this->get(ClientsSubscriptionsService::class)->getSubscriptionActiveByProduct($product->getId());
        /*if ($productSubscription and !$canUpgrade) {
            $this->displayError(__('Votre abonnement comporte déjà ce produit.'), Tools::makeLink('app', 'billing', 'plans'));
            return;
        }*/

        $subscription = $this->get(ClientsSubscriptionsService::class)->getRepository()->getSubscriptionActiveByType($product->getType());
        if (!$subscription) {
            $this->displayError(__('Une erreur est survenue.'), Tools::makeLink('app', 'billing', 'plans'));
            return;
        }

        /*if ($subscription->getProduct() and $subscription->getProduct()->getId() == $product->getId() and !$canUpgrade) {
            $this->displayError(__('Votre abonnement comporte déjà ce produit.'), Tools::makeLink('app', 'billing', 'plans'));
            return;
        }*/

        $country = $this->get(ConfigService::class)->findByName('country');
        if (!$country or !$country->getValue()) {
            $this->displayErrorAndRedirect(__('Vous devez d\'abord définir vos informations de facturation'), Tools::makeLink('app', 'billing', 'settings', 'return=app_billing_plan_change&id=' . $permalink));
        }

        $paymentMethod = $this->get(PaymentsMethodsService::class)->getDefault();
        if (!$paymentMethod) {
            $this->displayErrorAndRedirect(__('Vous devez d\'abord ajouter un moyen de paiement'), Tools::makeLink('app', 'billing', 'payment_method/add', 'return=app_billing_plan_change&id=' . $permalink));
        }

        $subscriptionLimits = [];
        if ($subscription->getProduct()) {
            $subscriptionProductLimits = $this->get(ShopProductsLimitsService::class)->getByProduct($subscription->getProduct()->getId());
            if ($subscriptionProductLimits) {
                foreach ($subscriptionProductLimits as $subscriptionProductLimit) {
                    $limit = $subscriptionProductLimit->getLimit();
                    if (!$limit) {
                        continue;
                    }
                    $subscriptionLimits[$limit->getId()] = [
                        'name' => $limit->getName(),
                        'reference' => $limit->getReference(),
                        'value' => $subscriptionProductLimit->getValue(),
                    ];
                }
                if ($subscriptionLimits) {
                    $clientLimits = $this->get(LimitClientsService::class)->getRepository()->findBy(['client' => $_SESSION['client']['id']]);
                    if ($clientLimits) {
                        foreach ($clientLimits as $clientLimit) {
                            if (isset($subscriptionLimits[$clientLimit->getLimit()->getId()])) {
                                $subscriptionLimits[$clientLimit->getLimit()->getId()]['value'] = $clientLimit->getValue();
                            }
                        }
                    }
                }
            }
        }

        $productLimits = [];
        $getProductLimits = $this->get(ShopProductsLimitsService::class)->getByProduct($product->getId());
        if ($getProductLimits) {
            foreach ($getProductLimits as $getProductLimit) {
                $limit = $getProductLimit->getLimit();
                if (!$limit) {
                    continue;
                }
                $productLimits[$limit->getId()] = [
                    'name' => $limit->getName(),
                    'reference' => $limit->getReference(),
                    'value' => $getProductLimit->getValue(),
                    'action' => $getProductLimit->getAction(),
                ];
            }
        }

        $prorataAmountTaxExcl = $this->get(ClientsSubscriptionsService::class)->getAmountLeft($subscription->getId());
        $vatRate = $this->get(ShopVatRulesService::class)->getVatRateByClient($product->getId());

        $product->setPriceTaxIncl($product->getPriceTaxExcl());
        if ($vatRate) {
            $product->setPriceTaxIncl($product->getPriceTaxExcl() * (1 + $vatRate / 100));
        }

        $toPayAmountTaxExcl = $product->getPriceTaxExcl() - $prorataAmountTaxExcl;
        $qty = 1;
        $qtyError = false;

        //client order the same product
        if ($productSubscription) {
            /*
             * client need to pay the amount left for the same product :
             * first order = January, 1st -> 9€ for 1 month
             * January, 7th -> $prorataAmountTaxExcl = 6€
             * client need to pay 6€ now, and next payment will be 18€ on February, 1st
             */
            if ($prorataAmountTaxExcl) {
                $toPayAmountTaxExcl = $prorataAmountTaxExcl;
            }

            if (isset($_GET['qty'])) {
                $qty = filter_input(INPUT_GET, 'qty', FILTER_VALIDATE_INT);
            }
            if (!$qty) {
                $qty = 1;
            }
            if ($qty > 1) {
                $toPayAmountTaxExcl += ($product->getPriceTaxExcl() * ($qty - 1));
                if ($prorataAmountTaxExcl) {
                    $toPayAmountTaxExcl = $prorataAmountTaxExcl * $qty;
                }
            } elseif ($qty < 0) {
                $toPayAmountTaxExcl = 0;
            }

            //client can upgrade so we need to handle quantities
            $product->setPriceTaxIncl(($product->getPriceTaxIncl() * $qty) + $productSubscription->getAmountTaxIncl());
            $product->setPriceTaxExcl(($product->getPriceTaxExcl() * $qty) + $productSubscription->getAmountTaxExcl());

            if ($productLimits) {
                foreach ($productLimits as $limitId => $productLimit) {
                    if ($productLimit['action'] == 'increment' and isset($subscriptionLimits[$limitId])) {
                        if ($qty != 1) {
                            $productLimits[$limitId]['value'] *= $qty;
                        }
                        $productLimits[$limitId]['value'] += $subscriptionLimits[$limitId]['value'];
                        if ($productLimits[$limitId]['value'] <= 0) {
                            $qtyError = true;
                            $productLimits[$limitId]['value'] = 0;
                        }
                    }
                }
            }
        }

        if ($toPayAmountTaxExcl <= 0) {
            $toPayAmountTaxExcl = 0;
        }

        $toPayAmount = $toPayAmountTaxExcl;
        if ($vatRate) {
            $toPayAmount = $toPayAmountTaxExcl * (1 + $vatRate / 100);
            $toPayAmount = number_format($toPayAmount, 2, '.', '');
        }

        $freeChange = $subscription->isInTrial();
        if ($freeChange) {
            $toPayAmount = 0;
            $toPayAmountTaxExcl = 0;
        }

        $output = $this->parser->set('permalink', $permalink)
            ->set('product', $product)
            ->set('subscription', $subscription)
            ->set('toPayAmount', $toPayAmount)
            ->set('toPayAmountTaxExcl', $toPayAmountTaxExcl)
            ->set('vatRate', $vatRate)
            ->set('subscriptionLimits', $subscriptionLimits)
            ->set('productLimits', $productLimits)
            ->set('qty', $qty)
            ->set('qtyError', $qtyError)
            ->set('productSubscription', $productSubscription)
            ->set('freeChange', $freeChange)
            ->render('app/billing/plan_change.php');

        Assets::addCss('app/billing.css');
        $this->displayOutput($output);
    }

    #[Route("/app/billing/plan/change/{permalink}/", name: "app_billing_change_plan_post", requirements: ['permalink' => Requirement::ASCII_SLUG], methods: ["POST"])]
    #[View('MatGyver\Services\Views\ViewAppSubscriptionSettings')]
    public function planChangePost(string $permalink)
    {
        $this->checkRequest();

        $changePlan = $this->get(ClientsSubscriptionsService::class)->changeSubscription($this->submittedData);
        if (!$changePlan['valid']) {
            if (isset($changePlan['redirect_url'])) {
                header('Location: ' . $changePlan['redirect_url']);
                exit();
            }
            $this->displayErrorAndRedirect($changePlan['message'], Tools::makeLink('app', 'billing', 'plan/change/' . $permalink));
        }

        $this->displaySuccessAndRedirect(__('Votre abonnement a été mis à jour.'), Tools::makeLink('app', 'billing', 'confirmation', 'id_order=' . $changePlan['id_order']));
    }
}
