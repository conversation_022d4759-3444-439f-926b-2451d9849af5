<?php

namespace MatGyver\Controllers\App;

use MatGyver\Controllers\AbstractController;
use MatGyver\Attributes\Universe;
use MatGyver\Attributes\View;
use MatGyver\Entity\Game\GameLevel;
use MatGyver\Entity\Game\GameQuest;
use MatGyver\Enums\ProductsEnum;
use MatGyver\Factories\BlankStates\FeatureBasicBlankState;
use MatGyver\Factories\BlankStates\GameLevelRequiredBlankState;
use MatGyver\Factories\BlankStates\GameQuestRequiredBlankState;
use MatGyver\Factories\BlankStates\SubscriptionCompleteBlankState;
use MatGyver\Factories\BlankStates\SubscriptionMediumBlankState;
use MatGyver\Factories\BlankStates\SubscriptionPremiumBlankState;
use MatGyver\Factories\BlankStates\SubscriptionStartBlankState;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Clients\ClientsService;
use MatGyver\Services\Game\GameLevelService;
use MatGyver\Services\Game\GameQuestService;
use MatGyver\Services\News\NewsContentService;
use MatGyver\Services\RightsService;
use MatGyver\Services\SiteService;
use MatGyver\Services\Tos\TosService;
use MatGyver\Services\Users\UsersLoginService;
use MatGyver\Services\Views\ViewApp;

#[Universe(UNIVERSE_APP_PUBLIC)]
#[View('MatGyver\Services\Views\ViewApp')]
class AbstractAppController extends AbstractController
{
    /**
     * @var string|null
     */
    protected $gameQuest = null;

    /**
     * @var string|null
     */
    protected $gameQuestStep = null;

    /**
     * @var int|null
     */
    protected $gameLevel = null;

    /**
     * AbstractAppController constructor.
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function __construct()
    {
        parent::__construct();
        $this->setView($this->get(ViewApp::class));
    }

    /**
     * @param string $action
     * @param string $param
     * @param string $query
     * @param string $route
     * @param array $params
     * @return mixed
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function dispatch(string $action = 'index', string $param = '', string $query = '', string $route = '', array $params = [])
    {
        $controller = 'app';
        $_SESSION['controller'] = $controller;
        $this->action = $action;
        $this->param = $param;
        $this->query = $query;
        $this->route = $route;

        $this->view->set('action', $this->action);
        $this->view->set('param', $this->param);
        $this->view->set('route', $this->route);
        $this->view->set('quest', $this->gameQuest);
        $this->view->set('quest_step', $this->gameQuestStep);

        //redirect if user is not on APP_URL
        if (ENV === ENV_PROD and !str_contains($_SERVER['HTTP_HOST'], APP_DOMAIN)) {
            $link = APP_URL . $_SERVER['REQUEST_URI'];
            header('Location: ' . $link);
            exit();
        }

        if (!$this->action) {
            SiteService::display404();
        }

        if (!RightsService::isSuperAdmin() and (!$_SESSION['client']['active'] or $_SESSION['client']['on_pause'])) {
            if ($this->action == 'logout') {
                UsersLoginService::logout();
            }
            if ($_SESSION['client']['freemium'] and $_SESSION['client']['date_desactivation'] == '0000-00-00 00:00:00') {
                SiteService::displayWaitingConfirmationPage($_SESSION['client']['id']);
                exit();
            }

            $this->get(UsersLoginService::class)->checkIfConnected();

            if (!$_SESSION['user']['admin']) {
                SiteService::displayClientDeactivated();
                exit();
            }

            $authorizedRoutes = ['billing', 'billingPaymentMethods', 'billingPaymentMethodAdd', 'paymentMethodAddCard', 'plans', 'billingInvoice', 'planAdd', 'planAddPost', 'planChange', 'billingSettings', 'billingConfirmation', 'billingCancelSubscription', 'billingCancelSubscriptionConfirm', 'billingPause', 'reset', 'resetPassword', 'tosId', 'tosIdPost'];
            if (!in_array($action, $authorizedRoutes)) {
                header('Location: ' . Tools::makeLink('app', 'billing'));
                exit();
            }
        }

        $routesWoFireWall = array(
            'logout',
            'reset',
            'resetPassword',
            'reconnect',
        );
        if (!in_array($action, $routesWoFireWall)) {
            $this->get(UsersLoginService::class)->checkIfConnected();

            if ($this->action != 'tosId' and $this->action != 'tosIdPost' and $this->action != 'support' and $this->action != 'supportCreateTicket') {
                $tos = $this->get(TosService::class)->hasRequiredTosToDisplay();
                if ($tos) {
                    header('Location: ' . Tools::makeLink('app', 'tos', $tos->getId()));
                    exit();
                }
            }

            if ($this->action != 'newsId') {
                $checkNews = $this->get(NewsContentService::class)->checkFullScreenNews();
                if ($checkNews and RightsService::hasAccess(UNIVERSE_APP_NEWS)) {
                    header('Location: ' . Tools::makeLink('app', 'news', $checkNews));
                    exit();
                }
            }

            if (GAME_ACTIVATED) {
                if ($this->gameQuest) {
                    $quest = $this->get(GameQuestService::class)->getRepository()->findOneBy(['identifier' => $this->gameQuest]);
                    if ($quest) {
                        $questClient = $quest->getQuestClient();
                        if (!$questClient) {
                            $this->displayQuestRequired($quest);
                            exit();
                        }
                    }
                }
                if ($this->gameLevel) {
                    $level = $this->get(GameLevelService::class)->getRepository()->findOneBy(['level' => $this->gameLevel]);
                    if ($level) {
                        $levelClient = $level->getClientLevel();
                        if (!$levelClient) {
                            $this->displayLevelRequired($level);
                            exit();
                        }
                    }
                }
            }
        }

        if (method_exists($this, $action)) {
            if ($params) {
                $method = new \ReflectionMethod($this, $action);
                if (!$method->getParameters()) {
                    $params = [];
                }
            }
            call_user_func_array(array($this, $action), $params);
            return;
        }

        $this->display404();
        exit();
    }

    /**
     * @return void
     */
    protected function display404(): void
    {
        if (!isset($_SESSION['user'])) {
            $this->get(UsersLoginService::class)->checkIfConnected();
        }
        parent::display404();
    }

    /**
     * @param GameQuest $quest
     * @return void
     */
    protected function displayQuestRequired(GameQuest $quest): void
    {
        $factory = new GameQuestRequiredBlankState($quest);
        $output = $factory->render();
        $this->displayOutput($output);
    }

    /**
     * @param GameLevel $level
     * @return void
     */
    protected function displayLevelRequired(GameLevel $level): void
    {
        $factory = new GameLevelRequiredBlankState($level);
        $output = $factory->render();
        $this->displayOutput($output);
    }

    /**
     * @param string $menu
     * @param string $content
     * @return void
     */
    protected function displaySettings(string $menu = '', string $content = ''): void
    {
        $output = $this->parser->set('menu', $menu)
            ->set('content', $content)
            ->render('layouts/app/settings.php');
        $this->displayOutput($output);
    }

    /**
     * @param string $questIdentifier
     * @return void
     */
    public function setGameQuest(string $questIdentifier): void
    {
        $this->gameQuest = $questIdentifier;
    }

    /**
     * @param string $questIdentifier
     * @return void
     */
    public function setGameQuestStep(string $questIdentifier): void
    {
        $this->gameQuestStep = $questIdentifier;
    }

    /**
     * @param int $levelId
     * @return void
     */
    public function setGameLevel(int $levelId): void
    {
        $this->gameLevel = $levelId;
    }

    /**
     * @param string $btnLink
     * @param string $btnText
     * @return string
     */
    public function displayBtnNextStep(string $btnLink, string $btnText = '', string $btnClass = 'btn-primary'): string
    {
        if (!$btnText) {
            $btnText = __('Passer à l\'étape suivante');
        }
        return '
        <div class="d-flex justify-content-end mt-4">
            <a class="btn ' . $btnClass . '" href="' . $btnLink . '">' . $btnText . '<i class="fas fa-arrow-right icon-sm ml-2"></i></a>
        </div>';
    }

    /**
     * @param string $productReference
     * @return void
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function checkSubscription(string $productReference)
    {
        $client = $this->get(ClientsService::class)->getClient();
        if (!$client->hasSubscription($productReference)) {
            switch ($productReference) {
                case ProductsEnum::REF_MEDIUM:
                    $factory = new SubscriptionMediumBlankState();
                    break;
                case ProductsEnum::REF_PREMIUM:
                    $factory = new SubscriptionPremiumBlankState();
                    break;
                case ProductsEnum::REF_COMPLETE:
                    $factory = new SubscriptionCompleteBlankState();
                    break;
                default:
                    $factory = new SubscriptionStartBlankState();
                    break;
            }
            $this->displayOutput($factory->render());
            exit();
        }
    }

    public function denyUnlessStart()
    {
        $this->checkSubscription(ProductsEnum::REF_START);
    }

    public function denyUnlessMedium()
    {
        $this->checkSubscription(ProductsEnum::REF_MEDIUM);
    }

    public function denyUnlessPremium()
    {
        $this->checkSubscription(ProductsEnum::REF_PREMIUM);
    }

    public function denyUnlessComplete()
    {
        $this->checkSubscription(ProductsEnum::REF_COMPLETE);
    }
}
