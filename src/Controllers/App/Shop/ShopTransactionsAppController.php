<?php

namespace MatGyver\Controllers\App\Shop;

use MatGyver\Attributes\Universe;
use MatGyver\Controllers\App\AbstractAppController;
use Mat<PERSON>yver\FormsFactory\FormFactory;
use Mat<PERSON>yver\FormsFactory\Shop\ShopTransactionRefundFormFactory;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Dossier\DossierPaymentLinkService;
use MatGyver\Services\Shop\Transaction\ShopTransactionService;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\Requirement\Requirement;

#[Universe(UNIVERSE_APP_SHOP)]
class ShopTransactionsAppController extends AbstractAppController
{
    #[Route('/app/shop/transaction/refund/{reference}/', name: 'app_shop_transaction_refund', requirements: ['reference' => Requirement::ASCII_SLUG])]
    public function transactionRefund(string $reference)
    {
        $this->view->setTitle(__('Remboursement d\'une transaction'));
        $this->denyUnlessPremium();

        $transaction = $this->get(ShopTransactionService::class)->getRepository()->findOneBy(['reference' => $reference]);
        if (!$transaction) {
            $this->displayError(__('Cette transaction n\'existe pas.'), Tools::makeLink('app', 'dossiers', 'payments'));
            return;
        }

        $redirect = Tools::makeLink('app', 'dossiers', 'payments');
        $dossierPaymentLink = $this->get(DossierPaymentLinkService::class)->getRepository()->findOneBy(['transaction' => $transaction]);
        if ($dossierPaymentLink and $dossierPaymentLink->getDossierPayment()) {
            $redirect = Tools::makeLink('app', 'dossier', 'payment/view/' . $dossierPaymentLink->getDossierPayment()->getId() . '/' . $dossierPaymentLink->getDossier()->getId());
        }

        $this->view->addBreadcrumbItem(__('Transaction') . ' #' . $reference, $redirect);

        if (!$transaction->getValid()) {
            $this->displayError(__('Impossible de rembourser cette transaction.'), $redirect);
            return;
        }
        if ($transaction->getPaymentMethod() != 'Stripe') {
            $this->displayError(__('Impossible de rembourser ce type de transaction.'), $redirect);
            return;
        }

        $transactionRefundForm = new ShopTransactionRefundFormFactory($redirect);
        if ($transactionRefundForm->isSubmitted() and $transactionRefundForm->isValid()) {
            $process = $transactionRefundForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                header('Location: ' . $redirect);
                exit();
            }
        }
        $this->displayOutput($this->builderForm->render($transactionRefundForm, $transaction));
    }
}
