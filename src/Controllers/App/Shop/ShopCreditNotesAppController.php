<?php

namespace MatGyver\Controllers\App\Shop;

use MatG<PERSON>ver\Attributes\Universe;
use MatG<PERSON>ver\Attributes\View;
use MatGyver\Controllers\App\AbstractAppController;
use MatGyver\Enums\ConfigEnum;
use Mat<PERSON><PERSON>ver\FormsFactory\FormFactory;
use MatGyver\FormsFactory\Shop\ShopCreditNoteFormFactory;
use MatGyver\FormsFactory\Shop\ShopCreditNotesSettingsFactory;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Table\Shop\AppShopCreditNotesViewHelper;
use MatGyver\Services\ConfigService;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\RightsService;
use MatGyver\Services\Shop\CreditNotes\ShopCreditNotesExportService;
use MatGyver\Services\Shop\CreditNotes\ShopCreditNotesService;
use MatGyver\Services\Shop\ShopAccountingService;
use MatG<PERSON>ver\Services\Shop\Transaction\ShopTransactionService;
use MatGyver\Services\Users\UserConfigService;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\Requirement\Requirement;

#[Universe(UNIVERSE_APP_SHOP)]
class ShopCreditNotesAppController extends AbstractAppController
{

    #[Route('/app/shop/credit_notes/', name: 'app_shop_credit_notes')]
    public function creditNotes()
    {
        $this->view->setTitle(__('Avoirs'));
        $this->denyUnlessPremium();

        if (RightsService::isGranted(ATTRIBUTE_EXPORT, UNIVERSE_APP_SHOP)) {
            $this->view->addAction('<i class="far fa-file-pdf pr-0"></i>', Tools::makeLink('app', 'shop', 'credit_notes/export'), 'btn-light');
            $this->view->addAction('<i class="fas fa-file-csv pr-0"></i>', Tools::makeLink('app', 'shop', 'credit_notes/export_csv'), 'btn-light');
        }
        if (RightsService::isGranted(ATTRIBUTE_CREATE, UNIVERSE_APP_SHOP)) {
            $this->view->addAction(__('Créer un avoir'), Tools::makeLink('app', 'shop', 'credit_note'));
        }

        $output = $this->get(AppShopCreditNotesViewHelper::class)->getContent();
        Assets::addJs('app/tables/credit_notes.js');
        $this->displayOutputCard($output);
    }

    #[Route('/app/shop/credit_note/', name: 'app_shop_credit_note_add')]
    public function creditNoteAdd()
    {
        $this->view->setTitle(__('Création d\'un avoir'));
        $this->view->addBreadcrumbItem(__('Avoirs'), Tools::makeLink('app', 'shop', 'credit_notes'));
        $this->denyUnlessPremium();
        $this->denyAccessUnlessGranted(ATTRIBUTE_CREATE, UNIVERSE_APP_SHOP);

        $creditNoteForm = new ShopCreditNoteFormFactory();
        if ($creditNoteForm->isSubmitted() and $creditNoteForm->isValid()) {
            $process = $creditNoteForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_shop_credit_note_detail', ['idCreditNote' => $process['id']]);
            }
        }
        $this->displayOutput($this->builderForm->render($creditNoteForm));
    }

    #[Route("/app/shop/credit_note_detail/{idCreditNote}/", name: "app_shop_credit_note_detail", requirements: ['idCreditNote' => Requirement::POSITIVE_INT])]
    #[View('MatGyver\Services\Views\ViewAppCreditNote')]
    public function creditNoteDetail(int $idCreditNote)
    {
        $this->view->setTitle(__('Détail d\'un avoir'));
        $this->view->addBreadcrumbItem(__('Avoirs'), Tools::makeLink('app', 'shop', 'credit_notes'));
        $this->denyUnlessPremium();

        $creditNote = $this->get(ShopCreditNotesService::class)->getCreditNoteById($idCreditNote);
        if (!$creditNote) {
            $this->displayError(__("Erreur : cet avoir n'existe pas"), Tools::makeLink('app', 'shop', 'credit_notes'));
            return;
        }

        $newInvoicesSettings = $this->get(ConfigService::class)->findByName(ConfigEnum::INVOICE);
        if ($newInvoicesSettings) {
            $newInvoicesSettings = json_decode($newInvoicesSettings->getValue(), true);
        }

        $creditNoteSettings = array();
        if ($creditNote->getSettings() and $creditNote->getSettings() != '[]') {
            $creditNoteSettings = json_decode($creditNote->getSettings(), true);
        }

        $logo = '';
        if ($newInvoicesSettings and isset($newInvoicesSettings['logo']) and $newInvoicesSettings['logo']) {
            $logo = $newInvoicesSettings['logo'];
        }

        $bgColor = '#985AED';
        if (isset($newInvoicesSettings['bg_color']) and $newInvoicesSettings['bg_color']) {
            $bgColor = $newInvoicesSettings['bg_color'];
        }

        $creditNoteUser = json_decode($creditNote->getCustomer(), true);

        $transaction = [];
        if ($creditNote->getTransactionReference()) {
            $transaction = $this->get(ShopTransactionService::class)->getTransactionByReference($creditNote->getTransactionReference());
        }

        $user = null;
        $userId = 0;
        if ($creditNote->getUser()) {
            $user = $creditNote->getUser();
            $userId = $user->getId();
        }
        if (!$userId and $transaction and $transaction->getUser()) {
            $user = $transaction->getUser();
            $userId = $user->getId();
        }

        $userConfig = array();
        if ($user) {
            $userConfig = $this->get(UserConfigService::class)->getUserConfig($user);
        }

        $details = $this->get(ShopAccountingService::class)->getDetailsForCreditNote($creditNote->getId());

        $infosFooter = '';
        if ($creditNoteSettings and isset($creditNoteSettings['code_siret']) and $creditNoteSettings['code_siret']) {
            $infosFooter .= __('N° SIRET') . ' : ' . $creditNoteSettings['code_siret'] . ' - ';
        }
        if ($creditNoteSettings and isset($creditNoteSettings['tva_intra']) and $creditNoteSettings['tva_intra']) {
            $infosFooter .= __('N° TVA') . ' : ' . $creditNoteSettings['tva_intra'] . ' - ';
        }
        if ($creditNoteSettings and isset($creditNoteSettings['code_naf']) and $creditNoteSettings['code_naf']) {
            $infosFooter .= __('NAF') . ' : ' . $creditNoteSettings['code_naf'] . ' - ';
        }
        $infosFooter = rtrim($infosFooter, ' - ');

        $output = $this->parser->set('creditNote', $creditNote)
            ->set('id_credit_note', $idCreditNote)
            ->set('creditNoteUser', $creditNoteUser)
            ->set('transaction', $transaction)
            ->set('user', $user)
            ->set('userId', $userId)
            ->set('userConfig', $userConfig)
            ->set('details', $details)
            ->set('logo', $logo)
            ->set('bgColor', $bgColor)
            ->set('creditNoteSettings', $creditNoteSettings)
            ->set('infosFooter', $infosFooter)
            ->render('admin/shop/credit_notes/credit_note.php');
        $this->displayOutput($output);
    }

    #[Route('/app/shop/credit_note/export/{idCreditNote}/', name: 'app_shop_export_credit_note', requirements: ['idCreditNote' => Requirement::POSITIVE_INT])]
    public function exportCreditNote(int $idCreditNote)
    {
        $this->denyUnlessPremium();
        $this->denyAccessUnlessGranted(ATTRIBUTE_EXPORT, UNIVERSE_APP_SHOP);

        $creditNote = $this->get(ShopCreditNotesService::class)->getCreditNoteById($idCreditNote);
        if (!$creditNote) {
            $this->displayError(__("Erreur : cet avoir n'existe pas"), Tools::makeLink('app', 'shop', 'credit_notes'));
            return;
        }

        if (!$creditNote->getLocked()) {
            $this->displayError(
                __('Erreur : cet avoir n\'est pas verrouillé et ne peut donc pas être exporté.'),
                Tools::makeLink('app', 'shop', 'credit_note/lock/' . $creditNote->getId()),
                __('Verrouiller l\'avoir')
            );
            exit();
        }

        $this->get(ShopCreditNotesExportService::class)->exportPdf($idCreditNote);
        exit();
    }

    #[Route('/app/shop/credit_note/lock/{id}/', name: 'app_shop_credit_note_lock')]
    public function lockCreditNote()
    {
        $this->denyUnlessPremium();
        $this->denyAccessUnlessGranted(ATTRIBUTE_EDIT, UNIVERSE_APP_SHOP);

        $idCreditNote = filter_var($this->param, FILTER_VALIDATE_INT);
        if (!$idCreditNote) {
            $this->displayError(__('Erreur : impossible de récupérer l\'avoir'), Tools::makeLink('app', 'shop', 'credit_notes'));
            return;
        }
        $creditNote = $this->get(ShopCreditNotesService::class)->getCreditNoteById($idCreditNote);
        if (!$creditNote) {
            $this->displayError(__("Erreur : cet avoir n'existe pas"), Tools::makeLink('app', 'shop', 'credit_notes'));
            return;
        }

        $creditNote->setLocked(true);
        try {
            $this->get(ShopCreditNotesService::class)->persistAndFlush($creditNote);
        } catch (\Exception $e) {
            $this->displayError(__("Une erreur est survenue lors du verrouillage de cet avoir."), Tools::makeLink('app', 'shop', 'credit_notes'));
            return;
        }

        $redirect = Tools::makeLink('app', 'shop', 'credit_note_detail/' . $idCreditNote);
        if ($creditNote->getDossier()) {
            $redirect = Tools::makeLink('app', 'dossier', 'credit_note/' . $idCreditNote . '/' . $creditNote->getDossier()->getId());
        }

        $this->displaySuccessAndRedirect(__('L\'avoir a bien été verrouillé.'), $redirect);
    }

    #[Route('/app/shop/credit_notes/export/', name: 'app_shop_export_credit_notes')]
    public function exportCreditNotes()
    {
        $this->view->setTitle(__('Export des avoirs'));
        $this->view->addBreadcrumbItem(__('Avoirs'), Tools::makeLink('app', 'shop', 'credit_notes'));
        $this->denyUnlessPremium();
        $this->denyAccessUnlessGranted(ATTRIBUTE_EXPORT, UNIVERSE_APP_SHOP);

        $nbCreditNotes = $this->get(ShopCreditNotesService::class)->getCountCreditNotes();
        if (!$nbCreditNotes) {
            $this->displayError(__('Aucun avoir trouvé'), Tools::makeLink('app', 'shop', 'credit_notes'));
            return;
        }

        $output = $this->parser->set('nbCreditNotes', $nbCreditNotes)
            ->render('admin/shop/credit_notes/export_credit_notes.php');

        Assets::addCss('common/blank_state.css');
        Assets::addJs('admin/shop/export_credit_notes.js');
        $this->displayOutputCard($output);
    }

    #[Route('/app/shop/credit_notes/export_csv/', name: 'app_shop_export_credit_notes_csv')]
    public function exportCreditNotesCsv()
    {
        $this->view->setTitle(__('Export des avoirs'));
        $this->view->addBreadcrumbItem(__('Avoirs'), Tools::makeLink('app', 'shop', 'credit_notes'));
        $this->denyUnlessPremium();
        $this->denyAccessUnlessGranted(ATTRIBUTE_EXPORT, UNIVERSE_APP_SHOP);

        $nbCreditNotes = $this->get(ShopCreditNotesService::class)->getCountCreditNotes();
        if (!$nbCreditNotes) {
            $this->displayError(__('Aucun avoir trouvé'), Tools::makeLink('app', 'shop', 'credit_notes'));
            return;
        }

        $output = $this->parser->set('nbCreditNotes', $nbCreditNotes)
            ->render('admin/shop/credit_notes/export_credit_notes.php');

        Assets::addCss('common/blank_state.css');
        Assets::addJs('admin/shop/export_credit_notes_csv.js');
        $this->displayOutputCard($output);
    }

    #[Route('/app/shop/credit_note/send/{id}/', name: 'app_shop_credit_note_send')]
    public function sendCreditNote()
    {
        $this->denyUnlessPremium();

        $idCreditNote = filter_var($this->param, FILTER_UNSAFE_RAW);
        if (!$idCreditNote) {
            $this->displayError(__('Erreur : impossible de récupérer l\'avoir'), Tools::makeLink('app', 'shop', 'invoices'));
            return;
        }
        $creditNote = $this->get(ShopCreditNotesService::class)->getCreditNoteById($idCreditNote);
        if (!$creditNote) {
            $this->displayError(__("Erreur : cet avoir n'existe pas"), Tools::makeLink('app', 'shop', 'credit_notes'));
            return;
        }

        $sendCreditNote = $this->get(ShopCreditNotesService::class)->sendCreditNote($creditNote);
        if (!$sendCreditNote['valid']) {
            $this->displayErrorAndRedirect($sendCreditNote['message'], Tools::makeLink('app', 'shop', 'credit_note_detail/' . $idCreditNote));
        }

        $this->displaySuccessAndRedirect(__('L\'avoir a bien été envoyé.'), Tools::makeLink('app', 'shop', 'credit_note_detail/' . $idCreditNote));
    }

    #[Route("/app/settings/credit_notes/", name: "app_settings_credit_notes")]
    #[View('MatGyver\Services\Views\ViewAppSettings')]
    public function creditNotesSettings()
    {
        $this->view->setTitle(__('Avoirs'));

        $creditNotesForm = new ShopCreditNotesSettingsFactory();
        if ($creditNotesForm->isSubmitted() and $creditNotesForm->isValid()) {
            $creditNotesForm->process(FormFactory::TYPE_PROCESS_INSERT);
            $this->get(DispatcherService::class)->redirectToRoute('app_settings_credit_notes');
        }
        $this->displayOutput($this->builderForm->render($creditNotesForm));
    }
}
