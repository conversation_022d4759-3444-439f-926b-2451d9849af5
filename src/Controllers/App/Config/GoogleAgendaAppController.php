<?php

namespace MatGyver\Controllers\App\Config;

use MatGyver\Attributes\View;
use MatGyver\Factories\BlankStates\GoogleAgendaSuccessBlankState;
use MatGyver\FormsFactory\FormFactory;
use MatGyver\FormsFactory\Google\GoogleAgendaFormFactory;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Integration\IntegrationAccountsDataService;
use MatGyver\Services\Integration\IntegrationAccountsService;
use MatGyver\Services\Integration\Services\GoogleAgendaService;
use Symfony\Component\Routing\Attribute\Route;

#[View('MatGyver\Services\Views\ViewAppSettings')]
class GoogleAgendaAppController extends AbstractConfigAppController
{
    #[Route('/app/google_agenda/redirect/', name: 'app_google_agenda_redirect')]
    public function googleAgendaRedirect()
    {
        $this->get(GoogleAgendaService::class)->oauthRedirect();
    }

    #[Route('/app/google_agenda/oauth/', name: 'app_google_agenda_oauth')]
    public function googleAgendaOauth()
    {
        $oauthToken = $this->get(GoogleAgendaService::class)->oauthToken();
        if (!$oauthToken['valid']) {
            $this->displayError(
                $oauthToken['message'],
                Tools::makeLink('app', 'integration', 'add/google_agenda'),
                __('Recommencer')
            );
            return;
        }

        /*$this->displaySuccess(
            __('Le compte Google a bien été ajouté. Cliquez sur le bouton "Continuer" pour récupérer vos agendas Google.'),
            Tools::makeLink('app', 'google_agenda', 'calendars/' . $oauthToken['id']),
            __('Continuer'),
            __('Compte Google ajouté'),
        );
        exit();*/

        header('Location: ' . Tools::makeLink('app', 'google_agenda', 'calendars/' . $oauthToken['id']));
        exit();
    }

    #[Route('/app/google_agenda/calendars/{id}/', name: 'app_google_agenda_calendars')]
    public function googleAgendaCalendars()
    {
        $accountId = filter_var($this->param, FILTER_VALIDATE_INT);
        $account = $this->get(IntegrationAccountsService::class)->getAccount($accountId);
        if (!$account or $account->getType() != 'google_agenda') {
            $this->displayError(__('Ce compte n\'existe pas'), Tools::makeLink('app', 'integrations'));
            return;
        }

        $getAgendas = $this->get(GoogleAgendaService::class)->getAgendas($account);
        if (!$getAgendas['valid']) {
            $this->displayError($getAgendas['message'], Tools::makeLink('app', 'integrations'));
            return;
        }

        $googleAgendaForm = new GoogleAgendaFormFactory($account);
        if ($googleAgendaForm->isSubmitted() and $googleAgendaForm->isValid()) {
            $process = $googleAgendaForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                $factory = new GoogleAgendaSuccessBlankState($account);
                $this->displayOutput($factory->render());
                return;
            }
        }
        $output = $this->builderForm->render($googleAgendaForm);
        $this->displayOutput($output);
    }

    #[Route('/app/google_agenda/event/{eventId}/', name: 'app_google_agenda_event')]
    public function googleAgendaEvent(string $eventId)
    {
        $account = $this->get(IntegrationAccountsService::class)->getFirstAccountByType('google_agenda');
        if (!$account) {
            echo "no account";
            exit();
        }

        $calendarId = $this->get(IntegrationAccountsDataService::class)->getData($account, 'calendar_id');
        if (!$calendarId or !$calendarId->getValue()) {
            echo "aucun calendrier";
            exit();
        }

        $getEvent = $this->get(GoogleAgendaService::class)->getEvent($account, $calendarId->getValue(), $eventId);
        p($getEvent, true);
    }
}
