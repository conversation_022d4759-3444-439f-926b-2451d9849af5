<?php
namespace MatGyver\Controllers\App\Config;

use MatGyver\Entity\Evaluation\Evaluation;
use MatGyver\FormsFactory\Evaluation\EvaluationFormFactory;
use MatGyver\FormsFactory\FormFactory;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Action;
use MatGyver\Helpers\View\Card\Card;
use MatGyver\Helpers\View\Table\Evaluation\EvaluationViewHelper;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\Evaluation\EvaluationService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class EvaluationAppController
 * @package MatGyver\Controllers\App\Evaluation
 */
class EvaluationAppController extends AbstractConfigAppController
{

    #[Route('/app/evaluations/', name: 'app_evaluations')]
    public function evaluations()
    {
        $this->view->setTitle(__('Évaluations'));

        $evaluations = $this->get(EvaluationService::class)->getRepository()->findAll();
        if (!$evaluations) {
            $this->displayError(__('Aucune évaluation enregistrée.'), Tools::makeLink('app', 'evaluation', 'add'), __('Ajouter une évaluation'));
            return;
        }

        $output = $this->get(EvaluationViewHelper::class)->getContent($evaluations);
        $card = new Card();
        $card->setTitle(__('Évaluations'));
        $card->setBody($output);
        $action = new Action();
        $action->setTitle(__('Ajouter une évaluation'));
        $action->setClass('btn-primary');
        $action->setHref(Tools::makeLink('app', 'evaluation', 'add'));
        $card->addAction($action);
        $this->displayOutput($card->getContent());
    }

    #[Route('/app/evaluation/add/', name: 'app_evaluation_add')]
    public function evaluationAdd()
    {
        $this->view->setTitle(__('Création d\'une évaluation'));
        $this->view->addBreadcrumbItem(__('Évaluation'), Tools::makeLink('app', 'evaluations'));

        $evaluationForm = new EvaluationFormFactory();
        if ($evaluationForm->isSubmitted() and $evaluationForm->isValid()) {
            $process = $evaluationForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_evaluations');
            }
        }
        $this->displayOutput($this->builderForm->render($evaluationForm));
    }

    #[Route('/app/evaluation/edit/{id}/', name: 'app_evaluation_edit')]
    public function evaluationEdit()
    {
        $this->view->setTitle(__('Modification d\'une évaluation'));
        $this->view->addBreadcrumbItem(__('Évaluations'), Tools::makeLink('app', 'evaluations'));

        $evaluation = $this->getFromRoute();

        $evaluationForm = new EvaluationFormFactory();
        if ($evaluationForm->isSubmitted() and $evaluationForm->isValid()) {
            $process = $evaluationForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_evaluations');
            }
        }
        $output = $this->builderForm->render($evaluationForm, $evaluation);
        $this->displayOutput($output);
    }

    #[Route('/app/evaluation/delete/{id}/', name: 'app_evaluation_delete')]
    public function evaluationDelete()
    {
        $evaluation = $this->getFromRoute();
        try {
            $this->get(EvaluationService::class)->deleteAndFlush($evaluation);
        } catch (\Exception $e) {
            $this->displayError(__('Impossible de supprimer cette évaluation.'));
            return;
        }
        $this->get(DispatcherService::class)->redirectToRoute('app_evaluations');
    }

    /**
     * @return Evaluation|null
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    private function getFromRoute(): ?Evaluation
    {
        $evaluationId = filter_var($this->param, FILTER_VALIDATE_INT);
        if (!$evaluationId) {
            $this->displayError(__('Erreur : cette évaluation n\'existe pas.'), Tools::makeLink('app', 'evaluations'));
            exit();
        }

        $evaluation = $this->get(EvaluationService::class)->getRepository()->find($evaluationId);
        if (!$evaluation) {
            $this->displayError(__('Erreur : cette évaluation n\'existe pas.'), Tools::makeLink('app', 'evaluations'));
            exit();
        }

        return $evaluation;
    }
}
