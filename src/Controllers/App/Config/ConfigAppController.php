<?php

namespace MatGyver\Controllers\App\Config;

use MatG<PERSON>ver\FormsFactory\Config\ConfigAppSettingsFormFactory;
use Mat<PERSON><PERSON>ver\FormsFactory\Config\ConfigContactInformationsFormFactory;
use MatGyver\FormsFactory\FormFactory;
use MatGyver\Services\DispatcherService;
use Symfony\Component\Routing\Attribute\Route;
use MatGyver\Attributes\View;

#[View('MatGyver\Services\Views\ViewAppSettings')]
class ConfigAppController extends AbstractConfigAppController
{
    #[Route('/app/settings/', name: 'app_settings')]
    public function settings()
    {
        $settingsForm = new ConfigAppSettingsFormFactory();
        if ($settingsForm->isSubmitted() and $settingsForm->isValid()) {
            $settingsForm->process(FormFactory::TYPE_PROCESS_INSERT);
            $this->get(DispatcherService::class)->redirectToRoute('app_settings');
        }
        $this->displayOutput($this->builderForm->render($settingsForm));
    }

    #[Route('/app/settings/contact_informations/', name: 'app_settings_contact_informations')]
    public function configContactInformations()
    {
        $contactForm = new ConfigContactInformationsFormFactory();
        if ($contactForm->isSubmitted() and $contactForm->isValid()) {
            $contactForm->process(FormFactory::TYPE_PROCESS_INSERT);
        }
        $this->displayOutput($this->builderForm->render($contactForm));
    }
}
