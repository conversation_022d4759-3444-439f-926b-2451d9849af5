<?php
namespace MatGyver\Controllers\App\Config;

use Mat<PERSON><PERSON>ver\Entity\Surcharge\SurchargeComment;
use MatGyver\FormsFactory\FormFactory;
use MatGyver\FormsFactory\Surcharge\SurchargeCommentFormFactory;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Action;
use MatGyver\Helpers\View\Card\Card;
use MatGyver\Helpers\View\Table\Surcharge\SurchargeCommentViewHelper;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\Surcharge\SurchargeCommentService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class SurchargeCommentAppController
 * @package MatGyver\Controllers\App\Config
 */
class SurchargeCommentAppController extends AbstractConfigAppController
{

    #[Route('/app/surcharges_comments/', name: 'app_surcharges_comments')]
    public function surchargesComments()
    {
        $this->view->setTitle(__('Commentaires sur les frais'));

        $surchargesComments = $this->get(SurchargeCommentService::class)->getRepository()->findAll();
        if (!$surchargesComments) {
            $this->displayEmpty(__('Commentaires sur les frais'), __('Aucun commentaire sur les frais enregistré.') . '<br>' . __('Cette fonctionnalité vous permet de rédiger rapidement des observations sur les frais d\'expertise lors de la création d\'un dossier.'), Tools::makeLink('app', 'surcharge_comment', 'add'), __('Ajouter un commentaire'));
            return;
        }

        $output = $this->get(SurchargeCommentViewHelper::class)->getContent($surchargesComments);
        $card = new Card();
        $card->setTitle(__('Commentaires sur les frais'));
        $card->setBody($output);
        $action = new Action();
        $action->setTitle(__('Ajouter un commentaire'));
        $action->setClass('btn-primary');
        $action->setHref(Tools::makeLink('app', 'surcharge_comment', 'add'));
        $card->addAction($action);
        $this->displayOutput($card->getContent());
    }

    #[Route('/app/surcharge_comment/add/', name: 'app_surcharge_comment_add')]
    public function surchargeCommentAdd()
    {
        $this->view->setTitle(__('Création d\'un commentaire'));
        $this->view->addBreadcrumbItem(__('Commentaires sur les frais'), Tools::makeLink('app', 'surcharges_comments'));

        $surchargeCommentForm = new SurchargeCommentFormFactory();
        if ($surchargeCommentForm->isSubmitted() and $surchargeCommentForm->isValid()) {
            $process = $surchargeCommentForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_surcharges_comments');
            }
        }
        $this->displayOutput($this->builderForm->render($surchargeCommentForm));
    }

    #[Route('/app/surcharge_comment/edit/{id}/', name: 'app_surcharge_comment_edit')]
    public function surchargeCommentEdit()
    {
        $this->view->setTitle(__('Modification d\'un commentaire'));
        $this->view->addBreadcrumbItem(__('Commentaires sur les frais'), Tools::makeLink('app', 'surcharges_comments'));

        $surchargeComment = $this->getFromRoute();

        $surchargeCommentForm = new SurchargeCommentFormFactory();
        if ($surchargeCommentForm->isSubmitted() and $surchargeCommentForm->isValid()) {
            $process = $surchargeCommentForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_surcharges_comments');
            }
        }
        $output = $this->builderForm->render($surchargeCommentForm, $surchargeComment);
        $this->displayOutput($output);
    }

    #[Route('/app/surcharge_comment/delete/{id}/', name: 'app_surcharge_comment_delete')]
    public function surchargeDelete()
    {
        $surchargeComment = $this->getFromRoute();
        try {
            $this->get(SurchargeCommentService::class)->deleteAndFlush($surchargeComment);
        } catch (\Exception $e) {
            $this->displayError(__('Impossible de supprimer ce frais annexe.'));
            return;
        }
        $this->get(DispatcherService::class)->redirectToRoute('app_surcharges_comments');
    }

    /**
     * @return SurchargeComment|null
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    private function getFromRoute(): ?SurchargeComment
    {
        $surchargeCommentId = filter_var($this->param, FILTER_VALIDATE_INT);
        if (!$surchargeCommentId) {
            $this->displayError(__('Erreur : ce commentaire n\'existe pas.'), Tools::makeLink('app', 'surcharges_comments'));
            exit();
        }

        $surchargeComment = $this->get(SurchargeCommentService::class)->getRepository()->find($surchargeCommentId);
        if (!$surchargeComment) {
            $this->displayError(__('Erreur : ce commentaire n\'existe pas.'), Tools::makeLink('app', 'surcharges_comments'));
            exit();
        }

        return $surchargeComment;
    }
}
