<?php
namespace MatGyver\Controllers\App\Chat;

use MatG<PERSON>ver\Components\Chat\ChatRoomComponent;
use MatGyver\Controllers\App\AbstractAppController;
use MatGyver\Entity\Chat\ChatRoom;
use MatGyver\Entity\Chat\ChatUser;
use MatG<PERSON>ver\FormsFactory\Chat\ChatRoomFormFactory;
use MatG<PERSON>ver\FormsFactory\FormFactory;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Aws\S3\MediasService;
use MatGyver\Services\Chat\ChatMessageService;
use MatGyver\Services\Chat\ChatRoomService;
use MatGyver\Services\Chat\ChatUserService;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\Users\UsersService;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\Requirement\Requirement;

/**
 * Class ChatAppController
 * @package MatGyver\Controllers\App\Chat
 */
class ChatAppController extends AbstractAppController
{

    #[Route('/app/conversation/add/', name: 'app_conversation_add')]
    public function conversationAdd()
    {
        if (!CHAT_ACTIVATED) {
            $this->display404();
        }

        $this->view->setTitle(__('Création d\'un groupe'));

        $chatRoomForm = new ChatRoomFormFactory();
        if ($chatRoomForm->isSubmitted() and $chatRoomForm->isValid()) {
            $process = $chatRoomForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_conversation', ['chatId' => $process['id']]);
            }
        }
        $this->displayOutput($this->builderForm->render($chatRoomForm));
    }

    #[Route('/app/conversations/', name: 'app_conversations')]
    #[Route('/app/conversation/{chatId}/', name: 'app_conversation', requirements: ['chatId' => Requirement::POSITIVE_INT])]
    public function conversations(?int $chatId = null)
    {
        if (!CHAT_ACTIVATED) {
            $this->display404();
        }

        $this->view->setTitle(__('Discussions'));
        $this->view->addAction(__('Créer un groupe'), Tools::makeLink('app', 'conversation', 'add'));

        $output = $this->get(ChatRoomComponent::class)->renderChatRooms($chatId);
        $this->displayOutput($output);
    }

    #[Route('/app/conversation/add/{userId}/', name: 'app_chat_user_add', requirements: ['userId' => Requirement::POSITIVE_INT])]
    public function chatAdd(int $userId)
    {
        if (!CHAT_ACTIVATED) {
            $this->display404();
        }

        $user = $this->get(UsersService::class)->getUser($userId);
        if (!$user or !$user->getValidated()) {
            $this->displayError(__('Cet utilisateur n\'existe pas.'));
            return;
        }

        $currentUser = $this->get(UsersService::class)->getUser();
        if ($currentUser === $user) {
            $this->get(DispatcherService::class)->redirectToRoute('app_conversations');
            return;
        }

        $chatRoom = $this->get(ChatUserService::class)->findRoomWithTheseUsers($currentUser, $user);
        if ($chatRoom) {
            $this->get(DispatcherService::class)->redirectToRoute('app_conversation', ['chatId' => $chatRoom->getId()]);
            return;
        }

        $createChatRoom = $this->get(ChatRoomService::class)->insert($currentUser, [$user]);
        if (!$createChatRoom['valid']) {
            $this->displayError($createChatRoom['message']);
            return;
        }

        $this->get(DispatcherService::class)->redirectToRoute('app_conversation', ['chatId' => $createChatRoom['id']]);
    }

    #[Route('/app/room/close/{chatRoomId}/', name: 'app_room_close', requirements: ['chatRoomId' => Requirement::POSITIVE_INT])]
    public function chatClose(int $chatRoomId)
    {
        if (!CHAT_ACTIVATED) {
            $this->display404();
        }

        $this->view->setTitle(__('Discussion'));
        $this->view->addBreadcrumbItem(__('Discussions'), Tools::makeLink('app', 'conversations'));

        $chatRoom = $this->getFromRoute($chatRoomId);
        $chatUser = $chatRoom->findUser();
        if ($chatUser->getRole() !== ChatUser::ROLE_ADMIN) {
            $this->displayError(__('Vous n\'avez pas les autorisations nécessaires pour fermer cette discussion.'));
            return;
        }

        $chatRoom->setActive(false);
        try {
            $this->get(ChatRoomService::class)->persistAndFlush($chatRoom);
        } catch (\Exception $e) {
            $this->displayError(__('Impossible de fermer cette discussion.'));
            return;
        }

        $this->get(DispatcherService::class)->redirectToRoute('app_conversation', ['chatId' => $chatRoom->getId()]);
    }

    #[Route('/app/room/leave/{chatRoomId}/', name: 'app_room_leave', requirements: ['chatRoomId' => Requirement::POSITIVE_INT])]
    public function chatLeave(int $chatRoomId)
    {
        if (!CHAT_ACTIVATED) {
            $this->display404();
        }

        $this->view->setTitle(__('Discussion'));
        $this->view->addBreadcrumbItem(__('Discussions'), Tools::makeLink('app', 'conversations'));

        $chatRoom = $this->getFromRoute($chatRoomId);
        $chatUser = $chatRoom->findUser();

        $chatRoom->removeUser($chatUser);
        try {
            $this->get(ChatRoomService::class)->persistAndFlush($chatRoom);
        } catch (\Exception $e) {
            $this->displayError(__('Impossible de fermer cette discussion.'));
            return;
        }

        $this->get(DispatcherService::class)->redirectToRoute('app_conversations');
    }

    #[Route('/app/room/delete/{chatRoomId}/', name: 'app_room_delete', requirements: ['chatRoomId' => Requirement::POSITIVE_INT])]
    public function chatDelete(int $chatRoomId)
    {
        if (!CHAT_ACTIVATED) {
            $this->display404();
        }

        $chatRoom = $this->getFromRoute($chatRoomId);
        $chatUser = $chatRoom->findUser();
        if ($chatUser->getRole() !== ChatUser::ROLE_ADMIN) {
            $this->displayError(__('Vous n\'avez pas les autorisations nécessaires pour supprimer cette discussion.'));
            return;
        }

        try {
            $this->get(ChatRoomService::class)->deleteAndFlush($chatRoom);
        } catch (\Exception $e) {
            $this->displayError(__('Impossible de supprimer cette discussion.'));
            return;
        }

        $this->get(DispatcherService::class)->redirectToRoute('app_conversations');
    }

    #[Route('/app/conversation/document/download/{chatMessageId}/', name: 'app_conversation_document_download', requirements: ['chatMessageId' => Requirement::POSITIVE_INT])]
    public function chatDocumentDownload(int $chatMessageId)
    {
        if (!CHAT_ACTIVATED) {
            $this->display404();
        }

        $chatMessage = $this->get(ChatMessageService::class)->getRepository()->findWoClient($chatMessageId);
        if (!$chatMessage) {
            $this->displayError(__('Ce message n\'existe pas'), Tools::makeLink('app', 'conversations'));
            return;
        }

        $chatRoomId = $chatMessage->getRoom()->getId();
        $chatRoom = $this->get(ChatRoomService::class)->getChatRoom($chatRoomId);
        if (!$chatRoom) {
            $this->displayError(__('Ce message n\'existe pas'), Tools::makeLink('app', 'conversations'));
            return;
        }

        if (FILE_SYSTEM === FILE_SYSTEM_FILES) {
            $link = $chatMessage->getContent();
            $link .= '?chatMessage=' . $chatMessageId;
            header('Location: ' . $link);
            exit();
        }

        $mediasService = new MediasService();
        $link = $mediasService->getPreSignedLink($chatMessage->getContent());
        header('Location: ' . $link);
        exit();
    }

    /**
     * @param int $chatRoomId
     * @return ChatRoom|null
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    private function getFromRoute(int $chatRoomId): ?ChatRoom
    {
        $chatRoom = $this->get(ChatRoomService::class)->getChatRoom($chatRoomId);
        if (!$chatRoom) {
            $this->displayError(__('Erreur : cette discussion n\'existe pas.'), Tools::makeLink('app', 'conversations'));
            exit();
        }

        return $chatRoom;
    }
}
