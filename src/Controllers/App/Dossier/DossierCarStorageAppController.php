<?php
namespace Mat<PERSON>yver\Controllers\App\Dossier;

use Mat<PERSON>yver\Entity\Dossier\DossierDocument;
use MatGyver\Factories\BlankStates\DocumentWordBlankState;
use MatGyver\FormsFactory\Dossier\Expertise\DossierExpertiseCarStorageCourrierFormFactory;
use MatGyver\FormsFactory\FormFactory;
use MatGyver\Helpers\Tools;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\Dossier\DossierDocumentService;
use MatGyver\Services\Dossier\Word\DossierWordCarStorageCourrierService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class DossierCarStorageAppController
 * @package MatGyver\Controllers\App\Dossier
 */
class DossierCarStorageAppController extends AbstractDossierAppController
{
    #[Route('/app/dossier/car_storage_courrier/add/{dossierId}/', name: 'app_dossier_car_storage_courrier_add')]
    public function dossierCarStorageCourrierAdd()
    {
        $this->view->setTitle(__('Création d\'un courrier de frais de gardiennage'));
        $dossier = $this->getDossier();
        $this->view->addBreadcrumbItem(__('Courriers de frais de gardiennage'), Tools::makeLink('app', 'dossier', 'car_storage_courriers/' . $dossier->getId()));

        $dossierCarStorageCourrierForm = new DossierExpertiseCarStorageCourrierFormFactory($dossier->getExpertise());
        if ($dossierCarStorageCourrierForm->isSubmitted() and $dossierCarStorageCourrierForm->isValid()) {
            $process = $dossierCarStorageCourrierForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                header('Location: ' . Tools::makeLink('app', 'dossier', 'car_storage_courrier/' . $process['id'] . '/' . $dossier->getId()));
                exit();
            }
        }
        $this->displayOutput($this->builderForm->render($dossierCarStorageCourrierForm));
    }

    #[Route('/app/dossier/car_storage_courrier/edit/{courrierId}/{dossierId}/', name: 'app_dossier_car_storage_courrier_edit')]
    public function dossierCarStorageCourrierEdit()
    {
        $this->view->setTitle(__('Modification d\'un courrier de frais de gardiennage'));
        $dossier = $this->getDossier();
        $this->view->addBreadcrumbItem(__('Courriers de frais de gardiennage'), Tools::makeLink('app', 'dossier', 'car_storage_courriers/' . $dossier->getId()));

        $params = explode('/', $this->param);
        $documentId = filter_var($params[0], FILTER_VALIDATE_INT);
        $document = $this->get(DossierDocumentService::class)->getRepository()->findOneBy(['id' => $documentId, 'dossier' => $dossier, 'type' => DossierDocument::TYPE_COURRIER_CAR_STORAGE]);
        if (!$document) {
            $this->displayError(__('Ce courrier de frais de gardiennage n\'existe pas.'), Tools::makeLink('app', 'dossier', 'car_storage_courriers/' . $dossier->getId()), __('Retour aux courriers de frais de gardiennage'));
            return;
        }

        $dossierCarStorageCourrierForm = new DossierExpertiseCarStorageCourrierFormFactory($dossier->getExpertise());
        if ($dossierCarStorageCourrierForm->isSubmitted() and $dossierCarStorageCourrierForm->isValid()) {
            $process = $dossierCarStorageCourrierForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                header('Location: ' . Tools::makeLink('app', 'dossier', 'car_storage_courrier/' . $document->getId() . '/' . $dossier->getId()));
                exit();
            }
        }
        $this->displayOutput($this->builderForm->render($dossierCarStorageCourrierForm, $document));
    }

    #[Route('/app/dossier/car_storage_courrier/word/{courrierId}/{dossierId}/', name: 'app_dossier_car_storage_courrier_word')]
    public function dossierCarStorageCourrierWord()
    {
        $document = $this->getFromRoute();

        if ($document->getClient()->getFreemium()) {
            $factory = new DocumentWordBlankState();
            $this->displayOutput($factory->render());
            return;
        }

        try {
            $this->get(DossierWordCarStorageCourrierService::class)->generate($document);
        } catch (\Exception $e) {
            $this->displayErrorAndRedirect(__('Une erreur est survenue lors de la génération du document'), Tools::makeLink('app', 'dossier', 'car_storage_courrier/' . $document->getId() . '/' . $document->getDossier()->getId()));
            return;
        }
    }

    #[Route('/app/dossier/car_storage_courrier/delete/{id}/', name: 'app_dossier_car_storage_courrier_delete')]
    public function dossierCarStorageCourrierDelete()
    {
        $document = $this->getFromRoute();
        $dossier = $document->getDossier();
        $delete = $this->get(DossierDocumentService::class)->delete($document);
        if (!$delete['valid']) {
            $this->displayError($delete['message'], Tools::makeLink('app', 'dossier', 'car_storage_courriers/' . $dossier->getId()));
            return;
        }

        $this->get(DispatcherService::class)->redirectToRoute('app_dossier_surcharges', ['dossierId' => $dossier->getId()]);
    }

    #[Route('/app/dossier/car_storage_courrier/{id}/{dossierId}/', name: 'app_dossier_car_storage_courrier_view')]
    public function dossierCarStorageCourrier()
    {
        $this->view->setTitle(__('Courrier de frais de gardiennage'));

        $dossier = $this->getDossier();
        $document = $this->getFromRoute();
        $this->view->addBreadcrumbItem(__('Courriers de frais de gardiennage'), Tools::makeLink('app', 'dossier', 'surcharges/' . $dossier->getId()));

        $output = $this->parser->set('dossierDocument', $document)
            ->render('app/dossier/car_storage_courrier.php');
        $this->displayOutput($output);
    }

    /**
     * @return DossierDocument|null
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    private function getFromRoute(): ?DossierDocument
    {
        $params = explode('/', $this->param);
        $dossierDocumentId = filter_var($params[0], FILTER_VALIDATE_INT);
        if (!$dossierDocumentId) {
            $this->displayError(__('Erreur : ce document n\'existe pas.'), Tools::makeLink('app', 'dossiers'));
            exit();
        }

        $dossierDocument = $this->get(DossierDocumentService::class)->getRepository()->find($dossierDocumentId);
        if (!$dossierDocument) {
            $this->displayError(__('Erreur : ce document n\'existe pas.'), Tools::makeLink('app', 'dossiers'));
            exit();
        }

        return $dossierDocument;
    }
}
