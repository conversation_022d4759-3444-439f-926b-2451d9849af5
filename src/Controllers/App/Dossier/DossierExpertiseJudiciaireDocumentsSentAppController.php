<?php
namespace MatGyver\Controllers\App\Dossier;

use MatG<PERSON>ver\Entity\Dossier\Dossier;
use Mat<PERSON><PERSON>ver\Entity\Dossier\Expertise\DossierExpertise;
use MatGyver\Entity\Dossier\Expertise\DossierExpertiseJudiciaireChronology;
use MatGyver\Factories\BlankStates\EmptyBlankState;
use MatGyver\FormsFactory\Dossier\Expertise\DossierExpertiseJudiciaireDocumentsSentFormFactory;
use MatGyver\FormsFactory\FormFactory;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Action;
use MatGyver\Helpers\View\Card\Card;
use MatGyver\Helpers\View\Table\Dossier\Expertise\DossierExpertiseJudiciaireDocumentsSentViewHelper;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\Dossier\DossierExpertiseService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseJudiciaireChronologyService;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\Requirement\Requirement;

class DossierExpertiseJudiciaireDocumentsSentAppController extends AbstractDossierAppController
{
    #[Route('/app/dossier/expertise_judiciaire/documents_sent/{dossierId}/', name: 'app_dossier_expertise_judiciaire_documents_sent')]
    public function dossierExpertiseJudiciairesDocumentsSent()
    {
        $this->view->setTitle(__('Documents communiqués aux parties'));

        $dossier = $this->getDossier();
        $expertise = $this->getExpertise($dossier);

        $dossierExpertiseJudiciaireChronology = $this->get(DossierExpertiseJudiciaireChronologyService::class)->getRepository()->findBy(['expertise' => $expertise, 'type' => DossierExpertiseJudiciaireChronology::TYPE_DOCUMENT_SENT]);
        if (!$dossierExpertiseJudiciaireChronology) {
            $factory = new EmptyBlankState(
                __('Documents communiqués aux parties'),
                __('Aucun document enregistré.'),
                Tools::makeLink('app', 'dossier', 'expertise_judiciaire/documents_sent/add/' . $dossier->getId(), 'expertise_id=' . $expertise->getId()),
                __('Ajouter un document')
            );
            $content = $factory->render();
        } else {
            $this->view->set('hide_left_menu', true);
            $this->parser->set('hideLeftMenu', true);

            $dossierExpertiseJudiciaireChronology = DossierExpertiseJudiciaireChronologyService::sort($dossierExpertiseJudiciaireChronology);

            $content = $this->get(DossierExpertiseJudiciaireDocumentsSentViewHelper::class)->getContent($dossierExpertiseJudiciaireChronology, $expertise, $dossier);
        }

        Assets::addJs('app/dossier/chronology.js');
        Assets::addInlineJs($this->get(DossierExpertiseJudiciaireChronologyService::class)->getModalMove($expertise));

        $output = $this->parser->set('dossier', $dossier)
            ->set('expertise', $expertise)
            ->set('expertises', $dossier->getExpertises())
            ->set('content', $content)
            ->render('app/dossier/chronology/documents_sent.php');
        $this->displayOutput($output);
    }

    #[Route('/app/dossier/expertise_judiciaire/documents_sent/add/{dossierId}/', name: 'app_dossier_expertise_judiciaire_documents_sent_add')]
    public function dossierExpertiseJudiciaireDocumentsSentAdd()
    {
        $this->view->setTitle(__('Création d\'un document'));

        $dossier = $this->getDossier();
        $expertise = $this->getExpertise($dossier);

        $this->view->addBreadcrumbItem(__('Documents communiqués aux parties'), Tools::makeLink('app', 'dossier', 'expertise_judiciaire/documents_sent/' . $dossier->getId()));

        $dossierExpertiseJudiciaireDocumentsSentForm = new DossierExpertiseJudiciaireDocumentsSentFormFactory($expertise, $dossier);
        if ($dossierExpertiseJudiciaireDocumentsSentForm->isSubmitted() and $dossierExpertiseJudiciaireDocumentsSentForm->isValid()) {
            $process = $dossierExpertiseJudiciaireDocumentsSentForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                header('Location: ' . Tools::makeLink('app', 'dossier', 'expertise_judiciaire/documents_sent/' . $dossier->getId(), 'expertise_id=' . $expertise->getId()));
                exit();
            }
        }
        $this->displayOutput($this->builderForm->render($dossierExpertiseJudiciaireDocumentsSentForm));
    }

    #[Route('/app/dossier/expertise_judiciaire/documents_sent/edit/{documentId}/{dossierId}/', name: 'app_dossier_expertise_judiciaire_documents_sent_edit', requirements: ['documentId' => Requirement::POSITIVE_INT, 'dossierId' => Requirement::POSITIVE_INT])]
    public function dossierExpertiseJudiciaireDocumentsSentEdit(int $documentId, int $dossierId)
    {
        $this->view->setTitle(__('Modification d\'un document'));

        $dossier = $this->getDossier();
        $this->view->addBreadcrumbItem(__('Documents communiqués aux parties'), Tools::makeLink('app', 'dossier', 'expertise_judiciaire/documents_sent/' . $dossier->getId()));

        $dossierExpertiseJudiciaireChronology = $this->getFromRoute($documentId, $dossier);
        $expertise = $dossierExpertiseJudiciaireChronology->getExpertise();

        $dossierExpertiseJudiciaireDocumentsSentForm = new DossierExpertiseJudiciaireDocumentsSentFormFactory($expertise, $dossier);
        if ($dossierExpertiseJudiciaireDocumentsSentForm->isSubmitted() and $dossierExpertiseJudiciaireDocumentsSentForm->isValid()) {
            $process = $dossierExpertiseJudiciaireDocumentsSentForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                header('Location: ' . Tools::makeLink('app', 'dossier', 'expertise_judiciaire/documents_sent/' . $dossier->getId(), 'expertise_id=' . $expertise->getId()));
                exit();
            }
        }
        $output = $this->builderForm->render($dossierExpertiseJudiciaireDocumentsSentForm, $dossierExpertiseJudiciaireChronology);
        $this->displayOutput($output);
    }

    #[Route('/app/dossier/expertise_judiciaire/documents_sent/delete/{documentId}/', name: 'app_dossier_expertise_judiciaire_documents_sent_delete', requirements: ['documentId' => Requirement::POSITIVE_INT])]
    public function dossierExpertiseJudiciaireDocumentsSentDelete(int $documentId)
    {
        $dossierExpertiseJudiciaireChronology = $this->get(DossierExpertiseJudiciaireChronologyService::class)->getRepository()->find($documentId);
        if (!$dossierExpertiseJudiciaireChronology) {
            $this->displayError(__('Erreur : ce document n\'existe pas.'), Tools::makeLink('app', 'dossiers'));
            exit();
        }

        $dossier = $dossierExpertiseJudiciaireChronology->getExpertise()->getDossier();
        $expertise = $dossierExpertiseJudiciaireChronology->getExpertise();
        try {
            $this->get(DossierExpertiseJudiciaireChronologyService::class)->deleteAndFlush($dossierExpertiseJudiciaireChronology);
        } catch (\Exception $e) {
            $this->displayError(__('Impossible de supprimer ce document.'));
            return;
        }

        header('Location: ' . Tools::makeLink('app', 'dossier', 'expertise_judiciaire/documents_sent/' . $dossier->getId(), 'expertise_id=' . $expertise->getId()));
        exit();
    }

    /**
     * @param int $dossierExpertiseJudiciaireChronologyId
     * @param Dossier $dossier
     * @return DossierExpertiseJudiciaireChronology|null
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    private function getFromRoute(int $dossierExpertiseJudiciaireChronologyId, Dossier $dossier): ?DossierExpertiseJudiciaireChronology
    {
        $dossierExpertiseJudiciaireChronology = $this->get(DossierExpertiseJudiciaireChronologyService::class)->getRepository()->find($dossierExpertiseJudiciaireChronologyId);
        if (!$dossierExpertiseJudiciaireChronology) {
            $this->displayError(__('Erreur : ce document n\'existe pas.'), Tools::makeLink('app', 'dossier', 'expertise_judiciaire/documents_sent/' . $dossier->getId()));
            exit();
        }

        return $dossierExpertiseJudiciaireChronology;
    }

    /**
     * @param Dossier $dossier
     * @return DossierExpertise|null
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    private function getExpertise(Dossier $dossier): ?DossierExpertise
    {
        if (isset($_GET['expertise_id'])) {
            $expertiseId = filter_input(INPUT_GET, 'expertise_id', FILTER_VALIDATE_INT);
            $expertise = $this->get(DossierExpertiseService::class)->getRepository()->findOneBy(['id' => $expertiseId, 'dossier' => $dossier]);
            if (!$expertise) {
                $this->displayError(__('Cette expertise n\'existe pas.'), Tools::makeLink('app', 'dossier', 'convocations/' . $dossier->getId()), __('Retour'));
                exit();
            }
            return $expertise;
        }

        $expertise = $dossier->getLastExpertise();
        if (!$expertise) {
            $this->displayError(__('Aucune expertise enregistrée'), Tools::makeLink('app', 'dossier', 'expertise/add/' . $dossier->getId()), __('Créer une expertise'));
            exit();
        }

        return $expertise;
    }
}
