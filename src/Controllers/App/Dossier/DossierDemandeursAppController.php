<?php
namespace MatGyver\Controllers\App\Dossier;

use Mat<PERSON>yver\Attributes\View;
use Mat<PERSON>yver\Entity\Dossier\DossierInstitution;
use MatGyver\FormsFactory\Dossier\DossierInstitutionFormFactory;
use Mat<PERSON>yver\FormsFactory\FormFactory;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Action;
use MatGyver\Helpers\View\Card\Card;
use MatGyver\Helpers\View\CardRow\Dossier\DossierInstitutionsViewHelper;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\Dossier\DossierInstitutionService;
use MatGyver\Services\Dossier\Expertise\DossierExpertisePersonService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class DossierDemandeursAppController
 * @package MatGyver\Controllers\App\Dossier
 */
class DossierDemandeursAppController extends AbstractDossierAppController
{

    #[Route('/app/dossier/demandeurs/{dossierId}/', name: 'app_dossier_demandeurs')]
    public function dossierDemandeurs()
    {
        $this->view->setTitle(__('Demandeurs'));

        $dossier = $this->getDossier();
        $this->get(DossierInstitutionService::class)->setInstitutions($dossier);

        $dossierInstitutions = $this->get(DossierInstitutionService::class)->getRepository()->findBy(['dossier' => $dossier, 'parent' => null, 'demandeur' => true], ['defendantNumber' => 'ASC', 'id' => 'ASC']);
        if (!$dossierInstitutions) {
            $dossierInstitutions = $this->get(DossierInstitutionService::class)->getRepository()->findBy(['dossier' => $dossier, 'parent' => null, 'type' => DossierInstitution::TYPE_CONTACT], ['id' => 'ASC']);
        }
        if (!$dossierInstitutions) {
            $this->displayEmpty(__('Demandeurs'), __('Aucun demandeur enregistré.'), Tools::makeLink('app', 'dossier', 'demandeur/add/' . $dossier->getId()), __('Ajouter un demandeur'));
            return;
        }

        $card = new Card();
        $card->setBody($this->get(DossierInstitutionsViewHelper::class)->getContent($dossierInstitutions, true));
        $card->setTitle(__('Demandeurs'));
        $action = new Action();
        $action->setTitle(__('Ajouter'));
        $action->setClass('btn-primary');
        $action->setHref(Tools::makeLink('app', 'dossier', 'demandeur/add/' . $dossier->getId()));
        $card->addAction($action);
        $output = $card->getContent();

        $nextStepLink = Tools::makeLink('app', 'dossier', 'vehicle/owner/' . $dossier->getId());
        $output .= $this->displayBtnNextStep($nextStepLink);
        $this->displayOutput($output);
    }

    #[Route('/app/dossier/demandeur/add/{dossierId}/', name: 'app_dossier_demandeur_add')]
    #[View('MatGyver\Services\Views\ViewAppDossier')]
    public function dossierDemandeurAdd()
    {
        $this->view->setTitle(__('Création d\'un demandeur'));

        $dossier = $this->getDossier();
        $this->view->addBreadcrumbItem(__('Demandeurs'), Tools::makeLink('app', 'dossier', 'demandeurs/' . $dossier->getId()));

        $dossierInstitutionForm = new DossierInstitutionFormFactory($dossier, true);
        if ($dossierInstitutionForm->isSubmitted() and $dossierInstitutionForm->isValid()) {
            $process = $dossierInstitutionForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_dossier_demandeurs', ['dossierId' => $dossier->getId()]);
            }
        }
        $this->displayOutput($this->builderForm->render($dossierInstitutionForm));
    }

    #[Route('/app/dossier/demandeur/edit/{id}/{dossierId}/', name: 'app_dossier_demandeur_edit')]
    #[View('MatGyver\Services\Views\ViewAppDossier')]
    public function dossierDemandeurEdit()
    {
        $this->view->setTitle(__('Modification d\'un demandeur'));

        $dossier = $this->getDossier();
        $this->view->addBreadcrumbItem(__('Demandeurs'), Tools::makeLink('app', 'dossier', 'demandeurs/' . $dossier->getId()));

        $dossierInstitution = $this->getFromRoute();
        $dossierInstitutionForm = new DossierInstitutionFormFactory($dossier, true);
        if ($dossierInstitutionForm->isSubmitted() and $dossierInstitutionForm->isValid()) {
            $process = $dossierInstitutionForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_dossier_demandeurs', ['dossierId' => $dossier->getId()]);
            }
        }
        $output = $this->builderForm->render($dossierInstitutionForm, $dossierInstitution);
        $this->displayOutput($output);
    }

    #[Route('/app/dossier/demandeur/delete/{id}/', name: 'app_dossier_demandeur_delete')]
    public function dossierDemandeurDelete()
    {
        $dossierInstitution = $this->getFromRoute();
        if ($dossierInstitution->getLocked()) {
            $this->displayError(__('Impossible de supprimer ce demandeur.'));
            return;
        }

        $dossier = $dossierInstitution->getDossier();
        try {
            $this->get(DossierExpertisePersonService::class)->removeInstitution($dossierInstitution);
            $this->get(DossierInstitutionService::class)->deleteAndFlush($dossierInstitution);
        } catch (\Exception $e) {
            $this->displayError(__('Impossible de supprimer ce demandeur.'));
            return;
        }
        $this->get(DispatcherService::class)->redirectToRoute('app_dossier_demandeurs', ['dossierId' => $dossier->getId()]);
    }

    /**
     * @return DossierInstitution|null
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    private function getFromRoute(): ?DossierInstitution
    {
        $params = explode('/', $this->param);
        $dossierInstitutionId = filter_var($params[0], FILTER_VALIDATE_INT);
        if (!$dossierInstitutionId) {
            $this->displayError(__('Erreur : ce demandeur n\'existe pas.'), Tools::makeLink('app', 'dossiers'));
            exit();
        }

        $dossierInstitution = $this->get(DossierInstitutionService::class)->getRepository()->find($dossierInstitutionId);
        if (!$dossierInstitution) {
            $this->displayError(__('Erreur : ce demandeur n\'existe pas.'), Tools::makeLink('app', 'dossiers'));
            exit();
        }

        return $dossierInstitution;
    }
}
