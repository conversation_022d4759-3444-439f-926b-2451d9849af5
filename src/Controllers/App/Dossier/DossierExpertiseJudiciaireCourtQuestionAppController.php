<?php
namespace Mat<PERSON>yver\Controllers\App\Dossier;

use Mat<PERSON><PERSON>ver\Entity\Dossier\Expertise\DossierExpertise;
use MatGyver\Entity\Dossier\Expertise\DossierExpertiseJudiciaireCourtQuestion;
use Mat<PERSON><PERSON>ver\FormsFactory\FormFactory;
use Mat<PERSON>yver\FormsFactory\Dossier\Expertise\DossierExpertiseJudiciaireCourtQuestionFormFactory;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Action;
use MatGyver\Helpers\View\Card\Card;
use MatGyver\Helpers\View\Table\Dossier\Expertise\DossierExpertiseJudiciaireCourtQuestionViewHelper;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseJudiciaireCourtQuestionService;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\Requirement\Requirement;

class DossierExpertiseJudiciaireCourtQuestionAppController extends AbstractDossierAppController
{
    #[Route('/app/dossier/court_questions/{dossierId}/', name: 'app_dossier_court_questions')]
    public function dossierCourtQuestions()
    {
        $this->view->setTitle(__('Questions du Tribunal'));

        $dossier = $this->getDossier();
        $expertise = $dossier->getExpertise();

        $dossierCourtQuestions = $this->get(DossierExpertiseJudiciaireCourtQuestionService::class)->getRepository()->findBy(['expertise' => $expertise], ['position' => 'ASC']);
        if (!$dossierCourtQuestions) {
            $this->displayEmpty(__('Questions du Tribunal'), __('Aucune question enregistrée.'), Tools::makeLink('app', 'dossier', 'court_question/add/' . $dossier->getId()), __('Ajouter une question'));
            return;
        }

        $content = $this->get(DossierExpertiseJudiciaireCourtQuestionViewHelper::class)->getContent($dossierCourtQuestions, true);

        $card = new Card();
        $card->setTitle(__('Questions du Tribunal'));
        $card->setBody($content);
        $action = new Action();
        $action->setTitle(__('Ajouter une question'));
        $action->setClass('btn-primary');
        $action->setHref(Tools::makeLink('app', 'dossier', 'court_question/add/' . $dossier->getId()));
        $card->addAction($action);
        $content = $card->getContent();
        $content .= $this->displayBtnNextStep(Tools::makeLink('app', 'dossier', 'expertise_judiciaire/' . $dossier->getId()));
        $this->displayOutput($content);
    }

    #[Route('/app/dossier/court_question/add/{dossierId}/', name: 'app_dossier_court_question_add')]
    public function dossierCourtQuestionAdd()
    {
        $this->view->setTitle(__('Création d\'une question'));

        $dossier = $this->getDossier();
        $expertise = $dossier->getExpertise();
        $this->view->addBreadcrumbItem(__('Questions du Tribunal'), Tools::makeLink('app', 'dossier', 'court_questions/' . $dossier->getId()));

        $dossierExpertiseJudiciaireCourtQuestionForm = new DossierExpertiseJudiciaireCourtQuestionFormFactory($expertise);
        if ($dossierExpertiseJudiciaireCourtQuestionForm->isSubmitted() and $dossierExpertiseJudiciaireCourtQuestionForm->isValid()) {
            $process = $dossierExpertiseJudiciaireCourtQuestionForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_dossier_court_questions', ['dossierId' => $dossier->getId()]);
            }
        }
        $this->displayOutput($this->builderForm->render($dossierExpertiseJudiciaireCourtQuestionForm));
    }

    #[Route('/app/dossier/court_question/edit/{courtQuestionId}/{dossierId}/', name: 'app_dossier_court_question_edit', requirements: ['courtQuestionId' => Requirement::POSITIVE_INT, 'dossierId' => Requirement::POSITIVE_INT])]
    public function dossierCourtQuestionEdit(int $courtQuestionId, int $dossierId)
    {
        $this->view->setTitle(__('Modification d\'une question'));

        $dossier = $this->getDossier();
        $expertise = $dossier->getExpertise();
        $this->view->addBreadcrumbItem(__('Questions du Tribunal'), Tools::makeLink('app', 'dossier', 'court_questions/' . $dossierId));

        $dossierExpertiseJudiciaireCourtQuestion = $this->getFromRoute($courtQuestionId, $expertise);

        $dossierExpertiseJudiciaireCourtQuestionForm = new DossierExpertiseJudiciaireCourtQuestionFormFactory($expertise);
        if ($dossierExpertiseJudiciaireCourtQuestionForm->isSubmitted() and $dossierExpertiseJudiciaireCourtQuestionForm->isValid()) {
            $process = $dossierExpertiseJudiciaireCourtQuestionForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_dossier_court_questions', ['dossierId' => $dossierId]);
            }
        }
        $output = $this->builderForm->render($dossierExpertiseJudiciaireCourtQuestionForm, $dossierExpertiseJudiciaireCourtQuestion);
        $this->displayOutput($output);
    }

    #[Route('/app/dossier/court_question/delete/{courtQuestionId}/', name: 'app_dossier_court_question_delete', requirements: ['courtQuestionId' => Requirement::POSITIVE_INT])]
    public function dossierCourtQuestionDelete(int $courtQuestionId)
    {
        $dossierCourtQuestion = $this->get(DossierExpertiseJudiciaireCourtQuestionService::class)->getRepository()->find($courtQuestionId);
        if (!$dossierCourtQuestion) {
            $this->displayError(__('Erreur : cette question n\'existe pas.'), Tools::makeLink('app', 'dossiers'));
            exit();
        }

        $dossier = $dossierCourtQuestion->getExpertise()->getDossier();
        try {
            $this->get(DossierExpertiseJudiciaireCourtQuestionService::class)->deleteAndFlush($dossierCourtQuestion);
        } catch (\Exception $e) {
            $this->displayError(__('Impossible de supprimer cette question.'));
            return;
        }
        $this->get(DispatcherService::class)->redirectToRoute('app_dossier_court_questions', ['dossierId' => $dossier->getId()]);
    }

    /**
     * @param int $dossierExpertiseJudiciaireCourtQuestionId
     * @param DossierExpertise $expertise
     * @return DossierExpertiseJudiciaireCourtQuestion|null
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    private function getFromRoute(int $dossierExpertiseJudiciaireCourtQuestionId, DossierExpertise $expertise): ?DossierExpertiseJudiciaireCourtQuestion
    {
        $dossierExpertiseJudiciaireCourtQuestion = $this->get(DossierExpertiseJudiciaireCourtQuestionService::class)->getRepository()->find($dossierExpertiseJudiciaireCourtQuestionId);
        if (!$dossierExpertiseJudiciaireCourtQuestion) {
            $this->displayError(__('Erreur : cette question n\'existe pas.'), Tools::makeLink('app', 'dossier', 'court_questions/' . $expertise->getDossier()->getId()));
            exit();
        }

        return $dossierExpertiseJudiciaireCourtQuestion;
    }
}
