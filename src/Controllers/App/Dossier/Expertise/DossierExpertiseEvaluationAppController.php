<?php
namespace MatGyver\Controllers\App\Dossier\Expertise;

use MatGyver\FormsFactory\Dossier\Expertise\DossierExpertiseEvaluationFormFactory;
use MatGyver\FormsFactory\FormFactory;
use MatGyver\Services\DispatcherService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class DossierExpertiseEvaluationAppController
 * @package MatGyver\Controllers\App\Dossier\Expertise
 */
class DossierExpertiseEvaluationAppController extends AbstractDossierExpertiseAppController
{
    #[Route('/app/dossier/expertise/evaluation/{expertiseId}/', name: 'app_dossier_expertise_evaluation')]
    public function dossierExpertiseEvaluation()
    {
        $this->view->setTitle(__('Évaluation'));

        $expertise = $this->getExpertise();

        $dossierExpertiseEvaluationForm = new DossierExpertiseEvaluationFormFactory($expertise->getDossier());
        if ($dossierExpertiseEvaluationForm->isSubmitted() and $dossierExpertiseEvaluationForm->isValid()) {
            $process = $dossierExpertiseEvaluationForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_dossier_expertise_notes', ['expertiseId' => $expertise->getId()]);
            }
        }
        $output = $this->builderForm->render($dossierExpertiseEvaluationForm, $expertise);
        $this->displayOutput($output);
    }

    #[Route('/app/dossier/expertise/report/evaluation/{expertiseId}/', name: 'app_dossier_expertise_report_evaluation')]
    public function dossierExpertiseReportEvaluation()
    {
        $this->view->setTitle(__('Évaluation'));

        $expertise = $this->getExpertise();

        $dossierExpertiseEvaluationForm = new DossierExpertiseEvaluationFormFactory($expertise->getDossier());
        if ($dossierExpertiseEvaluationForm->isSubmitted() and $dossierExpertiseEvaluationForm->isValid()) {
            $process = $dossierExpertiseEvaluationForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_dossier_expertise_report_notes', ['expertiseId' => $expertise->getId()]);
            }
        }
        $output = $this->builderForm->render($dossierExpertiseEvaluationForm, $expertise);
        $this->displayOutput($output);
    }

    #[Route('/app/dossier/expertise/judiciaires/evaluation/{expertiseId}/', name: 'app_dossier_expertise_judiciaires_evaluation')]
    public function dossierExpertiseJudiciairesEvaluation()
    {
        $this->view->setTitle(__('Méthodologie - Chiffrage'));

        $expertise = $this->getExpertise();

        $dossierExpertiseEvaluationForm = new DossierExpertiseEvaluationFormFactory($expertise->getDossier());
        if ($dossierExpertiseEvaluationForm->isSubmitted() and $dossierExpertiseEvaluationForm->isValid()) {
            $process = $dossierExpertiseEvaluationForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_dossier_expertise_judiciaires_notes', ['expertiseId' => $expertise->getId()]);
            }
        }
        $output = $this->builderForm->render($dossierExpertiseEvaluationForm, $expertise);
        $this->displayOutput($output);
    }
}
