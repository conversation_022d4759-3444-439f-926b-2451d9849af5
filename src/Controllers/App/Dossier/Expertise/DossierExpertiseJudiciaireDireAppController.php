<?php
namespace MatGyver\Controllers\App\Dossier\Expertise;

use MatGyver\Entity\Dossier\Expertise\DossierExpertise;
use MatGyver\Entity\Dossier\Expertise\DossierExpertiseJudiciaireDire;
use MatGyver\Factories\BlankStates\EmptyBlankState;
use MatGyver\FormsFactory\FormFactory;
use MatGyver\FormsFactory\Dossier\Expertise\DossierExpertiseJudiciaireDireFormFactory;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Action;
use MatGyver\Helpers\View\Card\Card;
use MatGyver\Helpers\View\Table\Dossier\Expertise\DossierExpertiseJudiciaireDireViewHelper;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseJudiciaireDireService;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\Requirement\Requirement;

class DossierExpertiseJudiciaireDireAppController extends AbstractDossierExpertiseAppController
{
    #[Route('/app/dossier/expertise/judiciaires/dires/{expertiseId}/', name: 'app_dossier_expertise_judiciaires_dires')]
    public function dossierExpertiseJudiciairesDires()
    {
        $this->view->setTitle(__('Dires'));

        $expertise = $this->getExpertise();

        $dossierExpertiseJudiciaireDires = $this->get(DossierExpertiseJudiciaireDireService::class)->getRepository()->findBy(['expertise' => $expertise, 'parent' => null], ['position' => 'ASC']);
        if (!$dossierExpertiseJudiciaireDires) {
            $factory = new EmptyBlankState(
                __('Dires'),
                __('Aucun dire enregistré.'),
                Tools::makeLink('app', 'dossier', 'expertise/judiciaires/dire/add/' . $expertise->getId()),
                __('Ajouter un dire')
            );
            $content = $factory->render();
            $content .= $this->displayBtnNextStep(Tools::makeLink('app', 'dossier', 'expertise/judiciaires/dires/answers/' . $expertise->getId()));
            $this->displayOutput($content);
            return;
        }

        $content = $this->get(DossierExpertiseJudiciaireDireViewHelper::class)->getContent($dossierExpertiseJudiciaireDires);

        $card = new Card();
        $card->setBody($content);
        $card->setTitle(__('Dires des parties'));
        $action = new Action();
        $action->setTitle(__('Ajouter un dire'));
        $action->setClass('btn-primary');
        $action->setHref(Tools::makeLink('app', 'dossier', 'expertise/judiciaires/dire/add/' . $expertise->getId()));
        $card->addAction($action);
        $content = $card->getContent();
        $content .= $this->displayBtnNextStep(Tools::makeLink('app', 'dossier', 'expertise/judiciaires/dires/answers/' . $expertise->getId()));

        $this->displayOutput($content);
    }

    #[Route('/app/dossier/expertise/judiciaires/dire/add/{expertiseId}/', name: 'app_dossier_expertise_judiciaires_dire_add')]
    public function dossierExpertiseJudiciairesDireAdd()
    {
        $this->view->setTitle(__('Création d\'un dire'));

        $expertise = $this->getExpertise();
        $this->view->addBreadcrumbItem(__('Dires'), Tools::makeLink('app', 'dossier', 'expertise/judiciaires/dires/' . $expertise->getId()));

        $dossierExpertiseJudiciaireDireForm = new DossierExpertiseJudiciaireDireFormFactory($expertise);
        if ($dossierExpertiseJudiciaireDireForm->isSubmitted() and $dossierExpertiseJudiciaireDireForm->isValid()) {
            $process = $dossierExpertiseJudiciaireDireForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_dossier_expertise_judiciaires_dires', ['expertiseId' => $expertise->getId()]);
            }
        }
        $this->displayOutput($this->builderForm->render($dossierExpertiseJudiciaireDireForm));
    }

    #[Route('/app/dossier/expertise/judiciaires/dire/edit/{direId}/{expertiseId}/', name: 'app_dossier_expertise_judiciaires_dire_edit', requirements: ['direId' => Requirement::POSITIVE_INT, 'expertiseId' => Requirement::POSITIVE_INT])]
    public function dossierExpertiseJudiciaireDireEdit(int $direId, int $expertiseId)
    {
        $this->view->setTitle(__('Modification d\'un dire'));

        $expertise = $this->getExpertise();
        $this->view->addBreadcrumbItem(__('Dires'), Tools::makeLink('app', 'dossier', 'expertise/judiciaires/dires/' . $expertise->getId()));

        $dossierExpertiseJudiciaireDire = $this->getFromRoute($direId, $expertise);

        $dossierExpertiseJudiciaireDireForm = new DossierExpertiseJudiciaireDireFormFactory($expertise);
        if ($dossierExpertiseJudiciaireDireForm->isSubmitted() and $dossierExpertiseJudiciaireDireForm->isValid()) {
            $process = $dossierExpertiseJudiciaireDireForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_dossier_expertise_judiciaires_dires', ['expertiseId' => $expertise->getId()]);
            }
        }
        $output = $this->builderForm->render($dossierExpertiseJudiciaireDireForm, $dossierExpertiseJudiciaireDire);
        $this->displayOutput($output);
    }

    #[Route('/app/dossier/expertise/judiciaires/dire/delete/{direId}/', name: 'app_dossier_expertise_judiciaires_dire_delete', requirements: ['direId' => Requirement::POSITIVE_INT])]
    public function dossierExpertiseJudiciaireDireDelete(int $direId)
    {
        $dossierExpertiseJudiciaireDire = $this->get(DossierExpertiseJudiciaireDireService::class)->getRepository()->find($direId);
        if (!$dossierExpertiseJudiciaireDire) {
            $this->displayError(__('Erreur : ce dire n\'existe pas.'), Tools::makeLink('app', 'dossiers'));
            exit();
        }

        $expertise = $dossierExpertiseJudiciaireDire->getExpertise();
        try {
            $this->get(DossierExpertiseJudiciaireDireService::class)->deleteAndFlush($dossierExpertiseJudiciaireDire);
        } catch (\Exception $e) {
            $this->displayError(__('Impossible de supprimer ce dire.'));
            return;
        }
        $this->get(DispatcherService::class)->redirectToRoute('app_dossier_expertise_judiciaires_dires', ['expertiseId' => $expertise->getId()]);
    }

    /**
     * @param int $dossierExpertiseJudiciaireDireId
     * @param DossierExpertise $expertise
     * @return DossierExpertiseJudiciaireDire|null
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    private function getFromRoute(int $dossierExpertiseJudiciaireDireId, DossierExpertise $expertise): ?DossierExpertiseJudiciaireDire
    {
        $dossierExpertiseJudiciaireDire = $this->get(DossierExpertiseJudiciaireDireService::class)->getRepository()->find($dossierExpertiseJudiciaireDireId);
        if (!$dossierExpertiseJudiciaireDire) {
            $this->displayError(__('Erreur : ce dire n\'existe pas.'), Tools::makeLink('app', 'dossier', 'expertise/judiciaires/dires/' . $expertise->getId()));
            exit();
        }

        return $dossierExpertiseJudiciaireDire;
    }
}
