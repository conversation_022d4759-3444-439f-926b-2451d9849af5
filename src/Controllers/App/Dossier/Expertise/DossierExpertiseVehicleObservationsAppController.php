<?php
namespace MatGyver\Controllers\App\Dossier\Expertise;

use MatGyver\Entity\Dossier\Expertise\DossierExpertiseVehicleObservations;
use MatGyver\FormsFactory\Dossier\Expertise\DossierExpertiseVehicleObservationsFormFactory;
use MatGyver\FormsFactory\FormFactory;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Action;
use MatGyver\Helpers\View\Card\Card;
use MatGyver\Helpers\View\Table\Dossier\Expertise\DossierExpertiseVehicleObservationsViewHelper;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseVehicleObservationsService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class DossierExpertiseVehicleObservationsAppController
 * @package MatGyver\Controllers\App\Dossier\Expertise
 */
class DossierExpertiseVehicleObservationsAppController extends AbstractDossierExpertiseAppController
{

    #[Route('/app/dossier/expertise/vehicle/observations/{expertiseId}/', name: 'app_dossier_expertise_vehicle_observations')]
    #[Route('/app/dossier/expertise/vehicle/appraised_value/observations/{expertiseId}/', name: 'app_dossier_expertise_vehicle_appraised_value_observations')]
    public function dossierExpertiseVehicleObservations()
    {
        $this->view->setTitle(__('Observations du véhicule'));

        $expertise = $this->getExpertise();

        $inAppraisedValue = false;
        $createLink = Tools::makeLink('app', 'dossier', 'expertise/vehicle/observation/add/' . $expertise->getId());
        $nextStep = Tools::makeLink('app', 'dossier', 'expertise/vehicle/evaluations/' . $expertise->getId());
        if ($this->route == 'app_dossier_expertise_vehicle_appraised_value_observations') {
            $inAppraisedValue = true;
            $createLink = Tools::makeLink('app', 'dossier', 'expertise/vehicle/appraised_value/observation/add/' . $expertise->getId());
            $nextStep = Tools::makeLink('app', 'dossier', 'expertise/vehicle/evaluations/' . $expertise->getId(), 'inAppraisedValue');
        }

        $dossierExpertiseVehicleObservations = $this->get(DossierExpertiseVehicleObservationsService::class)->getRepository()->findBy(['expertise' => $expertise], ['position' => 'ASC']);
        if (!$dossierExpertiseVehicleObservations) {
            $this->displayEmpty(__('Observations'), __('Aucune observation enregistrée.'), $createLink, __('Ajouter une observation'));
            return;
        }

        $observationsByType = DossierExpertiseVehicleObservationsService::sortObservations($dossierExpertiseVehicleObservations);

        $card = new Card();
        $card->setBody($this->get(DossierExpertiseVehicleObservationsViewHelper::class)->getContent($expertise, $observationsByType, $inAppraisedValue));
        $card->setTitle(__('Observations'));
        $action = new Action();
        $action->setTitle(__('Ajouter une observation'));
        $action->setClass('btn-primary');
        $action->setHref($createLink);
        $card->addAction($action);
        $content = $card->getContent();
        $content .= $this->displayBtnNextStep($nextStep);

        $this->displayOutput($content);
    }

    #[Route('/app/dossier/expertise/vehicle/observation/add/{expertiseId}/', name: 'app_dossier_expertise_vehicle_observation_add')]
    #[Route('/app/dossier/expertise/vehicle/appraised_value/observation/add/{expertiseId}/', name: 'app_dossier_expertise_vehicle_appraised_value_observation_add')]
    public function dossierExpertiseVehicleObservationAdd()
    {
        $this->view->setTitle(__('Création d\'une observation'));

        $expertise = $this->getExpertise();
        $this->view->addBreadcrumbItem(__('Observations'), Tools::makeLink('app', 'dossier', 'expertise/vehicle/observations/' . $expertise->getId()));

        $inAppraisedValue = ($this->route === 'app_dossier_expertise_vehicle_appraised_value_observation_add');
        $dossierExpertiseVehicleObservationsForm = new DossierExpertiseVehicleObservationsFormFactory($expertise, $inAppraisedValue);
        if ($dossierExpertiseVehicleObservationsForm->isSubmitted() and $dossierExpertiseVehicleObservationsForm->isValid()) {
            $process = $dossierExpertiseVehicleObservationsForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                if ($this->route == 'app_dossier_expertise_vehicle_appraised_value_observation_add') {
                    $this->get(DispatcherService::class)->redirectToRoute('app_dossier_expertise_vehicle_appraised_value_observations', ['expertiseId' => $expertise->getId()]);
                }
                $this->get(DispatcherService::class)->redirectToRoute('app_dossier_expertise_vehicle_observations', ['expertiseId' => $expertise->getId()]);
            }
        }
        $this->displayOutput($this->builderForm->render($dossierExpertiseVehicleObservationsForm));
    }

    #[Route('/app/dossier/expertise/vehicle/observation/edit/{id}/{expertiseId}/', name: 'app_dossier_expertise_vehicle_observation_edit')]
    #[Route('/app/dossier/expertise/vehicle/appraised_value/observation/edit/{id}/{expertiseId}/', name: 'app_dossier_expertise_vehicle_appraised_value_observation_edit')]
    public function dossierExpertiseVehicleObservationEdit()
    {
        $this->view->setTitle(__('Modification d\'une observation'));

        $expertise = $this->getExpertise();
        $this->view->addBreadcrumbItem(__('Observations'), Tools::makeLink('app', 'dossier', 'expertise/vehicle/observations/' . $expertise->getId()));

        $dossierExpertiseVehicleObservation = $this->getFromRoute();

        $dossierExpertiseVehicleObservationsForm = new DossierExpertiseVehicleObservationsFormFactory($expertise);
        if ($dossierExpertiseVehicleObservationsForm->isSubmitted() and $dossierExpertiseVehicleObservationsForm->isValid()) {
            $process = $dossierExpertiseVehicleObservationsForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                if ($this->route == 'app_dossier_expertise_vehicle_appraised_value_observation_edit') {
                    $this->get(DispatcherService::class)->redirectToRoute('app_dossier_expertise_vehicle_appraised_value_observations', ['expertiseId' => $expertise->getId()]);
                }
                $this->get(DispatcherService::class)->redirectToRoute('app_dossier_expertise_vehicle_observations', ['expertiseId' => $expertise->getId()]);
            }
        }
        $output = $this->builderForm->render($dossierExpertiseVehicleObservationsForm, $dossierExpertiseVehicleObservation);
        $this->displayOutput($output);
    }

    #[Route('/app/dossier/expertise/vehicle/observation/delete/{id}/', name: 'app_dossier_expertise_vehicle_observation_delete')]
    #[Route('/app/dossier/expertise/vehicle/appraised_value/observation/delete/{id}/', name: 'app_dossier_expertise_vehicle_appraised_value_observation_delete')]
    public function dossierExpertiseVehicleObservationDelete()
    {
        $expertiseVehicleObservation = $this->getFromRoute();
        $expertise = $expertiseVehicleObservation->getExpertise();
        try {
            $this->get(DossierExpertiseVehicleObservationsService::class)->deleteAndFlush($expertiseVehicleObservation);
        } catch (\Exception $e) {
            $this->displayError(__('Impossible de supprimer cette observation.'));
            return;
        }
        if ($this->route == 'app_dossier_expertise_vehicle_appraised_value_observation_delete') {
            $this->get(DispatcherService::class)->redirectToRoute('app_dossier_expertise_vehicle_appraised_value_observations', ['expertiseId' => $expertise->getId()]);
        }
        $this->get(DispatcherService::class)->redirectToRoute('app_dossier_expertise_vehicle_observations', ['expertiseId' => $expertise->getId()]);
    }

    /**
     * @return DossierExpertiseVehicleObservations|null
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    private function getFromRoute(): ?DossierExpertiseVehicleObservations
    {
        $params = explode('/', $this->param);
        $expertiseVehicleObservationId = filter_var($params[0], FILTER_VALIDATE_INT);

        if (!$expertiseVehicleObservationId) {
            $this->displayError(__('Erreur : cette observation n\'existe pas.'), Tools::makeLink('app', 'dossiers'));
            exit();
        }

        $expertiseVehicleObservation = $this->get(DossierExpertiseVehicleObservationsService::class)->getRepository()->find($expertiseVehicleObservationId);
        if (!$expertiseVehicleObservation) {
            $this->displayError(__('Erreur : cette observation n\'existe pas.'), Tools::makeLink('app', 'dossiers'));
            exit();
        }

        return $expertiseVehicleObservation;
    }
}
