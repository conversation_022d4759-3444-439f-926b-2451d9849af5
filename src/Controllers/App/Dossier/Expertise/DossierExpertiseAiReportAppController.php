<?php
namespace MatGyver\Controllers\App\Dossier\Expertise;

use MatGyver\Entity\Dossier\Expertise\DossierExpertiseAiReport;
use MatGyver\Factories\BlankStates\DossierExpertiseAiReportBlankState;
use MatGyver\Factories\BlankStates\DossierExpertiseAiReportProcessedBlankState;
use MatGyver\Factories\BlankStates\DossierExpertiseAiReportProcessingBlankState;
use MatGyver\Factories\BlankStates\DossierExpertiseAiReportSendDocumentBlankState;
use MatGyver\FormsFactory\Dossier\Expertise\DossierExpertiseAiReportFormFactory;
use MatGyver\FormsFactory\FormFactory;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\Dossier\DossierExpertiseService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseAiReportService;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\Requirement\Requirement;

/**
 * Class DossierExpertiseAiReportAppController
 * @package MatGyver\Controllers\App\Dossier\Expertise
 */
class DossierExpertiseAiReportAppController extends AbstractDossierExpertiseAppController
{
    #[Route('/app/dossier/expertise/ai_report/{expertiseId}/', name: 'app_dossier_expertise_ai_report')]
    public function dossierExpertiseAiReport(): void
    {
        $this->view->setTitle(__('Génération automatique du rapport d\'expertise'));

        $expertise = $this->getExpertise();

        $expertiseAiReport = $this->get(DossierExpertiseAiReportService::class)->getRepository()->findOneBy(['expertise' => $expertise], ['id' => 'DESC']);
        if ($expertiseAiReport and !isset($_GET['restart'])) {
            //display results
            $this->get(DispatcherService::class)->redirectToRoute('app_dossier_expertise_ai_report_results', ['reportId' => $expertiseAiReport->getId(), 'expertiseId' => $expertise->getId()]);
            return;
        }

        $dossierExpertiseAiReportForm = new DossierExpertiseAiReportFormFactory($expertise);
        if ($dossierExpertiseAiReportForm->isSubmitted() and $dossierExpertiseAiReportForm->isValid()) {
            $process = $dossierExpertiseAiReportForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_dossier_expertise_ai_report_generate', ['reportId' => $process['id'], 'expertiseId' => $expertise->getId()]);
            }
        }
        $output = $this->builderForm->render($dossierExpertiseAiReportForm, $expertiseAiReport);
        $output .= $this->displayBtnNextStep(Tools::makeLink('app', 'dossier', 'expertise/report/evaluation/' . $expertise->getId()), __('Ignorer et passer à l\'étape suivante'), 'btn-light-primary');
        $this->displayOutput($output);
    }

    #[Route('/app/dossier/expertise/ai_report/generate/{reportId}/{expertiseId}/', name: 'app_dossier_expertise_ai_report_generate', requirements: ['reportId' => Requirement::POSITIVE_INT, 'expertiseId' => Requirement::POSITIVE_INT])]
    public function dossierExpertiseAiReportGenerate(int $reportId, int $expertiseId): void
    {
        $this->view->setTitle(__('Génération automatique du rapport d\'expertise'));

        $expertise = $this->getExpertise();
        $expertiseAiReport = $this->getExpertiseAiReport($expertiseId, $reportId);

        $factory = new DossierExpertiseAiReportBlankState($expertise);
        $factoryContent = $factory->render();
        $output = str_replace('card-blank-state gutter-b"', 'card-blank-state gutter-b d-none" id="blank-state-start"', $factoryContent);

        $factory = new DossierExpertiseAiReportProcessingBlankState($expertise);
        $factoryContent = $factory->render();
        $output .= str_replace('card-blank-state gutter-b"', 'card-blank-state gutter-b" id="blank-state-processing"', $factoryContent);

        $factory = new DossierExpertiseAiReportSendDocumentBlankState($expertise);
        $factoryContent = $factory->render();
        $output .= str_replace('card-blank-state gutter-b"', 'card-blank-state gutter-b d-none" id="blank-state-send-document"', $factoryContent);

        $factory = new DossierExpertiseAiReportProcessedBlankState($expertise, $expertiseAiReport);
        $factoryContent = $factory->render();
        $output .= str_replace('card-blank-state gutter-b"', 'card-blank-state gutter-b d-none" id="blank-state-done"', $factoryContent);

        Assets::addJs('app/dossier/expertise_ai_report.js');
        Assets::addInlineJs('<script>
$(document).ready(function () {
    startAiReport(' . $reportId . ', ' . $expertiseId . ');
});</script>');

        $this->displayOutput($output);
    }

    #[Route('/app/dossier/expertise/ai_report/results/{reportId}/{expertiseId}/', name: 'app_dossier_expertise_ai_report_results', requirements: ['reportId' => Requirement::POSITIVE_INT, 'expertiseId' => Requirement::POSITIVE_INT])]
    public function dossierExpertiseAiReportResults(int $reportId, int $expertiseId): void
    {
        $expertise = $this->getExpertise();
        $expertiseAiReport = $this->getExpertiseAiReport($expertiseId, $reportId);
        $damageAttributionsTypes = DossierExpertiseService::getDamageAttributions();
        $this->view->setTitle(__('Résultats de la génération automatique du rapport d\'expertise'));

        Assets::addCss('common/recorder/recorder.css', 'assets/js/');
        Assets::addJs('common/recorder/recorder.js');
        Assets::addJs('common/recorder/Fr.voice.js');
        Assets::addJs('common/recorder/app.js');
        Assets::addJs('app/dossier/expertise_ai_report.js');

        if (!$expertiseAiReport->getResult()) {
            $this->displayError(
                __('Aucun résultat n\'a été retourné par la génération automatique du rapport d\'expertise.'),
                Tools::makeLink('app', 'dossier', 'expertise/ai_report/' . $expertiseId, 'restart'),
                __('Recommencer')
            );
            return;
        }

        $results = json_decode($expertiseAiReport->getResult(), true);
        foreach ($results as $resultKey => $value) {
            if ($resultKey === 'imputability') {
                $results[$resultKey][] = [
                    'person' => 'unknown',
                    'content' => '',
                ];
            }
        }

        $output = $this->parser->set('expertise', $expertise)
            ->set('expertiseAiReport', $expertiseAiReport)
            ->set('damageAttributionsTypes', $damageAttributionsTypes)
            ->set('results', $results)
            ->render('app/dossier/expertise_ai_report.php');
        $this->displayOutput($output);
    }

    #[Route('/app/dossier/expertise/ai_report/apply/{reportId}/{expertiseId}/', name: 'app_dossier_expertise_ai_report_apply', requirements: ['reportId' => Requirement::POSITIVE_INT, 'expertiseId' => Requirement::POSITIVE_INT])]
    public function dossierExpertiseAiReportApplyResults(int $reportId, int $expertiseId): void
    {
        $expertise = $this->getExpertise();
        $expertiseAiReport = $this->getExpertiseAiReport($expertiseId, $reportId);

        //$this->get(DossierExpertiseAiReportService::class)->applyResults($expertiseAiReport);
        $this->displaySuccess(
            __('Les résultats ont été appliqués avec succès.'),
            Tools::makeLink('app', 'dossier', 'expertise/report/evaluation/' . $expertiseId),
            __('Démarrer le rapport d\'expertise')
        );
    }

    public function getExpertiseAiReport(int $expertiseId, int $reportId): ?DossierExpertiseAiReport
    {
        $expertiseAiReport = $this->get(DossierExpertiseAiReportService::class)->getRepository()->find($reportId);
        if (!$expertiseAiReport or $expertiseAiReport->getExpertise()->getId() !== $expertiseId) {
            $this->displayError(
                __('Cette génération de rapport d\'expertise n\'existe pas.'),
                Tools::makeLink('app', 'dossier', 'expertise/ai_report/' . $expertiseId, 'restart'),
                __('Recommencer')
            );
            exit();
        }

        return $expertiseAiReport;
    }
}
