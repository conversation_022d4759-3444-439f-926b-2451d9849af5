<?php
namespace MatGyver\Controllers\App\Dossier\Expertise;

use MatGyver\FormsFactory\Dossier\Expertise\DossierExpertiseReportConsequencesFormFactory;
use MatGyver\FormsFactory\FormFactory;
use MatGyver\Services\DispatcherService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class DossierExpertiseReportConsequencesAppController
 * @package MatGyver\Controllers\App\Dossier\Expertise
 */
class DossierExpertiseReportConsequencesAppController extends AbstractDossierExpertiseAppController
{
    #[Route('/app/dossier/expertise/consequences/{expertiseId}/', name: 'app_dossier_expertise_consequences')]
    public function dossierExpertiseConsequences()
    {
        $this->view->setTitle(__('Conséquences'));

        $expertise = $this->getExpertise();
        $report = $expertise->getExpertiseReport();

        $dossierExpertiseConsequencesForm = new DossierExpertiseReportConsequencesFormFactory($expertise);
        if ($dossierExpertiseConsequencesForm->isSubmitted() and $dossierExpertiseConsequencesForm->isValid()) {
            $process = $dossierExpertiseConsequencesForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_dossier_expertise_responsibilities', ['expertiseId' => $expertise->getId()]);
            }
        }
        $output = $this->builderForm->render($dossierExpertiseConsequencesForm, $report);
        $this->displayOutput($output);
    }
}
