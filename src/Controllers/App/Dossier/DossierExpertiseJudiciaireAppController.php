<?php
namespace MatGyver\Controllers\App\Dossier;

use MatGyver\FormsFactory\Dossier\DossierExpertiseJudiciaireFormFactory;
use MatGyver\FormsFactory\FormFactory;
use MatGyver\Helpers\Tools;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\Dossier\Expertise\DossierExpertiseJudiciaireService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class DossierExpertiseJudiciaireAppController
 * @package MatGyver\Controllers\App\Dossier
 */
class DossierExpertiseJudiciaireAppController extends AbstractDossierAppController
{
    #[Route('/app/dossier/expertise_judiciaire/{dossierId}/', name: 'app_dossier_expertise_judiciaire')]
    public function dossierExpertiseJudiciaire()
    {
        $this->view->setTitle(__('Expertise judiciaire'));

        $dossier = $this->getDossier();
        $expertise = $dossier->getExpertise();
        if (!$expertise) {
            $this->displayError(__('Aucune expertise enregistrée'), Tools::makeLink('app', 'dossier', 'expertise/add/' . $dossier->getId()), __('Créer une expertise'));
            return;
        }

        $expertiseJudiciaire = $dossier->getExpertiseJudiciaire();
        if (!$expertiseJudiciaire) {
            $expertiseJudiciaire = $this->get(DossierExpertiseJudiciaireService::class)->createOne($expertise);
        }

        $dossierExpertiseJudiciaireForm = new DossierExpertiseJudiciaireFormFactory($dossier);
        if ($dossierExpertiseJudiciaireForm->isSubmitted() and $dossierExpertiseJudiciaireForm->isValid()) {
            $process = $dossierExpertiseJudiciaireForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_dossier_demandeurs', ['dossierId' => $dossier->getId()]);
            }
        }
        $output = $this->builderForm->render($dossierExpertiseJudiciaireForm, $expertiseJudiciaire);
        $this->displayOutput($output);
    }
}
