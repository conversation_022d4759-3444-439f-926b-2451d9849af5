<?php

namespace MatGyver\Controllers\App;

use Mat<PERSON>yver\Attributes\Universe;
use MatGyver\Entity\News\News;
use MatGyver\Factories\BlankStates\SuccessBlankState;
use MatGyver\Helpers\Assets;
use MatGyver\Menus\AppMenu;
use MatGyver\Services\News\NewsClientsService;
use MatGyver\Services\News\NewsContentService;
use MatGyver\Services\NotificationsService;
use MatGyver\Services\Versions\VersionsClientsService;
use MatGyver\Services\Versions\VersionsService;
use Symfony\Component\Routing\Attribute\Route;

#[Universe(UNIVERSE_APP_PUBLIC)]
class NotificationsAppController extends AbstractAppController
{

    #[Route('/app/notifications/', name: 'app_notifications')]
    public function notifications()
    {
        $this->view->setTitle(__('Centre de notifications'));

        $tempNotifications = $this->get(AppMenu::class)->getNotifications(false);
        $notifications = $this->get(NotificationsService::class)->getRepository()->findBy([], ['id' => 'DESC']);
        $nbNotifications = $this->get(NotificationsService::class)->getRepository()->count(['seen' => false]);
        $nbNotifications += count($tempNotifications);

        //versions
        $nbVersions = 0;
        $versions = $this->get(VersionsService::class)->getRepository()->getLastVersions(50);
        if (!$versions) {
            $factory = new SuccessBlankState('', __('Aucune version enregistrée pour l\'instant'));
            $displayVersions = $factory->render();
        } else {
            $lastView = $this->get(VersionsClientsService::class)->getRepository()->getLastViewByClient();
            if (!$lastView) {
                $nbVersions = count($versions);
            } else {
                foreach ($versions as $version) {
                    if ($lastView->getDate() < $version->getDatePublication()) {
                        $nbVersions++;
                    }
                }
            }
            $displayVersions = $this->parser->set('versions', $versions)
                ->set('lastView', $lastView)
                ->render('app/versions.php');
        }

        //news
        $nbNews = 0;
        $newsContents = $this->get(NewsContentService::class)->getRepository()->findActiveByLang($this->get(NewsContentService::class)->detectNewsLang());
        if (empty($newsContents)) {
            $factory = new SuccessBlankState('', __('Aucune news enregistrée pour l\'instant'));
            $displayNews = $factory->render();
        } else {
            $newsViews = $this->get(NewsClientsService::class)->getRepository()->getAllNewsViewsByUser();
            $newsViewsByUser = [];
            if ($newsViews) {
                foreach ($newsViews as $newsView) {
                    $newsViewsByUser[] = $newsView->getNews()->getId();
                }
            }

            $displayNews = '';
            foreach ($newsContents as $newsContent) {
                if ($newsContent->getNews()->getType() == News::TYPE_FULLSCREEN) {
                    continue;
                }
                if (!in_array($newsContent->getNews()->getId(), $newsViewsByUser)) {
                    $nbNews++;
                }
                $displayNews .= $this->parser->set('newsContent', $newsContent)
                    ->set('newsViewsByUser', $newsViewsByUser)
                    ->render('app/news/news_id.php');
            }
        }

        Assets::addCss('common/blank_state.css');
        Assets::addCss('app/news.css');
        Assets::addJs('app/notifications.js');

        if (isset($_GET['id'])) {
            $idNotification = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
            $js = '<script type="text/javascript">$(document).ready(function() { displayNotification(' . $idNotification . '); });</script>';
            Assets::addInlineJs($js);
        }

        if (isset($_GET['tab']) and $_GET['tab'] == 'versions') {
            $js = '<script type="text/javascript">$(document).ready(function() { displayVersions(false); });</script>';
            Assets::addInlineJs($js);
        }

        $output = $this->parser->set('notifications', $notifications)
            ->set('tempNotifications', $tempNotifications)
            ->set('versions', $displayVersions)
            ->set('news', $displayNews)
            ->set('nbNotifications', $nbNotifications)
            ->set('nbVersions', $nbVersions)
            ->set('nbNews', $nbNews)
            ->render('app/notifications/notifications.php');
        $this->displayOutput($output);
    }
}
