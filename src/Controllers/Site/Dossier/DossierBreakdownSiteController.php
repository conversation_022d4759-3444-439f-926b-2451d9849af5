<?php
namespace MatGyver\Controllers\Site\Dossier;

use Mat<PERSON>yver\FormsFactory\Dossier\Site\SiteDossierBreakdownFormFactory;
use Mat<PERSON>yver\FormsFactory\FormFactory;
use MatGyver\Services\DispatcherService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class DossierBreakdownSiteController
 * @package MatGyver\Controllers\Site\Dossier
 */
class DossierBreakdownSiteController extends AbstractDossierSiteController
{

    #[Route('/dossier/breakdown/{reference}/', name: 'site_dossier_breakdown')]
    public function dossierBreakdown()
    {
        $this->view->setTitle(__('Panne'));
        $this->view->set('no_title', true);

        $dossier = $this->getDossier();
        if (!$dossier->isEditable()) {
            $this->displayError(__('Ce dossier n\'est pas modifiable actuellement.'));
            return;
        }
        $breakdown = $dossier->getBreakdown();

        $dossierBreakdownForm = new SiteDossierBreakdownFormFactory($dossier);
        if ($dossierBreakdownForm->isSubmitted() and $dossierBreakdownForm->isValid()) {
            $process = $dossierBreakdownForm->process(($breakdown ? FormFactory::TYPE_PROCESS_UPDATE : FormFactory::TYPE_PROCESS_INSERT));
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('site_dossier_documents', ['reference' => $dossier->getReference()]);
            }
        }
        $output = $this->builderForm->render($dossierBreakdownForm, $breakdown);
        $this->displayOutput($output);
    }
}
