<?php

namespace MatGyver\Controllers\Site\Dossier;

use MatGyver\Attributes\View;
use MatGyver\Controllers\Site\AbstractSiteController;
use Mat<PERSON>yver\Entity\Dossier\Dossier;
use MatGyver\Factories\BlankStates\DossierWrongAccountBlankState;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Dossier\DossierService;
use MatGyver\Services\RightsService;
use MatGyver\Services\SiteService;
use MatGyver\Services\Users\UsersLoginService;
use MatGyver\Services\Users\UsersService;
use MatGyver\Services\Views\ViewSite;

#[View('MatGyver\Services\Views\ViewSiteDossier')]
abstract class AbstractDossierSiteController extends AbstractSiteController
{
    /**
     * @return Dossier|null
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    protected function getDossier(bool $checkMission = true): ?Dossier
    {
        if (str_contains($this->param, '/')) {
            $params = explode('/', $this->param);
            $dossierReference = filter_var($params[1], FILTER_UNSAFE_RAW);
        } else {
            $dossierReference = filter_var($this->param, FILTER_UNSAFE_RAW);
        }

        if (!$dossierReference) {
            $this->displayError(__('Erreur : ce dossier n\'existe pas.'));
            exit();
        }

        $dossier = $this->get(DossierService::class)->getRepository()->findByReference($dossierReference);
        if (!$dossier) {
            $this->displayError(__('Erreur : ce dossier n\'existe pas.'));
            exit();
        }

        if ($dossier->getStatus() == Dossier::STATUS_CLOSED or $dossier->getStatus() == Dossier::STATUS_CANCELED) {
            $this->displayError(__('Erreur : ce dossier n\'est plus disponible.'));
            exit();
        }
        if ($dossier->isJudiciaire()) {
            $this->displayError(__('Erreur : ce dossier n\'est pas disponible.'));
            exit();
        }

        /*if ($checkMission and $dossier->getMission() and $dossier->getMission()->getAssignment() and !$dossier->getMission()->getAccepted()) {
            $this->displayErrorAndRedirect(__('Vous devez accepter le missionnement avant de pouvoir éditer ce dossier.'), Tools::makeLink('site', 'dossier', $dossier->getReference()));
            exit();
        }

        return $dossier;*/


        if (isset($_GET['logout'])) {
            UsersLoginService::logout(false);
            header('Location: ' . Tools::getUrl(false));
            exit();
        }

        if (RightsService::isEditor()) {
            //check if user client and dossier client are the same
            $user = $this->get(UsersService::class)->getUser();
            if ($user and $user->getClient() !== $dossier->getClient()) {
                if ($user->getEmail() != $dossier->getContact()->getEmail()) {
                    UsersLoginService::logout(false);
                    header('Location: ' . Tools::getUrl());
                    exit();
                }
            }
        }

        $user = $dossier->getContact()->getUser();
        if (!$user) {
            //send link to create account
            $this->get(DossierService::class)->sendMailToContact($dossier);

            SiteService::displayWaitingUser();
            exit();
        }
        if ($user->getRestricted()) {
            SiteService::display403();
            exit();
        }

        if (!RightsService::isEditor() and isset($_SESSION['user']) and $_SESSION['user']['id'] != $user->getId()) {
            $view = $this->get(ViewSite::class);
            $view->resetAssets();
            $factory = new DossierWrongAccountBlankState($dossier);
            $content = $factory->render();

            $view->set('class', ['welcome'])
                ->set('title', __('Accès refusé'))
                ->render($content, true);
            exit();
        }

        $this->get(UsersLoginService::class)->checkIfConnected();

        if ($dossier->getStatus() == Dossier::STATUS_CLOSED or $dossier->getStatus() == Dossier::STATUS_CANCELED) {
            $this->displayError(__('Erreur : ce dossier n\'est plus disponible.'));
            exit();
        }

        if ($checkMission and $dossier->getMission() and $dossier->getMission()->getAssignment() and !$dossier->getMission()->getAccepted()) {
            $this->displayErrorAndRedirect(__('Vous devez accepter le missionnement avant de pouvoir éditer ce dossier.'), Tools::makeLink('site', 'dossier', $dossier->getReference()));
            exit();
        }

        return $dossier;
    }
}
