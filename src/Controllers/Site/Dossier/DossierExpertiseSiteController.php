<?php
namespace MatGyver\Controllers\Site\Dossier;

use MatGyver\Factories\BlankStates\ErrorBlankState;
use MatGyver\Helpers\Tools;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class DossierVehicleSiteController
 * @package MatGyver\Controllers\Site\Dossier
 */
class DossierExpertiseSiteController extends AbstractDossierSiteController
{

    #[Route('/dossier/expertise/{reference}/', name: 'site_dossier_expertise')]
    public function dossierExpertise()
    {
        $this->view->setTitle(__('Date et lieu d\'expertise'));
        $this->view->set('no_title', true);

        $dossier = $this->getDossier();
        if (!$dossier->isEditable()) {
            $this->displayError(__('Ce dossier n\'est pas modifiable actuellement.'));
            return;
        }
        $expertise = $dossier->getLastExpertise();
        if (!$expertise) {
            $factory = new ErrorBlankState(__('Aucune expertise prévue pour l\'instant. La date et le lieu d\'expertise vous seront communiqués ultérieurement.'), '', '', __('Expertise'));
            $this->displayOutput($factory->render());
            return;
        }
        $output = $this->parser->set('dossier', $dossier)
            ->set('expertise', $expertise)
            ->render('site/dossier/expertise.php');
        $output .= $this->displayBtnNextStep(Tools::makeLink('site', 'dossier', 'institutions/' . $dossier->getReference()));
        $this->displayOutput($output);
    }
}
