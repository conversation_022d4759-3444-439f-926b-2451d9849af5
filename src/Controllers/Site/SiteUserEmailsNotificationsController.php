<?php

namespace MatGyver\Controllers\Site;

use Doctrine\ORM\EntityManager;
use MatGyver\Entity\User\User;
use MatGyver\Services\Pages\Users\UsersUnsubscribeConfirmationService;
use MatGyver\Services\RightsService;
use MatGyver\Services\UniversesService;
use MatGyver\Services\Users\Emails\UsersEmailsNotificationsTypesService;
use MatGyver\Services\Users\Emails\UsersEmailsService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class SiteUserEmailsNotificationsController
 * @package MatGyver\Controllers\Site
 */
class SiteUserEmailsNotificationsController extends AbstractSiteController
{
    #[Route('/notifications/unsubscribe/{params}/', name: 'site_emails_notifications_unsubscribe')]
    public function notificationUnsubscribe(string $params)
    {
        $params = base64_decode($params);
        parse_str($params, $datas);

        $md5Email = ($datas['m'] ?? null);
        $idType = ($datas['t'] ?? 0);

        if (!$md5Email or !$idType) {
            $this->displayError(__('Accès non autorisé.'));
            return;
        }

        $user = $this->get(EntityManager::class)->getRepository(User::class)->findOneBy(['md5Email' => $md5Email]);
        if (!$user) {
            $this->displayError(__('Accès non autorisé.'));
            return;
        }

        $successMessage = '';
        $isUser = RightsService::isUser($user->getId());
        if ($isUser) {
            $unsubscribe = $this->get(UsersEmailsService::class)->unsubscribeByUserId($user->getId(), $user->getClient()->getId());
            if (!$unsubscribe['valid']) {
                $this->displayError(__('Une erreur est survenue lors de la mise à jour des notifications.'));
                return;
            }
        } else {
            $deleteTypeByUserId = $this->get(UsersEmailsNotificationsTypesService::class)->deleteTypeByUserId($idType, $user->getId());
            if (!$deleteTypeByUserId['valid']) {
                $this->displayError(__('Une erreur est survenue lors de la mise à jour des notifications.'));
                return;
            }

            $type = $this->get(UsersEmailsNotificationsTypesService::class)->getType($idType);
            $universe = $this->get(UsersEmailsNotificationsTypesService::class)->getUniverseByType($idType);
            if ($type and $universe) {
                $labelUniverse = $this->get(UniversesService::class)->getUniverseName($universe);
                $successMessage = __('Vous êtes bien désinscrit de la notification suivante :') . '<br>' . ($labelUniverse ? $labelUniverse . ' : ' : '') . $type;
            }
        }

        $pageService = $this->get(UsersUnsubscribeConfirmationService::class);
        $pageService->setSuccessMessage($successMessage);
        $pageService->renderPage();
    }
}
