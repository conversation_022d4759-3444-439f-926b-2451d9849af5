<?php

namespace MatGyver\Controllers\Site;

use MatGyver\FormsFactory\Client\ClientConfirmationLinkFormFactory;
use MatGyver\FormsFactory\FormFactory;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Affiliation\AffiliationClicsService;
use MatGyver\Services\Clients\ClientsService;
use MatGyver\Services\Tutorials\Site\AffiliationPartnerTutorialService;
use MatGyver\Services\Views\ViewAside;
use MatGyver\Services\Views\ViewSite;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\Requirement\Requirement;

/**
 * Class SiteAffiliationController
 * @package MatGyver\Controllers\Site
 */
class SiteAffiliationController extends AbstractSiteController
{
    #[Route('/partner/signup/', name: 'site_partner_signup')]
    public function partnerSignup()
    {
        if (!AFFILIATION_ACTIVATED) {
            $this->display404();
        }

        if (!SUBDOMAIN_ENABLED and $_SESSION['client']['id'] != CLIENT_MASTER) {
            $client = $this->get(ClientsService::class)->getRepository()->find(CLIENT_MASTER);
            $this->get(ClientsService::class)->setClientInSession($client);
        }
        if ($_SESSION['client']['id'] != CLIENT_MASTER) {
            $this->display404();
        }

        $signupForm = new AffiliationPartnerTutorialService();
        if ($signupForm->isSubmitted() and $signupForm->isValid()) {
            $process = $signupForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                header('Location: ' . $process['redirection']);
                exit();
            }
        }
        $signupForm->renderAsAsideTutoriel();
    }

    #[Route('/partner/confirmation/', name: 'site_partner_confirmation')]
    #[Route('/partner/confirmation/{idClient}/', name: 'site_partner_confirmation_id_client', requirements: ['idClient' => Requirement::POSITIVE_INT])]
    public function partnerConfirmation(?int $idClient = null)
    {
        if (!AFFILIATION_ACTIVATED) {
            //$this->display404();
        }

        $formConfirmationLink = '';
        if ($idClient) {
            $client = $this->get(ClientsService::class)->getRepository()->find($idClient);
            $confirmationLinkFormFactory = new ClientConfirmationLinkFormFactory();
            if ($confirmationLinkFormFactory->isSubmitted() and $confirmationLinkFormFactory->isValid()) {
                $process = $confirmationLinkFormFactory->process(FormFactory::TYPE_PROCESS_INSERT);
                if ($process['valid']) {
                    header('Location: ' . Tools::makeLink('site', 'partner', 'confirmation/' . $idClient, 'success'));
                    exit();
                }
            }
            $formConfirmationLink = $this->builderForm->render($confirmationLinkFormFactory, $client);
        }

        $this->setView($this->get(ViewSite::class));
        $this->view->set('fullwidth', true);
        //$this->view->setLeftTitle(__('Confirmation d\'inscription'));

        Assets::addCss('common/blank_state.css');
        Assets::addCss('pages/login.css');

        $output = $this->parser->set('id_client', $idClient)
            ->set('formConfirmationLink', $formConfirmationLink)
            ->render('site/affiliation/confirmation.php');
        $this->displayOutput($output);
        exit();
    }

    #[Route('/partner/', name: 'site_partner')]
    #[Route('/partner/{ridPartner}/', name: 'site_partner_permalink', requirements: ['ridPartner' => Requirement::ASCII_SLUG])]
    public function partner(string $ridPartner = '')
    {
        if ($ridPartner) {
            $insert = $this->get(AffiliationClicsService::class)->insert($ridPartner);
            if (isset($insert['redirection']) and $insert['redirection']) {
                header('Location: ' . $insert['redirection']);
                exit();
            }
        }

        header('Location: ' . APP_URL);
        exit();
    }

    #[Route('/partner/{client}/{permalink}/', name: 'site_partner_client_permalink', requirements: ['client' => Requirement::ASCII_SLUG, 'permalink' => Requirement::ASCII_SLUG])]
    public function partnerClient(string $client, string $permalink)
    {
        $ridPartner = $client . '/' . $permalink;
        $insert = $this->get(AffiliationClicsService::class)->insert($ridPartner);
        if (isset($insert['redirection']) and $insert['redirection']) {
            header('Location: ' . $insert['redirection']);
            exit();
        }

        header('Location: ' . APP_URL);
        exit();
    }
}
