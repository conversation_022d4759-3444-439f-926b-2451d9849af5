<?php

namespace MatGyver\Controllers\Admin\Affiliation;

use Mat<PERSON><PERSON>ver\FormsFactory\Affiliation\AffiliationSubPartnerFormFactory;
use Mat<PERSON>yver\FormsFactory\FormFactory;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Table\Affiliation\AffiliationSubPartnersViewHelper;
use MatGyver\Services\Affiliation\AffiliationSubPartnersService;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\RightsService;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\Requirement\Requirement;

/**
 * Class AffiliationSubPartnersAdminController
 * @package MatGyver\Controllers\Admin\Affiliation
 */
class AffiliationSubPartnersAdminController extends AbstractAffiliationController
{
    #[Route('/admin/affiliation/subpartners/', name: 'admin_affiliation_subpartners')]
    public function affiliationSubPartners()
    {
        $this->view->setTitle(__('Filleuls'));
        if (RightsService::isGranted(ATTRIBUTE_CREATE, UNIVERSE_ADMIN_AFFILIATION)) {
            $this->view->addAction(__('Ajouter un filleul'), Tools::makeLink('admin', 'affiliation', 'subpartner/add'));
        }

        $subPartners = $this->get(AffiliationSubPartnersService::class)->getRepository()->getAllSubPartners();
        if (!$subPartners) {
            $this->displayEmpty(__('Filleuls'), __('Aucun filleul enregistré.'));
            return;
        }

        $output = $this->get(AffiliationSubPartnersViewHelper::class)->getContent($subPartners);
        $this->displayOutputCard($output);
    }

    #[Route('/admin/affiliation/subpartner/add/', name: 'admin_affiliation_subpartner_add')]
    public function affiliationSubPartnerAdd()
    {
        $this->view->setTitle(__('Ajout d\'un filleul'));
        $this->view->addBreadcrumbItem(__('Filleuls'), Tools::makeLink('admin', 'affiliation', 'subpartners'));
        $this->denyAccessUnlessGranted(ATTRIBUTE_CREATE, UNIVERSE_ADMIN_AFFILIATION);

        $subPartnerForm = new AffiliationSubPartnerFormFactory();
        if ($subPartnerForm->isSubmitted() and $subPartnerForm->isValid()) {
            $process = $subPartnerForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('admin_affiliation_subpartners');
            }
        }
        $this->displayOutput($this->builderForm->render($subPartnerForm));
    }

    #[Route('/admin/affiliation/subpartner/edit/{id}/', name: 'admin_affiliation_subpartner_edit', requirements: ['id' => Requirement::POSITIVE_INT])]
    public function affiliationSubPartner(int $id)
    {
        $this->view->setTitle(__('Modification d\'un filleul'));
        $this->view->addBreadcrumbItem(__('Filleuls'), Tools::makeLink('admin', 'affiliation', 'subpartners'));
        $this->denyAccessUnlessGranted(ATTRIBUTE_EDIT, UNIVERSE_ADMIN_AFFILIATION);

        $subPartner = $this->get(AffiliationSubPartnersService::class)->getRepository()->findWoClient($id);
        if (!$subPartner) {
            $this->displayError(__('Erreur : ce filleul n\'existe pas'), Tools::makeLink('admin', 'affiliation', 'subpartners'));
            return;
        }

        $subPartnerForm = new AffiliationSubPartnerFormFactory();
        if ($subPartnerForm->isSubmitted() and $subPartnerForm->isValid()) {
            $process = $subPartnerForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('admin_affiliation_subpartners');
            }
        }
        $this->displayOutput($this->builderForm->render($subPartnerForm, $subPartner));
    }
}
