<?php
namespace <PERSON><PERSON><PERSON><PERSON>\Controllers\Admin\BugTracker;

use Mat<PERSON><PERSON><PERSON>\Attributes\Universe;
use Mat<PERSON><PERSON><PERSON>\Attributes\View;
use MatGyver\Controllers\Admin\AbstractAdminController;
use Mat<PERSON><PERSON>ver\Entity\BugTracker\Bug;
use MatG<PERSON>ver\FormsFactory\BugTracker\BugCommentFormFactory;
use MatGyver\FormsFactory\FormFactory;
use MatGyver\FormsFactory\BugTracker\BugFormFactory;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Table\BugTracker\BugViewHelper;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\BugTracker\BugService;
use MatGyver\Services\Views\ViewAdminBug;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\Requirement\Requirement;

#[Universe(UNIVERSE_ADMIN_DEV)]
class BugAdminController extends AbstractAdminController
{
    #[Route('/admin/bugs/', name: 'admin_bugs')]
    public function bugs()
    {
        $this->view->setTitle(__('Bugs'));

        $bugs = $this->get(BugService::class)->getRepository()->findBy([], [], null, null, false);
        if (!$bugs) {
            $this->displaySuccess(__('Aucun bug enregistré.'));
            return;
        }

        $output = $this->get(BugViewHelper::class)->getContent($bugs);
        $this->displayOutputCard($output);
    }

    #[Route('/admin/bug/edit/{bugId}/', name: 'admin_bug_edit', requirements: ['bugId' => Requirement::POSITIVE_INT])]
    public function bugEdit(int $bugId)
    {
        $this->view->setTitle(__('Modification d\'un bug'));
        $this->view->addBreadcrumbItem(__('Bugs'), Tools::makeLink('admin', 'bugs'));

        $bug = $this->getFromRoute($bugId);

        $bugForm = new BugFormFactory();
        if ($bugForm->isSubmitted() and $bugForm->isValid()) {
            $process = $bugForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('admin_bug', ['bugId' => $bug->getId()]);
            }
        }
        $output = $this->builderForm->render($bugForm, $bug);
        $this->displayOutput($output);
    }

    #[Route('/admin/bug/status/{status}/{bugId}/', name: 'admin_bug_status', requirements: ['bugId' => Requirement::POSITIVE_INT])]
    public function bugStatus(string $status, int $bugId)
    {
        $bug = $this->getFromRoute($bugId);
        $updateStatus = $this->get(BugService::class)->updateStatus($bug, $status);
        if (!$updateStatus['valid']) {
            $this->displayError(__('Impossible de modifier l\'état de ce bug.'), Tools::makeLink('admin', 'bug', $bugId));
            return;
        }

        $this->displaySuccessAndRedirect(__('Bug modifié'), Tools::makeLink('admin', 'bug', $bugId));
    }

    #[Route('/admin/bug/resolve/{bugId}/', name: 'admin_bug_resolve', requirements: ['bugId' => Requirement::POSITIVE_INT])]
    public function bugResolve(int $bugId)
    {
        $bug = $this->getFromRoute($bugId);
        $resolve = $this->get(BugService::class)->resolve($bug);
        if (!$resolve['valid']) {
            $this->displayError(__('Impossible de résoudre ce bug.'), Tools::makeLink('admin', 'bug', $bugId));
            return;
        }

        $this->displaySuccessAndRedirect(__('Bug résolu'), Tools::makeLink('admin', 'bug', $bugId));
    }

    #[Route('/admin/bug/delete/{bugId}/', name: 'admin_bug_delete', requirements: ['bugId' => Requirement::POSITIVE_INT])]
    public function bugDelete(int $bugId)
    {
        $bug = $this->getFromRoute($bugId);
        try {
            $this->get(BugService::class)->deleteAndFlush($bug);
        } catch (\Exception $e) {
            $this->displayError(__('Impossible de supprimer ce bug.'));
            return;
        }
        $this->displaySuccessAndRedirect(__('Bug supprimé'), Tools::makeLink('admin', 'bugs'));
    }

    #[Route('/admin/bug/{bugId}/', name: 'admin_bug', requirements: ['bugId' => Requirement::POSITIVE_INT])]
    #[View(ViewAdminBug::class)]
    public function bug(int $bugId)
    {
        $this->view->setTitle(__('Bug'));
        $this->view->addBreadcrumbItem(__('Bugs'), Tools::makeLink('admin', 'bugs'));

        $bug = $this->getFromRoute($bugId);

        $this->view->setTitle($bug->getTitle());

        $bugCommentForm = new BugCommentFormFactory();
        if ($bugCommentForm->isSubmitted() and $bugCommentForm->isValid()) {
            $process = $bugCommentForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('admin_bug', ['bugId' => $bug->getId()]);
            }
        }
        $commentForm = $this->builderForm->render($bugCommentForm, $bug);

        $output = $this->parser->set('bug', $bug)
            ->set('commentForm', $commentForm)
            ->render('admin/bugtracker/bug.php');
        $this->displayOutput($output);
    }

    /**
     * @param int $bugId
     * @return Bug|null
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    private function getFromRoute(int $bugId): ?Bug
    {
        $bug = $this->get(BugService::class)->getRepository()->findWoClient($bugId);
        if (!$bug) {
            $this->displayError(__('Erreur : ce bug n\'existe pas.'), Tools::makeLink('admin', 'bugs'));
            exit();
        }

        return $bug;
    }
}
