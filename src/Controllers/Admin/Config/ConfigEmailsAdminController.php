<?php

namespace MatGyver\Controllers\Admin\Config;

use MatG<PERSON><PERSON>\Attributes\View;
use Mat<PERSON><PERSON>ver\FormsFactory\Config\ConfigEmailsFormFactory;
use MatGyver\FormsFactory\FormFactory;
use MatGyver\Helpers\Tools;
use MatGyver\Services\ConfigService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class ConfigEmailsAdminController
 * @package MatGyver\Controllers\Admin\Config
 */
#[View('MatGyver\Services\Views\ViewAdminSettings')]
class ConfigEmailsAdminController extends AbstractConfigAdminController
{

    #[Route('/admin/settings/emails/', name: 'admin_settings_emails')]
    public function configEmails()
    {
        $this->view->setTitle(__('Envois d\'emails'));
        $this->view->addBreadcrumbItem(__('Réglages'), Tools::makeLink('admin', 'settings'));

        $emailForm = new ConfigEmailsFormFactory();
        if ($emailForm->isSubmitted() and $emailForm->isValid()) {
            $emailForm->process(FormFactory::TYPE_PROCESS_INSERT);
        }
        $this->displayOutput($this->builderForm->render($emailForm));
    }

    #[Route('/admin/settings/emails/test/', name: 'admin_settings_emails_test')]
    public function configEmailsTest()
    {
        $send = $this->get(ConfigService::class)->sendTestEmail();
        if (!$send['valid']) {
            $this->displayErrorAndRedirect($send['message'], Tools::makeLink('admin', 'settings', 'emails'));
        }
        $this->displaySuccessAndRedirect(__('Un email est en cours d\'envoi à l\'adresse %s', $_SESSION['email']), Tools::makeLink('admin', 'settings', 'emails'));
    }
}
