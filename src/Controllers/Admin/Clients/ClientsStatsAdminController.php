<?php

namespace Mat<PERSON>yver\Controllers\Admin\Clients;

use Mat<PERSON><PERSON><PERSON>\Attributes\Universe;
use MatG<PERSON>ver\Controllers\Admin\AbstractAdminController;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Table\Shop\ShopTransactionsViewHelper;
use MatGyver\Services\Clients\ClientsStatsService;
use MatGyver\Services\Shop\Transaction\ShopTransactionService;
use Symfony\Component\Routing\Attribute\Route;
use function __;

#[Universe(UNIVERSE_ADMIN_STATS)]
class ClientsStatsAdminController extends AbstractAdminController
{
    #[Route('/admin/clients/stats/', name: 'admin_clients_stats')]
    public function clientsStats()
    {
        $this->view->setTitle(__('Statistiques Clients'));
        $this->view->addBreadcrumbItem(__('Clients'), Tools::makeLink('admin', 'clients'));
        $this->view->addAction(__('Exporter'), Tools::makeLink('admin', 'clients', 'stats/export'), 'btn-light');
        $this->view->addAction(__('Exporter par mois'), Tools::makeLink('admin', 'clients', 'stats/export', 'p=month'), 'btn-light');

        $output = $this->get(ClientsStatsService::class)->displayStats();
        $this->displayOutput($output);
    }

    #[Route('/admin/clients/stats/export/', name: 'admin_clients_stats_export')]
    public function exportClientsStats()
    {
        $exportStats = $this->get(ClientsStatsService::class)->exportStats();
        if ($exportStats) {
            $this->displayError($exportStats['message']);
        }
    }

    #[Route('/admin/subscriptions/stats/', name: 'admin_subscriptions_stats')]
    public function subscriptionsStats()
    {
        $this->view->setTitle(__('Abonnements clients actifs'));
        $this->view->addBreadcrumbItem(__('Clients'), Tools::makeLink('admin', 'clients'));

        $data = $this->get(ClientsStatsService::class)->getStatsPerMonth();
        $nbSubscriptions = 0;
        foreach ($data as $aData) {
            $nbSubscriptions = count($aData);
            break;
        }
        $output = $this->parser->set('data', $data)
            ->set('nbSubscriptions', $nbSubscriptions)
            ->render('admin/clients/subscriptions_stats.php');
        $this->displayOutputCard($output);
    }

    #[Route('/admin/orders/month/', name: 'admin_orders_month')]
    public function ordersMonth()
    {
        $this->view->setTitle(__('Nouvelles commandes du mois'));
        $this->view->addBreadcrumbItem(__('Statistiques'), Tools::makeLink('admin', 'clients', 'stats'));

        $orders = $this->get(ShopTransactionService::class)->getRepository()->getNewPaidOrders();

        $dateThisMonth = new \DateTime(date('Y-m-') . '01 00:00:00');
        $ordersThisMonth = [];
        if ($orders) {
            foreach ($orders as $order) {
                if ($order->getValid() and $order->getAmountTaxIncl() > 0 and $order->getDate() >= $dateThisMonth) {
                    $ordersThisMonth[] = $order;
                }
            }
        }

        if (!$ordersThisMonth) {
            $this->displayError(__('Aucune nouvelle commande ce mois'));
            return;
        }

        $output = $this->get(ShopTransactionsViewHelper::class)->getContent($ordersThisMonth);
        $this->displayOutputCard($output);
    }
}
