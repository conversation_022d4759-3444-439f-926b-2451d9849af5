<?php

namespace MatGyver\Controllers\Admin;

use Mat<PERSON><PERSON>ver\Attributes\Universe;
use MatGyver\FormsFactory\FormFactory;
use MatGyver\FormsFactory\User\RoleFormFactory;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Table\Users\UsersRolesViewHelper;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\Users\UsersRolesService;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\Requirement\Requirement;

#[Universe(UNIVERSE_ADMIN_PRIVATE)]
class UsersRolesAdminController extends AbstractAdminController
{

    #[Route('/admin/roles/', name: 'admin_roles')]
    public function roles(): void
    {
        $this->view->setTitle(__('Rôles'));
        $this->view->addAction(__('Ajouter un rôle'), Tools::makeLink('admin', 'role', 'add'));

        $usersRoles = $this->get(UsersRolesService::class)->getAll();
        if (!$usersRoles) {
            $this->displayEmpty(__('Rôles'), __('Aucun rôle enregistré.'), Tools::makeLink('admin', 'role', 'add'));
            return;
        }

        $output = $this->get(UsersRolesViewHelper::class)->getContent($usersRoles);
        $this->displayOutputCard($output);
    }

    #[Route('/admin/role/add/', name: 'admin_role_add')]
    public function roleAdd(): void
    {
        $this->view->setTitle(__('Création d\'un rôle'));
        $this->view->addBreadcrumbItem(__('Rôles'), Tools::makeLink('admin', 'roles'));

        $userRoleForm = new RoleFormFactory();
        if ($userRoleForm->isSubmitted() and $userRoleForm->isValid()) {
            $process = $userRoleForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('admin_roles');
            }
        }
        $this->displayOutput($this->builderForm->render($userRoleForm));
    }

    #[Route('/admin/role/edit/{id}/', name: 'admin_role_edit', requirements: ['id' => Requirement::POSITIVE_INT])]
    public function roleEdit(int $id): void
    {
        $this->view->setTitle(__('Modification d\'un rôle'));
        $this->view->addBreadcrumbItem(__('Rôles'), Tools::makeLink('admin', 'roles'));

        $userRole = $this->get(UsersRolesService::class)->getRepository()->find($id);
        if (!$userRole) {
            $this->displayError(__('Erreur : ce rôle n\'existe pas.'), Tools::makeLink('admin', 'roles'));
            return;
        }

        $userRoleForm = new RoleFormFactory();
        if ($userRoleForm->isSubmitted() and $userRoleForm->isValid()) {
            $process = $userRoleForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('admin_roles');
            }
        }
        $this->displayOutput($this->builderForm->render($userRoleForm, $userRole));
    }

    #[Route('/admin/role/delete/{id}/', name: 'admin_role_delete', requirements: ['id' => Requirement::POSITIVE_INT])]
    public function roleDelete(int $id): void
    {
        $redirect = Tools::makeLink('admin', 'roles');
        $this->denyAccessUnlessGranted(ATTRIBUTE_DELETE, UNIVERSE_APP_USERS);

        $userRole = $this->get(UsersRolesService::class)->getRepository()->find($id);
        if (!$userRole) {
            $this->displayErrorAndRedirect(__('Erreur : ce rôle n\'existe pas.'), $redirect);
        }

        $deleteUserRole = $this->get(UsersRolesService::class)->removeUserRole($id);
        if (!$deleteUserRole['valid']) {
            $this->displayErrorAndRedirect($deleteUserRole['message'], $redirect);
        }

        $this->displaySuccessAndRedirect(__('Le rôle a bien été supprimé.'), $redirect);
    }
}
