<?php
namespace MatGyver\Controllers\Admin\Expertise;

use MatG<PERSON>ver\Entity\Expertise\Expertise;
use MatG<PERSON>ver\FormsFactory\Expertise\ExpertiseFormFactory;
use MatGyver\FormsFactory\FormFactory;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Action;
use MatGyver\Helpers\View\Card\Card;
use MatGyver\Helpers\View\Table\Expertise\ExpertiseViewHelper;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\Expertise\ExpertiseService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class ExpertisesAdminController
 * @package MatGyver\Controllers\Admin\Expertise
 */
class ExpertisesAdminController extends AbstractExpertiseAdminController
{

    #[Route('/admin/expertises/', name: 'admin_expertises')]
    public function expertises()
    {
        $this->view->setTitle(__('Types d\'expertises'));

        $expertises = $this->get(ExpertiseService::class)->getRepository()->findAll();
        if (!$expertises) {
            $this->displayError(__('Aucun type d\'expertise enregistré.'), Tools::makeLink('admin', 'expertise', 'add'), __('Ajouter un type d\'expertise'));
            return;
        }

        $output = $this->get(ExpertiseViewHelper::class)->getContent($expertises);
        $card = new Card();
        $card->setTitle(__('Types d\'expertises'));
        $card->setBody($output);
        $action = new Action();
        $action->setTitle(__('Ajouter un type d\'expertise'));
        $action->setClass('btn-primary');
        $action->setHref(Tools::makeLink('admin', 'expertise', 'add'));
        $card->addAction($action);
        $this->displayOutput($card->getContent());
    }

    #[Route('/admin/expertise/add/', name: 'admin_expertise_add')]
    public function expertiseAdd()
    {
        $this->view->setTitle(__('Création d\'un type d\'expertise'));
        $this->view->addBreadcrumbItem(__('Types d\'expertises'), Tools::makeLink('admin', 'expertises'));

        $expertiseForm = new ExpertiseFormFactory();
        if ($expertiseForm->isSubmitted() and $expertiseForm->isValid()) {
            $process = $expertiseForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('admin_expertises');
            }
        }
        $this->displayOutput($this->builderForm->render($expertiseForm));
    }

    #[Route('/admin/expertise/edit/{id}/', name: 'admin_expertise_edit')]
    public function expertiseEdit()
    {
        $this->view->setTitle(__('Modification d\'un type d\'expertise'));
        $this->view->addBreadcrumbItem(__('Types d\'expertises'), Tools::makeLink('admin', 'expertises'));

        $expertise = $this->getFromRoute();

        $expertiseForm = new ExpertiseFormFactory();
        if ($expertiseForm->isSubmitted() and $expertiseForm->isValid()) {
            $process = $expertiseForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('admin_expertises');
            }
        }
        $output = $this->builderForm->render($expertiseForm, $expertise);
        $this->displayOutput($output);
    }

    #[Route('/admin/expertise/delete/{id}/', name: 'admin_expertise_delete')]
    public function expertiseDelete()
    {
        $expertise = $this->getFromRoute();
        try {
            $this->get(ExpertiseService::class)->deleteAndFlush($expertise);
        } catch (\Exception $e) {
            $this->displayError(__('Impossible de supprimer ce type d\'expertise.'));
            return;
        }
        $this->get(DispatcherService::class)->redirectToRoute('admin_expertises');
    }

    /**
     * @return Expertise|null
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    private function getFromRoute(): ?Expertise
    {
        $expertiseId = filter_var($this->param, FILTER_VALIDATE_INT);
        if (!$expertiseId) {
            $this->displayError(__('Erreur : ce type d\'expertise n\'existe pas.'), Tools::makeLink('admin', 'expertises'));
            exit();
        }

        $expertise = $this->get(ExpertiseService::class)->getRepository()->find($expertiseId);
        if (!$expertise) {
            $this->displayError(__('Erreur : ce type d\'expertise n\'existe pas.'), Tools::makeLink('admin', 'expertises'));
            exit();
        }

        return $expertise;
    }
}
