<?php

namespace MatGyver\Controllers\Admin\Support;

use MatGyver\Attributes\View;
use MatGyver\Forms\Support\SupportUserForm;
use MatGyver\Forms\Support\SupportUserPermissionForm;
use MatG<PERSON>ver\FormsFactory\FormFactory;
use MatGyver\FormsFactory\Support\SupportUserPermissionFormFactory;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Table\Support\SupportAllUsersViewHelper;
use MatGyver\Helpers\View\Table\Support\SupportUserPermissionsViewHelper;
use MatGyver\Helpers\View\Table\Support\SupportUsersViewHelper;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\RightsService;
use MatGyver\Services\Support\SupportUserService;
use MatGyver\Services\Users\UsersService;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\Requirement\Requirement;

/**
 * Class SupportUserAdminController
 * @package MatGyver\Controllers\Admin\Support
 */
#[View('MatGyver\Services\Views\ViewAdminSupportSettings')]
class SupportUserAdminController extends AbstractSupportAdminController
{
    #[Route('/admin/support/users/', name: 'admin_support_users')]
    public function supportUsers()
    {
        $this->view->setTitle(__('Opérateurs'));
        $this->view->addBreadcrumbItem(__('Support'), Tools::makeLink('admin', 'support'));
        if (RightsService::isGranted(ATTRIBUTE_CREATE, UNIVERSE_ADMIN_SAV)) {
            $this->view->addAction( __('Ajouter un opérateur'), Tools::makeLink('admin', 'support', 'user/add'));
        }

        $admins = $this->get(SupportUserService::class)->getRepository()->getAllIdUsers();
        if (!$admins) {
            $this->displayEmpty(__('Opérateurs'), __('Aucun opérateur enregistré.'), Tools::makeLink('admin', 'support', 'user/add'), __('Ajouter un opérateur'));
            return;
        }

        $output = $this->get(SupportUsersViewHelper::class)->getContent($admins);
        $this->displayOutputCard($output);
    }

    #[Route('/admin/support/user/add/', name: 'admin_support_user_add')]
    public function supportUserAdd()
    {
        $this->view->setTitle(__('Ajouter un opérateur'));
        $this->view->addBreadcrumbItem(__('Support'), Tools::makeLink('admin', 'support'));
        $this->view->addBreadcrumbItem( __('Opérateurs'), Tools::makeLink('admin', 'support', 'users'));
        $this->denyAccessUnlessGranted(ATTRIBUTE_CREATE, UNIVERSE_ADMIN_SAV);

        $users = $this->get(UsersService::class)->getRepository()->getAllSuperUsers();
        if (!$users) {
            $this->displayError(__('Aucun administrateur enregistré.'), Tools::makeLink('admin', 'user', 'add'), __('Ajouter un administrateur'));
            return;
        }

        $supportUsers = [];
        $getSupportUsers = $this->get(SupportUserService::class)->getRepository()->getAllIdUsers();
        if ($getSupportUsers) {
            foreach ($getSupportUsers as $getSupportUser) {
                if (!$getSupportUser->getUser()) {
                    continue;
                }
                $supportUsers[] = $getSupportUser->getUser()->getId();
            }
        }

        $output = $this->get(SupportAllUsersViewHelper::class)->getContent($users, $supportUsers);
        $this->displayOutputCard($output);
    }

    #[Route('/admin/support/user/add/{userId}/', name: 'admin_support_user_add_id', requirements: ['userId' => Requirement::POSITIVE_INT])]
    public function supportUserAddId(int $userId)
    {
        $validate = $this->get(SupportUserForm::class)->validatePostAddUser($userId);
        if (!$validate['valid']) {
            $this->displayErrorAndRedirect($validate['message'], Tools::makeLink('admin', 'support', 'user/add'));
        }

        $addUser = $this->get(SupportUserForm::class)->addUser($userId);
        if (!$addUser['valid']) {
            $this->displayErrorAndRedirect($addUser['message'], Tools::makeLink('admin', 'support', 'user/add'));
        }

        $this->displaySuccessAndRedirect(__("L'administrateur a bien été ajouté."), Tools::makeLink('admin', 'support', 'user/permission/add/' . $userId));
    }

    #[Route('/admin/support/user/delete/{userId}/', name: 'admin_support_user_delete', requirements: ['userId' => Requirement::POSITIVE_INT])]
    public function supportUserDelete(int $userId)
    {
        $deleteUser = $this->get(SupportUserService::class)->deleteUser($userId);
        if (!$deleteUser['valid']) {
            $this->displayErrorAndRedirect($deleteUser['message'], Tools::makeLink('admin', 'support', 'users'));
        }

        $this->displaySuccessAndRedirect(__("L'administrateur a bien été supprimé."), Tools::makeLink('admin', 'support', 'users'));
    }

    #[Route('/admin/support/user/permissions/{userId}/', name: 'admin_support_user_permissions_edit', requirements: ['userId' => Requirement::POSITIVE_INT])]
    public function supportUserPermissions(int $userId)
    {
        $this->view->setTitle(__('Permissions d\'un opérateur'));
        $this->view->addBreadcrumbItem(__('Support'), Tools::makeLink('admin', 'support'));
        $this->view->addBreadcrumbItem( __('Opérateurs'), Tools::makeLink('admin', 'support', 'users'));
        $this->denyAccessUnlessGranted(ATTRIBUTE_EDIT, UNIVERSE_ADMIN_SAV);

        $user = $this->get(UsersService::class)->getUser($userId);
        if (!$user) {
            $this->displayError(__('Cet opérateur n\'existe pas.'));
            return;
        }

        if (RightsService::isSuperAdmin()) {
            $this->view->addAction(__('Ajouter une permission'), Tools::makeLink('admin', 'support', 'user/permission/add/' . $userId));
        }

        $userPermissions = $this->get(SupportUserService::class)->getUserPermissions($userId);
        if ($userPermissions) {
            foreach ($userPermissions as $id => $userPermission) {
                if (!$userPermission->getService()) {
                    unset($userPermissions[$id]);
                }
            }
        }
        if (!$userPermissions) {
            if (RightsService::isSuperAdmin()) {
                $this->displayEmpty(__('Permissions de cet opérateur'), __('Aucune permission trouvée pour cet opérateur.'), Tools::makeLink('admin', 'support', 'user/permission/add/' . $userId), __('Ajouter une permission'));
            } else {
                $this->displayEmpty(__('Permissions de cet opérateur'), __('Aucune permission trouvée pour cet opérateur.'));
            }
            return;
        }

        $output = $this->get(SupportUserPermissionsViewHelper::class)->getContent($user, $userPermissions);
        $this->displayOutput($output);
    }

    #[Route('/admin/support/user/permission/add/{userId}/', name: 'admin_support_user_permission_add', requirements: ['userId' => Requirement::POSITIVE_INT])]
    public function supportUserAddPermissions(int $userId)
    {
        $this->view->setTitle(__('Ajout d\'une permission'));
        $this->view->addBreadcrumbItem(__('Support'), Tools::makeLink('admin', 'support'));
        $this->view->addBreadcrumbItem( __('Opérateurs'), Tools::makeLink('admin', 'support', 'users'));
        $this->denyAccessUnlessGranted(ATTRIBUTE_EDIT, UNIVERSE_ADMIN_SAV);

        if (!RightsService::isSuperAdmin() and $_SESSION['user']['id'] != $userId) {
            $this->displayError(__('Cet utilisateur n\'existe pas.'));
            return;
        }

        $user = $this->get(SupportUserService::class)->getUserById($userId);
        if (!$user) {
            $this->displayError(__('Cet utilisateur n\'existe pas.'));
            return;
        }

        $userPermissionForm = new SupportUserPermissionFormFactory($user);
        if ($userPermissionForm->isSubmitted() and $userPermissionForm->isValid()) {
            $process = $userPermissionForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('admin_support_user_permissions_edit', ['userId' => $userId]);
            }
        }
        $this->displayOutput($this->builderForm->render($userPermissionForm));
    }

    #[Route('/admin/support/user/permission/edit/{userId}/{idPermission}/', name: 'admin_support_user_permission_edit', requirements: ['userId' => Requirement::POSITIVE_INT, 'idPermission' => Requirement::POSITIVE_INT])]
    public function supportUserEditPermissions(int $userId, int $idPermission)
    {
        $this->view->setTitle(__('Modification d\'une permission'));
        $this->view->addBreadcrumbItem(__('Support'), Tools::makeLink('admin', 'support'));
        $this->view->addBreadcrumbItem( __('Opérateurs'), Tools::makeLink('admin', 'support', 'users'));
        $this->denyAccessUnlessGranted(ATTRIBUTE_EDIT, UNIVERSE_ADMIN_SAV);

        $permission = $this->get(SupportUserService::class)->getRepository()->find($idPermission);
        if (!$permission) {
            $this->displayError(__('Cette permission n\'existe pas.'), Tools::makeLink('admin', 'support', 'user/permissions/' . $userId));
            return;
        }

        if (!RightsService::isSuperAdmin() and $_SESSION['user']['id'] != $permission->getUser()->getId()) {
            $this->displayError(__('Cet utilisateur n\'existe pas.'));
            return;
        }

        $userPermissionForm = new SupportUserPermissionFormFactory();
        if ($userPermissionForm->isSubmitted() and $userPermissionForm->isValid()) {
            $process = $userPermissionForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('admin_support_user_permissions_edit', ['userId' => $permission->getUser()->getId()]);
            }
        }
        $this->displayOutput($this->builderForm->render($userPermissionForm, $permission));
    }

    #[Route('/admin/support/user/permission/delete/{userId}/{idPermission}/', name: 'admin_support_user_permission_delete', requirements: ['userId' => Requirement::POSITIVE_INT, 'idPermission' => Requirement::POSITIVE_INT])]
    public function supportUserDeletePermission(int $userId, int $idPermission)
    {
        $this->denyAccessUnlessGranted(ATTRIBUTE_DELETE, UNIVERSE_ADMIN_SAV);

        $permission = $this->get(SupportUserService::class)->getRepository()->find($idPermission);
        if (!$permission) {
            $this->displayErrorAndRedirect(__('Cette permission n\'existe pas.'), Tools::makeLink('admin', 'support', 'user/permissions/' . $userId));
        }

        if (!RightsService::isSuperAdmin() and $_SESSION['user']['id'] != $permission->getUser()->getId()) {
            $this->displayErrorAndRedirect(__('Cette permission n\'existe pas.'), Tools::makeLink('admin', 'support', 'users'));
        }

        $delete = $this->get(SupportUserPermissionForm::class)->deleteUserPermissions($idPermission);
        if (!$delete['valid']) {
            $this->displayErrorAndRedirect(__('Cette permission n\'existe pas.'), Tools::makeLink('admin', 'support', 'user/permissions/' . $userId));
        }

        $this->displaySuccessAndRedirect(__("La permission de cet administrateur a bien été supprimée."), Tools::makeLink('admin', 'support', 'user/permissions/' . $userId));
    }
}
