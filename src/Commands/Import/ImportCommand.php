<?php

namespace MatGyver\Commands\Import;

use Mat<PERSON><PERSON><PERSON>\Commands\AbstractCommand;
use <PERSON><PERSON><PERSON>ver\Entity\Import\Import;
use MatGyver\Entity\Import\ImportData;
use MatGyver\Services\Clients\ClientsService;
use MatGyver\Services\Import\ImportDataService;
use MatGyver\Services\Import\ImportService;

/**
 * Class ImportCommand
 * @package MatGyver\Commands\Import
 */
class ImportCommand extends AbstractCommand
{
    private ClientsService $clientsService;
    private ImportService $importService;
    private ImportDataService $importDataService;

    /**
     * ImportCommand constructor.
     * @param ClientsService $clientsService
     * @param ImportService $importService
     * @param ImportDataService $importDataService
     */
    public function __construct(
        ClientsService $clientsService,
        ImportService $importService,
        ImportDataService $importDataService
    ) {
        $this->clientsService = $clientsService;
        $this->importService = $importService;
        $this->importDataService = $importDataService;
    }

    public function execute()
    {
        $importId = $this->param;
        /** @var Import $import */
        $import = $this->importService->getRepository()->findWoClient($importId);
        if (!$import) {
            return ['valid' => false, 'message' => __('Cette importation n\'existe pas.')];
        }
        if ($import->getStatus() != Import::STATUS_PENDING) {
            return ['valid' => false, 'message' => __('Cette importation est déjà en cours de traitement.')];
        }

        $import->setStatus(Import::STATUS_PROCESSING);
        try {
            $this->importService->persistAndFlush($import);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Une erreur est survenue lors de la mise à jour du statut de l\'importation.')];
        }

        //login as client
        $client = $import->getClient();
        $this->clientsService->setClientInSession($client);

        //get import data
        $importData = $this->importDataService->getRepository()->findBy(['import' => $import, 'status' => ImportData::STATUS_PENDING]);
        if (!$importData) {
            $this->importService->complete($import);
            return ['valid' => true, 'log' => ''];
        }

        //process data
        $nbData = count($importData);
        $nbDataProcessed = 0;
        $nbDataInError = 0;
        $errorLog = '';

        foreach ($importData as $item) {
            $process = $this->importDataService->process($item);
            if (!$process['valid']) {
                $nbDataInError++;
                $errorLog .= $process['message'] . "\n";
            } else {
                $nbDataProcessed++;
            }
        }

        $log = '<p>' . __('Nombre de contacts à importer : %s', $nbData) . '<br>';
        $log .= __('Nombre de contacts importés : %s', $nbDataProcessed) . '<br>';
        if ($nbDataInError) {
            $log .= __('Nombre de contacts en erreur : %s', $nbDataInError) . '<br>';
        }
        $log .= '</p>';
        if ($errorLog) {
            $log .= '<p>' . __('Certaines erreurs sont survenues lors du traitement : ') . '<br>' . $errorLog . '</p>';
        }

        $this->importService->complete($import, Import::STATUS_PROCESSED, $log);

        return ['valid' => true, 'log' => $log];
    }
}
