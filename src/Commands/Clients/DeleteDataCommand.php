<?php

namespace MatG<PERSON>ver\Commands\Clients;

use MatGyver\Commands\AbstractCommand;
use MatGyver\Services\Clients\ClientsRelancesService;

/**
 * Class DeleteDataCommand
 * @package MatGyver\Commands\Clients
 */
class DeleteDataCommand extends AbstractCommand
{
    /**
     * @var ClientsRelancesService
     */
    private $clientsRelancesService;

    /**
     * DeleteDataCommand constructor.
     * @param ClientsRelancesService $clientsRelancesService
     */
    public function __construct(ClientsRelancesService $clientsRelancesService)
    {
        $this->clientsRelancesService = $clientsRelancesService;
    }

    public function execute()
    {
        return $this->clientsRelancesService->deleteOldData();
    }
}
