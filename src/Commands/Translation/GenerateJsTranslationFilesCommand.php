<?php

namespace MatGyver\Commands\Translation;

use Gettext\Generator\PoGenerator;
use Gettext\Loader\PoLoader;
use Gettext\Merge;
use MatGyver\Commands\AbstractCommand;
use MatGyver\Services\TranslationsService;

/**
 * Class GenerateJsTranslationFilesCommand
 * @package MatGyver\Commands\Translation
 */
class GenerateJsTranslationFilesCommand extends AbstractCommand
{
    /**
     * @var TranslationsService
     */
    protected $translationsService;

    /**
     * GeneratePotTranslationFileCommand constructor.
     * @param TranslationsService $translationsService
     */
    public function __construct(TranslationsService $translationsService)
    {
        $this->translationsService = $translationsService;
    }

    public function execute()
    {
        // Fix PHP error Allowed memory size exhausted
        ini_set('memory_limit','256M');

        foreach ($this->translationsService->getLanguages() as $language) {
            if ('fr_FR' == $language) {
                continue;
            }

            $allPoFilePath = $this->translationsService->getPoFilePath($language);
            if (!is_file($allPoFilePath)) {
                echo ".json file " . $language . " \e[31mKO : .po file " . $allPoFilePath . " not found.\e[0m\n";
                continue;
            }

            $jsPoFilePath = $this->translationsService->getPoFilePath($language, 'js');
            if (!is_file($jsPoFilePath)) {
                echo ".json file " . $language . " \e[31mKO : .po file " . $jsPoFilePath . " not found.\e[0m\n";
                continue;
            }

            try {
                $loader = new PoLoader();

                $allTranslations = $loader->loadFile($allPoFilePath);
                $jsTranslations = $loader->loadFile($jsPoFilePath);

                $allTranslations = $allTranslations->mergeWith($jsTranslations, Merge::TRANSLATIONS_THEIRS);
                $generator = new PoGenerator();
                $generator->generateFile($allTranslations, $jsPoFilePath);

                $jsTranslations = json_encode($allTranslations->getTranslations());
                $jsFile = fopen($this->translationsService->getJsonFilePath($language), 'w');
                fwrite($jsFile, $jsTranslations);
                fclose($jsFile);
            } catch (\Exception $e) {
                echo ".json file " . $language . " \e[31mKO : ". $e->getMessage() ."\e[0m\n";
                continue;
            }

            echo ".json file " . $language . " \e[32mOK\e[0m\n";
        }

        $this->translationsService->generateJsScripts();
    }
}
