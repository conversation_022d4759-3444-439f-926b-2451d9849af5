<?php

namespace MatGyver\Commands;

use Mat<PERSON><PERSON><PERSON>\Helpers\Encryption;
use Mat<PERSON><PERSON><PERSON>\Helpers\Tools;
use Mat<PERSON><PERSON>ver\Services\ChromeService;
use MatGyver\Services\Mail\MailHistoryService;

/**
 * Class MailHistoriesPreviewsCommand
 * @package MatGyver\Commands
 */
class MailHistoriesPreviewsCommand extends AbstractCommand
{
    private ChromeService $chromeService;
    private MailHistoryService $mailHistoryService;

    /**
     * MailHistoriesPreviewsCommand constructor.
     * @param ChromeService $chromeService
     * @param MailHistoryService $mailHistoryService
     */
    public function __construct(
        ChromeService $chromeService,
        MailHistoryService $mailHistoryService
    ) {
        $this->chromeService = $chromeService;
        $this->mailHistoryService = $mailHistoryService;
    }

    public function execute()
    {
        $mailHistories = $this->mailHistoryService->getRepository()->findBy([], ['id' => 'ASC'], null, null, false);
        if (!$mailHistories) {
            return ['valid' => false, 'message' => __('This mail history does not exist.')];
        }
        foreach ($mailHistories as $mailHistory) {
            if (!$mailHistory->getDossier()) {
                continue;
            }
            if ($mailHistory->getPreview()) {
                continue;
            }
            echo "Mail history : " . $mailHistory->getId() . "\n";

            $token = Encryption::encrypt('id_mail=' . $mailHistory->getId());
            $url = Tools::makeLink('site', 'mail', $token);
            $fileName = 'mail-history-' . $mailHistory->getId() . '.pdf';
            try {
                $generatePdf = $this->chromeService->setExtension('pdf')
                    ->generatePdf($url, ['displayHeaderFooter' => false]);
                if (!$generatePdf['valid']) {
                    return $generatePdf;
                }

                $content = $generatePdf['content'];
                try {
                    $fp = fopen(WEB_PATH . '/medias/' . $mailHistory->getDossier()->getFolder() . $fileName, 'w');
                } catch (\Exception $e) {
                    @exec('chmod 666 ' . WEB_PATH . '/medias/' . $mailHistory->getDossier()->getFolder() . $fileName);
                    $fp = fopen(WEB_PATH . '/medias/' . $mailHistory->getDossier()->getFolder() . $fileName, 'w');
                }
                if (!$fp) {
                    return ['valid' => false, 'message' => __('Impossible de créer le fichier.')];
                }

                fwrite($fp, $content);
                fclose($fp);
            } catch (\Exception $e) {
                echo "Exception : " . $e->getMessage() . "\n";
                continue;
            }

            $mailHistory->setPreview($fileName);
            try {
                $this->mailHistoryService->persistAndFlush($mailHistory);
            } catch (\Exception $e) {
                echo "Exception : " . $e->getMessage() . "\n";
                continue;
            }
        }

        echo "all done\n";
        return ['valid' => true];
    }
}
