<?php

namespace MatGyver\Commands\Subscriptions;

use MatGyver\Commands\AbstractCommand;
use MatGyver\Services\Subscription\StripeSubscriptionService;

/**
 * Class StripeSubscriptionCommand
 * @package MatGyver\Commands\Subscriptions
 */
class StripeSubscriptionCommand extends AbstractCommand
{
    /**
     * @var StripeSubscriptionService
     */
    private $stripeSubscriptionService;

    /**
     * StripeSubscriptionCommand constructor.
     * @param StripeSubscriptionService $stripeSubscriptionService
     */
    public function __construct(StripeSubscriptionService $stripeSubscriptionService)
    {
        $this->stripeSubscriptionService = $stripeSubscriptionService;
    }

    public function execute()
    {
        return $this->stripeSubscriptionService->processSubscriptions();
    }
}
