<?php

namespace MatGyver\Commands\Dossier;

use Mat<PERSON><PERSON>ver\Commands\AbstractCommand;
use Mat<PERSON><PERSON>ver\Entity\Dossier\DossierDocument;
use MatGyver\Services\Dossier\DossierDocumentService;
use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\ThumbnailService;

/**
 * Class DossierDocumentsResizeCommand
 * @package MatGyver\Commands\Dossier
 */
class DossierDocumentResizeCommand extends AbstractCommand
{
    private DossierDocumentService $dossierDocumentService;

    /**
     * DossierDocumentsResizeCommand constructor.
     * @param DossierDocumentService $dossierDocumentService
     */
    public function __construct(
        DossierDocumentService $dossierDocumentService
    ) {
        $this->dossierDocumentService = $dossierDocumentService;
    }

    public function execute()
    {
        $documentId = filter_var($this->param, FILTER_VALIDATE_INT);
        if (!$documentId) {
            return ['valid' => false, 'message' => 'No documentId provided'];
        }

        /** @var DossierDocument $document */
        $document = $this->dossierDocumentService->getRepository()->findWoClient($documentId);
        if (!$document) {
            return ['valid' => false, 'message' => __('Document introuvable.')];
        }

        $path = WEB_PATH . '/medias/' . $document->getDossier()->getFolder();
        if (!is_dir($path)) {
            return ['valid' => false, 'message' => __('Le dossier %s n\'existe pas.', $path)];
        }

        $filePath = $document->getFullPath();

        $extension = getExtension($filePath);
        if (!in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
            return ['valid' => true];
        }

        ThumbnailService::fixOrientation($filePath);

        list($width, $height) = getimagesize($filePath);
        if ($width > 1600) {
            try {
                ThumbnailService::resizeToWidth($filePath, $filePath, 1600);
            } catch (\Exception $e) {
                LoggerService::logError('Unable to resize image : ' . $e->getMessage());
            }
        } elseif ($height > 1000) {
            try {
                ThumbnailService::resizeToHeight($filePath, $filePath, 1000);
            } catch (\Exception $e) {
                LoggerService::logError('Unable to resize image : ' . $e->getMessage());
            }
        }

        return ['valid' => true];
    }
}
