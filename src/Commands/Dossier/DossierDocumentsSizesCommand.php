<?php

namespace Mat<PERSON><PERSON><PERSON>\Commands\Dossier;

use Mat<PERSON>yver\Commands\AbstractCommand;
use MatGyver\Services\Dossier\DossierService;

/**
 * Class DossierDocumentsResizeCommand
 * @package MatGyver\Commands\Dossier
 */
class DossierDocumentsSizesCommand extends AbstractCommand
{
    private DossierService $dossierService;

    /**
     * DossierDocumentsResizeCommand constructor.
     * @param DossierService $dossierService
     */
    public function __construct(
        DossierService $dossierService
    ) {
        $this->dossierService = $dossierService;
    }

    public function execute()
    {
        $dossiers = $this->dossierService->getRepository()->findBy([], ['id' => 'DESC'], null, null, false);
        if (!$dossiers) {
            return ['valid' => false, 'message' => __('Dossiers introuvable.')];
        }

        $nbFiles = 0;
        foreach ($dossiers as $dossier) {
            $path = WEB_PATH . '/medias/' . $dossier->getFolder();
            if (!is_dir($path)) {
                return ['valid' => false, 'message' => __('Aucun média trouvé dans ce dossier.')];
            }
            echo "analyze path $path\n";

            /** @var \SplFileInfo[] $files */
            $files = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($path),
                \RecursiveIteratorIterator::LEAVES_ONLY
            );
            echo count($files) . " files\n";
            foreach ($files as $name => $file) {
                if ($file->isDir()) {
                    continue;
                }

                $extension = getExtension($file->getFilename());
                if (!in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
                    continue;
                }

                $filePath = $file->getRealPath();

                //get file size
                $size = filesize($filePath);

                //alert if size > 10M
                if ($size > 10000000 and !str_contains($filePath, '-temp') and !str_contains($filePath, '-thumbnail')) {
                    echo "file too big: " . $filePath . " : " . formatSizeUnits($size) . "\n";
                    $nbFiles++;
                }

                if ($nbFiles > 10) {
                    break;
                }
            }
        }

        return ['valid' => true];
    }
}
