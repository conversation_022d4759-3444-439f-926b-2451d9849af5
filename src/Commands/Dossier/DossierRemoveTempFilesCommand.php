<?php

namespace MatGyver\Commands\Dossier;

use Mat<PERSON>yver\Commands\AbstractCommand;
use MatGyver\Helpers\ImagickHelper;
use MatGyver\Services\Dossier\DossierService;

/**
 * Class DossierRemoveTempFilesCommand
 * @package MatGyver\Commands\Dossier
 */
class DossierRemoveTempFilesCommand extends AbstractCommand
{
    private DossierService $dossierService;

    /**
     * DossierRemoveTempFilesCommand constructor.
     * @param DossierService $dossierService
     */
    public function __construct(DossierService $dossierService)
    {
        $this->dossierService = $dossierService;
    }

    public function execute()
    {
        $dossiers = $this->dossierService->getRepository()->findBy([], ['id' => 'DESC'], null, null, false);
        if (!$dossiers) {
            return ['valid' => false, 'message' => __('Dossiers introuvable.')];
        }

        $log = '';
        foreach ($dossiers as $dossier) {
            $path = WEB_PATH . '/medias/' . $dossier->getFolder();
            if (!is_dir($path)) {
                $log .= __('Aucun média trouvé dans ce dossier.') . PHP_EOL;
                continue;
            }
            $log .= "analyze path $path" . PHP_EOL;

            /** @var \SplFileInfo[] $files */
            $files = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($path),
                \RecursiveIteratorIterator::LEAVES_ONLY
            );

            foreach ($files as $file) {
                if ($file->isDir()) {
                    continue;
                }

                $filePath = $file->getRealPath();

                if (str_contains($filePath, ImagickHelper::TEMP_SUFFIX)) {
                    $log .= "remove file " . $filePath . PHP_EOL;
                    @unlink($filePath);
                }
            }
        }

        return ['valid' => true, 'log' => $log];
    }
}
