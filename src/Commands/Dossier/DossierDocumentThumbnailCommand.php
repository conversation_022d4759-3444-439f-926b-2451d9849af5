<?php

namespace Mat<PERSON><PERSON>ver\Commands\Dossier;

use Mat<PERSON><PERSON><PERSON>\Commands\AbstractCommand;
use Mat<PERSON><PERSON><PERSON>\Entity\Dossier\DossierDocument;
use MatGyver\Helpers\ImagickHelper;
use MatGyver\Services\Dossier\DossierDocumentService;
use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\ThumbnailService;

/**
 * Class DossierDocumentsThumbnailCommand
 * @package MatGyver\Commands\Dossier
 */
class DossierDocumentThumbnailCommand extends AbstractCommand
{
    private DossierDocumentService $dossierDocumentService;

    /**
     * DossierDocumentsThumbnailsCommand constructor.
     * @param DossierDocumentService $dossierDocumentService
     */
    public function __construct(
        DossierDocumentService $dossierDocumentService
    ) {
        $this->dossierDocumentService = $dossierDocumentService;
    }

    public function execute()
    {
        $documentId = filter_var($this->param, FILTER_VALIDATE_INT);
        if (!$documentId) {
            return ['valid' => false, 'message' => 'No documentId provided'];
        }

        /** @var DossierDocument $document */
        $document = $this->dossierDocumentService->getRepository()->findWoClient($documentId);
        if (!$document) {
            return ['valid' => false, 'message' => __('Document introuvable.')];
        }

        $path = WEB_PATH . '/medias/' . $document->getDossier()->getFolder();
        if (!is_dir($path)) {
            return ['valid' => false, 'message' => __('Le dossier %s n\'existe pas.', $path)];
        }

        $dossier = $document->getDossier();

        $fullPath = WEB_PATH . '/medias/' . $dossier->getFolder();
        if (!file_exists($fullPath . $document->getFile())) {
            LoggerService::logError("Unable to find document " . $document->getFile() . " in " . $fullPath);
            $this->setDocumentThumbnailInError($document);
            return ['valid' => false, 'message' => 'Unable to find document'];
        }

        $pathInfo = pathinfo($document->getFile());
        $fileName = $pathInfo['filename'] . ImagickHelper::THUMBNAIL_SUFFIX;
        $extension = getExtension($document->getFile());
        if ($extension == 'pdf') {
            $extension = 'jpg';
            $images = ImagickHelper::convertPdf($fullPath . $document->getFile(), true, true, false, true);
            if (!$images) {
                LoggerService::logError("Unable to convert document " . $document->getFile());
                $this->setDocumentThumbnailInError($document);
                return ['valid' => false, 'message' => 'Unable to convert document'];
            }

            $pathInfo = pathinfo($images[0]);
            $fileName = $pathInfo['filename'];
        } elseif (in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
            ThumbnailService::fixOrientation($fullPath . $document->getFile());

            //resize image if necessary
            list($width, $height) = getimagesize($fullPath . $document->getFile());
            if ($width > 2000) {
                try {
                    ThumbnailService::resizeToWidth($fullPath . $document->getFile(), $fullPath . $document->getFile(), 2000);
                } catch (\Exception $e) {
                    LoggerService::logError('Unable to resize image : ' . $e->getMessage());
                }
            } elseif ($height > 1000) {
                try {
                    ThumbnailService::resizeToHeight($fullPath . $document->getFile(), $fullPath . $document->getFile(), 1000);
                } catch (\Exception $e) {
                    LoggerService::logError('Unable to resize image : ' . $e->getMessage());
                }
            }

            //generate thumbnail
            try {
                ThumbnailService::resizeToHeight($fullPath . $document->getFile(), $fullPath . $fileName . '.' . $extension, 200);
            } catch (\Exception $e) {
                LoggerService::logError('Unable to generate thumbnail : ' . $e->getMessage());
                $this->setDocumentThumbnailInError($document);
                return ['valid' => false, 'message' => 'Unable to generate thumbnail'];
            }
        } else {
            $this->setDocumentThumbnailInError($document);
            return ['valid' => true];
        }

        $document->setThumbnail($fileName . '.' . $extension);
        try {
            $this->dossierDocumentService->persistAndFlush($document);
        } catch (\Exception $e) {
            LoggerService::logError("Unable to update document " . $document->getFile() . " : " . $e->getMessage());
        }

        return ['valid' => true];
    }

    /**
     * @param DossierDocument $document
     * @return void
     */
    private function setDocumentThumbnailInError(DossierDocument $document): void
    {
        $document->setThumbnail('error');
        try {
            $this->dossierDocumentService->persistAndFlush($document);
        } catch (\Exception $e) {
            LoggerService::logError("Unable to update document " . $document->getFile() . " : " . $e->getMessage());
        }
    }
}
