<?php

namespace MatGyver\Commands\Dossier;

use MatGyver\Commands\AbstractCommand;
use MatGyver\Services\Dossier\DossierDocumentService;
use MatGyver\Services\Dossier\DossierService;
use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\ThumbnailService;

/**
 * Class DossierDocumentsResizeCommand
 * @package MatGyver\Commands\Dossier
 */
class DossierDocumentsResizeCommand extends AbstractCommand
{
    private DossierService $dossierService;
    private DossierDocumentService $dossierDocumentService;

    /**
     * DossierDocumentsResizeCommand constructor.
     * @param DossierService $dossierService
     * @param DossierDocumentService $dossierDocumentService
     */
    public function __construct(
        DossierService $dossierService,
        DossierDocumentService $dossierDocumentService
    ) {
        $this->dossierService = $dossierService;
        $this->dossierDocumentService = $dossierDocumentService;
    }

    public function execute()
    {
        $dossier = $this->dossierService->getRepository()->findWoClient(501);
        if (!$dossier) {
            return ['valid' => false, 'message' => __('Dossier introuvable.')];
        }

        $path = WEB_PATH . '/medias/' . $dossier->getFolder();
        if (!is_dir($path)) {
            return ['valid' => false, 'message' => __('Aucun média trouvé dans ce dossier.')];
        }

        /** @var \SplFileInfo[] $files */
        $files = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($path),
            \RecursiveIteratorIterator::LEAVES_ONLY
        );
        foreach ($files as $name => $file) {
            if ($file->isDir()) {
                continue;
            }

            $extension = getExtension($file->getFilename());
            if (!in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
                continue;
            }

            $filePath = $file->getRealPath();

            list($width, $height) = getimagesize($filePath);
            echo $width . 'x' . $height . "\n";

            if ($width > 2000) {
                try {
                    ThumbnailService::resizeToWidth($filePath, $filePath, 2000);
                } catch (\Exception $e) {
                    LoggerService::logError('Unable to resize image : ' . $e->getMessage());
                    continue;
                }
            } elseif ($height > 1000) {
                try {
                    ThumbnailService::resizeToHeight($filePath, $filePath, 1000);
                } catch (\Exception $e) {
                    LoggerService::logError('Unable to resize image : ' . $e->getMessage());
                    continue;
                }
            }
        }

        return ['valid' => true];
    }
}
