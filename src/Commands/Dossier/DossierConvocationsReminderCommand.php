<?php

namespace Mat<PERSON><PERSON><PERSON>\Commands\Dossier;

use Mat<PERSON><PERSON><PERSON>\Commands\AbstractCommand;
use Mat<PERSON><PERSON><PERSON>\Components\Mailer\MailSender;
use <PERSON><PERSON><PERSON><PERSON>\Entity\Dossier\Dossier;
use Mat<PERSON><PERSON>ver\Entity\Dossier\DossierHistory;
use MatGyver\Enums\UserEmailNotificationTypeEnum;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Dossier\DossierHistoryService;
use MatGyver\Services\Dossier\DossierService;

class DossierConvocationsReminderCommand extends AbstractCommand
{
    /**
     * @var DossierService
     */
    private $dossierService;

    /**
     * @var DossierHistoryService
     */
    private $dossierHistoryService;

    /**
     * @var MailSender
     */
    private $mailSender;

    /**
     * DossierReminderCommand constructor.
     * @param DossierService $dossierService
     * @param DossierHistoryService $dossierHistoryService
     * @param MailSender $mailSender
     */
    public function __construct(
        DossierService $dossierService,
        DossierHistoryService $dossierHistoryService,
        MailSender $mailSender,
    ) {
        $this->dossierService = $dossierService;
        $this->dossierHistoryService = $dossierHistoryService;
        $this->mailSender = $mailSender;
    }

    public function execute()
    {
        $date7days = new \DateTime('-7 days');

        $dossiers = $this->dossierService->getRepository()->findBy(['status' => Dossier::STATUS_CONVOCATION_TO_BE_MADE], null, null, null, false);
        if (!$dossiers) {
            return ['valid' => true];
        }

        $log = '';
        $valid = true;
        foreach ($dossiers as $dossier) {
            $dossierHistory = $this->dossierHistoryService->getRepository()->findOneBy(['dossier' => $dossier, 'client' => $dossier->getClient(), 'action' => DossierHistory::ACTION_UPDATE_STATUS, 'param' => Dossier::STATUS_CONVOCATION_TO_BE_MADE], ['id' => 'DESC'], false);
            if (!$dossierHistory) {
                continue;
            }
            $date = $dossierHistory->getDate();
            if ($date->format('Y-m-d') != $date7days->format('Y-m-d')) {
                continue;
            }

            $log .= "process dossier " . $dossier->getId() . "\n";

            $vars = [
                'SUBJECT_DOSSIER' => $dossier->getSubject(),
                'DOSSIER' => $dossier->getContact()->getFirstName() . ' ' . $dossier->getContact()->getLastName(),
                'DOSSIER_LINK' => Tools::makeLink('app', 'dossier', $dossier->getId()),
                'VEHICLE_REGISTRATION' => ($dossier->getVehicle() ? $dossier->getVehicle()->getRegistration() : ''),
                'DOSSIER_STATUS' => $dossier->getStatusName(),
                'DATE' => $dossierHistory->getDate()->format('d/m/Y'),
            ];
            if ($dossier->getStatusMessage()) {
                $vars['DOSSIER_STATUS'] .= ' (' . $dossier->getStatusMessage() . ')';
            }

            $dossierUsers = $dossier->getUsers();
            $mainAdmin = $dossier->getClient()->getMainAdmin();
            $sendToMainAdmin = false;
            if (count($dossierUsers)) {
                foreach ($dossierUsers as $dossierUser) {
                    if ($dossierUser->getUser() === $mainAdmin) {
                        $sendToMainAdmin = true;
                    }

                    $recipient = [['user_id' => $dossierUser->getUser()->getId()]];
                    $send = $this->mailSender->sendTemplateToClient('dossier_convocations_alert', $vars, UserEmailNotificationTypeEnum::IMPORTANT, $recipient, $dossier->getClient()->getId());
                    if (!$send['valid']) {
                        $valid = false;
                        $log .= $send['message'];
                    }
                }
            }

            if (!$sendToMainAdmin) {
                $recipient = [['user_id' => $mainAdmin->getId()]];
                $send = $this->mailSender->sendTemplateToClient('dossier_convocations_alert', $vars, UserEmailNotificationTypeEnum::IMPORTANT, $recipient, $dossier->getClient()->getId());
                if (!$send['valid']) {
                    $valid = false;
                    $log .= $send['message'];
                }
            }
        }

        return ['valid' => $valid, 'log' => $log];
    }
}
