<?php

namespace MatGyver\Commands\Dossier;

use Mat<PERSON>yver\Commands\AbstractCommand;
use MatGyver\Entity\Dossier\Dossier;
use MatGyver\Services\Dossier\DossierService;

/**
 * Class DossierUnCompressCommand
 * @package MatGyver\Commands\Dossier
 */
class DossierUnCompressCommand extends AbstractCommand
{
    /**
     * @var DossierService
     */
    private $dossierService;

    /**
     * DossierUnCompressCommand constructor.
     * @param DossierService $dossierService
     */
    public function __construct(DossierService $dossierService)
    {
        $this->dossierService = $dossierService;
    }

    public function execute()
    {
        $dossierId = filter_var($this->param, FILTER_VALIDATE_INT);
        if (!$dossierId) {
            echo "Invalid dossier id\n";
            exit();
        }

        /** @var Dossier $dossier */
        $dossier = $this->dossierService->getRepository()->findOneBy(['id' => $dossierId, 'compressed' => true], null, false);
        if (!$dossier) {
            echo "Unable to find dossier $dossierId\n";
            exit();
        }

        echo "uncompress dossier " . $dossier->getId() . " : " . $dossier->getReference() . "\n";

        $unCompress = $this->dossierService->unCompress($dossier);
        if (!$unCompress) {
            echo "!!! Unable to uncompress dossier\n";
            exit();
        }
        echo "uncompress dossier : DONE\n";
    }
}
