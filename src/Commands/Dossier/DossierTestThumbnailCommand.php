<?php

namespace MatGyver\Commands\Dossier;

use Mat<PERSON>yver\Commands\AbstractCommand;
use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\ThumbnailService;

/**
 * Class DossierTestThumbnailCommand
 * @package MatGyver\Commands\Dossier
 */
class DossierTestThumbnailCommand extends AbstractCommand
{
    public function execute()
    {
        $time = time();
        $dir = '/var/www/app/web/medias/645a90e42ce4f864344643/dossiers/67da91648f1af683725413/';
        $pdf = $dir . '67dad167e43cc0.82811145.pdf';
        $jpeg = '67dad167e43cc0.82811145-test';
        $png = '67dad167e43cc0.82811145-test';
        try {
            $imagick = new \Imagick();
            $imagick->setCompressionQuality(100);
            $imagick->setResolution(144, 144);
            $imagick->readImage($pdf);
            $imagick->setImageFormat("png");
            $imagick->setBackgroundColor('white');
            $imagick->setImageBackgroundColor('#ffffff');
            $imagick->setImageAlphaChannel(\Imagick::ALPHACHANNEL_REMOVE);
            $nbPages = $imagick->getNumberImages();
            if ($nbPages == 1) {
                $imagick->writeImage($dir . $png . '.png');
                $this->resize($dir . $png . '.png');
            } else {
                foreach ($imagick as $i => $page) {
                    $j = $i;
                    $j++;
                    $imagick->writeImage($dir . $png . '-' . $i . '.png');
                    $this->resize($dir . $png . '-' . $i . '.png');
                }
            }
            $imagick->clear();


        } catch (\Exception $e) {
            echo $e->getMessage();
        }

        echo "time : " . (time() - $time) . "\n";
        $time = time();

        try {
            $imagick = new \Imagick();
            $imagick->setCompressionQuality(100);
            $imagick->setResolution(144, 144);
            $imagick->readImage($pdf . '[0]');
            $imagick->setImageFormat('jpeg');
            $imagick->setBackgroundColor('white');
            $imagick->setImageBackgroundColor('#ffffff');
            $imagick->setImageAlphaChannel(\Imagick::ALPHACHANNEL_REMOVE);
            $imagick->writeImage($dir . $jpeg . '.jpg');
            $imagick->clear();
            $this->resize($dir . $jpeg . '.jpg');
        } catch (\Exception $e) {
            echo $e->getMessage();
        }

        echo "time : " . (time() - $time) . "\n";
        echo "done";
    }

    public function resize(string $img)
    {
        echo "resize " . $img . "\n";
        //resize image if necessary
        list($width, $height) = getimagesize($img);
        echo $width . 'x' . $height."\n";
        if ($width > 2000) {
            try {
                echo "resize to width\n";
                ThumbnailService::resizeToWidth($img, $img, 2000);
            } catch (\Exception $e) {
                LoggerService::logError('Unable to resize image : ' . $e->getMessage());
            }
        } elseif ($height > 1000) {
            try {
                echo "resize to height\n";
                ThumbnailService::resizeToHeight($img, $img, 1000);
            } catch (\Exception $e) {
                LoggerService::logError('Unable to resize image : ' . $e->getMessage());
            }
        }
    }
}
