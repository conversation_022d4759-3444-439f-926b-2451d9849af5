<?php

namespace Mat<PERSON>yver\Commands\Dossier;

use Mat<PERSON><PERSON>ver\Commands\AbstractCommand;
use Mat<PERSON>yver\Entity\Dossier\Expertise\DossierExpertisePicture;
use MatGyver\Services\Dossier\Expertise\DossierExpertisePictureService;
use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\ThumbnailService;

/**
 * Class DossierPictureResizeCommand
 * @package MatGyver\Commands\Dossier
 */
class DossierPictureResizeCommand extends AbstractCommand
{
    private DossierExpertisePictureService $dossierExpertisePictureService;

    /**
     * DossierPictureResizeCommand constructor.
     * @param DossierExpertisePictureService $dossierExpertisePictureService
     */
    public function __construct(
        DossierExpertisePictureService $dossierExpertisePictureService
    ) {
        $this->dossierExpertisePictureService = $dossierExpertisePictureService;
    }

    public function execute()
    {
        $pictureId = filter_var($this->param, FILTER_VALIDATE_INT);
        if (!$pictureId) {
            return ['valid' => false, 'message' => 'No pictureId provided'];
        }

        /** @var DossierExpertisePicture $picture */
        $picture = $this->dossierExpertisePictureService->getRepository()->findWoClient($pictureId);
        if (!$picture) {
            return ['valid' => false, 'message' => __('Photographie introuvable.')];
        }

        $path = WEB_PATH . '/medias/' . $picture->getDossier()->getFolder();
        if (!is_dir($path)) {
            return ['valid' => false, 'message' => __('Le dossier %s n\'existe pas.', $path)];
        }

        $filePath = $picture->getFullPath();

        $extension = getExtension($filePath);
        if (!in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
            return ['valid' => true];
        }

        ThumbnailService::fixOrientation($filePath);

        list($width, $height) = getimagesize($filePath);
        if ($width > 2000) {
            try {
                ThumbnailService::resizeToWidth($filePath, $filePath, 2000);
            } catch (\Exception $e) {
                LoggerService::logError('Unable to resize image : ' . $e->getMessage());
            }
        } elseif ($height > 1000) {
            try {
                ThumbnailService::resizeToHeight($filePath, $filePath, 1000);
            } catch (\Exception $e) {
                LoggerService::logError('Unable to resize image : ' . $e->getMessage());
            }
        }

        return ['valid' => true];
    }
}
