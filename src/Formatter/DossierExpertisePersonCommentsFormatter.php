<?php

namespace MatGyver\Formatter;

use Mat<PERSON><PERSON><PERSON>\Entity\Dossier\DossierInstitution;
use <PERSON><PERSON><PERSON><PERSON>\Entity\Dossier\Expertise\DossierExpertise;
use MatGyver\Entity\Dossier\Expertise\DossierExpertisePerson;

class DossierExpertisePersonCommentsFormatter
{
    /**
     * @param DossierExpertise $expertise
     * @param DossierExpertisePerson[] $dossierExpertisePersons
     * @return array
     */
    public static function sortPersons(DossierExpertise $expertise, array $dossierExpertisePersons): array
    {
        if ($expertise->getDossier()->isJudiciaire()) {
            return self::sortJudiciairePersons($dossierExpertisePersons);
        }

        $expertisePersons = [];
        $id = 3;
        foreach ($dossierExpertisePersons as $dossierExpertisePerson) {
            if ($dossierExpertisePerson->getType() == DossierExpertisePerson::TYPE_INSTITUTION) {
                $type = ($dossierExpertisePerson->getInstitution() ? $dossierExpertisePerson->getInstitution()->getType() : '');
                if ($type == DossierInstitution::TYPE_CONTACT) {
                    $expertisePersons[0] = $dossierExpertisePerson;
                } elseif ($type == DossierInstitution::TYPE_MANDATE) {
                    $expertisePersons[1] = $dossierExpertisePerson;
                } elseif ($type == DossierInstitution::TYPE_VEHICLE_OWNER) {
                    $expertisePersons[2] = $dossierExpertisePerson;
                } else {
                    $expertisePersons[$id] = $dossierExpertisePerson;
                    $id++;
                }
            } else {
                $type = $dossierExpertisePerson->getType();
                if ($type == DossierExpertisePerson::TYPE_CONTACT) {
                    $expertisePersons[0] = $dossierExpertisePerson;
                } elseif ($type == DossierExpertisePerson::TYPE_MANDATE) {
                    $expertisePersons[1] = $dossierExpertisePerson;
                } elseif ($type == DossierExpertisePerson::TYPE_VEHICLE_USER) {
                    $expertisePersons[2] = $dossierExpertisePerson;
                } else {
                    $expertisePersons[$id] = $dossierExpertisePerson;
                    $id++;
                }
            }
        }

        ksort($expertisePersons);

        return $expertisePersons;
    }

    /**
     * @param DossierExpertisePerson[] $persons
     * @return array
     */
    public static function sortJudiciairePersons(array $persons): array
    {
        $result = [];
        $persons = arrayCollectionChangeKey($persons);

        //add demandeurs
        $demandeurs = [];
        foreach ($persons as $id => $person) {
            if ($person->getInstitution() and ($person->getInstitution()->isDemandeur() or $person->getInstitution()->getType() == DossierInstitution::TYPE_CONTACT)) {
                if ($person->getParent()) {
                    //children will be added after
                    continue;
                }
                $demandeurs[] = $person;
                unset($persons[$id]);
            }
        }

        //sort demandeurs by defendant number
        usort($demandeurs, function (DossierExpertisePerson $person1, DossierExpertisePerson $person2) {
            return ($person1->getInstitution()->getDefendantNumber() > $person2->getInstitution()->getDefendantNumber() ? 1 : -1);
        });

        foreach ($demandeurs as $demandeur) {
            $result[] = $demandeur;
            if ($demandeur->getChildren()) {
                foreach ($demandeur->getChildren() as $child) {
                    if (isset($persons[$child->getId()])) {
                        $result[] = $child;
                        unset($persons[$child->getId()]);
                    }
                }
            }
        }


        //add defendants
        $defendants = [];
        foreach ($persons as $id => $person) {
            if ($person->getInstitution() and $person->getInstitution()->isDefendant() and !$person->getInstitution()->getParent()) {
                if ($person->getParent()) {
                    //children will be added after
                    continue;
                }
                $defendants[] = $person;
                unset($persons[$id]);
            }
        }

        //sort defendants by defendant number
        usort($defendants, function (DossierExpertisePerson $person1, DossierExpertisePerson $person2) {
            return ($person1->getInstitution()->getDefendantNumber() > $person2->getInstitution()->getDefendantNumber() ? 1 : -1);
        });

        foreach ($defendants as $defendant) {
            $result[] = $defendant;
            if ($defendant->getChildren()) {
                foreach ($defendant->getChildren() as $child) {
                    if (isset($persons[$child->getId()])) {
                        $result[] = $child;
                        unset($persons[$child->getId()]);
                    }
                }
            }
        }

        //add remaining persons
        $result = array_merge($result, $persons);

        return $result;
    }
}
