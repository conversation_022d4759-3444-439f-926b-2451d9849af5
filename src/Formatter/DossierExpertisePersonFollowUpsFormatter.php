<?php

namespace MatGyver\Formatter;

use MatGyver\Entity\Dossier\DossierInstitution;
use <PERSON><PERSON><PERSON>ver\Entity\Dossier\Expertise\DossierExpertise;
use MatGyver\Entity\Dossier\Expertise\DossierExpertisePerson;

class DossierExpertisePersonFollowUpsFormatter
{
    /**
     * @param DossierExpertisePerson[] $persons
     * @return array
     */
    public static function sortJudiciairePersons(array $persons): array
    {
        $result = [];
        $persons = arrayCollectionChangeKey($persons);

        //add demandeurs
        $demandeurs = [];
        foreach ($persons as $id => $person) {
            if ($person->getInstitution() and ($person->getInstitution()->isDemandeur() or $person->getInstitution()->getType() == DossierInstitution::TYPE_CONTACT)) {
                if ($person->getParent()) {
                    //children will be added after
                    continue;
                }
                $demandeurs[] = $person;
                unset($persons[$id]);
            }
        }

        //sort demandeurs by defendant number
        usort($demandeurs, function (DossierExpertisePerson $person1, DossierExpertisePerson $person2) {
            return ($person1->getInstitution()->getDefendantNumber() > $person2->getInstitution()->getDefendantNumber() ? 1 : -1);
        });

        foreach ($demandeurs as $demandeur) {
            if ($demandeur->getChildren()) {
                foreach ($demandeur->getChildren() as $child) {
                    if (isset($persons[$child->getId()])) {
                        $result[] = $child;
                        unset($persons[$child->getId()]);
                    }
                }
            }
            $result[] = $demandeur;
        }


        //add defendants
        $defendants = [];
        foreach ($persons as $id => $person) {
            if ($person->getInstitution() and $person->getInstitution()->isDefendant() and !$person->getInstitution()->getParent()) {
                if ($person->getParent()) {
                    //children will be added after
                    continue;
                }
                $defendants[] = $person;
                unset($persons[$id]);
            }
        }

        //sort defendants by defendant number
        usort($defendants, function (DossierExpertisePerson $person1, DossierExpertisePerson $person2) {
            return ($person1->getInstitution()->getDefendantNumber() > $person2->getInstitution()->getDefendantNumber() ? 1 : -1);
        });

        foreach ($defendants as $defendant) {
            if ($defendant->getChildren()) {
                foreach ($defendant->getChildren() as $child) {
                    if (isset($persons[$child->getId()])) {
                        $result[] = $child;
                        unset($persons[$child->getId()]);
                    }
                }
            }
            $result[] = $defendant;
        }

        //add remaining persons
        foreach ($persons as $person) {
            if ($person->getInstitution() and in_array($person->getInstitution()->getType(), [DossierInstitution::TYPE_EXPERT, DossierInstitution::TYPE_MANDATE, DossierInstitution::TYPE_COURT_PRESIDENT, DossierInstitution::TYPE_CLERK, DossierInstitution::TYPE_COURT_ASSISTANT])) {
                continue;
            }
            $result[] = $person;
        }

        return $result;
    }
}
