<?php

namespace MatGyver\Repository\Client\Stat;

use MatGyver\Entity\Client\Stat\ClientStat;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method ClientStat|null find($id, $lockMode = null, $lockVersion = null)
 * @method ClientStat|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method ClientStat[]    findAll()
 * @method ClientStat[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class ClientStatRepository extends AbstractEntityRepository
{
    /**
     * @param string $date
     * @return ClientStat[]
     */
    public function getClientsStatsByDate(string $date): array
    {
        return $this->createQueryBuilder('s')
            ->andWhere('s.date >= :date')
            ->setParameter('date', $date)
            ->getQuery()
            ->getResult();
    }

    /**
     * @return ClientStat|null
     */
    public function getLastClientsStats(): ?ClientStat
    {
        return $this->createQueryBuilder('s')
            ->orderBy('s.date', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * @param \DateTime $date
     * @return ClientStat|null
     */
    public function getLastClientsStatsByMonth(\DateTime $date): ?ClientStat
    {
        return $this->createQueryBuilder('s')
            ->andWhere('s.date LIKE :date')
            ->setParameter('date', '%' . $date->format('Y-m-') . '%')
            ->orderBy('s.date', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * @param string $date1year
     * @return ClientStat[]
     */
    public function getClientsStatsByMonth(string $date1year): array
    {
        return $this->createQueryBuilder('s')
            ->select('MAX(s.nbClients) as nbClients, MAX(s.turnover) as turnover, MAX(s.nbMonthly) as nbMonthly, MAX(s.nbYearly) as nbYearly, MAX(s.nbFreemium) as nbFreemium, MAX(s.nbDesactives) as nbDesactives, MAX(s.nbPause) as nbPause, MAX(s.nbCancel) as nbCancel, MAX(s.churn) as churn, SUBSTRING(s.date, 1, 7) as month')
            ->andWhere('s.date >= :date')
            ->setParameter('date', $date1year)
            ->groupBy('month')
            ->getQuery()
            ->getResult();
    }
}
