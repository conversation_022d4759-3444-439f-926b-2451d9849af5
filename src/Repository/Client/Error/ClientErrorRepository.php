<?php

namespace MatGyver\Repository\Client\Error;

use MatGyver\Entity\Client\Error\ClientError;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method ClientError|null find($id, $lockMode = null, $lockVersion = null)
 * @method ClientError|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method ClientError[]    findAll()
 * @method ClientError[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class ClientErrorRepository extends AbstractEntityRepository
{
    /**
     * @return array
     */
    public function getClientsErrors(): array
    {
        return $this->createQueryBuilder('c')
            ->getQuery()
            ->getResult();
    }

    public function getClientsErrorsToReview()
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.toReview = :toReview')
            ->setParameter('toReview', true)
            ->andWhere('c.reviewed = :reviewed')
            ->setParameter('reviewed', false)
            ->getQuery()
            ->getResult();
    }

    /**
     * @param int $idClientError
     * @return ClientError|null
     */
    public function getClientErrorById(int $idClientError): ?ClientError
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.id = :id')
            ->setParameter('id', $idClientError)
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * @return ClientError[]
     */
    public function findRelancesToSend(): array
    {
        $date = date('Y-m-d');
        return $this->createQueryBuilder('c')
            ->andWhere('c.relance2 = :date OR c.relance3 = :date OR c.relance4 = :date')
            ->setParameter('date', $date)
            ->getQuery()
            ->getResult();
    }
}
