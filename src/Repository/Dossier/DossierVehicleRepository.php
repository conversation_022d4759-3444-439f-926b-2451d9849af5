<?php
namespace MatGyver\Repository\Dossier;

use MatGyver\Entity\Dossier\DossierVehicle;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method DossierVehicle|null find($id, $lockMode = null, $lockVersion = null)
 * @method DossierVehicle|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method DossierVehicle[]    findAll()
 * @method DossierVehicle[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DossierVehicleRepository extends AbstractEntityRepository
{

}
