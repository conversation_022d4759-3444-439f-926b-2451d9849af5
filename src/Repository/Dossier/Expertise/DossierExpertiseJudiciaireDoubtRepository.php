<?php
namespace MatGyver\Repository\Dossier\Expertise;

use MatGyver\Entity\Dossier\Expertise\DossierExpertiseJudiciaireDoubt;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method DossierExpertiseJudiciaireDoubt|null find($id, $lockMode = null, $lockVersion = null)
 * @method DossierExpertiseJudiciaireDoubt|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method DossierExpertiseJudiciaireDoubt[]    findAll()
 * @method DossierExpertiseJudiciaireDoubt[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DossierExpertiseJudiciaireDoubtRepository extends AbstractEntityRepository
{
}
