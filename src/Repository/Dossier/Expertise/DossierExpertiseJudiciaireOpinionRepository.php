<?php
namespace MatGyver\Repository\Dossier\Expertise;

use MatGyver\Entity\Dossier\Expertise\DossierExpertiseJudiciaireOpinion;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method DossierExpertiseJudiciaireOpinion|null find($id, $lockMode = null, $lockVersion = null)
 * @method DossierExpertiseJudiciaireOpinion|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method DossierExpertiseJudiciaireOpinion[]    findAll()
 * @method DossierExpertiseJudiciaireOpinion[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DossierExpertiseJudiciaireOpinionRepository extends AbstractEntityRepository
{
}
