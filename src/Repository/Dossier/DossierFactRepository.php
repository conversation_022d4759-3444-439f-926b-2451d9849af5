<?php
namespace <PERSON><PERSON><PERSON>ver\Repository\Dossier;

use <PERSON><PERSON><PERSON><PERSON>\Entity\Dossier\Dossier;
use <PERSON><PERSON><PERSON><PERSON>\Entity\Dossier\DossierFact;
use MatGyver\Entity\Dossier\Expertise\DossierExpertise;
use MatG<PERSON>ver\Repository\AbstractEntityRepository;

/**
 * @method DossierFact|null find($id, $lockMode = null, $lockVersion = null)
 * @method DossierFact|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method DossierFact[]    findAll()
 * @method DossierFact[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DossierFactRepository extends AbstractEntityRepository
{
    /**
     * @param DossierExpertise $expertise
     * @return DossierFact[]
     */
    public function findByDossierAndExpertise(DossierExpertise $expertise)
    {
        return $this->createQueryBuilder('df')
            ->where('df.dossier = :dossier')
            ->andWhere('(df.expertise IS NULL OR df.expertise = :expertise)')
            ->setParameter('dossier', $expertise->getDossier())
            ->setParameter('expertise', $expertise)
            ->orderBy('df.position', 'ASC')
            ->getQuery()
            ->getResult();
    }
}
