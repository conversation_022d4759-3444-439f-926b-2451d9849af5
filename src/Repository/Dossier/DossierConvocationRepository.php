<?php
namespace MatGyver\Repository\Dossier;

use MatGyver\Entity\Dossier\DossierConvocation;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method DossierConvocation|null find($id, $lockMode = null, $lockVersion = null)
 * @method DossierConvocation|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method DossierConvocation[]    findAll()
 * @method DossierConvocation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DossierConvocationRepository extends AbstractEntityRepository
{

}
