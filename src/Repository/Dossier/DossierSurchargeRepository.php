<?php
namespace MatGyver\Repository\Dossier;

use MatGyver\Entity\Dossier\DossierSurcharge;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method DossierSurcharge|null find($id, $lockMode = null, $lockVersion = null)
 * @method DossierSurcharge|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method DossierSurcharge[]    findAll()
 * @method DossierSurcharge[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DossierSurchargeRepository extends AbstractEntityRepository
{

}
