<?php

namespace MatGyver\Repository\Notification;

use MatGyver\Entity\Notification\Notification;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method Notification|null find($id, $lockMode = null, $lockVersion = null)
 * @method Notification|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method Notification[]    findAll()
 * @method Notification[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class NotificationRepository extends AbstractEntityRepository
{

}
