<?php
namespace MatGyver\Repository\OpenAi;

use MatGyver\Entity\OpenAi\OpenAiAssistant;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method OpenAiAssistant|null find($id, $lockMode = null, $lockVersion = null)
 * @method OpenAiAssistant|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method OpenAiAssistant[]    findAll()
 * @method OpenAiAssistant[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class OpenAiAssistantRepository extends AbstractEntityRepository
{

}
