<?php

namespace MatGyver\Repository\Migration;

use MatGyver\Entity\Migration\Migration;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method Migration|null find($id, $lockMode = null, $lockVersion = null)
 * @method Migration|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method Migration[]    findAll()
 * @method Migration[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class MigrationRepository extends AbstractEntityRepository
{

}
