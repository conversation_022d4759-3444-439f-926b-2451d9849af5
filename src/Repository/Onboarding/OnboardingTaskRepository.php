<?php

namespace MatGyver\Repository\Onboarding;

use Mat<PERSON>yver\Entity\Onboarding\OnboardingTask;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method OnboardingTask|null find($id, $lockMode = null, $lockVersion = null)
 * @method OnboardingTask|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method OnboardingTask[]    findAll()
 * @method OnboardingTask[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class OnboardingTaskRepository extends AbstractEntityRepository
{
}
