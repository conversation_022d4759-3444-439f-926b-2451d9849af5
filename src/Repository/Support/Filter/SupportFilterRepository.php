<?php

namespace MatGyver\Repository\Support\Filter;

use MatGyver\Entity\Support\Filter\SupportFilter;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method SupportFilter|null find($id, $lockMode = null, $lockVersion = null)
 * @method SupportFilter|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method SupportFilter[]    findAll()
 * @method SupportFilter[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class SupportFilterRepository extends AbstractEntityRepository
{

}
