<?php

namespace MatGyver\Repository\Support\Reply;

use MatGyver\Entity\Support\Reply\SupportReply;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method SupportReply|null find($id, $lockMode = null, $lockVersion = null)
 * @method SupportReply|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method SupportReply[]    findAll()
 * @method SupportReply[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class SupportReplyRepository extends AbstractEntityRepository
{
    /**
     * @param string $dateStart
     * @param string $dateEnd
     * @return array
     */
    public function adminGetAllRepliesGroupByAdminId(string $dateStart = '', string $dateEnd = ''): array
    {
        $qb = $this->createQueryBuilder('r')
            ->select('count(r.id) as nb, IDENTITY(r.admin) as admin');

        if ($dateStart) {
            $qb->andWhere('r.date >= :dateStart')
                ->setParameter('dateStart', $dateStart);
        }
        if ($dateEnd) {
            $qb->andWhere('r.date <= :dateEnd')
                ->setParameter('dateEnd', $dateEnd);
        }

        return $qb->groupBy('r.admin')
            ->getQuery()
            ->getResult();
    }

    /**
     * @param int|null $adminId
     * @param string|null $idsCategories
     * @param string|null $dateStart
     * @param string|null $dateEnd
     * @return int
     */
    public function getTicketsNbReplies(int $adminId = null, string $idsCategories = null, string $dateStart = null, string $dateEnd = null): int
    {
        if ($idsCategories) {
            return $this->createQueryBuilder('r')
                ->leftJoin('r.ticket', 't')
                ->select('count(r.id) as nb')
                ->andWhere('(t.categories = :categories1 OR t.categories LIKE :categories2 OR t.categories LIKE :categories3)')
                ->setParameter('categories1', $idsCategories)
                ->setParameter('categories2', '%,' . $idsCategories . '%')
                ->setParameter('categories3', '%' . $idsCategories . ',%')
                ->setMaxResults(1)
                ->getQuery()
                ->getSingleScalarResult();
        }

        $qb = $this->createQueryBuilder('r')
            ->select('count(r.id) as nb');

        if ($adminId) {
            $qb->andWhere('r.admin = :admin')
                ->setParameter('admin', $adminId);
        }
        if ($dateStart) {
            $qb->andWhere('r.date >= :dateStart')
                ->setParameter('dateStart', $dateStart);
        }
        if ($dateEnd) {
            $qb->andWhere('r.date <= :dateEnd')
                ->setParameter('dateEnd', $dateEnd);
        }

        return $qb->setMaxResults(1)
            ->getQuery()
            ->getSingleScalarResult();
    }
}
