<?php

namespace MatGyver\Repository\Mollie\Charge;

use MatGyver\Entity\Mollie\Charge\MollieCharge;
use MatGyver\Repository\PaymentMethod\PaymentMethodChargeRepository;

/**
 * @method MollieCharge|null find($id, $lockMode = null, $lockVersion = null)
 * @method MollieCharge|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method MollieCharge[]    findAll()
 * @method MollieCharge[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class MollieChargeRepository extends PaymentMethodChargeRepository
{
}
