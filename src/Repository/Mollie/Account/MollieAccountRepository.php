<?php

namespace MatGyver\Repository\Mollie\Account;

use <PERSON><PERSON><PERSON><PERSON>\Entity\Mollie\Account\MollieAccount;
use MatGyver\Repository\PaymentMethod\PaymentMethodChargeRepository;

/**
 * @method MollieAccount|null find($id, $lockMode = null, $lockVersion = null)
 * @method MollieAccount|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method MollieAccount[]    findAll()
 * @method MollieAccount[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class MollieAccountRepository extends PaymentMethodChargeRepository
{
}
