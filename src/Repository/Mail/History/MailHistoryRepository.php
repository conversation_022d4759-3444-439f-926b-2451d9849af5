<?php

namespace MatGyver\Repository\Mail\History;

use Mat<PERSON><PERSON><PERSON>\Entity\Mail\History\MailHistory;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method MailHistory|null find($id, $lockMode = null, $lockVersion = null)
 * @method MailHistory|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method MailHistory[]    findAll()
 * @method MailHistory[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class MailHistoryRepository extends AbstractEntityRepository
{

}
