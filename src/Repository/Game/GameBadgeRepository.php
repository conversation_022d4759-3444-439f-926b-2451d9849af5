<?php
namespace Mat<PERSON>yver\Repository\Game;

use Mat<PERSON>yver\Entity\Game\GameBadge;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method GameBadge|null find($id, $lockMode = null, $lockVersion = null)
 * @method GameBadge|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method GameBadge[]    findAll()
 * @method GameBadge[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class GameBadgeRepository extends AbstractEntityRepository
{

}
