<?php
namespace MatGyver\Repository\Game;

use MatGyver\Entity\Game\GameDailyQuestClient;
use MatG<PERSON>ver\Repository\AbstractEntityRepository;

/**
 * @method GameDailyQuestClient|null find($id, $lockMode = null, $lockVersion = null)
 * @method GameDailyQuestClient|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method GameDailyQuestClient[]    findAll()
 * @method GameDailyQuestClient[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class GameDailyQuestClientRepository extends AbstractEntityRepository
{
    /**
     * @return GameDailyQuestClient[]
     */
    public function getAll(): array
    {
        return $this->createQueryBuilder('dqc')
            ->getQuery()
            ->getResult();
    }
}
