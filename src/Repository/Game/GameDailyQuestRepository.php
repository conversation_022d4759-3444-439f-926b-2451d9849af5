<?php
namespace MatGyver\Repository\Game;

use Mat<PERSON>yver\Entity\Game\GameDailyQuest;
use MatG<PERSON>ver\Repository\AbstractEntityRepository;

/**
 * @method GameDailyQuest|null find($id, $lockMode = null, $lockVersion = null)
 * @method GameDailyQuest|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method GameDailyQuest[]    findAll()
 * @method GameDailyQuest[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class GameDailyQuestRepository extends AbstractEntityRepository
{

}
