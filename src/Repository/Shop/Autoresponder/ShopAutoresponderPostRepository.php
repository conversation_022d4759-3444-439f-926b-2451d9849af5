<?php

namespace MatGyver\Repository\Shop\Autoresponder;

use MatGyver\Entity\Shop\Autoresponder\ShopAutoresponderPost;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method ShopAutoresponderPost|null find($id, $lockMode = null, $lockVersion = null)
 * @method ShopAutoresponderPost|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method ShopAutoresponderPost[]    findAll()
 * @method ShopAutoresponderPost[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class ShopAutoresponderPostRepository extends AbstractEntityRepository
{

}
