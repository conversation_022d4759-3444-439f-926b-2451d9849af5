<?php

namespace MatGyver\Repository\Shop\Product;

use MatGyver\Entity\Shop\Product\ShopProductAttributeLimit;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method ShopProductAttributeLimit|null find($id, $lockMode = null, $lockVersion = null)
 * @method ShopProductAttributeLimit|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method ShopProductAttributeLimit[]    findAll()
 * @method ShopProductAttributeLimit[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class ShopProductAttributeLimitRepository extends AbstractEntityRepository
{

}
