<?php

namespace MatGyver\Repository\Shop\Transaction;

use MatGyver\Entity\Shop\Transaction\ShopTransactionPayment;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method ShopTransactionPayment|null find($id, $lockMode = null, $lockVersion = null)
 * @method ShopTransactionPayment|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method ShopTransactionPayment[]    findAll()
 * @method ShopTransactionPayment[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class ShopTransactionPaymentRepository extends AbstractEntityRepository
{
}
