<?php

namespace MatGyver\Repository\Shop\Transaction;

use MatGyver\Entity\Shop\Transaction\ShopTransaction;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method ShopTransaction|null find($id, $lockMode = null, $lockVersion = null)
 * @method ShopTransaction|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method ShopTransaction[]    findAll()
 * @method ShopTransaction[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class ShopTransactionRepository extends AbstractEntityRepository
{

    /**
     * @param \DateTime $startDate
     * @param \DateTime $endDate
     * @return array|null
     */
    public function findByDates(\DateTime $startDate, \DateTime $endDate): ?array
    {
        return $this->createQueryBuilder('t')
            ->where('t.date >= :startDate')
            ->andWhere('t.date <= :endDate')
            ->setParameters([
                'startDate' => $startDate,
                'endDate' => $endDate
            ])
            ->getQuery()->getResult();
    }

    /**
     * @param \DateTime $startDate
     * @param \DateTime $endDate
     * @param int $valid
     * @return array|null
     */
    public function countMontantByDates(\DateTime $startDate, \DateTime $endDate, int $valid = 1): ?array
    {
        $results = $this->createQueryBuilder('t')
            ->select('SUM(t.amountTaxExcl) ht, SUM(t.amountTaxIncl) ttc')
            ->where('t.date >= :startDate')
            ->andWhere('t.date <= :endDate')
            ->andWhere('t.valid = :valid')
            ->setParameters([
                'startDate' => $startDate,
                'endDate' => $endDate,
                'valid' => $valid
            ])
            ->getQuery()->getArrayResult();

        if (empty($results)) {
            return ['ht' => 0, 'ttc' => 0];
        }

        $result = array_shift($results);

        return ['ht' => (float) $result['ht'], 'ttc' => (float) $result['ttc']];
    }

    /**
     * @param \DateTime|null $dateFrom
     * @param \DateTime|null $dateEnd
     * @param null $period
     * @return array
     * @throws \Doctrine\DBAL\DBALException
     */
    public function statsByDates(\DateTime $dateFrom = null, \DateTime $dateEnd = null, $period = null): array
    {
        $conn = $this->_em->getConnection();

        if ($period === '1Y') {
            $sqlGroupBy = 'month';
            $sqlSubstring = 'SUBSTRING(date, 6, 2) as ' . $sqlGroupBy;
        } else {
            $sqlGroupBy = 'day';
            $sqlSubstring = 'SUBSTRING(date, 1, 10) as ' . $sqlGroupBy;
        }

        $sql = "SELECT COUNT(*) as nb_elts, " . $sqlSubstring . " FROM shop_transactions WHERE 1";

        $requestParams = [];
        if ($dateFrom !== null) {
            $sql .= " AND date >= :date_from";
            $requestParams['date_from'] = $dateFrom->format('Y-m-d H:i:s');
        }

        if ($dateEnd !== null) {
            $sql .= " AND date <= :date_end";
            $requestParams['date_end'] = $dateEnd->format('Y-m-d H:i:s');
        }

        $sql .= " AND valid=1";
        $sql .= " AND (status='COMPLETED' OR status='delivered' OR status='delivering')";
        $sql .= " GROUP BY " . $sqlGroupBy;
        $sql .= " ORDER BY " . $sqlGroupBy . " ASC";

        $stmt = $conn->prepare($sql);
        $stmt->execute($requestParams);
        return $stmt->fetchAllAssociative();
    }

    /**
     * @param \DateTime|null $dateFrom
     * @param \DateTime|null $dateEnd
     * @return array
     * @throws \Doctrine\DBAL\DBALException
     */
    public function statsTotalByDates(\DateTime $dateFrom = null, \DateTime $dateEnd = null): array
    {
        $conn = $this->_em->getConnection();
        $sql = "SELECT COUNT(*) as nb_elts, SUBSTRING(date, 1, 10) as day FROM shop_transactions WHERE 1";

        $requestParams = [];

        if ($dateFrom !== null) {
            $sql .= " AND date >= :date_from";
            $requestParams['date_from'] = $dateFrom->format('Y-m-d H:i:s');
        }

        if ($dateEnd !== null) {
            $sql .= " AND date <= :date_end";
            $requestParams['date_end'] = $dateEnd->format('Y-m-d H:i:s');
        }

        $sql .= " AND valid=1";
        $sql .= " AND (status='COMPLETED' OR status='delivered' OR status='delivering')";

        $stmt = $conn->prepare($sql);
        $stmt->execute($requestParams);
        return $stmt->fetchAllAssociative();
    }

    /**
     * @param int $userId
     * @return int
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function countAllTransactions(int $userId): int
    {
        $queryBuilder = $this->createQueryBuilder('t')
            ->select('COUNT(t.user) as count')
            ->andWhere('t.user = :userId')
            ->setParameter('userId', $userId);

        return $queryBuilder->getQuery()->getSingleScalarResult();
    }

    /**
     * @param int $userId
     * @return null|object
     */
    public function getFirstTransaction(int $userId)
    {
        return $this->findOneBy(['user' => $userId], ['date' => 'ASC']);
    }

    /**
     * @param string $email
     * @return null|object
     */
    public function getFirstTransactionByEmail(string $email)
    {
        return $this->findOneBy(['email' => $email], ['date' => 'ASC']);
    }

    public function getNewFreeOrders(): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.amountTaxIncl = :amountTaxIncl')
            ->setParameter('amountTaxIncl', 0)
            ->andWhere('t.valid = :valid')
            ->setParameter('valid', true)
            ->andWhere('t.client = :client')
            ->setParameter('client', CLIENT_MASTER)
            ->groupBy('t.shopCustomer')
            ->getQuery()
            ->getResult();
    }

    /**
     * @return ShopTransaction[]
     */
    public function getNewPaidOrders(): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.amountTaxIncl > :amountTaxIncl')
            ->setParameter('amountTaxIncl', 0)
            ->andWhere('t.valid = :valid')
            ->setParameter('valid', true)
            ->andWhere('t.client = :client')
            ->setParameter('client', CLIENT_MASTER)
            ->groupBy('t.shopCustomer')
            ->getQuery()
            ->getResult();
    }

    public function getNewErrorOrders(): array
    {
        $dateYesterday = date('Y-m-d', strtotime('-1 day'));
        return $this->createQueryBuilder('t')
            ->andWhere('t.status != :status')
            ->setParameter('status', ShopTransaction::STATUS_REFUNDED)
            ->andWhere('t.valid = :valid')
            ->setParameter('valid', false)
            ->andWhere('t.client = :client')
            ->setParameter('client', CLIENT_MASTER)
            ->andWhere('t.paymentMethod != :paymentMethodCheque')
            ->setParameter('paymentMethodCheque', ShopTransaction::METHOD_CHEQUE)
            ->andWhere('t.paymentMethod != :paymentMethodVirement')
            ->setParameter('paymentMethodVirement', ShopTransaction::METHOD_VIREMENT)
            ->andWhere('t.date LIKE :date')
            ->setParameter('date', '%' . $dateYesterday . '%')
            ->getQuery()
            ->getResult();
    }

    public function getNextValidOrder(ShopTransaction $transaction): ?ShopTransaction
    {
        $qb = $this->createQueryBuilder('t')
            ->andWhere('t.client = :client')
            ->setParameter('client', CLIENT_MASTER)
            ->andWhere('t.id > :id')
            ->setParameter('id', $transaction->getId())
            ->andWhere('t.valid = :valid')
            ->setParameter('valid', true);

        if ($transaction->getShopCustomer()) {
            $qb->andWhere('(t.shopCustomer = :customer OR t.email = :email)')
                ->setParameter('customer', $transaction->getShopCustomer())
                ->setParameter('email', $transaction->getEmail());
        } else {
            $qb->andWhere('t.email = :email')
                ->setParameter('email', $transaction->getEmail());
        }

        return $qb->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * @return ShopTransaction[]
     */
    public function getFirstCustomersOrders(): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.client = :client')
            ->setParameter('client', $_SESSION['client']['id'])
            ->andWhere('t.valid = :valid')
            ->setParameter('valid', true)
            ->groupBy('t.email, t.product')
            ->orderBy('t.date', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * @return array
     */
    public function getLastYearStats(): array
    {
        return $this->createQueryBuilder('t')
            ->select('count(t.id) as nb, SUBSTRING(t.date, 1, 7) as month, SUM(t.amountTaxIncl) as totalTaxIncl, SUM(t.amountTaxExcl) as totalTaxExcl')
            ->andWhere('t.client = :client')
            ->setParameter('client', $_SESSION['client']['id'])
            ->andWhere('t.valid = :valid')
            ->setParameter('valid', true)
            ->groupBy('month')
            ->orderBy('t.date', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * @return array
     */
    public function getAffiliateOrdersStats(): array
    {
        return $this->createQueryBuilder('t')
            ->select('count(t.id) as nb, SUBSTRING(t.date, 1, 7) as month, SUM(t.amountTaxIncl) as totalTaxIncl, SUM(t.amountTaxExcl) as totalTaxExcl')
            ->andWhere('t.client = :client')
            ->setParameter('client', $_SESSION['client']['id'])
            ->andWhere('t.valid = :valid')
            ->setParameter('valid', true)
            ->andWhere('t.partner IS NOT NULL')
            ->groupBy('month')
            ->orderBy('t.date', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function getRefundsStats(): array
    {
        return $this->createQueryBuilder('t')
            ->select('count(t.id) as nb, SUBSTRING(t.date, 1, 7) as month')
            ->andWhere('t.client = :client')
            ->setParameter('client', $_SESSION['client']['id'])
            ->andWhere('t.status = :status')
            ->setParameter('status', ShopTransaction::STATUS_REFUNDED)
            ->groupBy('month')
            ->orderBy('t.date', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * @param int $userId
     * @param int|null $idClient
     * @return float
     */
    public function getTransactionsAmountByUser(int $userId, ?int $idClient = null): float
    {
        if (null === $idClient) {
            $idClient = $_SESSION['client']['id'];
        }

        $result = $this->createQueryBuilder('t')
            ->select('SUM(t.amountTaxIncl) as total')
            ->andWhere('t.client = :client')
            ->setParameter('client', $idClient)
            ->andWhere('t.valid = :valid')
            ->setParameter('valid', true)
            ->andWhere('t.user = :user')
            ->setParameter('user', $userId)
            ->setMaxResults(1)
            ->getQuery()
            ->getSingleScalarResult();
        return $result ?: 0;
    }

    /**
     * @param int|null $idClient
     * @return array
     */
    public function getCountOrdersGroupByCustomer(?int $idClient = null): array
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }

        return $this->createQueryBuilder('t')
            ->select('COUNT(t.id) as nbOrders, IDENTITY(t.shopCustomer) as customerId')
            ->andWhere('t.client = :client')
            ->setParameter('client', $idClient)
            ->andWhere('t.valid = :valid')
            ->setParameter('valid', true)
            ->groupBy('t.shopCustomer')
            ->getQuery()
            ->getResult();
    }

    /**
     * @param int|null $idClient
     * @return array
     */
    public function getTotalAmountGroupByCustomer(?int $idClient = null): array
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }

        return $this->createQueryBuilder('t')
            ->select('SUM(t.amountTaxIncl) as totalTaxIncl, IDENTITY(t.shopCustomer) as customerId')
            ->andWhere('t.client = :client')
            ->setParameter('client', $idClient)
            ->andWhere('t.valid = :valid')
            ->setParameter('valid', true)
            ->groupBy('t.shopCustomer')
            ->getQuery()
            ->getResult();
    }

    /**
     * @param int $idProduct
     * @param string $date
     * @param int|null $idClient
     * @return array
     */
    public function getOrdersByProductAndDate(int $idProduct, string $date, ?int $idClient = null): array
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }

        return $this->createQueryBuilder('t')
            ->select('count(t.id) as nbOrders, SUBSTRING(t.date, 6, 5) as day')
            ->andWhere('t.client = :client')
            ->setParameter('client', $idClient)
            ->andWhere('t.product = :product')
            ->setParameter('product', $idProduct)
            ->andWhere('t.date >= :date')
            ->setParameter('date', $date)
            ->andWhere('t.valid = :valid')
            ->setParameter('valid', true)
            ->groupBy('day')
            ->getQuery()
            ->getResult();
    }

    /**
     * @return array
     */
    public function getDistinctTransactionsByProducts(): array
    {
        return $this->createQueryBuilder('t')
            ->select('count(t.id) as nbSales, t.productName, SUM(t.amountTaxExcl) as totalTaxExcl, IDENTITY(t.product) as productId')
            ->andWhere('t.client = :client')
            ->setParameter('client', $_SESSION['client']['id'])
            ->andWhere('t.valid = :valid')
            ->setParameter('valid', true)
            ->groupBy('t.product')
            ->getQuery()
            ->getResult();
    }

    /**
     * @return array
     */
    public function getDistinctTransactionsByPayment(): array
    {
        return $this->createQueryBuilder('t')
            ->select('t.paymentMethod, count(t.id) as nbSales, SUM(t.amountTaxExcl) as totalTaxExcl')
            ->andWhere('t.client = :client')
            ->setParameter('client', $_SESSION['client']['id'])
            ->andWhere('t.valid = :valid')
            ->setParameter('valid', true)
            ->groupBy('t.paymentMethod')
            ->getQuery()
            ->getResult();
    }

    /**
     * @param int|null       $idClient
     * @param \DateTime|null $startDate
     * @param \DateTime|null $endDate
     * @return array
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function getTotalAmounts(?int $idClient = null, ?\DateTime $startDate = null, ?\DateTime $endDate = null): array
    {
        if (!$idClient) {
            $idClient = $_SESSION['client']['id'];
        }

        $qb = $this->createQueryBuilder('t')
            ->select('SUM(t.amountTaxExcl) as totalTaxExcl, SUM(t.amountTaxIncl) as totalTaxIncl, COUNT(t.id) as nbOrders')
            ->andWhere('t.client = :client')
            ->setParameter('client', $idClient)
            ->andWhere('t.valid = :valid')
            ->setParameter('valid', true);

        if ($startDate) {
            $qb->andWhere('t.date >= :dateStart')
                ->setParameter('dateStart', $startDate);
        }
        if ($endDate) {
            $qb->andWhere('t.date <= :dateEnd')
                ->setParameter('dateEnd', $endDate);
        }

        return $qb->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * @param string $reference
     * @param string $custom
     * @param int|null $idClient
     * @return ShopTransaction|null
     */
    public function getPrevTransactionByCustom(string $reference, string $custom, ?int $idClient = null): ?ShopTransaction
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }

        return $this->createQueryBuilder('t')
            ->andWhere('t.client = :client')
            ->setParameter('client', $idClient)
            ->andWhere('t.reference != :reference')
            ->setParameter('reference', $reference)
            ->andWhere('t.custom = :custom')
            ->setParameter('custom', $custom)
            ->setMaxResults(1)
            ->orderBy('t.id', 'DESC')
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * @param string $dateStart
     * @param bool $valid
     * @return array
     */
    public function getTransactionsByDate(string $dateStart, bool $valid = false): array
    {
        return $this->createQueryBuilder('t')
            ->select('count(t.id) as nbSales, SUM(t.amountTaxExcl) as totalTaxExcl, SUBSTRING(t.date, 6, 5) as day')
            ->andWhere('t.client = :client')
            ->setParameter('client', $_SESSION['client']['id'])
            ->andWhere('t.valid = :valid')
            ->setParameter('valid', $valid)
            ->andWhere('t.date >= :date')
            ->setParameter('date', $dateStart)
            ->groupBy('day')
            ->getQuery()
            ->getResult();
    }

    /**
     * @param string $status
     * @param int|null $start
     * @param int|null $limit
     * @param int|null $idClient
     * @param string $dateStart
     * @param string $dateEnd
     * @param int $idProduct
     * @param string|null $search
     * @return ShopTransaction[]
     */
    public function getAllTransactionsWithLimit(string $status = '', ?int $start = null, ?int $limit = null, ?int $idClient = null, string $dateStart = '', string $dateEnd = '', int $idProduct = 0, string $search = null): array
    {
        if (null === $idClient) {
            $idClient = $_SESSION['client']['id'];
        }

        $qb = $this->createQueryBuilder('t')
            ->andWhere('t.client = :client')
            ->setParameter('client', $idClient);

        if ($search) {
            $qb->andWhere('t.reference LIKE :search OR t.status LIKE :search OR t.paymentMethod LIKE :search OR t.amountTaxIncl LIKE :search OR t.lastName LIKE :search OR t.firstName LIKE :search OR t.email LIKE :search OR t.productName LIKE :search OR t.date LIKE :search')
                ->setParameter('search', '%' . $search . '%');
        }

        if ($status) {
            $qb->andWhere('t.status = :status');
            switch ($status) {
                case 'valid':
                    $qb->setParameter('status', ShopTransaction::STATUS_COMPLETED);
                    break;
                case 'refund':
                    $qb->setParameter('status', ShopTransaction::STATUS_REFUNDED);
                    break;
                case 'error':
                    $qb->setParameter('status', ShopTransaction::STATUS_ERROR);
                    break;
                case 'waiting':
                    $qb->setParameter('status', ShopTransaction::STATUS_WAITING);
                    break;
            }
        }

        if ($dateStart) {
            $qb->andWhere('t.date >= :dateStart')
                ->setParameter('dateStart', convertDateFromClientToServerTimezone($dateStart . ' 00:00:00'));
        }
        if ($dateEnd) {
            $qb->andWhere('t.date <= :dateEnd')
                ->setParameter('dateEnd', convertDateFromClientToServerTimezone($dateEnd . ' 23:59:59'));
        }

        if ($idProduct) {
            $qb->andWhere('t.product = :product')
                ->setParameter('product', $idProduct);
        }

        if ($start) {
            $qb->setFirstResult(0);
        }
        if ($limit) {
            $qb->setMaxResults($limit);
        }

        return $qb->orderBy('t.date', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * @param string $status
     * @param string $dateStart
     * @param string $dateEnd
     * @param int $idProduct
     * @param string $search
     * @param int|null $idClient
     * @return array
     */
    public function getTotalAmountValid(string $status = '', string $dateStart = '', string $dateEnd = '', int $idProduct = 0, string $search = '', ?int $idClient = null): array
    {
        if (null === $idClient) {
            $idClient = $_SESSION['client']['id'];
        }

        $qb = $this->createQueryBuilder('t')
            ->select('SUM(t.amountTaxExcl) as totalTaxExcl, SUM(t.amountTaxIncl) as totalTaxIncl, COUNT(t.id) as nbTransactions')
            ->andWhere('t.client = :client')
            ->setParameter('client', $idClient)
            ->andWhere('t.valid = :valid')
            ->setParameter('valid', true);

        if ($status) {
            $qb->andWhere('t.status = :status');
            switch ($status) {
                case 'valid':
                    $qb->setParameter('status', ShopTransaction::STATUS_COMPLETED);
                    break;
                case 'refund':
                    $qb->setParameter('status', ShopTransaction::STATUS_REFUNDED);
                    break;
                case 'error':
                    $qb->setParameter('status', ShopTransaction::STATUS_ERROR);
                    break;
                case 'waiting':
                    $qb->setParameter('status', ShopTransaction::STATUS_WAITING);
                    break;
            }
        }
        if ($idProduct) {
            $qb->andWhere('t.product = :product')
                ->setParameter('product', $idProduct);
        }
        if ($dateStart) {
            $qb->andWhere('t.date >= :dateStart')
                ->setParameter('dateStart', convertDateFromClientToServerTimezone($dateStart . ' 00:00:00'));
        }
        if ($dateEnd) {
            $qb->andWhere('t.date <= :dateEnd')
                ->setParameter('dateEnd', convertDateFromClientToServerTimezone($dateEnd . ' 23:59:59'));
        }
        if ($search) {
            $columns = array(
                't.reference',
                't.status',
                't.paymentMethod',
                't.amountTaxIncl',
                't.lastName',
                't.firstName',
                't.email',
                't.productName',
                't.date',
            );
            $searchCriteria = '(' . implode(' LIKE :search OR ', $columns) . ' LIKE :search)';
            $qb->andWhere($searchCriteria)
                ->setParameter('search', '%' . $search . '%');
        }

        return $qb->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * @param int|null $idClient
     * @return array
     */
    public function getTotalAmountValidGroupByProduct(?int $idClient = null): array
    {
        if (null === $idClient) {
            $idClient = $_SESSION['client']['id'];
        }

        return $this->createQueryBuilder('t')
            ->select('count(t.id) as nbTransactions, SUM(t.amountTaxExcl) as totalTaxExcl, SUM(t.amountTaxIncl) as totalTaxIncl, IDENTITY(t.product) as productId')
            ->andWhere('t.client = :client')
            ->setParameter('client', $idClient)
            ->andWhere('t.valid = :valid')
            ->setParameter('valid', true)
            ->groupBy('t.product')
            ->getQuery()
            ->getResult();
    }

    /**
     * @return array
     */
    public function getTransactionsByMonth(): array
    {
        return $this->createQueryBuilder('t')
            ->select('count(t.id) as nbOrders, SUM(t.amountTaxExcl) as totalTaxExcl, SUM(t.amountTaxIncl) as totalTaxIncl, SUBSTRING(t.date, 1, 7) as month')
            ->andWhere('t.client = :client')
            ->setParameter('client', CLIENT_MASTER)
            ->andWhere('t.valid = :valid')
            ->setParameter('valid', true)
            ->andWhere('t.amountTaxIncl > :amountTaxIncl')
            ->setParameter('amountTaxIncl', 0)
            ->groupBy('month')
            ->getQuery()
            ->getResult();
    }

    /**
     * @param string $dateStart
     * @param string $dateEnd
     * @param string $status
     * @param int|null $idProduct
     * @param string $search
     * @param string $orderVar
     * @param string $orderDir
     * @param int $start
     * @param int $length
     * @return ShopTransaction[]
     */
    public function searchTransactions(string $dateStart = '', string $dateEnd = '', string $status = '', ?int $idProduct = null, string $search = '', string $orderVar = 'date', string $orderDir = 'DESC', int $start = 0, int $length = 10): array
    {
        $qb = $this->createQueryBuilder('t')
            ->andWhere('t.client = :client')
            ->setParameter('client', $_SESSION['client']['id']);

        if ($dateStart) {
            $qb->andWhere('t.date >= :dateStart')
                ->setParameter('dateStart', convertDateFromClientToServerTimezone($dateStart . ' 00:00:00'));
        }
        if ($dateEnd) {
            $qb->andWhere('t.date <= :dateEnd')
                ->setParameter('dateEnd', convertDateFromClientToServerTimezone($dateEnd . ' 23:59:59'));
        }
        if ($status) {
            switch ($status) {
                case 'valid':
                    $qb->andWhere("(t.status='" . ShopTransaction::STATUS_COMPLETED . "' OR t.status='" . ShopTransaction::STATUS_DELIVERED . "' OR t.status='" . ShopTransaction::STATUS_DELIVERING . "')");
                    break;
                case 'refund':
                    $qb->andWhere('t.status = :status')
                        ->setParameter('status', ShopTransaction::STATUS_REFUNDED);
                    break;
                case 'error':
                    $qb->andWhere('t.status = :status')
                        ->setParameter('status', ShopTransaction::STATUS_ERROR);
                    break;
                case 'waiting':
                    $qb->andWhere('t.status = :status')
                        ->setParameter('status', ShopTransaction::STATUS_WAITING);
                    break;
            }
        }
        if ($idProduct) {
            $qb->andWhere('t.product = :product')
                ->setParameter('product', $idProduct);
        }

        if ($search) {
            $columns = array(
                't.reference',
                't.status',
                't.paymentMethod',
                't.amountTaxIncl',
                't.lastName',
                't.firstName',
                't.email',
                't.productName',
                't.date',
            );
            $searchCriteria = '(' . implode(' LIKE :search OR ', $columns) . ' LIKE :search)';
            $qb->andWhere($searchCriteria)
                ->setParameter('search', '%' . $search . '%');
        }

        return $qb->orderBy('t.' . $orderVar, $orderDir)
            ->setFirstResult($start)
            ->setMaxResults($length)
            ->getQuery()
            ->getResult();
    }

    /**
     * @param string $dateStart
     * @param string $dateEnd
     * @param string $status
     * @param int|null $idProduct
     * @param string $search
     * @return int
     * @throws \Doctrine\ORM\NoResultException
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function countSearchTransactions(string $dateStart = '', string $dateEnd = '', string $status = '', ?int $idProduct = null, string $search = ''): int
    {
        $qb = $this->createQueryBuilder('t')
            ->select('count(t.id) as nb')
            ->andWhere('t.client = :client')
            ->setParameter('client', $_SESSION['client']['id']);

        if ($dateStart) {
            $qb->andWhere('t.date >= :dateStart')
                ->setParameter('dateStart', convertDateFromClientToServerTimezone($dateStart . ' 00:00:00'));
        }
        if ($dateEnd) {
            $qb->andWhere('t.date <= :dateEnd')
                ->setParameter('dateEnd', convertDateFromClientToServerTimezone($dateEnd . ' 23:59:59'));
        }
        if ($status) {
            switch ($status) {
                case 'valid':
                    $qb->andWhere("(t.status='" . ShopTransaction::STATUS_COMPLETED . "' OR t.status='" . ShopTransaction::STATUS_DELIVERED . "' OR t.status='" . ShopTransaction::STATUS_DELIVERING . "')");
                    break;
                case 'refund':
                    $qb->andWhere('t.status = :status')
                        ->setParameter('status', ShopTransaction::STATUS_REFUNDED);
                    break;
                case 'error':
                    $qb->andWhere('t.status = :status')
                        ->setParameter('status', ShopTransaction::STATUS_ERROR);
                    break;
                case 'waiting':
                    $qb->andWhere('t.status = :status')
                        ->setParameter('status', ShopTransaction::STATUS_WAITING);
                    break;
            }
        }
        if ($idProduct) {
            $qb->andWhere('t.product = :product')
                ->setParameter('product', $idProduct);
        }

        if ($search) {
            $columns = array(
                't.reference',
                't.status',
                't.paymentMethod',
                't.amountTaxIncl',
                't.lastName',
                't.firstName',
                't.email',
                't.productName',
                't.date',
            );
            $searchCriteria = '(' . implode(' LIKE :search OR ', $columns) . ' LIKE :search)';
            $qb->andWhere($searchCriteria)
                ->setParameter('search', '%' . $search . '%');
        }

        return $qb->setMaxResults(1)
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * @param int $idDiscount
     * @param int|null $idClient
     * @return ShopTransaction[]
     */
    public function getOrdersByDiscount(int $idDiscount, ?int $idClient = null): array
    {
        if (null === $idClient) {
            $idClient = $_SESSION['client']['id'];
        }

        return $this->createQueryBuilder('t')
            ->leftJoin('t.shopCart', 'c')
            ->andWhere('t.client = :client')
            ->setParameter('client', $idClient)
            ->andWhere('c.discount LIKE :discount')
            ->setParameter('discount', '%"id":' . $idDiscount . ',%')
            ->getQuery()
            ->getResult();
    }
}
