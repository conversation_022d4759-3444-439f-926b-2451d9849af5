<?php

namespace MatGyver\Repository\Shop\Invoice;

use MatGyver\Entity\Shop\Invoice\ShopInvoiceDossierPayment;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method ShopInvoiceDossierPayment|null find($id, $lockMode = null, $lockVersion = null)
 * @method ShopInvoiceDossierPayment|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method ShopInvoiceDossierPayment[]    findAll()
 * @method ShopInvoiceDossierPayment[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class ShopInvoiceDossierPaymentRepository extends AbstractEntityRepository
{
}
