<?php
namespace MatGyver\Repository\Surcharge;

use MatGyver\Entity\Surcharge\SurchargeComment;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method SurchargeComment|null find($id, $lockMode = null, $lockVersion = null)
 * @method SurchargeComment|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method SurchargeComment[]    findAll()
 * @method SurchargeComment[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SurchargeCommentRepository extends AbstractEntityRepository
{

}
