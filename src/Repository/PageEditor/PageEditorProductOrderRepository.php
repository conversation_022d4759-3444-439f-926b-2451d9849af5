<?php

namespace MatGyver\Repository\PageEditor;

use MatGyver\Entity\PageEditor\PageEditor;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method PageEditor|null find($id, $lockMode = null, $lockVersion = null)
 * @method PageEditor|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method PageEditor[]    findAll()
 * @method PageEditor[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class PageEditorProductOrderRepository extends AbstractEntityRepository
{
    public function findPageEditor(int $idProduct, ?int $idClient = null)
    {
        return $this->findOneBy(['product' => $idProduct, 'client' => $idClient]);
    }

    public function postUpdate(PageEditor $pageEditor, array $design)
    {
        $product = $pageEditor->getProduct();
        if (!$product) {
            return null;
        }

        /*$product->setDisplayHT(false);
        if (isset($design['display_ht']) and $design['display_ht']) {
            $product->setDisplayHT(true);
        }*/

        return $product;
    }
}
