<?php
namespace MatGyver\EventListener\Subscriber\Game;

use MatGyver\Event\Game\GameQuestStepEvent;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\Game\GameQuestClientService;
use MatGyver\Services\Logger\LoggerService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class GameQuestStepEventSubscriber implements EventSubscriberInterface
{
    /**
     * @return array
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            GameQuestStepEvent::QUEST_STEP_COMPLETED => 'onQuestStepCompleted',
        );
    }

    /**
     * @param GameQuestStepEvent $event
     * @return void
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function onQuestStepCompleted(GameQuestStepEvent $event)
    {
        $quest = $event->getQuest();
        $step = $event->getStep();
        if (!$quest or !$step) {
            return;
        }

        $questClient = $quest->getQuestClient();
        if (!$questClient) {
            return;
        }
        if ($questClient->getStep() !== $step) {
            return;
        }

        $container = ContainerBuilderService::getInstance();
        $goToNextStep = $container->get(GameQuestClientService::class)->goToNextStep($quest);
        if (!$goToNextStep['valid']) {
            LoggerService::logError(sprintf('onQuestStepCompleted (quest %s / step %s) : %s', $quest->getIdentifier(), $step->getIdentifier(), $goToNextStep['message']));
            return;
        }

        $nextStep = $step->getNext();
        if ($nextStep and $nextStep->getStartRoute()) {
            $container->get(DispatcherService::class)->redirectToRoute($nextStep->getStartRoute(), [], ['quest' => $quest->getId(), 'step' => $nextStep->getId()]);
        }
    }
}
