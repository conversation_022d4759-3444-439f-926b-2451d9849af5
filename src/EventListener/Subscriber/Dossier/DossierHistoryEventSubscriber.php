<?php
namespace Mat<PERSON><PERSON>ver\EventListener\Subscriber\Dossier;

use MatGyver\Components\Mailer\MailSender;
use Mat<PERSON>yver\Entity\Dossier\Dossier;
use MatGyver\Entity\Dossier\DossierHistory;
use Mat<PERSON>yver\Entity\Notification\Notification;
use Mat<PERSON>yver\Enums\UserEmailNotificationTypeEnum;
use MatGyver\Event\Dossier\DossierHistoryEvent;
use MatGyver\Helpers\Aws\Message;
use MatGyver\Helpers\Aws\Queue;
use MatGyver\Helpers\Tools;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Dossier\DossierService;
use MatGyver\Services\Dossier\DossierUserService;
use MatGyver\Services\Integration\Services\GoogleAgendaService;
use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\NotificationsService;
use MatGyver\Services\Users\UsersService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class DossierHistoryEventSubscriber implements EventSubscriberInterface
{
    /**
     * @return array
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            DossierHistory::ACTION_DOCUMENT => 'onDocumentAdd',
            DossierHistory::ACTION_DOCUMENT_UPDATE => 'onDocumentAdd',
            DossierHistory::ACTION_DOCUMENT_ROTATE => 'onDocumentAdd',
            DossierHistory::ACTION_CANCEL => 'sendNotification',
            /*DossierHistory::ACTION_EXPERTISE_CONFIRMED => 'sendNotification',
            DossierHistory::ACTION_EXPERTISE_COMPLETED => 'sendNotification',*/
            DossierHistory::ACTION_FINISHED => 'sendNotification',
            /*DossierHistory::ACTION_DOCUMENT_CONVOCATION => 'sendNotification',
            DossierHistory::ACTION_DOCUMENT_SUMMARY_SENT => 'sendNotification',
            DossierHistory::ACTION_DOCUMENT_EXPERTISE_SENT => 'sendNotification',
            DossierHistory::ACTION_DOCUMENT_CONTRADICTORY_SENT => 'sendNotification',
            DossierHistory::ACTION_INSTITUTION => 'sendNotification',*/
            DossierHistory::ACTION_PAYMENT => 'sendNotification',
            /*DossierHistory::ACTION_SENT => 'sendNotification',
            DossierHistory::ACTION_SIGN => 'sendNotification',
            DossierHistory::ACTION_SIGN_REFUSED => 'sendNotification',*/
            DossierHistory::ACTION_UPDATE_STATUS => 'onUpdateStatus',
            DossierHistory::ACTION_DOCUMENT_CONVOCATION => 'setConvocation',
        );
    }

    /**
     * @param DossierHistoryEvent $event
     * @return void
     */
    public function onDocumentAdd(DossierHistoryEvent $event): void
    {
        $history = $event->getDossierHistory();
        $documentId = $history->getParam();

        $queue = new Queue('default');
        $message = new Message($queue, 'dossier_document_thumbnail', $documentId);
        $message->send();
    }

    /**
     * @param DossierHistoryEvent $event
     * @return void
     */
    public function sendNotification(DossierHistoryEvent $event): void
    {
        $history = $event->getDossierHistory();
        $dossier = $history->getDossier();

        $container = ContainerBuilderService::getInstance();
        $currentUser = $container->get(UsersService::class)->getUser();
        $dossierUsers = $container->get(DossierUserService::class)->getRepository()->findBy(['dossier' => $dossier]);
        if (!$dossierUsers) {
            return;
        }

        foreach ($dossierUsers as $id => $dossierUser) {
            if ($currentUser and $currentUser === $dossierUser->getUser()) {
                unset($dossierUsers[$id]);
            }
        }
        if (!$dossierUsers) {
            return;
        }

        $subject = $history->getActionName();
        $message = $history->displayAction();
        $message .= '<br><br><a class="btn btn-primary" href="' . Tools::makeLink('app', 'dossier', $dossier->getId()) . '">' . __('Voir le dossier') . '</a>';
        NotificationsService::insert($subject, $message, Notification::TYPE_IMPORTANT, $dossier->getClient()->getId());

        foreach ($dossierUsers as $dossierUser) {
            if (!$dossierUser->getEmail()) {
                continue;
            }

            $recipient = [['first_name' => $dossierUser->getUser()->getFirstName(), 'last_name' => $dossierUser->getUser()->getLastName(), 'email' => $dossierUser->getUser()->getEmail()]];
            $vars = [
                'SUBJECT_DOSSIER' => $dossier->getSubject(),
                'DOSSIER' => $dossier->getContact()->getFirstName() . ' ' . $dossier->getContact()->getLastName(),
                'DOSSIER_LINK' => Tools::makeLink('app', 'dossier', $dossier->getId()),
                'VEHICLE_REGISTRATION' => ($dossier->getVehicle() ? $dossier->getVehicle()->getRegistration() : ''),
                'ACTION' => $history->displayAction(),
            ];
            $send = $container->get(MailSender::class)->sendTemplateToClient('dossier_notification', $vars, UserEmailNotificationTypeEnum::IMPORTANT, $recipient, $dossier->getClient()->getId());
            if (!$send['valid']) {
                LoggerService::logError('Unable to send notification email : ' . $send['message']);
            }
        }
    }

    /**
     * @param DossierHistoryEvent $event
     * @return void
     */
    public function onUpdateStatus(DossierHistoryEvent $event): void
    {
        $dossier = $event->getDossierHistory()->getDossier();

        $container = ContainerBuilderService::getInstance();
        if ($dossier->getStatus() == Dossier::STATUS_CANCELED) {
            $expertises = $dossier->getExpertises();
            foreach ($expertises as $expertise) {
                if ($expertise->getGoogleCalendarEventId()) {
                    $container->get(GoogleAgendaService::class)->deleteExpertise($expertise);
                }
            }
            return;
        }

        /*if ($dossier->getStatus() != Dossier::STATUS_CLOSED) {
            return;
        }
        if ($dossier->getCompressed()) {
            return;
        }

        //compress documents
        $container->get(DossierService::class)->compress($dossier);*/
    }

    /**
     * @param DossierHistoryEvent $event
     * @return void
     */
    public function setConvocation(DossierHistoryEvent $event): void
    {
        $history = $event->getDossierHistory();
        $container = ContainerBuilderService::getInstance();
        $container->get(DossierService::class)->setFirstConvocation($history->getDossier(), $history->getDate());
    }
}
