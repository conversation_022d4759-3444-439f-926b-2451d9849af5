<?php if ($addPlaceholder) : ?>
<option></option>
<?php endif; ?>
<?php foreach ($groups as $groupName => $options) : ?>
    <optgroup label="<?php echo $groupName; ?>">
        <?php foreach ($options as $optionValue => $optionName) : ?>
            <?php if (is_array($selectedValue)) : ?>
                <?php $selected = (in_array($optionValue, $selectedValue)); ?>
            <?php else : ?>
                <?php $selected = ($optionValue and $selectedValue and $selectedValue == $optionValue); ?>
            <?php endif; ?>
            <option value="<?php echo $optionValue; ?>" <?php echo ($selected ? 'selected' : ''); ?>>
                <?php echo $optionName; ?>
            </option>
        <?php endforeach; ?>
    </optgroup>
<?php endforeach; ?>
