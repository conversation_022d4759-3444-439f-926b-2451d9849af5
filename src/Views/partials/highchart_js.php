<script type="text/javascript">
    $(document).ready(function () {
        $('#<?php echo $id; ?>').highcharts({
            chart: {
                <?php if (isset($options['type'])) : ?>
                type: '<?php echo $options['type']; ?>',
                <?php endif; ?>
                spacing: [40, 40, 15, 40],
                <?php if (isset($options['type']) and $options['type'] == 'gauge') : ?>
                plotBackgroundColor: null,
                plotBackgroundImage: null,
                plotBorderWidth: 0,
                plotShadow: false,
                height: '100%'
                <?php endif; ?>
            },
            title: {
                text: '<?php echo addslashes($title); ?>'
            },
            credits: {
                enabled: false
            },
            <?php if (isset($options['type']) and $options['type'] == 'gauge') : ?>
            pane: {
                startAngle: -90,
                endAngle: 90,
                background: null,
                center: ['50%', '75%'],
                size: '100%'
            },
            <?php endif; ?>
            xAxis: {
                tickInterval:1,
                <?php if (isset($options['ticks'])) : ?>
                categories: [
                    <?php echo '\'' . implode('\',\'', $options['ticks']) . '\''; ?>
                ]
                <?php endif; ?>
            },
            yAxis: {
                min: 0,
                <?php if (isset($options['type']) and $options['type'] == 'gauge') : ?>
                max: <?php echo $options['max']; ?>,
                <?php endif; ?>
                gridLineColor: '#edeff4',
                <?php if (isset($options['yAxisTitle'])) : ?>
                title: { enabled: true, text: '<?php echo $options['yAxisTitle']; ?>' },
                <?php else : ?>
                title: { enabled: false },
                <?php endif; ?>
                allowDecimals: false,
                <?php if (isset($options['type']) and $options['type'] == 'gauge') : ?>
                tickPixelInterval: <?php echo $options['max']; ?>,
                tickPosition: 'inside',
                tickColor: Highcharts.defaultOptions.chart.backgroundColor || '#FFFFFF',
                tickLength: 5,
                tickWidth: 1,
                minorTickInterval: null,
                lineWidth: 0,
                labels: {
                    distance: 20,
                    style: {
                        fontSize: '14px'
                    }
                },
                <?php else : ?>
                plotLines: [{
                    color: '#9a9fa6',
                    width: 1,
                    value: 0
                }],
                <?php endif; ?>
                <?php if (isset($options['plotBands'])) : ?>
                plotBands: <?php echo json_encode($options['plotBands'], JSON_PRETTY_PRINT); ?>,
                <?php endif; ?>
            },
            <?php if (!isset($options['type']) or $options['type'] != 'gauge') : ?>
            tooltip: {
                shared: true,
                crosshairs: true,
                style: {
                    fontSize: '14px'
                }
            },
            <?php endif; ?>
            plotOptions: {
                line: {
                    lineWidth: 1,
                    color: '#555555'
                },
                <?php if (isset($options['type']) and $options['type'] == 'pie') : ?>
                series: {
                    allowPointSelect: true,
                    cursor: 'pointer',
                    dataLabels: [
                        {
                            enabled: true,
                            distance: 20,
                            style: {
                                fontSize: '1em',
                                textOutline: 'none',
                            },
                        },
                        {
                            enabled: true,
                            distance: -40,
                            format: '{point.percentage:.1f}%',
                            style: {
                                fontSize: '1.2em',
                                textOutline: 'none',
                                color: 'white'
                            },
                        }
                    ]
                },
                <?php else : ?>
                series: {
                    cursor: 'pointer',
                    marker: {
                        lineWidth: 1
                    }
                },
                <?php endif; ?>
                <?php if (isset($options['pie']['colors'])) : ?>
                pie: {
                    colors: ['<?php echo implode('\',\'', $options['pie']['colors']); ?>'],
                }
                <?php endif; ?>
            },
            legend: {
                itemMarginTop: 20
            },
            series: [
                <?php foreach ($series as $set) : ?>
                {
                    name: '<?php echo addslashes($set['title']); ?>',
                    data: [ <?php echo $set['points']; ?> ],
                    shadow: {
                        color: '#FFFFFF',
                        width: 3,
                        opacity: 1
                    },
                    lineWidth: 3,
                    states: {
                        hover: {
                            halo: {opacity: 0},
                            lineWidthPlus:0
                        }
                    },
                    marker: {
                        radius: 3,
                        enabled: false,
                        fillColor: '#999999',
                        states: {
                            hover: {
                                enabled: true,
                                fillColor: '#ffffff',
                                lineColor: '#ff477d',
                                lineWidth: 3
                            }
                        }
                    },
                    <?php if (isset($options['type']) and $options['type'] == 'gauge') : ?>
                        dataLabels: {
                            <?php if (isset($options['dataLabel']) and $options['dataLabel']) : ?>
                            format: '{y} <?php echo $options['dataLabel']; ?>',
                            <?php endif; ?>
                            borderWidth: 0,
                            color: (
                                Highcharts.defaultOptions.title &&
                                Highcharts.defaultOptions.title.style &&
                                Highcharts.defaultOptions.title.style.color
                            ) || '#333333',
                            style: {
                                fontSize: '14px'
                            }
                        },
                        dial: {
                            radius: '60%',
                            backgroundColor: 'gray',
                            baseWidth: 8,
                            baseLength: '0%',
                            rearLength: '0%'
                        },
                        pivot: {
                            backgroundColor: 'gray',
                            radius: 4
                        },
                    <?php endif; ?>
                },
                <?php endforeach; ?>
            ]
        });
    });
</script>
