<?php

namespace Mat<PERSON>yver\Factories\BlankStates;

use MatGyver\Entity\Dossier\Dossier;
use Mat<PERSON>yver\Helpers\Tools;

class DossierCourriersBlankState extends AbstractBlankState
{
    /**
     * @param Dossier $dossier
     */
    public function __construct(Dossier $dossier)
    {
        $this->setTitle(__('Courriers'));
        $this->setDescription(__('Aucun courrier envoyé dans ce dossier.'));
        $this->setButtonUrl(Tools::makeLink('app', 'dossier', 'courrier/send/' . $dossier->getId()));
        $this->setButtonText(__('Envoyer un courrier'));
        $this->setIcon('
        <span class="svg-icon svg-icon-primary svg-icon-8x">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                    <rect x="0" y="0" width="24" height="24"></rect>
                    <path d="M5,6 L19,6 C20.1045695,6 21,6.8954305 21,8 L21,17 C21,18.1045695 20.1045695,19 19,19 L5,19 C3.8954305,19 3,18.1045695 3,17 L3,8 C3,6.8954305 3.8954305,6 5,6 Z M18.1444251,7.83964668 L12,11.1481833 L5.85557487,7.83964668 C5.4908718,7.6432681 5.03602525,7.77972206 4.83964668,8.14442513 C4.6432681,8.5091282 4.77972206,8.96397475 5.14442513,9.16035332 L11.6444251,12.6603533 C11.8664074,12.7798822 12.1335926,12.7798822 12.3555749,12.6603533 L18.8555749,9.16035332 C19.2202779,8.96397475 19.3567319,8.5091282 19.1603533,8.14442513 C18.9639747,7.77972206 18.5091282,7.6432681 18.1444251,7.83964668 Z" fill="#000000"></path>
                </g>
            </svg>
        </span>');

        $this->setLinkText(__('Télécharger un modèle'));
        $this->setLinkUrl(Tools::makeLink('app', 'dossier', 'courrier/download/word/' . $dossier->getId()));
    }
}
