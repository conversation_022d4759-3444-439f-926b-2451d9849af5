<?php

namespace MatGyver\Factories\BlankStates;

use Mat<PERSON><PERSON>ver\Entity\Dossier\Dossier;
use MatG<PERSON>ver\Entity\Dossier\Expertise\DossierExpertise;
use MatGyver\Helpers\Tools;

class DossierExpertiseCompletedBlankState extends AbstractBlankState
{
    /**
     * @param Dossier $dossier
     */
    public function __construct(Dossier $dossier)
    {
        $this->setTitle(__('Expertise terminée'));
        $this->setDescription(__('Cette expertise est terminée et ne peut donc plus être modifiée. Vous pouvez créer une nouvelle expertise en cliquant sur le bouton ci-dessous.'));
        $this->setButtonUrl(Tools::makeLink('app', 'dossier', 'expertise/add/' . $dossier->getId()));
        $this->setButtonText(__('Créer une nouvelle expertise'));
        $this->setIcon('<i class="flaticon2-information icon-8x text-primary"></i>');
    }
}
