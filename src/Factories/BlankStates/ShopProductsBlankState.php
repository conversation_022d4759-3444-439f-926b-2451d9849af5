<?php

namespace MatGyver\Factories\BlankStates;

use MatGyver\Helpers\Tools;

class ShopProductsBlankState extends AbstractBlankState
{
    public function __construct()
    {
        $this->setTitle(__('Produits'));
        $this->setDescription(__('Oups, nous n\'avons trouvé aucun produit pour l\'instant !'));
        $this->setButtonUrl(Tools::makeLink('admin', 'shop', 'product/add'));
        $this->setButtonText(__('Ajouter un produit'));
        $this->setIcon('<i class="flaticon2-shopping-cart icon-8x text-primary"></i>');
    }
}
