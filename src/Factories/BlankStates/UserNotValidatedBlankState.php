<?php

namespace MatGyver\Factories\BlankStates;

use MatGyver\Entity\User\User;
use MatGyver\Helpers\Tools;

class UserNotValidatedBlankState extends AbstractBlankState
{
    public function __construct(User $user)
    {
        $this->setTitle(__('Utilisateur non validé'));
        $this->setDescription(__('Cet utilisateur n\'a pas encore accepté votre invitation.'));
        $this->setButtonUrl(Tools::makeLink($_SESSION['controller'], 'user', 'invite/email/' . $user->getId()));
        $this->setButtonText(__('Renvoyer l\'invitation'));
        $this->setIcon('<i class="flaticon-user icon-8x text-primary"></i>');
    }
}
