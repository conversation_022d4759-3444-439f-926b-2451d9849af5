<?php

namespace MatGyver\Factories\BlankStates;

use MatGyver\Entity\Dossier\Dossier;
use MatGyver\Helpers\Tools;

class DossierHistovecErrorBlankState extends AbstractBlankState
{
    /**
     * @param Dossier $dossier
     * @param string  $errorMessage
     */
    public function __construct(Dossier $dossier, string $errorMessage)
    {
        $this->setTitle(__('Une erreur est survenue'));
        $this->setDescription($errorMessage);
        $this->setButtonUrl(Tools::makeLink('app', 'dossier', 'histovec/' . $dossier->getId()));
        $this->setButtonText(__('Réessayer'));
        $this->setLinkUrl(Tools::makeLink('app', 'dossier', 'histovec/edit/' . $dossier->getId()));
        $this->setLinkText(__('Modifier les paramètres'));
        $this->setIcon('<i class="flaticon2-information icon-8x text-danger"></i>');
    }
}
