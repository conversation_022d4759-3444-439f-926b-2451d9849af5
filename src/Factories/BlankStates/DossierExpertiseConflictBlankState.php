<?php

namespace MatGyver\Factories\BlankStates;

use MatGyver\Entity\Dossier\Expertise\DossierExpertise;
use MatGyver\Helpers\Tools;

class DossierExpertiseConflictBlankState extends AbstractBlankState
{
    /**
     * @param DossierExpertise $expertise
     * @param DossierExpertise $otherExpertise
     */
    public function __construct(DossierExpertise $expertise, DossierExpertise $otherExpertise)
    {
        $this->setTitle(__('Date similaire à une autre expertise'));

        $content = __('Une autre expertise est programmée à une date proche de cette expertise. Confirmez la date et l\'heure de l\'expertise en cliquant sur le bouton ci-dessous.') . '<br><br>';

        $start = $expertise->getDate();
        $end = clone $start;
        $end->modify('+2 hours');
        $content .= __('Date de l\'expertise actuelle :<br>le %s de %s à %s', '<strong>' . dateFrench('d F Y', $expertise->getDate()->getTimestamp()), $start->format('H\hi'), $end->format('H\hi')) . '</strong><br><br>';


        $start = $otherExpertise->getDate();
        $end = clone $start;
        $end->modify('+2 hours');
        $content .= __('Autre expertise trouvée :') . '<br>';
        $content .= $otherExpertise->getDossier()->getContact()->getCompanyOrName() . '<br>';
        $content .= __('Prévue le %s de %s à %s', '<strong>' . dateFrench('d F Y', $otherExpertise->getDate()->getTimestamp()), $start->format('H\hi'), $end->format('H\hi') . '</strong>') . '<br>';


        $buttonUrl = Tools::makeLink('app', 'dossier', 'expertise/persons/' . $expertise->getId());
        $linkUrl = Tools::makeLink('app', 'dossier', 'expertise/edit/' . $expertise->getId());

        if (isset($_GET['route'])) {
            $route = filter_input(INPUT_GET, 'route', FILTER_UNSAFE_RAW);
            if ($route == 'app_dossier') {
                $buttonUrl = Tools::makeLink('app', 'dossier', $expertise->getDossier()->getId());
                $linkUrl = Tools::makeLink('app', 'dossier', 'expertise_date/' . $expertise->getDossier()->getId());
            } elseif ($route == 'app_dossier_expertise_date') {
                $buttonUrl = Tools::makeLink('app', 'dossier', 'institutions/' . $expertise->getDossier()->getId());
                $linkUrl = Tools::makeLink('app', 'dossier', 'expertise_date/' . $expertise->getDossier()->getId());
            } elseif ($route == 'app_dossier_expertise_edit2' or $route == 'app_dossier_expertise_add') {
                $buttonUrl = Tools::makeLink('app', 'dossier', 'expertises/' . $expertise->getDossier()->getId());
                $linkUrl = Tools::makeLink('app', 'dossier', 'expertise_edit/' . $expertise->getId() . '/' . $expertise->getDossier()->getId());
            }
        }

        $this->setDescription($content);
        $this->setButtonUrl($buttonUrl);
        $this->setButtonText(__('Confirmer la date d\'expertise'));
        $this->setLinkUrl($linkUrl);
        $this->setLinkText(__('Modifier la date d\'expertise'));

        $this->setIcon('<i class="flaticon-calendar-with-a-clock-time-tools icon-8x text-primary"></i>');
    }
}
