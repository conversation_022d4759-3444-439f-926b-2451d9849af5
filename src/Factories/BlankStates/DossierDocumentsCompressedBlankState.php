<?php

namespace MatGyver\Factories\BlankStates;

use MatGyver\Entity\Dossier\Dossier;
use MatGyver\Helpers\Tools;

class DossierDocumentsCompressedBlankState extends AbstractBlankState
{
    /**
     * @param Dossier $dossier
     */
    public function __construct(Dossier $dossier)
    {
        $this->setTitle(__('Documents compressés'));

        $this->setDescription(__('Ce dossier est archivé.') . '<br>' . __('Les documents de ce dossier ont donc été compressés et sont inaccessibles actuellement.'));
        $this->setButtonUrl(Tools::makeLink('app', 'dossier', 'documents/uncompress/' . $dossier->getId()));
        $this->setButtonText(__('Décompresser les documents'));
        $this->setIcon('<i class="fas fa-file-archive icon-8x text-primary"></i>');
    }
}
