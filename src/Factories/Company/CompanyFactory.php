<?php

namespace MatGyver\Factories\Company;

use MatGyver\Entity\Company\Company;
use MatGyver\Enums\ConfigEnum;

/**
 * Class CompanyFactory
 * @package MatGyver\Factories
 */
class CompanyFactory
{
    /**
     * @param array $settings
     * @return Company|null
     */
    public static function createFromSettings(array $settings): ?Company
    {
        $company = new Company();
        $company->setName($settings[ConfigEnum::COMPANY]);
        $company->setAddress(($settings[ConfigEnum::ADDRESS] ?? ''));
        $company->setAddress2(($settings[ConfigEnum::ADDRESS2] ?? ''));
        $company->setZip(($settings[ConfigEnum::ZIP] ?? ''));
        $company->setCity(($settings[ConfigEnum::CITY] ?? ''));
        $company->setCountry(($settings[ConfigEnum::COUNTRY] ?? ''));
        $company->setTelephone(($settings[ConfigEnum::TELEPHONE] ?? ''));
        return $company;
    }
}
