<?php

namespace MatGyver\Factories\Subscription;

use Doctrine\ORM\EntityManager;
use MatGyver\Entity\PaymentMethod\PaymentMethodSubscription;
use MatGyver\Entity\PaymentMethod\PaymentMethodCharge;
use MatGyver\Services\BaseEntityService;

/**
 * Class AbstractSubscriptionFactory
 * @package MatGyver\Factories\Subscription
 */
abstract class AbstractSubscriptionFactory extends BaseEntityService
{
    /**
     * AbstractSubscriptionFactory constructor.
     * @param EntityManager $em
     */
    public function __construct(EntityManager $em)
    {
        $this->em = $em;
    }

    /**
     * @param PaymentMethodSubscription $subscription
     * @param PaymentMethodCharge     $charge
     * @return PaymentMethodSubscription
     */
    abstract public function addMissingVars(PaymentMethodSubscription $subscription, PaymentMethodCharge $charge) : PaymentMethodSubscription;

    /**
     * @return PaymentMethodSubscription
     */
    abstract public function getSubscriptionEntity() : PaymentMethodSubscription;

    /**
     * @param PaymentMethodCharge $charge
     * @return array
     */
    public function createSubscriptionFromCharge(PaymentMethodCharge $charge): array
    {
        $log = "createSubscriptionFromCharge " . $charge->getTransactionReference() . "\n";

        $subscriptionEntity = $this->getSubscriptionEntity();
        $subscription = $this->em->getRepository(get_class($subscriptionEntity))->findOneBy(['transactionReference' => $charge->getTransactionReference(), 'client' => $charge->getClient()]);
        if ($subscription) {
            $log .= "a subscription already exists for this charge\n";
            return ['valid' => true, 'id_subscription' => 0, 'log' => $log];
        }

        $subscription = $this->createSubscriptionIfNecessary($charge);
        if (!$subscription) {
            $log .= __("Aucun abonnement à créer\n");
            return ['valid' => true, 'id_subscription' => 0, 'log' => $log];
        }

        $log .= __("Abonnement à créer :\n");
        $log .= "nb_payments : " . $subscription->getNbPayments() . "\n";
        $log .= "amount : " . $subscription->getAmount() . "\n";
        $log .= "next_date : " . $subscription->getDate()->format('Y-m-d H:i:s') . "\n";

        //add missing vars
        $this->addMissingVars($subscription, $charge);

        try {
            $this->persistAndFlush($subscription);
        } catch (\Exception $e) {
            return ['valid' => false, 'id_subscription' => 0, 'log' => $log . $e->getMessage()];
        }

        $idSubscription = $subscription->getId();

        //update charge
        $charge->setSubscriptionId($idSubscription);
        try {
            $this->persistAndFlush($charge);
        } catch (\Exception $e) {
            return ['valid' => false, 'id_subscription' => 0, 'log' => $log . $e->getMessage()];
        }

        return ['valid' => true, 'id_subscription' => $idSubscription, 'log' => $log];
    }

    /**
     * @param PaymentMethodCharge $charge
     * @return PaymentMethodSubscription|null
     * @throws \Exception
     */
    public function createSubscriptionIfNecessary(PaymentMethodCharge $charge): ?PaymentMethodSubscription
    {
        $checkout = json_decode($charge->getCheckout(), true);
        $custom = $charge->getCustom();
        $amount = $charge->getAmount();

        $createSubscription = false;
        if (isset($checkout['trial_period']) and $checkout['trial_period']) {
            //trial period
            $createSubscription = true;

            $amount = $checkout['amount_tax_incl'];
            $decalage = $checkout['trial_time'];
            $trialTimetype = $checkout['trial_timetype'];

            $nextDate = date('Y-m-d', strtotime('+' . $decalage . ' ' . $trialTimetype));
            $hour = date('H');
            $hour = ltrim($hour, '0');
            if (!$hour) {
                $hour = '0';
            }

            $nbPayments = '1';
            $subscriptionType = 'days';
            if ('subscription' == $checkout['payment']) {
                $nbPayments = $checkout['nb_payments'];
                $decalage = $checkout['subscription_time'];
                $subscriptionType = $checkout['subscription_timetype'];
            }

            if ('x' != $nbPayments) {
                $amount = round($amount / $nbPayments, 2);
            }

            $amount = $this->formatAmount($amount);
        } elseif (isset($checkout['payment']) and $checkout['payment'] == 'subscription') {
            $createSubscription = true;

            $nbPayments = $checkout['nb_payments'];
            if ($checkout['nb_payments'] != 'x') {
                $nbPayments--;
            }

            $decalage = $checkout['subscription_time'];
            $subscriptionType = $checkout['subscription_timetype'];

            $nextDate = date('Y-m-d', strtotime('+' . $decalage . ' ' . $subscriptionType));
            $hour = date('H');
            $hour = ltrim($hour, '0');
            if (!$hour) {
                $hour = '0';
            }
        }

        if (!$createSubscription) {
            return null;
        }

        $subscriptionEntity = $this->getSubscriptionEntity();
        $subscription = new $subscriptionEntity;
        $subscription->setClient($charge->getClient());
        $subscription->setTransactionReference($charge->getTransactionReference());
        $subscription->setAccount($charge->getAccount());
        $subscription->setProduct($charge->getProduct());
        $subscription->setAmount($amount);
        $subscription->setCurrency($charge->getCurrency());
        $subscription->setLastName($charge->getLastName());
        $subscription->setFirstName($charge->getFirstName());
        $subscription->setEmail($charge->getEmail());
        $subscription->setCustom($custom);
        $subscription->setCheckout(json_encode($checkout));
        $subscription->setIp($charge->getIp());
        $subscription->setNbPayments($nbPayments);
        $subscription->setNbPaymentsLeft($nbPayments);
        $subscription->setDecalage($decalage . ' ' . $subscriptionType);
        $subscription->setValid(true);
        $subscription->setReattempt(0);
        $subscription->setResult('');
        $subscription->setError('');
        $subscription->setDate(new \DateTime($nextDate));
        $subscription->setHour($hour);

        return $subscription;
    }
}
