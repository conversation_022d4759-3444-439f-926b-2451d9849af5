<?php

namespace MatGyver\Factories\Subscription;

use MatGyver\Entity\Braintree\Charge\BraintreeCharge;
use MatG<PERSON>ver\Entity\Braintree\Subscription\BraintreeSubscription;

/**
 * Class SubscriptionBraintreeFactory
 * @package MatGyver\Factories\Subscription
 */
class SubscriptionBraintreeFactory extends AbstractSubscriptionFactory implements SubscriptionFactoryInterface
{
    /**
     * @return BraintreeSubscription
     */
    public function getSubscriptionEntity(): BraintreeSubscription
    {
        return new BraintreeSubscription();
    }

    /**
     * @param BraintreeSubscription $subscription
     * @param BraintreeCharge $charge
     * @return BraintreeSubscription
     */
    public function addMissingVars($subscription, $charge): BraintreeSubscription
    {
        $subscription->setPaymentToken($charge->getPaymentToken());
        $subscription->setCustomerId($charge->getCustomerId());
        return $subscription;
    }

    /**
     * @param float $amount
     * @return string
     */
    public function formatAmount(float $amount): string
    {
        return number_format($amount, 2, '.', '');
    }
}
