<?php

namespace MatGyver\Factories\Subscription;

use Mat<PERSON>yver\Entity\Stripe\Charge\StripeCharge;
use MatGyver\Entity\Stripe\Subscription\StripeSubscription;

/**
 * Class SubscriptionStripeFactory
 * @package MatGyver\Factories\Subscription
 */
class SubscriptionStripeFactory extends AbstractSubscriptionFactory implements SubscriptionFactoryInterface
{
    /**
     * @return StripeSubscription
     */
    public function getSubscriptionEntity(): StripeSubscription
    {
        return new StripeSubscription();
    }

    /**
     * @param StripeSubscription $subscription
     * @param StripeCharge $charge
     * @return StripeSubscription
     */
    public function addMissingVars($subscription, $charge): StripeSubscription
    {
        $subscription->setCustomerId($charge->getCustomerId());
        $subscription->setPaymentMethod($charge->getPaymentMethod());
        $subscription->setSecretKey($charge->getSecretKey());
        return $subscription;
    }

    /**
     * @param float $amount
     * @return int
     */
    public function formatAmount(float $amount): int
    {
        return round($amount * 100);
    }
}
