<?php

namespace MatGyver\Factories\PageEditor\Templates;

use MatGyver\Factories\PageEditor\Elements\Types\TextElementFactory;
use MatGyver\Factories\PageEditor\Elements\Types\VideoElementFactory;

/**
 * Class SiteTosTemplateFactory
 * @package MatGyver\Factories\PageEditor\Templates
 */
class SiteAffiliationInscriptionTemplateFactory extends AbstractTemplateFactory
{
    public function init()
    {
        parent::init();
        $this->makePage();
    }

    private function makePage()
    {
        $elements[] = VideoElementFactory::add('video', '', __('Vidéo'), '.bloc-video');
        $elements[] = TextElementFactory::add('description', __('Description'), __('Description'), '.bloc-description');
        $this->addElements($elements);
    }
}
