<?php

namespace MatGyver\Factories\PageEditor\Elements\Types;

use MatGyver\Factories\PageEditor\Elements\Element;
use MatGyver\Factories\PageEditor\Elements\ElementFactory;

/**
 * Class StringElementFactory
 * @package MatGyver\Factories\PageEditor\Elements\Types
 */
class StringElementFactory
{
    /**
     * @param string $id
     * @param string $value
     * @param string|null $title
     * @param string|null $target
     * @param string $parent
     * @param bool $active
     * @param bool $action
     * @param bool $canHide
     * @param string $description
     * @return Element
     */
    public static function add(string $id, string $value, string $title = null, string $target = null, string $parent = '', bool $active = true, bool $action = true, bool $canHide = true, string $description = ''): Element
    {
        if (!$title) {
            $title = __('Texte');
        }
        if (!$target) {
            $target = '.bloc-' . $id . ' p';
        }

        return ElementFactory::make($id, $title, Element::TYPE_STRING, $target, $value, $parent, $active, $action, $canHide, [], $description);
    }
}
