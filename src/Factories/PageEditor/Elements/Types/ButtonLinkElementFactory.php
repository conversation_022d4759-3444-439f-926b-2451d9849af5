<?php

namespace MatGyver\Factories\PageEditor\Elements\Types;

use MatGyver\Factories\PageEditor\Elements\Element;
use MatGyver\Factories\PageEditor\Elements\ElementFactory;

/**
 * Class ButtonLinkElementFactory
 * @package MatGyver\Factories\PageEditor\Elements\Types
 */
class ButtonLinkElementFactory
{
    /**
     * @param string $id
     * @param string $value
     * @param string|null $title
     * @param string|null $target
     * @param string $size
     * @param string $color
     * @param string $link
     * @param string $parent
     * @param bool $active
     * @param bool $action
     * @param bool $canHide
     * @param string $description
     * @return Element
     */
    public static function add(string $id, string $value, string $title = null, string $target = null, string $size = 'btn', string $color = '', string $link = '', string $parent = '', bool $active = true, bool $action = true, bool $canHide = true, string $description = ''): Element
    {
        if (!$title) {
            $title = __('Bouton');
        }
        if (!$target) {
            $target = '.bloc-' . $id . ' .btn';
        }

        $parameters = [
            'size' => $size,
            'color' => $color,
            'link' => $link
        ];

        return ElementFactory::make($id, $title, Element::TYPE_BUTTON_LINK, $target, $value, $parent, $active, $action, $canHide, $parameters, $description);
    }
}
