<?php

namespace MatGyver\Factories\PageEditor\Elements\Types;

use MatGyver\Factories\PageEditor\Elements\Element;
use MatGyver\Factories\PageEditor\Elements\ElementFactory;

/**
 * Class ButtonCategoryElementFactory
 * @package MatGyver\Factories\PageEditor\Elements\Types
 */
class ButtonCategoryElementFactory
{
    /**
     * @param string $id
     * @param string $value
     * @param string|null $title
     * @param string|null $target
     * @param string $size
     * @param string $color
     * @param int $idCategory
     * @param string $parent
     * @param bool $active
     * @param bool $action
     * @param bool $canHide
     * @param string $description
     * @return Element
     */
    public static function add(string $id, string $value, string $title = null, string $target = null, string $size = 'btn', string $color = '', int $idCategory = 0, string $parent = '', bool $active = true, bool $action = true, bool $canHide = true, string $description = ''): Element
    {
        if (!$title) {
            $title = __('Bouton');
        }
        if (!$target) {
            $target = '.bloc-' . $id . ' .btn';
        }

        $parameters = [
            'size' => $size,
            'color' => $color,
            'id_category' => $idCategory,
            'link' => '',
        ];

        return ElementFactory::make($id, $title, Element::TYPE_BUTTON_CATEGORY, $target, $value, $parent, $active, $action, $canHide, $parameters, $description);
    }
}
