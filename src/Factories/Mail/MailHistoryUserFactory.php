<?php

namespace MatGyver\Factories\Mail;

use MatGyver\Entity\Mail\History\MailHistory;
use MatG<PERSON>ver\Entity\Mail\History\MailHistoryUser;
use MatGyver\Entity\Mail\Queue\MailQueueUser;
use MatGyver\Entity\User\User;

/**
 * Class MailQueueUserFactory
 * @package MatGyver\Factories\Mail
 */
class MailHistoryUserFactory
{

    /**
     * @param MailHistory $mailHistory
     * @param MailQueueUser $mailQueueUser
     * @param User|null $user
     * @return MailHistoryUser|null
     */
    public function createOneFromMailQueueUser(MailHistory $mailHistory, MailQueueUser $mailQueueUser, ?User $user): ?MailHistoryUser
    {
        $mailHistoryUser = new MailHistoryUser();
        $mailHistoryUser->setMail($mailHistory);
        $mailHistoryUser->setClient($mailHistory->getClient());
        $mailHistoryUser->setUser($user);
        $mailHistoryUser->setFirstName($mailQueueUser->getFirstName());
        $mailHistoryUser->setLastName($mailQueueUser->getLastName());
        $mailHistoryUser->setEmail($mailQueueUser->getEmail());
        $mailHistoryUser->setDate(new \DateTime());

        return $mailHistoryUser;
    }
}
