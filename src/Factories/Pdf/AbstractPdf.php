<?php

namespace MatGyver\Factories\Pdf;

use MatGyver\Enums\ConfigEnum;

/**
 * class AbstractPdf
 * @package MatGyver\Factories\Pdf
 */
abstract class AbstractPdf extends \FPDF
{
    public string $topRightTitle = '';
    public string $mainTitle = '';
    public string $subTitle = '';
    public array $clientInfos = [];

    /**
     * @return string
     */
    public function getTopRightTitle(): string
    {
        return $this->topRightTitle;
    }

    /**
     * @param string $topRightTitle
     */
    public function setTopRightTitle(string $topRightTitle): void
    {
        $this->topRightTitle = $topRightTitle;
    }

    /**
     * @return string
     */
    public function getMainTitle(): string
    {
        return $this->mainTitle;
    }

    /**
     * @param string $mainTitle
     */
    public function setMainTitle(string $mainTitle): void
    {
        $this->mainTitle = $mainTitle;
    }

    /**
     * @return string
     */
    public function getSubTitle(): string
    {
        return $this->subTitle;
    }

    /**
     * @param string $subTitle
     */
    public function setSubTitle(string $subTitle): void
    {
        $this->subTitle = $subTitle;
    }

    /**
     * @return array
     */
    public function getClientInfos(): array
    {
        return $this->clientInfos;
    }

    /**
     * @param array $clientInfos
     */
    public function setClientInfos(array $clientInfos): void
    {
        $this->clientInfos = $clientInfos;
    }

    /**
     * @return void
     */
    public function Header()
    {
        $this->SetAutoPageBreak(true, 25);


        $clientInfos = $this->getClientInfos();
        if (ENV !== ENV_PROD) {
            $this->Image('https://app.rcpj.fr/assets/images/logo.png', 10, 6, 40);
        } elseif ($clientInfos[ConfigEnum::LOGO]) {
            $this->Image($clientInfos[ConfigEnum::LOGO], 10, 6, 40);
        }

        $this->SetFont('Helvetica', 'B', 15);
        $this->Cell(80);
        if ($this->getTopRightTitle()) {
            $this->SetTextColor(188, 72, 119);
            $this->Cell(110, 2, $this->formatText($this->getTopRightTitle()), 0, 0, 'C');
            $this->Ln(20);
            $this->SetTextColor(0);
        } else {
            $this->Ln(10);
        }
        if ($this->getMainTitle()) {
            $this->SetTextColor(38, 91, 195);
            $this->MultiCell(190, 6, $this->formatText($this->getMainTitle()), 0, 'C');
            $this->SetTextColor(0);
            $this->Ln(5);
        }
        if ($this->getSubTitle()) {
            $this->Ln(1);
            $this->SetFont('Helvetica', 'B', 9);
            $this->Cell(190, 0, $this->formatText($this->getSubTitle()), 0, 0, 'C');
            $this->Ln(5);
        }
        $this->Ln(10);
    }

    /**
     * @return void
     */
    public function Footer()
    {
        $clientInfos = $this->getClientInfos();

        $this->SetY(-22);
        $this->SetFont('Helvetica','B', 8);
        $this->SetTextColor(27, 15, 173);
        $this->Cell(90, 0, $this->formatText($clientInfos[ConfigEnum::COMPANY]), 0, 0, 'L');
        $this->Ln(3);
        $this->SetFont('Helvetica','');
        $this->SetTextColor(0);
        $this->Cell(90, 0, $this->formatText($clientInfos['name']), 0, 0, 'L');
        $this->Ln(1);

        $address = $clientInfos[ConfigEnum::ADDRESS];
        if ($clientInfos[ConfigEnum::ADDRESS2]) {
            $address .= ', ' . $clientInfos[ConfigEnum::ADDRESS2];
        }
        $address .= "\n" . $clientInfos[ConfigEnum::ZIP] . ' ' . $clientInfos[ConfigEnum::CITY];
        if ($clientInfos[ConfigEnum::TELEPHONE]) {
            $address .= "\nTél. " . $clientInfos[ConfigEnum::TELEPHONE];
        }
        $address .= "\nMail. " . $clientInfos['email'];

        $this->MultiCell(0, 3, $this->formatText($address), 0, 'L');

        $this->SetY(-7);
        $this->SetFont('Helvetica', 'I', 8);
        $this->Cell(0, 0, 'Page ' . $this->PageNo() . '/{nb}', 0, 0, 'R');
    }

    /**
     * @param string $title
     * @param bool   $addContent
     * @param string $content
     * @return void
     */
    public function addSection(string $title, bool $addContent, string $content)
    {
        $this->AddPage();
        $this->addSectionTitle($title);
        if ($addContent) {
            $this->addSectionContent($content);
        }
    }

    /**
     * @param string $title
     * @return void
     */
    public function addSectionTitle(string $title)
    {
        $this->SetFont('Helvetica', 'B', 12);
        $this->SetFillColor(38, 91, 195);
        $this->SetTextColor(255);
        $this->Cell(0, 8, ' ' . $this->formatText($title), 0, 1, 'L', true);
        $this->Ln(4);
        $this->SetTextColor(0);
        $this->SetFont('');
    }

    public function addSectionContent(string $content)
    {
        $this->SetFont('Helvetica', '', 12);
        $this->MultiCell(0, 5, $content);
        $this->Ln();
    }

    /**
     * @param array $columns
     * @param array $data
     * @param array $w
     * @return void
     */
    public function addTable(array $columns, array $data, array $w = [95, 95])
    {
        // Couleurs, épaisseur du trait et police grasse
        $this->SetFillColor(188, 72, 119);
        $this->SetTextColor(255);
        $this->SetDrawColor(188, 72, 119);
        $this->SetLineWidth(.3);
        $this->SetFont('Helvetica', 'B');

        // En-tête
        if ($columns) {
            foreach ($columns as $id => $column) {
                $this->Cell($w[$id], 8, $this->formatText($column), 1, 0, 'L', true);
            }
            $this->Ln();
        }

        // Restauration des couleurs et de la police
        $this->SetFillColor(224,235,255);
        $this->SetTextColor(0);
        $this->SetFont('Helvetica');

        // Données
        $fill = false;
        $x = 10;
        $y = $this->GetY();
        $pageNumber = $this->PageNo();

        $lineHeight = 8;
        foreach($data as $row) {
            //reinit
            $yPositions = [];
            $this->SetXY($x, $y);

            $this->MultiCell($w[0],$lineHeight,$this->formatText($row[0]),'T','L',$fill);
            $yPositions[] = $this->GetY();
            $this->SetXY($x + $w[0], $y);

            if (filter_var($row[1], FILTER_VALIDATE_URL)) {
                if (ENV !== ENV_PROD) {
                    $row[1] = str_replace('https://profeelexpert.code:8890/medias/644bcc2d8ecf7498709257/', 'https://app.rcpj.fr/medias/645a90e42ce4f864344643/', $row[1]);
                    $row[1] = str_replace('https://profeelexpert.code:8890/medias/645a90e42ce4f864344643/', 'https://app.rcpj.fr/medias/645a90e42ce4f864344643/', $row[1]);
                }
                $this->Cell($w[1], $lineHeight, $this->Image($row[1]), 'T', 0, 'L', $fill);
            } elseif (str_contains($row[1], WEB_PATH)) {
                $size = @getimagesize($row[1]);
                if (!$size) {
                    $this->Cell($w[1], $lineHeight, '', 'T', 0, 'L');
                } else {
                    $imageWidth = $size[0];
                    $imageHeight = $size[1];
                    $height = round($imageHeight * $w[1] / $imageWidth);
                    $this->Cell($w[1], $lineHeight, $this->Image($row[1], null, null, $w[1], $height), 'T', 0, 'C');
                }
            } else {
                $this->MultiCell($w[1], $lineHeight, $this->formatText($row[1]), 'T', 'L', $fill);
            }

            $newPageNumber = $this->PageNo();
            if ($pageNumber != $newPageNumber) {
                $yPositions = [];
            }
            $yPositions[] = $this->GetY();
            $this->SetXY($x + $w[0] + $w[1], $y);

            if (isset($row[2])) {
                if (filter_var($row[2], FILTER_VALIDATE_URL)) {
                    if (ENV !== ENV_PROD) {
                        $row[2] = str_replace('https://profeelexpert.code:8890/medias/644bcc2d8ecf7498709257/', 'https://app.rcpj.fr/medias/645a90e42ce4f864344643/', $row[2]);
                        $row[2] = str_replace('https://profeelexpert.code:8890/medias/645a90e42ce4f864344643/', 'https://app.rcpj.fr/medias/645a90e42ce4f864344643/', $row[2]);
                    }
                    $this->Image($row[2], null, null,$w[2]);
                } elseif (str_contains($row[2], WEB_PATH)) {
                    $size = @getimagesize($row[2]);
                    if (!$size) {
                        $this->Cell($w[2], $lineHeight, '', 'T', 0, 'L');
                    } else {
                        $imageWidth = $size[0];
                        $imageHeight = $size[1];
                        $height = round($imageHeight * $w[2] / $imageWidth);
                        $this->Cell($w[2], $lineHeight, $this->Image($row[2], null, null, $w[2], $height), 'T', 0, 'C');
                    }
                } else {
                    $this->MultiCell($w[2], $lineHeight, $this->formatText($row[2]), 'T', 'L', $fill);
                }

                $newPageNumber = $this->PageNo();
                if ($pageNumber != $newPageNumber) {
                    $yPositions = [];
                }
                $yPositions[] = $this->GetY();
                $this->SetXY($x + $w[0] + $w[1] + $w[2], $y);
            }

            if (isset($row[3])) {
                if (filter_var($row[3], FILTER_VALIDATE_URL)) {
                    if (ENV !== ENV_PROD) {
                        $row[3] = str_replace('https://profeelexpert.code:8890/medias/644bcc2d8ecf7498709257/', 'https://app.rcpj.fr/medias/645a90e42ce4f864344643/', $row[3]);
                        $row[3] = str_replace('https://profeelexpert.code:8890/medias/645a90e42ce4f864344643/', 'https://app.rcpj.fr/medias/645a90e42ce4f864344643/', $row[3]);
                    }
                    $this->Image($row[3], null, null,$w[3]);
                } elseif (str_contains($row[3], WEB_PATH)) {
                    $size = @getimagesize($row[3]);
                    if (!$size) {
                        $this->Cell($w[3], $lineHeight, '', 'T', 0, 'L');
                    } else {
                        $imageWidth = $size[0];
                        $imageHeight = $size[1];
                        $height = round($imageHeight * $w[3] / $imageWidth);
                        $this->Cell($w[3], $lineHeight, $this->Image($row[3], null, null, $w[3], $height), 'T', 0, 'C');
                    }
                } else {
                    $this->MultiCell($w[3], $lineHeight, $this->formatText($row[3]), 'T', 'L', $fill);
                }

                $newPageNumber = $this->PageNo();
                /*if ($pageNumber != $newPageNumber) {
                    $yPositions = [];
                }*/
                $yPositions[] = $this->GetY();
            }

            //echo $row[0];
            //p($yPositions);

            $newPageNumber = $this->PageNo();
            if ($pageNumber == $newPageNumber) {
                $y = max($yPositions);
            } else {
                $pageNumber = $newPageNumber;
                $y = min($yPositions);
            }

            //echo $row[0] . '<br>';
            //p($yPositions);
            //var_dump($y);
            //echo '<br><br>';
            $fill = !$fill;
        }
        // Trait de terminaison
        //$y = $this->GetY();
        //var_dump($y);
        //exit();
        $this->SetXY($x, $y);
        $this->Cell(array_sum($w),0,'','T');
    }

    /**
     * @param array $data
     * @return void
     */
    public function addTablePictures(array $data)
    {
        $w = array(95, 95);
        foreach($data as $row) {
            $this->Cell($w[0], 6, $this->formatText($row[0]), 0, 0, 'L');
            $this->Image($row[1], null, null, 50);
            $this->Ln(20);
        }
    }

    /**
     * @param string|null $text
     * @return string
     */
    public function formatText(?string $text = null): string
    {
        if ($text === null) {
            return '';
        }

        $text = str_replace('&#039;', '\'', $text);
        $text = str_replace('&#39;', '\'', $text);
        $text = str_replace(' €', ' Euros', $text);
        $text = str_replace('€', 'Euros', $text);
        return mb_convert_encoding($text, 'ISO-8859-1', 'UTF-8');
    }
}
