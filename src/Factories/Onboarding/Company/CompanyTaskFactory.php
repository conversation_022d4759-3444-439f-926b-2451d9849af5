<?php

namespace Mat<PERSON>yver\Factories\Onboarding\Company;

use Mat<PERSON>yver\Entity\Onboarding\OnboardingTask;
use MatGyver\Factories\Onboarding\AbstractTaskFactory;
use MatGyver\Factories\Onboarding\TaskInterface;
use MatGyver\FormsFactory\Config\ConfigContactInformationsFormFactory;
use MatGyver\FormsFactory\FormFactory;

/**
 * Class CompanyTaskFactory
 * @package MatGyver\Services\Onboarding\Groups\Branding
 */
class CompanyTaskFactory extends AbstractTaskFactory implements TaskInterface
{
    /**
     * CompanyTaskFactory constructor.
     */
    public function __construct()
    {
        $this->setTitle(__('Société'));
        $this->setIcon('
            <span class="svg-icon">                
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <polygon points="0 0 24 0 24 24 0 24"></polygon>
                        <path d="M6,5 L18,5 C19.6568542,5 21,6.34314575 21,8 L21,17 C21,18.6568542 19.6568542,20 18,20 L6,20 C4.34314575,20 3,18.6568542 3,17 L3,8 C3,6.34314575 4.34314575,5 6,5 Z M5,17 L14,17 L9.5,11 L5,17 Z M16,14 C17.6568542,14 19,12.6568542 19,11 C19,9.34314575 17.6568542,8 16,8 C14.3431458,8 13,9.34314575 13,11 C13,12.6568542 14.3431458,14 16,14 Z" fill="#000000"></path>
                    </g>
                </svg>
            </span>');
    }

    /**
     * @param OnboardingTask $task
     * @return string
     */
    public function render(OnboardingTask $task): string
    {
        $contactForm = new ConfigContactInformationsFormFactory();
        if ($contactForm->isSubmitted() and $contactForm->isValid()) {
            $process = $contactForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                $this->onboardingTaskClientService->setTaskCompleted($task);
            }
        }
        return $this->builderForm->render($contactForm);
    }
}
