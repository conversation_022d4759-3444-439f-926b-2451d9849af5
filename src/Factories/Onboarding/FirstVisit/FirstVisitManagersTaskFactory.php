<?php

namespace MatGyver\Factories\Onboarding\FirstVisit;

use MatG<PERSON><PERSON>\Entity\Onboarding\OnboardingTask;
use MatGyver\Factories\Onboarding\AbstractTaskFactory;
use MatGyver\Factories\Onboarding\TaskInterface;
use MatGyver\FormsFactory\User\UserManagerCreateFormFactory;
use MatGyver\Services\TwigService;

/**
 * Class FirstVisitManagersTaskFactory
 * @package MatGyver\Services\Onboarding\Groups\FirstVisit
 */
class FirstVisitManagersTaskFactory extends AbstractTaskFactory implements TaskInterface
{
    /**
     * FirstVisitManagersTaskFactory constructor.
     */
    public function __construct()
    {
        $this->setTitle(__('Gestionnaires'));
    }

    /**
     * @param OnboardingTask $task
     * @return string
     */
    public function render(OnboardingTask $task): string
    {
        $content = TwigService::getInstance()->render('app/onboarding/first_visit/managers.php');

        $managerCreateForm = new UserManagerCreateFormFactory();
        $content .= $this->builderForm->render($managerCreateForm);
        $content = str_replace('<form class="form " method="post" action="" id="form">', '', $content);
        $content = str_replace('</form>', '', $content);
        $content = str_replace("\n", '', $content);
        $content = preg_replace('/<div class="card-toolbar">(.*?)<\/div>/', '', $content);

        return $content;
    }
}
