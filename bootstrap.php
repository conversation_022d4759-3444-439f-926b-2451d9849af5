<?php
date_default_timezone_set('Europe/Paris');
setlocale(LC_ALL, 'fr_FR');
@ini_set('memory_limit', '1024M');
@ini_set('upload_max_filesize', '100M');
@ini_set('post_max_size', '100M');
@ini_set('max_execution_time', '600');

// Security
if (isset($_SERVER['REQUEST_URI']) and (str_contains($_SERVER['REQUEST_URI'], 'medias/') or str_contains($_SERVER['REQUEST_URI'], 'mail/'))) {
    //ok
} else {
    header('X-Frame-Options: DENY');
}
header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');

// Autoloading
require_once FULL_DOCUMENT_ROOT . '/vendor/autoload.php';

require_once FULL_DOCUMENT_ROOT . '/config/environments.php';

// Load local conf
require_once FULL_DOCUMENT_ROOT . '/config/config.php';

// Check env is set
if (!defined('ENV')) {
    exit('Please set ENV in config/config.php');
}

// Start session
if (defined('SESSIONS_PATH') && !empty(SESSIONS_PATH)) {
    session_save_path(SESSIONS_PATH);
}
ini_set('session.gc_maxlifetime', SESSION_TIMEOUT);
ini_set('session.cookie_httponly', true);
ini_set('session.cookie_secure', true);
//ini_set('session.cookie_samesite', 'Strict');
ini_set('session.use_strict_mode', true);
session_start();

require_once CONFIG_PATH . '/error_handler.php';

// Init custom translation functions
$t = new \Gettext\Translator();
\Gettext\TranslatorFunctions::register($t);

// Get DI container
$container = MatGyver\Services\DI\ContainerBuilderService::getInstance();

$i18n = $container->get(\MatGyver\Services\I18nService::class);
$i18n->setLanguage();

// Bootstrap Doctrine ORM
$isDevMode = ENV === ENV_DEV;
$doctrineConfig = \Doctrine\ORM\Tools\Setup::createAnnotationMetadataConfiguration(array(FULL_DOCUMENT_ROOT . '/src/Entity'), $isDevMode, CACHE_PATH . '/doctrine/proxies', null, false);
$entityManager = $container->get(\MatGyver\Services\EntityService::class)->createEntityManager();

// Bootstrap Gedmo DoctrineExtensions
$cachedAnnotationReader = $doctrineConfig->getMetadataDriverImpl()->getReader();
Gedmo\DoctrineExtensions::registerAbstractMappingIntoDriverChainORM(
    new \Doctrine\Persistence\Mapping\Driver\MappingDriverChain(),
    $cachedAnnotationReader
);
$eventManager = $entityManager->getEventManager();

// clean associations
$cleanAssociationsSubscriber = new \MatGyver\EventListener\Doctrine\CleanAssociationsSubscriber();
$eventManager->addEventSubscriber($cleanAssociationsSubscriber);

// timestampable
$timestampableListener = new Gedmo\Timestampable\TimestampableListener();
$timestampableListener->setAnnotationReader($cachedAnnotationReader);
$eventManager->addEventSubscriber($timestampableListener);

// sortable
$sortableListener = new Gedmo\Sortable\SortableListener();
$sortableListener->setAnnotationReader($cachedAnnotationReader);
$eventManager->addEventSubscriber($sortableListener);

// blameable
/*$blameableListener = new Gedmo\Blameable\BlameableListener();
$loggedUser = $container->get(\MatGyver\Services\Users\UsersService::class)->getUser();
if (null !== $loggedUser) {
    $blameableListener->setUserValue($loggedUser);
}
$blameableListener->setAnnotationReader($cachedAnnotationReader);
$eventManager->addEventSubscriber($blameableListener);*/


// soft deleteable
$softDeleteableListener = new Gedmo\SoftDeleteable\SoftDeleteableListener();
$softDeleteableListener->setAnnotationReader($cachedAnnotationReader);
$eventManager->addEventSubscriber($softDeleteableListener);
$entityManager->getFilters()->enable('soft-deleteable');

// Bootstrap EventDispatcher
$dispatcher = new \Symfony\Component\EventDispatcher\EventDispatcher();
$container->set(get_class($dispatcher), $dispatcher);

// Autowire Subscribers
$container->get(\MatGyver\EventListener\Subscriber\DependencyInjectionSubscriberResolver::class)->resolve();

//required files
require_once HELPERS_PATH . '/datetime.php';
require_once HELPERS_PATH . '/functions.php';


// Verify actual host and redirect if needed
if (!isset($skipCheckClient) or !$skipCheckClient) {
    $clientsService = $container->get(MatGyver\Services\Clients\ClientsService::class);
    $clientsService->checkClient();
}

//client ID
if (!isset($_SESSION['client']['id'])) {
    $_SESSION['client']['id'] = CLIENT_MASTER;
    $_SESSION['client_uniqid'] = 'master';
}

// Debugbar init
if (MatGyver\Services\DebugBar\DebugBarService::showDevToolbar()) {
    MatGyver\Services\DebugBar\DebugBarService::init();
}
